<template>
  <div class="workstation-stats-detail">
    <div class="stats-grid">
      <div class="stat-item">
        <span class="stat-label">等待接诊</span>
        <span class="stat-value waiting">{{ stats.waiting }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">正在接诊</span>
        <span class="stat-value progress">{{ stats.inProgress }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已完成</span>
        <span class="stat-value completed">{{ stats.completed }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">未收费</span>
        <span class="stat-value unpaid">{{ stats.unpaid }}</span>
      </div>
    </div>
    <div class="summary-breakdown">
      <div class="summary-item">
        <span class="summary-label">今日收入</span>
        <span class="summary-value income">¥{{ stats.todayIncome }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">平均时长</span>
        <span class="summary-value duration">{{ stats.avgDuration }}分钟</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">回诊患者</span>
        <span class="summary-value return">{{ stats.returnVisit }}人</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">未接诊</span>
        <span class="summary-value not-admitted">{{ stats.notAdmitted }}人</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { workstationStats } from './index'

// 计算属性获取统计数据
const stats = computed(() => workstationStats.value)
</script>

<style scoped>
.workstation-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(88, 86, 214, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(88, 86, 214, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .workstation-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.stat-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
}

.stat-value.waiting {
  color: #007AFF;
}

.stat-value.progress {
  color: #34C759;
}

.stat-value.completed {
  color: #5856D6;
}

.stat-value.unpaid {
  color: #FF9500;
}

.summary-breakdown {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.summary-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.summary-value {
  font-size: 11px;
  font-weight: 600;
}

.summary-value.income {
  color: #34C759;
}

.summary-value.duration {
  color: #5856D6;
}

.summary-value.return {
  color: #FF9500;
}

.summary-value.not-admitted {
  color: #FF3B30;
}
</style>
