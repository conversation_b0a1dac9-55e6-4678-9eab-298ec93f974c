#!/usr/bin/env python3
"""
工作流程问诊演示脚本
"""

import requests
import json
import time

# 配置
API_BASE = "http://localhost:8002/api"
WORKFLOW_API = f"{API_BASE}/workflow"

def print_separator():
    print("\n" + "=" * 60)

def print_step(step, description):
    print(f"\n🔸 {step}. {description}")
    print("-" * 40)

def demo_workflow_inquiry():
    """演示工作流程问诊"""
    print("🏥 多智能体协同预问诊工作流程演示")
    print_separator()
    
    # 1. 启动工作流程
    print_step(1, "启动工作流程问诊")
    
    patient_data = {
        "patient_name": "李四",
        "patient_age": 28,
        "patient_gender": "女"
    }
    
    try:
        response = requests.post(f"{WORKFLOW_API}/start", json=patient_data)
        if response.status_code == 200:
            data = response.json()
            session_id = data['session_id']
            print(f"✅ 启动成功，会话ID: {session_id}")
            print(f"📋 欢迎消息:\n{data['message']}")
            print(f"📊 当前进度: {data['progress']['current_agent']}/{data['progress']['total_agents']}")
        else:
            print(f"❌ 启动失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return
    
    # 2. 模拟完整的问诊对话
    print_step(2, "模拟患者回复")
    
    # 精心设计的患者回复，确保包含所需信息
    patient_responses = [
        # 主诉阶段 - 包含症状和时间
        "我头痛三天了，主要在太阳穴附近，有时候还伴有恶心",
        
        # 现病史阶段 - 发病诱因
        "是因为最近工作压力特别大，经常加班到很晚",
        
        # 现病史阶段 - 症状变化
        "头痛症状逐渐加重，从轻微疼痛变成现在的剧烈疼痛",
        
        # 现病史阶段 - 影响因素
        "休息的时候会缓解一些，但是一工作就又开始痛了",
        
        # 既往史阶段 - 既往疾病
        "以前身体一直很健康，没有得过什么大病",
        
        # 既往史阶段 - 过敏史
        "没有药物过敏史，食物也没有过敏的"
    ]
    
    for i, message in enumerate(patient_responses, 1):
        print(f"\n第{i}轮对话:")
        print(f"👤 患者: {message}")
        
        try:
            response = requests.post(f"{WORKFLOW_API}/chat", json={
                "session_id": session_id,
                "message": message
            })
            
            if response.status_code == 200:
                data = response.json()
                print(f"🤖 AI回复: {data['message'][:100]}...")
                print(f"📊 进度: {data['progress']['progress_percentage']:.1f}% - {data['progress']['current_agent_name']}")
                
                if data['status'] == 'completed':
                    print("\n🎉 工作流程问诊完成！")
                    print(f"📋 完整回复:\n{data['message']}")
                    break
                    
            else:
                print(f"❌ 对话失败: {response.status_code}")
                break
                
        except Exception as e:
            print(f"❌ 对话请求失败: {e}")
            break
        
        # 短暂等待
        time.sleep(1)
    
    # 3. 获取最终会话信息
    print_step(3, "获取最终会话信息")
    
    try:
        response = requests.get(f"{WORKFLOW_API}/session/{session_id}")
        if response.status_code == 200:
            data = response.json()
            print("✅ 会话信息获取成功")
            print(f"👤 患者: {data['patient_info']['name']}")
            print(f"📅 开始时间: {data['start_time']}")
            print(f"📊 状态: {data['status']}")
            print(f"📋 智能体报告数量: {len(data['agent_reports'])}")
            
            # 显示智能体报告
            if data['agent_reports']:
                print("\n📋 智能体工作报告:")
                for report in data['agent_reports']:
                    print(f"\n🤖 {report['agent_name']}:")
                    print(f"   {report['report']}")
        else:
            print(f"❌ 获取会话信息失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 4. 获取对话历史
    print_step(4, "获取完整对话历史")
    
    try:
        response = requests.get(f"{WORKFLOW_API}/history/{session_id}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 对话历史获取成功，共 {data['total_messages']} 条消息")
            
            print("\n📝 完整对话记录:")
            for i, msg in enumerate(data['history'], 1):
                role = "👤 患者" if msg['role'] == 'user' else "🤖 AI助手"
                print(f"{i:2d}. {role}: {msg['content'][:80]}...")
        else:
            print(f"❌ 获取对话历史失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    # 检查服务器状态
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code != 200:
            print("❌ 服务器未运行，请先启动服务器")
            print("运行命令: python quick_start_mvp.py")
            return
        print("✅ 服务器运行正常")
    except:
        print("❌ 无法连接到服务器，请先启动服务器")
        print("运行命令: python quick_start_mvp.py")
        return
    
    # 运行演示
    demo_workflow_inquiry()
    
    print_separator()
    print("🎯 演示完成！")
    print("\n🌐 您可以访问以下页面体验工作流程:")
    print("• 工作流程演示页面: http://localhost:8002/pages/workflow")
    print("• 快捷访问: http://localhost:8002/workflow")
    print("• API文档: http://localhost:8002/api/docs")

if __name__ == "__main__":
    main()
