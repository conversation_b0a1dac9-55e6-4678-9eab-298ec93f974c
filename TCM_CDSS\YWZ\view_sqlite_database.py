#!/usr/bin/env python3
"""
SQLite数据库查看工具
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime

def print_header(title: str):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🗄️ {title}")
    print("=" * 60)

def print_section(title: str):
    """打印章节"""
    print(f"\n🔸 {title}")
    print("-" * 40)

def connect_database():
    """连接数据库"""
    db_path = Path("data/tcm_inquiry.db")
    if not db_path.exists():
        print("❌ 数据库文件不存在，请先运行 setup_database.py")
        return None
    
    return sqlite3.connect(str(db_path))

def view_patients():
    """查看患者信息"""
    print_section("患者信息表")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM patients ORDER BY created_at DESC")
        patients = cursor.fetchall()
        
        if not patients:
            print("📭 暂无患者记录")
            return
        
        print(f"📊 共有 {len(patients)} 位患者:\n")
        
        for patient in patients:
            id, name, age, gender, contact, created_at = patient
            print(f"👤 患者 {id}: {name}")
            print(f"   🎂 年龄: {age or '未提供'}")
            print(f"   ⚧ 性别: {gender or '未提供'}")
            print(f"   📞 联系方式: {contact or '未提供'}")
            print(f"   📅 创建时间: {created_at}")
            print()
            
    except Exception as e:
        print(f"❌ 查看患者信息失败: {e}")
    finally:
        conn.close()

def view_sessions():
    """查看会话信息"""
    print_section("会话信息表")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        cursor.execute('''
        SELECT s.id, s.session_type, s.status, s.start_time, s.end_time, p.name
        FROM sessions s
        LEFT JOIN patients p ON s.patient_id = p.id
        ORDER BY s.start_time DESC
        ''')
        sessions = cursor.fetchall()
        
        if not sessions:
            print("📭 暂无会话记录")
            return
        
        print(f"📊 共有 {len(sessions)} 个会话:\n")
        
        for session in sessions:
            session_id, session_type, status, start_time, end_time, patient_name = session
            print(f"💬 会话: {session_id}")
            print(f"   👤 患者: {patient_name or '未知'}")
            print(f"   🔄 类型: {session_type}")
            print(f"   📊 状态: {status}")
            print(f"   🕐 开始: {start_time}")
            print(f"   🕑 结束: {end_time or '进行中'}")
            print()
            
    except Exception as e:
        print(f"❌ 查看会话信息失败: {e}")
    finally:
        conn.close()

def view_messages(session_id: str = None):
    """查看消息记录"""
    print_section("消息记录表")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        if session_id:
            cursor.execute('''
            SELECT id, session_id, role, content, timestamp
            FROM messages
            WHERE session_id = ?
            ORDER BY timestamp
            ''', (session_id,))
        else:
            cursor.execute('''
            SELECT id, session_id, role, content, timestamp
            FROM messages
            ORDER BY timestamp DESC
            LIMIT 20
            ''')
        
        messages = cursor.fetchall()
        
        if not messages:
            print("📭 暂无消息记录")
            return
        
        if session_id:
            print(f"📊 会话 {session_id} 的消息记录:\n")
        else:
            print(f"📊 最近 {len(messages)} 条消息:\n")
        
        for message in messages:
            msg_id, sess_id, role, content, timestamp = message
            role_icon = "👤" if role == "user" else "🤖" if role == "assistant" else "⚙️"
            role_name = "患者" if role == "user" else "AI助手" if role == "assistant" else "系统"
            
            print(f"{role_icon} {role_name} [{timestamp}]")
            print(f"   会话: {sess_id}")
            print(f"   内容: {content[:100]}{'...' if len(content) > 100 else ''}")
            print()
            
    except Exception as e:
        print(f"❌ 查看消息记录失败: {e}")
    finally:
        conn.close()

def view_agent_reports():
    """查看智能体报告"""
    print_section("智能体报告表")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        cursor.execute('''
        SELECT id, session_id, agent_name, report_content, collected_info, completion_time
        FROM agent_reports
        ORDER BY completion_time DESC
        ''')
        reports = cursor.fetchall()
        
        if not reports:
            print("📭 暂无智能体报告")
            return
        
        print(f"📊 共有 {len(reports)} 个智能体报告:\n")
        
        for report in reports:
            report_id, session_id, agent_name, report_content, collected_info, completion_time = report
            
            print(f"🤖 {agent_name} (ID: {report_id})")
            print(f"   💬 会话: {session_id}")
            print(f"   🕐 完成时间: {completion_time}")
            print(f"   📋 报告内容:")
            for line in report_content.split('\n'):
                if line.strip():
                    print(f"      {line}")
            
            if collected_info:
                try:
                    info = json.loads(collected_info)
                    print(f"   📊 收集信息: {info}")
                except:
                    print(f"   📊 收集信息: {collected_info}")
            print()
            
    except Exception as e:
        print(f"❌ 查看智能体报告失败: {e}")
    finally:
        conn.close()

def view_workflow_records():
    """查看工作流程记录"""
    print_section("工作流程记录表")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        cursor.execute('''
        SELECT id, patient_info, inquiry_summary, created_at
        FROM workflow_records
        ORDER BY created_at DESC
        ''')
        records = cursor.fetchall()
        
        if not records:
            print("📭 暂无工作流程记录")
            return
        
        print(f"📊 共有 {len(records)} 条工作流程记录:\n")
        
        for record in records:
            record_id, patient_info, inquiry_summary, created_at = record
            
            try:
                patient = json.loads(patient_info)
                summary = json.loads(inquiry_summary)
                
                print(f"🔄 工作流程: {record_id}")
                print(f"   👤 患者: {patient['name']} ({patient.get('age', '未知')}岁, {patient.get('gender', '未知')})")
                print(f"   📅 创建时间: {created_at}")
                print(f"   ⏱️ 问诊时长: {summary.get('duration_minutes', 0):.1f}分钟")
                print(f"   📊 完成度: {summary.get('completeness', '未知')}")
                print(f"   🤖 智能体数量: {summary.get('total_agents', 0)}")
                print()
                
            except Exception as e:
                print(f"❌ 解析记录失败: {e}")
            
    except Exception as e:
        print(f"❌ 查看工作流程记录失败: {e}")
    finally:
        conn.close()

def view_database_statistics():
    """查看数据库统计"""
    print_section("数据库统计信息")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 获取表统计
        tables = ['patients', 'sessions', 'messages', 'agent_reports', 'workflow_records', 'system_config']
        
        print("📊 数据表统计:")
        total_records = 0
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total_records += count
                print(f"   📋 {table}: {count} 条记录")
            except:
                print(f"   ❌ {table}: 查询失败")
        
        print(f"\n📈 总记录数: {total_records}")
        
        # 数据库文件信息
        db_path = Path("data/tcm_inquiry.db")
        file_size = db_path.stat().st_size
        print(f"💾 数据库大小: {file_size / 1024:.1f} KB")
        
        # 最近活动
        cursor.execute("SELECT MAX(created_at) FROM workflow_records")
        latest_workflow = cursor.fetchone()[0]
        if latest_workflow:
            print(f"🕒 最新工作流程: {latest_workflow}")
        
        cursor.execute("SELECT MAX(timestamp) FROM messages")
        latest_message = cursor.fetchone()[0]
        if latest_message:
            print(f"💬 最新消息: {latest_message}")
            
    except Exception as e:
        print(f"❌ 查看统计信息失败: {e}")
    finally:
        conn.close()

def search_data(keyword: str):
    """搜索数据"""
    print_section(f"搜索结果: '{keyword}'")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 搜索患者
        cursor.execute("SELECT name, age, gender FROM patients WHERE name LIKE ?", (f"%{keyword}%",))
        patients = cursor.fetchall()
        if patients:
            print("👤 匹配的患者:")
            for patient in patients:
                print(f"   {patient[0]} ({patient[1]}岁, {patient[2]})")
        
        # 搜索消息
        cursor.execute("SELECT session_id, role, content, timestamp FROM messages WHERE content LIKE ? LIMIT 10", (f"%{keyword}%",))
        messages = cursor.fetchall()
        if messages:
            print(f"\n💬 匹配的消息 (最多10条):")
            for msg in messages:
                role_name = "患者" if msg[1] == "user" else "AI助手"
                print(f"   [{msg[3]}] {role_name}: {msg[2][:50]}...")
        
        if not patients and not messages:
            print("📭 未找到匹配的数据")
            
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
    finally:
        conn.close()

def main():
    """主函数"""
    print_header("SQLite数据库查看工具")
    
    print("\n🎯 可用功能:")
    print("1. 查看患者信息")
    print("2. 查看会话信息")
    print("3. 查看消息记录")
    print("4. 查看智能体报告")
    print("5. 查看工作流程记录")
    print("6. 查看数据库统计")
    print("7. 搜索数据")
    print("8. 查看所有数据")
    
    while True:
        print("\n" + "=" * 40)
        choice = input("请选择功能 (1-8, q退出): ").strip()
        
        if choice == 'q':
            print("👋 再见！")
            break
        elif choice == '1':
            view_patients()
        elif choice == '2':
            view_sessions()
        elif choice == '3':
            session_id = input("输入会话ID (回车查看最近20条): ").strip()
            view_messages(session_id if session_id else None)
        elif choice == '4':
            view_agent_reports()
        elif choice == '5':
            view_workflow_records()
        elif choice == '6':
            view_database_statistics()
        elif choice == '7':
            keyword = input("输入搜索关键词: ").strip()
            if keyword:
                search_data(keyword)
            else:
                print("❌ 请输入搜索关键词")
        elif choice == '8':
            view_patients()
            view_sessions()
            view_agent_reports()
            view_workflow_records()
            view_database_statistics()
        else:
            print("❌ 无效选择，请输入 1-8 或 q")

if __name__ == "__main__":
    main()
