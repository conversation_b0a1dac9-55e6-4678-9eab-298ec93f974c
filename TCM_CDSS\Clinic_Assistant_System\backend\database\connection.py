"""
数据库连接配置
SQLite数据库连接管理
"""

import sqlite3
import aiosqlite
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
import asyncio

# 数据库文件路径
DB_PATH = Path(__file__).parent.parent.parent / "datasets" / "tcm_clinic_system.db"

# 检查数据库文件是否存在
if not DB_PATH.exists():
    raise FileNotFoundError(f"Database file not found: {DB_PATH}")

class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: str = str(DB_PATH)):
        self.db_path = db_path
        self._connection = None

    async def get_connection(self):
        """获取异步数据库连接"""
        if self._connection is None:
            self._connection = await aiosqlite.connect(self.db_path)
            # 启用外键约束
            await self._connection.execute('PRAGMA foreign_keys = ON')
            # 设置WAL模式提高并发性能
            await self._connection.execute('PRAGMA journal_mode = WAL')
            # 设置同步模式
            await self._connection.execute('PRAGMA synchronous = NORMAL')
            # 设置缓存大小
            await self._connection.execute('PRAGMA cache_size = 10000')
            # 设置临时存储为内存
            await self._connection.execute('PRAGMA temp_store = MEMORY')
        return self._connection

    async def close_connection(self):
        """关闭数据库连接"""
        if self._connection:
            await self._connection.close()
            self._connection = None

# 全局数据库管理器实例
db_manager = DatabaseManager()

async def test_connection() -> bool:
    """测试数据库连接"""
    try:
        conn = await db_manager.get_connection()
        await conn.execute('SELECT 1')
        print('✅ Database connection established successfully')
        return True
    except Exception as error:
        print(f'❌ Unable to connect to database: {error}')
        return False

async def close_connection():
    """关闭数据库连接"""
    try:
        await db_manager.close_connection()
        print('📴 Database connection closed')
    except Exception as error:
        print(f'❌ Error closing database connection: {error}')

async def execute_query(sql: str, params: tuple = ()) -> List[Dict[str, Any]]:
    """执行SQL查询"""
    try:
        conn = await db_manager.get_connection()
        async with conn.execute(sql, params) as cursor:
            rows = await cursor.fetchall()
            # 获取列名
            columns = [description[0] for description in cursor.description]
            # 转换为字典列表
            return [dict(zip(columns, row)) for row in rows]
    except Exception as error:
        print(f'❌ Query execution error: {error}')
        raise error

async def execute_insert(sql: str, params: tuple = ()) -> int:
    """执行INSERT查询并返回lastrowid"""
    try:
        conn = await db_manager.get_connection()
        async with conn.execute(sql, params) as cursor:
            await conn.commit()
            return cursor.lastrowid
    except Exception as error:
        print(f'❌ Insert execution error: {error}')
        await conn.rollback()
        raise error

async def execute_transaction(callback):
    """执行事务"""
    conn = await db_manager.get_connection()
    try:
        await conn.execute('BEGIN')
        result = await callback(conn)
        await conn.commit()
        return result
    except Exception as error:
        await conn.rollback()
        print(f'❌ Transaction error: {error}')
        raise error

async def health_check() -> Dict[str, Any]:
    """数据库健康检查"""
    try:
        # 检查连接
        conn = await db_manager.get_connection()
        await conn.execute('SELECT 1')

        # 检查表是否存在
        tables = await execute_query(
            "SELECT name FROM sqlite_master WHERE type='table'"
        )

        # 检查数据完整性
        integrity_check = await execute_query('PRAGMA integrity_check')

        return {
            "status": "healthy",
            "connection": True,
            "tables": len(tables),
            "integrity": integrity_check[0]["integrity_check"] == "ok" if integrity_check else False,
            "timestamp": asyncio.get_event_loop().time()
        }
    except Exception as error:
        return {
            "status": "unhealthy",
            "connection": False,
            "error": str(error),
            "timestamp": asyncio.get_event_loop().time()
        }

async def get_database_stats() -> Dict[str, Any]:
    """获取数据库统计信息"""
    try:
        stats = {}

        # 获取表统计
        tables = [
            'staff', 'patients', 'appointments', 'visits',
            'medical_records', 'prescriptions', 'prescription_items'
        ]

        for table in tables:
            try:
                count_result = await execute_query(f'SELECT COUNT(*) as count FROM {table}')
                stats[table] = count_result[0]['count'] if count_result else 0
            except Exception:
                stats[table] = 0

        # 获取数据库文件大小
        file_stats = os.stat(DB_PATH)
        stats['file_size'] = file_stats.st_size
        stats['file_size_mb'] = round(file_stats.st_size / 1024 / 1024, 2)

        return stats
    except Exception as error:
        print(f'❌ Error getting database stats: {error}')
        raise error

async def backup_database(backup_path: Optional[str] = None) -> str:
    """备份数据库"""
    try:
        if not backup_path:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = Path(__file__).parent.parent.parent / "backups"
            backup_path = backup_dir / f"backup_{timestamp}.db"

        # 确保备份目录存在
        backup_dir = Path(backup_path).parent
        backup_dir.mkdir(parents=True, exist_ok=True)

        # 复制数据库文件
        import shutil
        shutil.copy2(DB_PATH, backup_path)

        print(f'✅ Database backed up to: {backup_path}')
        return str(backup_path)
    except Exception as error:
        print(f'❌ Backup error: {error}')
        raise error

# 导出的函数和变量
__all__ = [
    'db_manager',
    'test_connection',
    'close_connection',
    'execute_query',
    'execute_insert',
    'execute_transaction',
    'health_check',
    'get_database_stats',
    'backup_database',
    'DB_PATH'
]
