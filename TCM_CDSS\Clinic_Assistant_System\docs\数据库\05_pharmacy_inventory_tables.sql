-- =====================================================
-- 中医诊所助手系统 - 药品与库存组表结构
-- =====================================================
-- 文件: 05_pharmacy_inventory_tables.sql
-- 描述: 创建药品和库存相关表
-- 版本: v1.0
-- 创建日期: 2025-06-24
-- =====================================================

USE `tcm_clinic_system`;

-- =====================================================
-- 4.4 药品与库存组
-- =====================================================

-- 4.4.1 供应商表（需要先创建，因为产品表引用）
CREATE TABLE `suppliers` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '供应商ID',
    `name` VARCHAR(100) NOT NULL COMMENT '供应商名称',
    `contact_person` VARCHAR(50) NULL COMMENT '联系人',
    `phone` VARCHAR(20) NULL COMMENT '联系电话',
    `address` VARCHAR(255) NULL COMMENT '地址',
    `email` VARCHAR(100) NULL COMMENT '邮箱',
    `business_license` VARCHAR(100) NULL COMMENT '营业执照号',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_suppliers_name` (`name`),
    KEY `idx_suppliers_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商表';

-- 4.4.2 产品表（药品、耗材、服务）
CREATE TABLE `products` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '产品ID',
    `product_code` VARCHAR(50) NOT NULL COMMENT '产品编码',
    `name` VARCHAR(100) NOT NULL COMMENT '项目名称',
    `pinyin_code` VARCHAR(50) NULL COMMENT '拼音码，用于快速搜索',
    `product_type` ENUM('herbal', 'patent', 'western', 'service', 'consumable') NOT NULL COMMENT '产品类型',
    `category` VARCHAR(50) NULL COMMENT '分类',
    `specifications` VARCHAR(100) NULL COMMENT '规格',
    `unit` VARCHAR(20) NOT NULL COMMENT '基本单位',
    `cost_price` DECIMAL(10,2) NULL COMMENT '成本价',
    `selling_price` DECIMAL(10,2) NOT NULL COMMENT '销售价',
    `supplier_id` INT NULL COMMENT '供应商ID',
    `manufacturer` VARCHAR(100) NULL COMMENT '生产厂家',
    `approval_number` VARCHAR(100) NULL COMMENT '批准文号',
    `expiry_days` INT NULL COMMENT '有效期天数',
    `storage_condition` VARCHAR(100) NULL COMMENT '储存条件',
    `is_prescription` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否处方药',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_products_code` (`product_code`),
    KEY `idx_products_name` (`name`),
    KEY `idx_products_pinyin` (`pinyin_code`),
    KEY `idx_products_type` (`product_type`),
    KEY `idx_products_category` (`category`),
    KEY `idx_products_supplier` (`supplier_id`),
    KEY `idx_products_active` (`is_active`),
    CONSTRAINT `fk_products_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';

-- 4.4.3 库存表
CREATE TABLE `inventory_stock` (
    `product_id` INT NOT NULL COMMENT '产品ID',
    `quantity_on_hand` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '当前库存量',
    `reserved_quantity` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '预留数量',
    `available_quantity` DECIMAL(10,2) GENERATED ALWAYS AS (`quantity_on_hand` - `reserved_quantity`) COMMENT '可用数量',
    `location` VARCHAR(100) NULL COMMENT '存储位置',
    `low_stock_threshold` DECIMAL(10,2) NULL COMMENT '低库存预警阈值',
    `last_updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`product_id`),
    KEY `idx_inventory_stock_low` (`low_stock_threshold`, `quantity_on_hand`),
    KEY `idx_inventory_stock_available` (`available_quantity`),
    CONSTRAINT `fk_inventory_stock_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存表';

-- 4.4.4 库存变动记录表
CREATE TABLE `inventory_transactions` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '变动记录ID',
    `product_id` INT NOT NULL COMMENT '产品ID',
    `transaction_type` ENUM('in', 'out', 'adjust', 'expired', 'damaged') NOT NULL COMMENT '变动类型',
    `quantity` DECIMAL(10,2) NOT NULL COMMENT '变动数量（正数为入库，负数为出库）',
    `unit_cost` DECIMAL(10,2) NULL COMMENT '单位成本',
    `reference_type` ENUM('purchase', 'prescription', 'adjustment', 'waste') NULL COMMENT '关联类型',
    `reference_id` INT NULL COMMENT '关联ID',
    `batch_number` VARCHAR(50) NULL COMMENT '批次号',
    `expiry_date` DATE NULL COMMENT '有效期',
    `note` TEXT NULL COMMENT '备注',
    `operator_id` INT NOT NULL COMMENT '操作人',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_inventory_transactions_product` (`product_id`),
    KEY `idx_inventory_transactions_type` (`transaction_type`),
    KEY `idx_inventory_transactions_date` (`created_at`),
    KEY `idx_inventory_transactions_operator` (`operator_id`),
    KEY `idx_inventory_transactions_reference` (`reference_type`, `reference_id`),
    CONSTRAINT `fk_inventory_transactions_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
    CONSTRAINT `fk_inventory_transactions_operator` FOREIGN KEY (`operator_id`) REFERENCES `staff` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存变动记录表';

-- =====================================================
-- 添加处方明细表的外键约束（现在产品表已创建）
-- =====================================================

ALTER TABLE `prescription_items` 
ADD CONSTRAINT `fk_prescription_items_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`);

-- =====================================================
-- 创建触发器：库存变动自动更新库存表
-- =====================================================

DELIMITER $$
CREATE TRIGGER `tr_inventory_transactions_update_stock` 
AFTER INSERT ON `inventory_transactions`
FOR EACH ROW
BEGIN
    -- 如果库存记录不存在，先创建
    INSERT IGNORE INTO `inventory_stock` (`product_id`, `quantity_on_hand`) 
    VALUES (NEW.product_id, 0);
    
    -- 更新库存数量
    UPDATE `inventory_stock` 
    SET `quantity_on_hand` = `quantity_on_hand` + NEW.quantity,
        `last_updated_at` = NOW()
    WHERE `product_id` = NEW.product_id;
END$$
DELIMITER ;

-- =====================================================
-- 创建视图：库存预警
-- =====================================================

CREATE VIEW `v_low_stock_alert` AS
SELECT 
    p.id,
    p.product_code,
    p.name,
    p.product_type,
    p.category,
    p.unit,
    s.quantity_on_hand,
    s.reserved_quantity,
    s.available_quantity,
    s.low_stock_threshold,
    s.location,
    CASE 
        WHEN s.quantity_on_hand <= COALESCE(s.low_stock_threshold, 0) THEN '库存不足'
        WHEN s.quantity_on_hand <= COALESCE(s.low_stock_threshold, 0) * 1.5 THEN '库存偏低'
        ELSE '库存正常'
    END AS stock_status
FROM `products` p
LEFT JOIN `inventory_stock` s ON p.id = s.product_id
WHERE p.is_active = TRUE
  AND p.product_type IN ('herbal', 'patent', 'western', 'consumable')
ORDER BY 
    CASE 
        WHEN s.quantity_on_hand <= COALESCE(s.low_stock_threshold, 0) THEN 1
        WHEN s.quantity_on_hand <= COALESCE(s.low_stock_threshold, 0) * 1.5 THEN 2
        ELSE 3
    END,
    p.name;

-- =====================================================
-- 创建视图：产品库存汇总
-- =====================================================

CREATE VIEW `v_product_inventory_summary` AS
SELECT 
    p.id,
    p.product_code,
    p.name,
    p.product_type,
    p.category,
    p.specifications,
    p.unit,
    p.selling_price,
    s.quantity_on_hand,
    s.reserved_quantity,
    s.available_quantity,
    s.low_stock_threshold,
    s.location,
    (s.quantity_on_hand * p.selling_price) AS inventory_value,
    sup.name AS supplier_name,
    p.is_active
FROM `products` p
LEFT JOIN `inventory_stock` s ON p.id = s.product_id
LEFT JOIN `suppliers` sup ON p.supplier_id = sup.id
WHERE p.is_active = TRUE
ORDER BY p.product_type, p.name;

-- =====================================================
-- 插入示例数据
-- =====================================================

-- 插入供应商数据
INSERT INTO `suppliers` (`name`, `contact_person`, `phone`, `address`, `business_license`) VALUES
('北京同仁堂', '张经理', '010-12345678', '北京市东城区前门大街', '110000000001'),
('广州医药集团', '李经理', '020-87654321', '广州市越秀区中山路', '440000000002'),
('上海医药公司', '王经理', '021-11111111', '上海市黄浦区南京路', '310000000003'),
('天津中药厂', '赵经理', '022-22222222', '天津市和平区解放路', '120000000004');

-- 插入中药饮片产品
INSERT INTO `products` (`product_code`, `name`, `pinyin_code`, `product_type`, `category`, `specifications`, `unit`, `cost_price`, `selling_price`, `supplier_id`, `low_stock_threshold`) VALUES
('CP001', '柴胡', 'CH', 'herbal', '解表药', '统货', '克', 0.30, 0.50, 1, 100),
('CP002', '当归', 'DG', 'herbal', '补血药', '统货', '克', 0.60, 0.80, 1, 100),
('CP003', '白芍', 'BS', 'herbal', '补血药', '统货', '克', 0.40, 0.60, 1, 100),
('CP004', '黄芪', 'HQ', 'herbal', '补气药', '统货', '克', 0.80, 1.20, 1, 100),
('CP005', '甘草', 'GC', 'herbal', '补气药', '统货', '克', 0.20, 0.35, 1, 200),
('CP006', '生姜', 'SJ', 'herbal', '解表药', '鲜品', '克', 0.05, 0.10, 2, 500),
('CP007', '大枣', 'DZ', 'herbal', '补气药', '统货', '克', 0.15, 0.25, 2, 300),
('CP008', '人参', 'RS', 'herbal', '补气药', '红参', '克', 8.00, 12.00, 1, 50);

-- 插入中成药产品
INSERT INTO `products` (`product_code`, `name`, `pinyin_code`, `product_type`, `category`, `specifications`, `unit`, `cost_price`, `selling_price`, `supplier_id`) VALUES
('ZC001', '逍遥丸', 'XYW', 'patent', '理气药', '6g×10袋', '盒', 8.50, 12.80, 2),
('ZC002', '六味地黄丸', 'LWDHW', 'patent', '补肾药', '9g×10丸', '盒', 15.00, 22.50, 2),
('ZC003', '感冒清热颗粒', 'GMQRK', 'patent', '解表药', '12g×10袋', '盒', 6.80, 10.20, 3),
('ZC004', '藿香正气水', 'HXZQS', 'patent', '祛湿药', '10ml×10支', '盒', 12.00, 18.00, 3);

-- 插入医疗服务项目
INSERT INTO `products` (`product_code`, `name`, `product_type`, `category`, `unit`, `selling_price`) VALUES
('SV001', '中医内科诊疗', 'service', '诊疗费', '次', 50.00),
('SV002', '针刺治疗', 'service', '针灸费', '次', 80.00),
('SV003', '推拿治疗', 'service', '推拿费', '次', 60.00),
('SV004', '拔罐治疗', 'service', '理疗费', '次', 40.00),
('SV005', '艾灸治疗', 'service', '理疗费', '次', 50.00);

-- 插入初始库存数据
INSERT INTO `inventory_stock` (`product_id`, `quantity_on_hand`, `low_stock_threshold`, `location`) VALUES
(1, 500, 100, 'A药柜-1层'),
(2, 300, 100, 'A药柜-2层'),
(3, 800, 100, 'A药柜-3层'),
(4, 200, 100, 'B药柜-1层'),
(5, 1000, 200, 'B药柜-2层'),
(6, 2000, 500, '冷藏柜'),
(7, 1500, 300, 'C药柜-1层'),
(8, 80, 50, '贵重药品柜'),
(9, 50, 10, '中成药架-1层'),
(10, 30, 10, '中成药架-2层'),
(11, 40, 10, '中成药架-3层'),
(12, 25, 10, '中成药架-4层');

-- 插入库存变动记录（初始入库）
INSERT INTO `inventory_transactions` (`product_id`, `transaction_type`, `quantity`, `unit_cost`, `reference_type`, `note`, `operator_id`) VALUES
(1, 'in', 500, 0.30, 'purchase', '初始入库', 1),
(2, 'in', 300, 0.60, 'purchase', '初始入库', 1),
(3, 'in', 800, 0.40, 'purchase', '初始入库', 1),
(4, 'in', 200, 0.80, 'purchase', '初始入库', 1),
(5, 'in', 1000, 0.20, 'purchase', '初始入库', 1),
(6, 'in', 2000, 0.05, 'purchase', '初始入库', 1),
(7, 'in', 1500, 0.15, 'purchase', '初始入库', 1),
(8, 'in', 80, 8.00, 'purchase', '初始入库', 1),
(9, 'in', 50, 8.50, 'purchase', '初始入库', 1),
(10, 'in', 30, 15.00, 'purchase', '初始入库', 1),
(11, 'in', 40, 6.80, 'purchase', '初始入库', 1),
(12, 'in', 25, 12.00, 'purchase', '初始入库', 1);

-- =====================================================
-- 插入示例处方数据（现在产品表已创建）
-- =====================================================

-- 插入处方数据
INSERT INTO `prescriptions` (`prescription_number`, `visit_id`, `patient_id`, `doctor_id`, `prescription_type`, `status`, `dosage_instructions`, `total_doses`) VALUES
('R20250624001', 1, 1, 2, 'herbal', 'confirmed', '每日一剂，水煎服，分早晚两次温服', 7),
('R20250624002', 2, 2, 2, 'herbal', 'confirmed', '每日一剂，水煎服，饭前30分钟服用', 14);

-- 插入处方明细数据
INSERT INTO `prescription_items` (`prescription_id`, `product_id`, `quantity`, `unit`, `unit_price`, `total_price`, `usage_method`) VALUES
-- 处方1：逍遥散加减（治疗肝郁脾虚）
(1, 1, 10, '克', 0.50, 5.00, '正常煎煮'),  -- 柴胡
(1, 2, 12, '克', 0.80, 9.60, '正常煎煮'),  -- 当归
(1, 3, 15, '克', 0.60, 9.00, '正常煎煮'),  -- 白芍
(1, 5, 6, '克', 0.35, 2.10, '后下'),      -- 甘草
(1, 7, 5, '枚', 0.25, 1.25, '掰开煎煮'),  -- 大枣

-- 处方2：四君子汤加减（治疗脾胃虚弱）
(2, 4, 15, '克', 1.20, 18.00, '正常煎煮'), -- 黄芪
(2, 3, 12, '克', 0.60, 7.20, '正常煎煮'),  -- 白芍
(2, 5, 6, '克', 0.35, 2.10, '后下'),      -- 甘草
(2, 6, 3, '片', 0.10, 0.30, '后下'),      -- 生姜
(2, 7, 5, '枚', 0.25, 1.25, '掰开煎煮');  -- 大枣
