-- =====================================================
-- 中医诊所助手系统 - 映射表结构
-- =====================================================
-- 文件: create_mappings.sql
-- 描述: 创建所有映射表，用于敏感信息和枚举值的映射
-- 版本: v1.0
-- 创建日期: 2025-06-24
-- =====================================================

PRAGMA foreign_keys = ON;
PRAGMA encoding = 'UTF-8';

-- =====================================================
-- 基础映射表
-- =====================================================

-- 性别映射表
CREATE TABLE IF NOT EXISTS gender_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 状态映射表
CREATE TABLE IF NOT EXISTS status_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category TEXT NOT NULL, -- 状态分类：patient, appointment, visit, prescription等
    code TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category, code)
);

-- 角色映射表
CREATE TABLE IF NOT EXISTS role_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    level INTEGER NOT NULL DEFAULT 1, -- 权限级别
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 科室映射表
CREATE TABLE IF NOT EXISTS department_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    parent_id INTEGER,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES department_mappings(id)
);

-- =====================================================
-- 医疗相关映射表
-- =====================================================

-- 症状映射表
CREATE TABLE IF NOT EXISTS symptom_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    category TEXT, -- 症状分类
    description TEXT,
    icd10_code TEXT, -- ICD-10编码
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 诊断映射表
CREATE TABLE IF NOT EXISTS diagnosis_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    category TEXT, -- 诊断分类：tcm, western
    icd10_code TEXT, -- ICD-10编码
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 证型映射表（中医特色）
CREATE TABLE IF NOT EXISTS syndrome_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    category TEXT, -- 证型分类
    description TEXT,
    treatment_principle TEXT, -- 治法
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 药品类型映射表
CREATE TABLE IF NOT EXISTS product_type_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    category TEXT, -- 大分类：medicine, service, consumable
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 药品分类映射表
CREATE TABLE IF NOT EXISTS product_category_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    parent_id INTEGER,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES product_category_mappings(id)
);

-- 用法映射表
CREATE TABLE IF NOT EXISTS usage_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    instruction TEXT, -- 详细用法说明
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 业务相关映射表
-- =====================================================

-- 预约来源映射表
CREATE TABLE IF NOT EXISTS appointment_source_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 就诊类型映射表
CREATE TABLE IF NOT EXISTS visit_type_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 支付方式映射表
CREATE TABLE IF NOT EXISTS payment_method_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    is_online BOOLEAN NOT NULL DEFAULT 0, -- 是否在线支付
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 权限相关映射表
-- =====================================================

-- 权限模块映射表
CREATE TABLE IF NOT EXISTS permission_module_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    icon TEXT, -- 图标
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 权限操作映射表
CREATE TABLE IF NOT EXISTS permission_action_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 地区映射表
-- =====================================================

-- 地区映射表
CREATE TABLE IF NOT EXISTS region_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    parent_id INTEGER,
    level INTEGER NOT NULL, -- 1:省, 2:市, 3:区县
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES region_mappings(id)
);

-- =====================================================
-- 创建索引
-- =====================================================

-- 基础映射表索引
CREATE INDEX IF NOT EXISTS idx_gender_mappings_code ON gender_mappings(code);
CREATE INDEX IF NOT EXISTS idx_status_mappings_category_code ON status_mappings(category, code);
CREATE INDEX IF NOT EXISTS idx_role_mappings_code ON role_mappings(code);
CREATE INDEX IF NOT EXISTS idx_department_mappings_code ON department_mappings(code);

-- 医疗映射表索引
CREATE INDEX IF NOT EXISTS idx_symptom_mappings_code ON symptom_mappings(code);
CREATE INDEX IF NOT EXISTS idx_symptom_mappings_category ON symptom_mappings(category);
CREATE INDEX IF NOT EXISTS idx_diagnosis_mappings_code ON diagnosis_mappings(code);
CREATE INDEX IF NOT EXISTS idx_diagnosis_mappings_category ON diagnosis_mappings(category);
CREATE INDEX IF NOT EXISTS idx_syndrome_mappings_code ON syndrome_mappings(code);

-- 产品映射表索引
CREATE INDEX IF NOT EXISTS idx_product_type_mappings_code ON product_type_mappings(code);
CREATE INDEX IF NOT EXISTS idx_product_category_mappings_code ON product_category_mappings(code);
CREATE INDEX IF NOT EXISTS idx_usage_mappings_code ON usage_mappings(code);

-- 业务映射表索引
CREATE INDEX IF NOT EXISTS idx_appointment_source_mappings_code ON appointment_source_mappings(code);
CREATE INDEX IF NOT EXISTS idx_visit_type_mappings_code ON visit_type_mappings(code);
CREATE INDEX IF NOT EXISTS idx_payment_method_mappings_code ON payment_method_mappings(code);

-- 权限映射表索引
CREATE INDEX IF NOT EXISTS idx_permission_module_mappings_code ON permission_module_mappings(code);
CREATE INDEX IF NOT EXISTS idx_permission_action_mappings_code ON permission_action_mappings(code);

-- 地区映射表索引
CREATE INDEX IF NOT EXISTS idx_region_mappings_code ON region_mappings(code);
CREATE INDEX IF NOT EXISTS idx_region_mappings_parent ON region_mappings(parent_id);
CREATE INDEX IF NOT EXISTS idx_region_mappings_level ON region_mappings(level);

SELECT 'Mapping tables created successfully!' AS message;
