# 应用配置
APP_NAME="中医预问诊智能体系统"
APP_VERSION="1.0.0"
DEBUG=false
ENVIRONMENT=development
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./data/inquiry.db
DATABASE_ECHO=false

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_PASSWORD=

# LLM配置 - OpenRouter
OPENROUTER_API_KEY=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
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=moonshotai/kimi-dev-72b:free
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2048
LLM_TIMEOUT=30

# 备用Ollama配置（可选）
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:32b

# 安全配置
SECRET_KEY=your-very-secret-key-here-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
PASSWORD_MIN_LENGTH=8

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://localhost:5173"]
CORS_CREDENTIALS=true

# 文件存储配置
UPLOAD_DIR=./data/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=[".pdf", ".doc", ".docx", ".txt"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# 缓存配置
CACHE_TTL=3600
SESSION_TTL=86400

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 工作流配置
MAX_CONVERSATION_ROUNDS=50
MAX_RETRY_ATTEMPTS=3
AGENT_TIMEOUT=30
