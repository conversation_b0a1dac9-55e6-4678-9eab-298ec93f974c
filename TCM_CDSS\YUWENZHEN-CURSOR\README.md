# 中医诊前预问诊智能体系统

基于最新LangChain和LangGraph技术栈开发的多智能体协同中医预问诊系统，通过OpenRouter调用多种大模型，实现智能化的患者病史信息采集。

## 🎯 项目特色

- **多智能体协同**: 10个专业智能体分工协作，覆盖完整病史采集
- **统一逻辑流程**: 所有智能体采用"先问有无，若有则追问，无则结束"的标准化流程
- **OpenRouter集成**: 支持多种大模型，灵活选择最适合的模型
- **结构化输出**: 自动提取医疗实体，生成标准化中医病历
- **智能追问**: 基于患者回答自动生成针对性追问

## 🏗️ 系统架构

```
用户交互层 → API网关层 → 智能体编排层 → 专业智能体层 → 大模型调用层 → 数据处理层 → 数据存储层
```

### 核心组件

- **LangGraph工作流引擎**: 协调多个智能体的执行顺序和状态转换
- **专业智能体层**: 10个专业智能体，每个都有明确的职责分工
- **OpenRouter集成**: 统一的LLM调用接口，支持多种模型选择
- **LCEL管道**: 使用LangChain Expression Language构建数据处理管道

## 🤖 智能体设计

### 统一工作流程

所有智能体都遵循相同的"先问有无，若有则追问，无则结束"逻辑：

1. **询问有无**: 智能体首先询问患者是否有相关症状或情况
2. **解析回答**: 使用LLM解析患者的回答，判断是否有相关情况
3. **追问详情**: 如果有，则生成详细的追问问题
4. **提取信息**: 从患者回答中提取结构化信息和医疗实体
5. **完成判断**: 判断当前智能体是否完成，决定下一步行动

### 核心智能体

| 智能体 | 职责 | 主要功能 |
|--------|------|----------|
| 主诉采集智能体 | 采集主要症状 | 询问症状性质、部位、持续时间等 |
| 现病史采集智能体 | 详细追问当前疾病 | 按症状类别系统询问（寒热、汗症、疼痛等） |
| 既往史采集智能体 | 收集既往疾病史 | 询问既往疾病、手术史、过敏史等 |
| 家族史采集智能体 | 了解家族疾病史 | 询问家族遗传疾病情况 |
| 过敏史采集智能体 | 收集过敏信息 | 询问药物、食物、环境过敏等 |

## 🚀 快速开始

### 环境要求

- Python 3.9+
- OpenRouter API密钥
- PostgreSQL数据库
- Redis缓存

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd ywz2

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp env.example .env
# 编辑.env文件，设置OpenRouter API密钥等配置
```

### 配置环境变量

```bash
# OpenRouter配置
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=anthropic/claude-3.5-sonnet

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost/tcm_db
REDIS_URL=redis://localhost:6379
```

### 运行测试

```bash
# 测试OpenRouter集成
python test_openrouter.py

# 运行单元测试
pytest tests/
```

### 启动服务

```bash
# 使用Docker Compose启动
docker-compose up -d

# 或直接启动Python服务
python -m uvicorn src.main:app --host 0.0.0.0 --port 8000
```

## 📋 API接口

### 会话管理

```bash
# 创建问诊会话
POST /api/v1/consultations
{
    "patient_id": "string",
    "patient_name": "string",
    "gender": "男|女",
    "age": "integer"
}

# 发送消息
POST /api/v1/consultations/{session_id}/messages
{
    "message": "string",
    "message_type": "text"
}
```

### 智能体管理

```bash
# 获取可用智能体列表
GET /api/v1/agents

# 手动切换智能体
POST /api/v1/consultations/{session_id}/switch-agent
{
    "agent_name": "string"
}
```

## 🔧 技术实现

### OpenRouter集成

系统使用OpenRouter作为统一的LLM调用接口，支持多种模型：

```python
from src.utils.openrouter_client import OpenRouterClient, ModelSelector

# 创建客户端
client = OpenRouterClient()

# 模型选择器
selector = ModelSelector()
model = selector.get_model_for_agent("chief_complaint")
```

### 智能体开发

创建新的智能体只需继承BaseAgent并实现必要方法：

```python
class CustomAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="自定义智能体",
            description="智能体描述",
            agent_type="custom"
        )
    
    def ask_has_or_not(self, context: Dict[str, Any]) -> str:
        return "您有相关情况吗？"
    
    def ask_details_if_has(self, context: Dict[str, Any], has_result: Dict[str, Any]) -> str:
        return "请详细说明..."
```

### LangGraph工作流

```python
from langgraph import StateGraph

def create_consultation_workflow():
    workflow = StateGraph(ConsultationState)
    
    # 添加节点
    workflow.add_node("controller", consultation_controller)
    workflow.add_node("chief_complaint_agent", lambda s: execute_agent(s, "chief_complaint"))
    
    # 设置流程
    workflow.set_entry_point("controller")
    
    return workflow.compile()
```

## 📊 数据模型

### 核心数据结构

```python
class AgentResponse(BaseModel):
    response: str
    extracted_data: Dict[str, Any]
    entities: List[Dict[str, Any]]
    confidence: float
    is_complete: bool

class TCMMedicalRecord(BaseModel):
    chief_complaint: str
    present_illness: Dict[str, Any]
    past_history: Dict[str, Any]
    family_history: Dict[str, Any]
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_agents.py

# 生成覆盖率报告
pytest --cov=src tests/
```

### 测试覆盖

- 单元测试：智能体功能、数据模型、工具函数
- 集成测试：API接口、工作流、数据库集成
- 端到端测试：完整问诊流程

## 📈 监控和日志

### 监控指标

- 系统性能：响应时间、吞吐量
- 智能体指标：成功率、准确率
- 业务指标：问诊质量、完成率
- OpenRouter指标：API调用次数、响应时间

### 日志管理

```python
import structlog

logger = structlog.get_logger()
logger.info("智能体执行", agent_name="chief_complaint", confidence=0.95)
```

## 🔒 安全考虑

- 患者数据加密存储
- API接口认证授权
- OpenRouter API密钥保护
- 输入数据验证和清洗

## 🚀 部署

### Docker部署

```bash
# 构建镜像
docker build -t tcm-consultation .

# 启动服务
docker-compose up -d
```

### 生产环境

```bash
# 使用生产配置
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 文档

- [系统设计文档](docs/系统设计文档.md)
- [API文档](docs/API文档.md)
- [部署指南](docs/部署指南.md)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题，请通过Issue或邮件联系。 

## 🚀 演示脚本

- python examples/demo_workflow.py
- # 或
- python examples/demo_agents.py 