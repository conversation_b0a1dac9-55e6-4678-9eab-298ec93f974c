/**
 * 认证服务
 * 
 * 主要功能：
 * 1. 用户登录/登出
 * 2. Token管理
 * 3. 用户信息管理
 * 4. 权限验证
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApiService } from './api'
import { ElMessage } from 'element-plus'

// 用户信息接口
export interface UserInfo {
  id: number
  employee_number: string
  full_name: string
  phone: string
  title: string
  role_name: string
  department_name: string
  avatar?: string
}

// 登录请求接口
export interface LoginRequest {
  phone: string
  password: string
}

// 登录响应接口
export interface LoginResponse {
  token: string
  user: UserInfo
  expires_in: number
}

// Token存储键名
const TOKEN_KEY = 'auth_token'
const USER_KEY = 'user_info'
const TOKEN_EXPIRES_KEY = 'token_expires'

export class AuthService {
  /**
   * 用户登录
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // 临时实现：使用后端测试接口验证用户
      // 实际项目中应该有专门的登录接口
      const response = await ApiService.post('/api/auth/login', credentials)
      
      // 存储认证信息
      this.setToken(response.token)
      this.setUser(response.user)
      this.setTokenExpires(response.expires_in)
      
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  /**
   * 临时登录方法（使用默认账号）
   * 在后端认证接口完成前使用
   */
  static async tempLogin(credentials: LoginRequest): Promise<boolean> {
    try {
      // 验证默认账号
      const defaultAccounts = [
        { phone: 'admin', password: 'admin', role: 'admin', name: '系统管理员' },
        { phone: '***********', password: 'password', role: 'admin', name: '系统管理员' },
        { phone: '***********', password: 'password', role: 'doctor', name: '李时珍' },
        { phone: '***********', password: 'password', role: 'receptionist', name: '前台小王' }
      ]

      const account = defaultAccounts.find(
        acc => acc.phone === credentials.phone && acc.password === credentials.password
      )

      if (account) {
        // 模拟用户信息
        const userInfo: UserInfo = {
          id: 1,
          employee_number: account.role.toUpperCase() + '001',
          full_name: account.name,
          phone: account.phone,
          title: account.role === 'admin' ? '系统管理员' : 
                 account.role === 'doctor' ? '主任医师' : '前台接待',
          role_name: account.role,
          department_name: account.role === 'admin' ? '行政科' : 
                          account.role === 'doctor' ? '中医内科' : '行政科'
        }

        // 生成临时token
        const token = 'temp_token_' + Date.now()
        
        // 存储认证信息
        this.setToken(token)
        this.setUser(userInfo)
        this.setTokenExpires(24 * 60 * 60) // 24小时

        ElMessage.success(`欢迎，${userInfo.full_name}！`)
        return true
      } else {
        ElMessage.error('用户名或密码错误')
        return false
      }
    } catch (error) {
      console.error('Temp login error:', error)
      ElMessage.error('登录失败')
      return false
    }
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      // 调用后端登出接口（如果有）
      // await ApiService.post('/api/auth/logout')
      
      // 清除本地存储
      this.clearAuth()
      
      ElMessage.success('已安全退出')
    } catch (error) {
      console.error('Logout error:', error)
      // 即使后端请求失败，也要清除本地存储
      this.clearAuth()
    }
  }

  /**
   * 获取当前用户信息
   */
  static getCurrentUser(): UserInfo | null {
    const userStr = localStorage.getItem(USER_KEY)
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch (error) {
        console.error('Parse user info error:', error)
        return null
      }
    }
    return null
  }

  /**
   * 获取当前token
   */
  static getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY)
  }

  /**
   * 检查是否已登录
   */
  static isLoggedIn(): boolean {
    const token = this.getToken()
    const expires = localStorage.getItem(TOKEN_EXPIRES_KEY)
    
    if (!token || !expires) {
      return false
    }

    // 检查token是否过期
    const expiresTime = parseInt(expires)
    const now = Date.now()
    
    if (now > expiresTime) {
      this.clearAuth()
      return false
    }

    return true
  }

  /**
   * 检查用户权限
   */
  static hasPermission(permission: string): boolean {
    const user = this.getCurrentUser()
    if (!user) return false

    // 管理员拥有所有权限
    if (user.role_name === 'admin') return true

    // 这里可以根据实际需求实现权限检查逻辑
    // 例如从后端获取用户权限列表进行比较
    return true
  }

  /**
   * 检查用户角色
   */
  static hasRole(role: string): boolean {
    const user = this.getCurrentUser()
    return user?.role_name === role
  }

  /**
   * 存储token
   */
  private static setToken(token: string): void {
    localStorage.setItem(TOKEN_KEY, token)
  }

  /**
   * 存储用户信息
   */
  private static setUser(user: UserInfo): void {
    localStorage.setItem(USER_KEY, JSON.stringify(user))
  }

  /**
   * 存储token过期时间
   */
  private static setTokenExpires(expiresIn: number): void {
    const expiresTime = Date.now() + (expiresIn * 1000)
    localStorage.setItem(TOKEN_EXPIRES_KEY, expiresTime.toString())
  }

  /**
   * 清除认证信息
   */
  private static clearAuth(): void {
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(USER_KEY)
    localStorage.removeItem(TOKEN_EXPIRES_KEY)
    // 同时清除旧的认证信息（兼容性）
    localStorage.removeItem('user')
    localStorage.removeItem('auth_token')
  }

  /**
   * 刷新token
   */
  static async refreshToken(): Promise<boolean> {
    try {
      const response = await ApiService.post('/api/auth/refresh')
      this.setToken(response.token)
      this.setTokenExpires(response.expires_in)
      return true
    } catch (error) {
      console.error('Refresh token error:', error)
      this.clearAuth()
      return false
    }
  }
}

export default AuthService
