"""
问诊API - 支持智能体记忆功能
"""

from fastapi import APIRouter, HTTPException
from models.request_models import InquiryStartRequest, ChatRequest
from models.response_models import InquiryStartResponse, ChatResponse
from services.inquiry_service import inquiry_service

router = APIRouter()

@router.post("/start", response_model=InquiryStartResponse)
async def start_inquiry(request: InquiryStartRequest):
    """开始问诊"""
    try:
        result = inquiry_service.start_inquiry(
            patient_name=request.patient_name,
            patient_age=request.patient_age,
            patient_gender=request.patient_gender
        )

        return InquiryStartResponse(
            status="success",
            session_id=result["session_id"],
            message=result["message"]
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"启动问诊失败: {str(e)}"
        )

@router.post("/chat", response_model=ChatResponse)
async def inquiry_chat(request: ChatRequest):
    """问诊对话 - 使用智能体记忆"""
    try:
        if not request.session_id:
            raise HTTPException(
                status_code=400,
                detail="问诊对话需要提供session_id"
            )

        # 使用问诊服务处理消息
        response_text = inquiry_service.process_inquiry_message(
            session_id=request.session_id,
            message=request.message
        )

        return ChatResponse(
            status="success",
            response=response_text,
            session_id=request.session_id
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"问诊对话失败: {str(e)}"
        )

@router.get("/session/{session_id}")
async def get_inquiry_session(session_id: str):
    """获取问诊会话信息"""
    try:
        session_info = inquiry_service.get_session(session_id)
        if not session_info:
            raise HTTPException(
                status_code=404,
                detail="会话不存在"
            )

        return {
            "status": "success",
            "session": session_info
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取会话信息失败: {str(e)}"
        )

@router.get("/session/{session_id}/summary")
async def get_inquiry_summary(session_id: str):
    """获取问诊会话摘要"""
    try:
        summary = inquiry_service.get_session_summary(session_id)

        return {
            "status": "success",
            "summary": summary
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取会话摘要失败: {str(e)}"
        )
