<template>
  <div class="mall-management-stats-detail">
    <div class="stats-grid">
      <div class="stat-item">
        <span class="stat-label">商品总数</span>
        <span class="stat-value total">{{ stats.totalProducts }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">今日订单</span>
        <span class="stat-value orders">{{ stats.todayOrders }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">月销售额</span>
        <span class="stat-value sales">¥{{ (stats.monthlySales / 1000).toFixed(1) }}K</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">活跃用户</span>
        <span class="stat-value users">{{ stats.activeUsers }}</span>
      </div>
    </div>
    <div class="order-status-breakdown">
      <div class="status-item">
        <span class="status-label">待处理</span>
        <span class="status-value pending">{{ stats.orderStatus.pending }}</span>
      </div>
      <div class="status-item">
        <span class="status-label">处理中</span>
        <span class="status-value processing">{{ stats.orderStatus.processing }}</span>
      </div>
      <div class="status-item">
        <span class="status-label">已发货</span>
        <span class="status-value shipped">{{ stats.orderStatus.shipped }}</span>
      </div>
      <div class="status-item">
        <span class="status-label">已送达</span>
        <span class="status-value delivered">{{ stats.orderStatus.delivered }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { mallManagementStats } from './index'

// 计算属性获取统计数据
const stats = computed(() => mallManagementStats.value)
</script>

<style scoped>
.mall-management-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(255, 107, 53, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(255, 107, 53, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .mall-management-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.stat-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
}

.stat-value.total {
  color: #FF6B35;
}

.stat-value.orders {
  color: #007AFF;
}

.stat-value.sales {
  color: #34C759;
}

.stat-value.users {
  color: #5856D6;
}

.order-status-breakdown {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.status-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.status-value {
  font-size: 11px;
  font-weight: 600;
}

.status-value.pending {
  color: #FF9500;
}

.status-value.processing {
  color: #007AFF;
}

.status-value.shipped {
  color: #5856D6;
}

.status-value.delivered {
  color: #34C759;
}
</style>
