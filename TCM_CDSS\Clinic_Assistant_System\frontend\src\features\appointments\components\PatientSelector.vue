<template>
  <div class="patient-selector">
    <el-select
      v-model="selectedPatientId"
      placeholder="搜索并选择患者"
      filterable
      remote
      reserve-keyword
      :remote-method="searchPatients"
      :loading="loading"
      clearable
      style="width: 100%"
      @change="handlePatientChange"
    >
      <el-option
        v-for="patient in patientOptions"
        :key="patient.id"
        :label="`${patient.name} (${patient.phone})`"
        :value="patient.id"
      >
        <div class="patient-option">
          <div class="patient-info">
            <span class="patient-name">{{ patient.name }}</span>
            <span class="patient-details">{{ patient.gender }} · {{ patient.age }}岁 · {{ patient.phone }}</span>
          </div>
          <div class="patient-diagnosis">{{ patient.diagnosis }}</div>
        </div>
      </el-option>
    </el-select>

    <!-- 新建患者按钮 -->
    <el-button 
      type="text" 
      @click="showCreatePatient = true"
      style="margin-top: 8px; padding: 0;"
    >
      <el-icon><Plus /></el-icon>
      新建患者
    </el-button>

    <!-- 新建患者弹窗 -->
    <el-dialog
      v-model="showCreatePatient"
      title="新建患者"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="patientFormRef"
        :model="patientForm"
        :rules="patientRules"
        label-width="80px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="patientForm.name" placeholder="请输入患者姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="patientForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="patientForm.gender" placeholder="请选择性别">
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
          </el-select>
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input-number 
            v-model="patientForm.age" 
            :min="0" 
            :max="120" 
            placeholder="请输入年龄"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="诊断" prop="diagnosis">
          <el-input 
            v-model="patientForm.diagnosis" 
            placeholder="请输入初步诊断（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreatePatient = false">取消</el-button>
        <el-button type="primary" @click="createPatient">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { patientService, type Patient } from '@/mock/database/patients'

// Props
interface Props {
  modelValue?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: number | null]
  'patient-selected': [patient: Patient | null]
}>()

// 响应式数据
const selectedPatientId = ref<number | null>(props.modelValue)
const patientOptions = ref<Patient[]>([])
const loading = ref(false)
const showCreatePatient = ref(false)

// 新建患者表单
const patientFormRef = ref()
const patientForm = reactive({
  name: '',
  phone: '',
  gender: '',
  age: null as number | null,
  diagnosis: ''
})

const patientRules = {
  name: [{ required: true, message: '请输入患者姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }]
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedPatientId.value = newValue
  if (newValue) {
    loadPatientById(newValue)
  }
})

// 搜索患者
const searchPatients = async (query: string) => {
  if (!query) {
    patientOptions.value = []
    return
  }

  loading.value = true
  try {
    const patients = await patientService.searchPatients(query)
    patientOptions.value = patients.slice(0, 10) // 限制显示数量
  } catch (error) {
    console.error('搜索患者失败:', error)
    ElMessage.error('搜索患者失败')
  } finally {
    loading.value = false
  }
}

// 根据ID加载患者信息
const loadPatientById = async (patientId: number) => {
  try {
    const patient = await patientService.getPatientById(patientId)
    if (patient && !patientOptions.value.find(p => p.id === patient.id)) {
      patientOptions.value.unshift(patient)
    }
  } catch (error) {
    console.error('加载患者信息失败:', error)
  }
}

// 处理患者选择变化
const handlePatientChange = async (patientId: number | null) => {
  emit('update:modelValue', patientId)
  
  if (patientId) {
    const patient = patientOptions.value.find(p => p.id === patientId)
    if (patient) {
      emit('patient-selected', patient)
    } else {
      // 如果在选项中找不到，从数据库加载
      try {
        const patient = await patientService.getPatientById(patientId)
        emit('patient-selected', patient)
      } catch (error) {
        console.error('获取患者信息失败:', error)
        emit('patient-selected', null)
      }
    }
  } else {
    emit('patient-selected', null)
  }
}

// 创建新患者
const createPatient = async () => {
  if (!patientFormRef.value) return
  
  try {
    await patientFormRef.value.validate()
    
    const newPatient = await patientService.addPatient({
      name: patientForm.name,
      phone: patientForm.phone,
      gender: patientForm.gender as '男' | '女',
      age: patientForm.age!,
      diagnosis: patientForm.diagnosis || '待诊断',
      lastVisit: null,
      nextAppointment: null,
      visitCount: 0,
      status: 'active'
    })
    
    // 添加到选项列表
    patientOptions.value.unshift(newPatient)
    
    // 自动选择新创建的患者
    selectedPatientId.value = newPatient.id
    emit('update:modelValue', newPatient.id)
    emit('patient-selected', newPatient)
    
    // 重置表单
    resetPatientForm()
    showCreatePatient.value = false
    
    ElMessage.success('患者创建成功')
  } catch (error) {
    console.error('创建患者失败:', error)
    ElMessage.error('创建患者失败')
  }
}

// 重置患者表单
const resetPatientForm = () => {
  Object.assign(patientForm, {
    name: '',
    phone: '',
    gender: '',
    age: null,
    diagnosis: ''
  })
  if (patientFormRef.value) {
    patientFormRef.value.clearValidate()
  }
}

// 初始化时如果有值，加载患者信息
if (props.modelValue) {
  loadPatientById(props.modelValue)
}
</script>

<style lang="scss" scoped>
.patient-selector {
  width: 100%;
}

.patient-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;
}

.patient-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.patient-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.patient-details {
  font-size: 12px;
  color: #8c8c8c;
}

.patient-diagnosis {
  font-size: 12px;
  color: #1890ff;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
  align-self: flex-start;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
  line-height: normal;
}
</style>
