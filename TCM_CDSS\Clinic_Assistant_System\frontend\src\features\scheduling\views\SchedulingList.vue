<template>
  <div class="scheduling-container">
    <!-- 头部 -->
    <div class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        <span>功能中心</span>
      </div>
      <h1 class="module-title">排班管理</h1>
      <div class="header-actions">
        <el-button :icon="Download" @click="exportWeekSchedule">
          导出本周排班
        </el-button>
        <el-button type="primary" :icon="Plus" @click="openAddEditDialog(null)">
          新增排班
        </el-button>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-select v-model="selectedDoctor" placeholder="所有医生" clearable style="width: 200px;">
        <el-option
          v-for="doctor in doctors"
          :key="doctor.id"
          :label="doctor.name"
          :value="doctor.id"
        />
      </el-select>
    </div>

    <!-- 日历核心区 -->
    <div class="calendar-wrapper">
      <el-calendar v-model="calendarDate">
        <template #date-cell="{ data }">
          <div class="date-cell" @click="openAddEditDialog(null, new Date(data.day))">
            <p class="date-number">{{ data.day.split('-').slice(2).join('-') }}</p>
            <div class="schedule-items">
              <div
                v-for="item in getSchedulesForDate(data.day)"
                :key="item.id"
                class="schedule-item"
                :style="{ backgroundColor: getDoctorColor(item.doctorId) }"
                @click.stop="openAddEditDialog(item)"
              >
                {{ getDoctorName(item.doctorId) }}: {{ item.startTime }} - {{ item.endTime }}
              </div>
            </div>
          </div>
        </template>
      </el-calendar>
    </div>

    <!-- 新增/编辑排班对话框 -->
    <el-dialog
      v-model="showDialog"
      :title="isEditMode ? '编辑排班' : '新增排班'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="scheduleFormRef" :model="scheduleForm" :rules="scheduleRules" label-width="80px">
        <el-form-item label="选择医生" prop="doctorId">
          <el-select v-model="scheduleForm.doctorId" placeholder="请选择医生" style="width: 100%;">
            <el-option
              v-for="doctor in doctors"
              :key="doctor.id"
              :label="doctor.name"
              :value="doctor.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="排班日期" prop="date">
          <el-date-picker
            v-model="scheduleForm.date"
            type="date"
            placeholder="选择日期"
            style="width: 100%;"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-time-picker v-model="scheduleForm.startTime" placeholder="选择开始时间" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-time-picker v-model="scheduleForm.endTime" placeholder="选择结束时间" style="width: 100%;" />
        </el-form-item>
         <el-form-item label="班次类型" prop="shiftType">
          <el-radio-group v-model="scheduleForm.shiftType">
            <el-radio label="morning">上午班</el-radio>
            <el-radio label="afternoon">下午班</el-radio>
            <el-radio label="night">夜班</el-radio>
            <el-radio label="full">全天</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="scheduleForm.notes" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button v-if="isEditMode" type="danger" @click="deleteSchedule" style="float: left;">删除排班</el-button>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSchedule">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { ArrowLeft, Plus, Download } from '@element-plus/icons-vue';

const router = useRouter();

const goBack = () => router.push('/dashboard');

// Mock Data
const doctors = ref([
  { id: 1, name: '张三丰', color: '#409EFF' },
  { id: 2, name: '李时珍', color: '#67C23A' },
  { id: 3, name: '华佗', color: '#E6A23C' },
]);

const schedules = ref<any[]>([
  { id: 1, doctorId: 1, date: '2024-05-20', startTime: '09:00', endTime: '12:00', shiftType: 'morning', notes: '' },
  { id: 2, doctorId: 2, date: '2024-05-20', startTime: '14:00', endTime: '18:00', shiftType: 'afternoon', notes: '' },
  { id: 3, doctorId: 1, date: '2024-05-21', startTime: '09:00', endTime: '18:00', shiftType: 'full', notes: '全天班' },
]);

// Calendar and View State
const calendarDate = ref(new Date());
const selectedDoctor = ref<number | null>(null);

// Dialog State
const showDialog = ref(false);
const isEditMode = ref(false);
const scheduleFormRef = ref<FormInstance>();
const scheduleForm = ref<any>({
  id: null,
  doctorId: null,
  date: '',
  startTime: '',
  endTime: '',
  shiftType: '',
  notes: '',
});

const scheduleRules = ref<FormRules>({
  doctorId: [{ required: true, message: '请选择医生', trigger: 'change' }],
  date: [{ required: true, message: '请选择排班日期', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
});

// Computed properties
const filteredSchedules = computed(() => {
  if (selectedDoctor.value === null || selectedDoctor.value === '') {
    return schedules.value;
  }
  return schedules.value.filter(s => s.doctorId === selectedDoctor.value);
});

// Methods
const getSchedulesForDate = (date: string) => {
  return filteredSchedules.value.filter(s => s.date === date);
};

const getDoctorName = (doctorId: number) => {
  return doctors.value.find(d => d.id === doctorId)?.name || '未知医生';
};

const getDoctorColor = (doctorId: number) => {
  return doctors.value.find(d => d.id === doctorId)?.color || '#909399';
};

const exportWeekSchedule = () => {
  ElMessage.success('排班表导出成功')
};

const openAddEditDialog = (scheduleItem: any | null, date: Date | null = null) => {
  if (scheduleItem) { // Edit mode
    isEditMode.value = true;
    const startTimeDate = scheduleItem.startTime ? new Date(`${scheduleItem.date} ${scheduleItem.startTime}`) : null;
    const endTimeDate = scheduleItem.endTime ? new Date(`${scheduleItem.date} ${scheduleItem.endTime}`) : null;
    scheduleForm.value = { ...scheduleItem, startTime: startTimeDate, endTime: endTimeDate };
  } else { // Add mode
    isEditMode.value = false;
    scheduleForm.value = {
      id: null,
      doctorId: null,
      date: date ? date.toISOString().split('T')[0] : '',
      startTime: '',
      endTime: '',
      shiftType: '',
      notes: '',
    };
  }
  showDialog.value = true;
};

const formatTime = (date: any) => {
  if (!date || typeof date !== 'object') return '';
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

const saveSchedule = async () => {
  if (!scheduleFormRef.value) return;
  await scheduleFormRef.value.validate();
  
  const formValue = {
      ...scheduleForm.value,
      startTime: formatTime(scheduleForm.value.startTime),
      endTime: formatTime(scheduleForm.value.endTime)
  };

  if (isEditMode.value) {
    const index = schedules.value.findIndex(s => s.id === formValue.id);
    if (index !== -1) {
      schedules.value[index] = formValue;
    }
  } else {
    formValue.id = Date.now();
    schedules.value.push(formValue);
  }

  ElMessage.success(isEditMode.value ? '排班更新成功' : '排班新增成功');
  showDialog.value = false;
};

const deleteSchedule = async () => {
  try {
    await ElMessageBox.confirm('确定要删除此排班吗?', '警告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    const index = schedules.value.findIndex(s => s.id === scheduleForm.value.id);
    if (index !== -1) {
      schedules.value.splice(index, 1);
      ElMessage.success('排班删除成功');
      showDialog.value = false;
    }
  } catch {
    // User cancelled
  }
};
</script>

<style scoped>
.scheduling-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  flex-shrink: 0;
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.module-title {
  flex: 1;
  text-align: center;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.toolbar {
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.calendar-wrapper {
  flex: 1;
  overflow: auto;
  padding: var(--spacing-lg);
}

.el-calendar {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-light);
}

.date-cell {
  height: 100%;
  padding: 4px;
}

.date-number {
  font-size: var(--font-size-sm);
  margin: 0 0 4px 0;
}

.schedule-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
  overflow: hidden;
  font-size: 12px;
}

.schedule-item {
  padding: 2px 4px;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 