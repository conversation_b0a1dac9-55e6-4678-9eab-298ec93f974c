"""
医疗记录数据模型

定义中医病历、医疗实体等核心数据结构。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class MedicalEntity(BaseModel):
    """医疗实体模型"""
    
    entity_id: str = Field(description="实体唯一标识符")
    entity_type: str = Field(description="实体类型")
    entity_value: str = Field(description="实体值")
    confidence: float = Field(description="置信度", ge=0.0, le=1.0)
    source_agent: str = Field(description="来源智能体")
    extracted_at: datetime = Field(description="提取时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "entity_id": "symptom_001",
                "entity_type": "symptom",
                "entity_value": "头痛",
                "confidence": 0.95,
                "source_agent": "主诉采集智能体",
                "extracted_at": "2024-12-01T10:00:00",
                "metadata": {
                    "location": "头部",
                    "severity": "中等"
                }
            }
        }


class TCMMedicalRecord(BaseModel):
    """中医病历模型"""
    
    record_id: str = Field(description="病历唯一标识符")
    patient_id: str = Field(description="患者ID")
    session_id: str = Field(description="会话ID")
    
    # 基本信息
    chief_complaint: str = Field(description="主诉")
    present_illness: Dict[str, Any] = Field(description="现病史")
    past_history: Dict[str, Any] = Field(description="既往史")
    family_history: Dict[str, Any] = Field(description="家族史")
    
    # 四诊信息
    inspection: Dict[str, Any] = Field(description="望诊")
    auscultation: Dict[str, Any] = Field(description="闻诊")
    inquiry: Dict[str, Any] = Field(description="问诊")
    palpation: Dict[str, Any] = Field(description="切诊")
    
    # 诊断信息
    diagnosis: List[str] = Field(description="诊断")
    syndrome_differentiation: List[str] = Field(description="辨证")
    
    # 治疗信息
    treatment_plan: Dict[str, Any] = Field(description="治疗方案")
    prescriptions: List[Dict[str, Any]] = Field(description="处方")
    
    # 系统信息
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    doctor_id: Optional[str] = Field(default=None, description="医生ID")
    status: str = Field(default="draft", description="状态")
    
    class Config:
        json_schema_extra = {
            "example": {
                "record_id": "MR20241201001",
                "patient_id": "P20241201001",
                "session_id": "S20241201001",
                "chief_complaint": "头痛3天",
                "present_illness": {
                    "寒热症状": "无发热",
                    "汗症": "正常",
                    "疼痛症状": "头部胀痛"
                },
                "past_history": "既往体健",
                "family_history": "无特殊",
                "inspection": {},
                "auscultation": {},
                "inquiry": {},
                "palpation": {},
                "diagnosis": ["头痛"],
                "syndrome_differentiation": ["肝阳上亢"],
                "treatment_plan": {},
                "prescriptions": [],
                "created_at": "2024-12-01T10:00:00",
                "updated_at": "2024-12-01T10:00:00",
                "status": "draft"
            }
        }


class ConsultationSession(BaseModel):
    """问诊会话模型"""
    
    session_id: str = Field(description="会话唯一标识符")
    patient_id: str = Field(description="患者ID")
    status: str = Field(description="会话状态")
    current_agent: str = Field(description="当前智能体")
    progress: float = Field(description="进度", ge=0.0, le=1.0)
    collected_data: Dict[str, Any] = Field(description="已收集数据")
    conversation_history: List[Dict[str, str]] = Field(description="对话历史")
    
    # 系统信息
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "S20241201001",
                "patient_id": "P20241201001",
                "status": "进行中",
                "current_agent": "主诉采集智能体",
                "progress": 0.3,
                "collected_data": {
                    "chief_complaint": "头痛"
                },
                "conversation_history": [
                    {
                        "role": "patient",
                        "content": "我头痛",
                        "timestamp": "2024-12-01T10:00:00"
                    }
                ],
                "created_at": "2024-12-01T10:00:00",
                "updated_at": "2024-12-01T10:00:00"
            }
        }


class ConsultationMessage(BaseModel):
    """问诊消息模型"""
    
    message_id: str = Field(description="消息唯一标识符")
    session_id: str = Field(description="会话ID")
    role: str = Field(description="角色")
    content: str = Field(description="内容")
    message_type: str = Field(description="消息类型")
    timestamp: datetime = Field(description="时间戳")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message_id": "MSG001",
                "session_id": "S20241201001",
                "role": "patient",
                "content": "我头痛",
                "message_type": "text",
                "timestamp": "2024-12-01T10:00:00",
                "metadata": {}
            }
        }


MedicalRecord = TCMMedicalRecord 