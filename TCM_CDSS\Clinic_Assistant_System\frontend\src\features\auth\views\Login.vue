<template>
  <div class="login-container">
    <div class="login-box">
      <div class="logo-area">
        <div class="logo-title-container">
          <img src="@/assets/logo.jpg" alt="Clinic Logo" class="logo-img" />
          <h2 class="clinic-name">观建在-智慧中医诊所</h2>
        </div>
      </div>
      <el-form @submit.prevent="handleLogin">
        <div class="input-group">
          <el-input v-model="username" placeholder="请输入用户名或手机号" size="large" />
        </div>
        <div class="input-group">
          <el-input v-model="password" type="password" placeholder="请输入密码" show-password size="large" />
        </div>
        <el-button
          class="login-button"
          type="primary"
          @click="handleLogin"
          :loading="isLoading"
          size="large"
        >
          {{ isLoading ? '登录中...' : '登 录' }}
        </el-button>
        <div class="links">
          <a href="#">忘记密码？</a>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/core/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const username = ref('')
const password = ref('')
const isLoading = ref(false)

const handleLogin = async () => {
  if (!username.value || !password.value) {
    ElMessage.error('请输入用户名和密码')
    return
  }

  isLoading.value = true
  try {
    const success = await authStore.login({
      phone: username.value,
      password: password.value
    })
    if (success) {
      ElMessage.success('登录成功')
      router.push('/dashboard')
    } else {
      ElMessage.error('用户名或密码错误')
    }
  } catch (error) {
    ElMessage.error('登录失败，请重试')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  /* 背景图片现在由全局样式提供 */
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.login-box {
  position: relative;
  width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.logo-area {
  margin-bottom: 30px;
}

.logo-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.logo-img {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.clinic-name {
  margin: 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}

.input-group {
  margin-bottom: 20px;
}

.input-group :deep(.el-input__inner) {
  border-radius: 10px;
}

.login-button {
  width: 100%;
  border-radius: 10px;
  padding: 15px 0;
  font-size: 16px;
  letter-spacing: 2px;
}

.links {
  margin-top: 20px;
}

.links a {
  font-size: 14px;
  color: #999;
  text-decoration: none;
}
</style> 