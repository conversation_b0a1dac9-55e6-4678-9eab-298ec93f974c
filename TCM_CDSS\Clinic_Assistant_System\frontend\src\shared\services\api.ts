/**
 * API服务配置
 * 
 * 主要功能：
 * 1. 配置axios实例
 * 2. 设置请求/响应拦截器
 * 3. 统一错误处理
 * 4. 提供基础API方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// API配置
const API_CONFIG = {
  // 后端API基础URL
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  // 请求超时时间
  timeout: 10000,
  // 请求头
  headers: {
    'Content-Type': 'application/json',
  }
}

// 创建axios实例
const apiClient: AxiosInstance = axios.create(API_CONFIG)

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('auth_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 计算请求耗时
    const endTime = new Date()
    const startTime = response.config.metadata?.startTime
    const duration = startTime ? endTime.getTime() - startTime.getTime() : 0
    
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`)
    
    // 检查业务状态码
    if (response.data && response.data.success === false) {
      const errorMessage = response.data.error || '请求失败'
      ElMessage.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    }
    
    return response
  },
  (error) => {
    console.error('❌ Response Error:', error)
    
    // 处理不同类型的错误
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      let errorMessage = '请求失败'
      
      switch (status) {
        case 400:
          errorMessage = data?.detail?.error || '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          // 清除token并跳转到登录页
          localStorage.removeItem('auth_token')
          window.location.href = '/login'
          break
        case 403:
          errorMessage = '权限不足'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data?.detail?.error || `请求失败 (${status})`
      }
      
      ElMessage.error(errorMessage)
    } else if (error.request) {
      // 网络错误
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      ElMessage.error(error.message || '未知错误')
    }
    
    return Promise.reject(error)
  }
)

// API响应类型定义
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  error?: string
  timestamp: string
}

// 基础API类
export class ApiService {
  /**
   * GET请求
   */
  static async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.get<ApiResponse<T>>(url, config)
    return response.data.data
  }

  /**
   * POST请求
   */
  static async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.post<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  /**
   * PUT请求
   */
  static async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.put<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  /**
   * DELETE请求
   */
  static async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.delete<ApiResponse<T>>(url, config)
    return response.data.data
  }

  /**
   * 获取完整响应（包含元数据）
   */
  static async getFullResponse<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.get<ApiResponse<T>>(url, config)
    return response.data
  }
}

// 健康检查API
export const healthApi = {
  /**
   * 检查后端服务健康状态
   */
  async check() {
    return ApiService.getFullResponse('/health')
  }
}

// 映射数据API
export const mappingApi = {
  /**
   * 获取所有映射数据
   */
  async getAll() {
    return ApiService.get('/api/mappings?type=all')
  },

  /**
   * 获取性别映射
   */
  async getGenders() {
    return ApiService.get('/api/mappings?type=genders')
  },

  /**
   * 获取角色映射
   */
  async getRoles() {
    return ApiService.get('/api/mappings?type=roles')
  },

  /**
   * 获取科室映射
   */
  async getDepartments() {
    return ApiService.get('/api/mappings?type=departments')
  },

  /**
   * 获取状态映射
   */
  async getStatuses(category: string) {
    return ApiService.get(`/api/mappings?type=statuses&category=${category}`)
  }
}

// 统计数据API
export const statsApi = {
  /**
   * 获取数据库统计信息
   */
  async getDatabaseStats() {
    return ApiService.get('/api/stats')
  }
}

// 测试API
export const testApi = {
  /**
   * 测试加密功能
   */
  async testEncryption(text: string) {
    return ApiService.post('/api/test/encrypt', { text })
  }
}

// 导出axios实例供其他地方使用
export { apiClient }
export default ApiService
