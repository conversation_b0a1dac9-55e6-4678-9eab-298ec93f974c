// 患者管理卡片模块
import type { CardModule } from './cards'
import { ref, computed } from 'vue'

// 患者统计数据
export const patientsStats = ref({
  total: 0,
  active: 0,
  inactive: 0,
  male: 0,
  female: 0,
  withAppointment: 0,
  ageGroups: {
    child: 0,
    adult: 0,
    senior: 0
  }
})

// 患者卡片信息计算
export const patientCardInfo = computed(() => {
  const stats = patientsStats.value
  if (stats.total === 0) return '加载中...'
  
  return `${stats.total}位患者 | 活跃${stats.active}人 | 男${stats.male}女${stats.female} | 预约${stats.withAppointment}人`
})

// 患者管理卡片配置
export const patientsCard: CardModule = {
  key: 'patients',
  name: '患者管理',
  route: '/patients',
  icon: 'User',
  color: '#34C759',
  info: () => patientCardInfo.value,
  hasDetailStats: true,
  statsData: patientsStats,
  permission: 'patient_manage'
}

// 患者详细统计数据获取函数
export const getPatientsDetailStats = () => {
  return {
    total: patientsStats.value.total,
    active: patientsStats.value.active,
    inactive: patientsStats.value.inactive,
    male: patientsStats.value.male,
    female: patientsStats.value.female,
    withAppointment: patientsStats.value.withAppointment,
    ageGroups: patientsStats.value.ageGroups
  }
}

// 更新患者统计数据
export const updatePatientsStats = (newStats: any) => {
  patientsStats.value = { ...patientsStats.value, ...newStats }
} 