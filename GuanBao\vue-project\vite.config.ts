import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import fs from 'fs'
import path from 'path'

// 加载配置文件
const configPath = path.resolve(__dirname, '../config/app.config.json')
let config = {
  frontend: { host: 'localhost', port: 5173 },
  backend: { host: 'localhost', port: 82 }
}

try {
  const configData = fs.readFileSync(configPath, 'utf8')
  config = JSON.parse(configData)
} catch (error) {
  console.warn('无法加载配置文件，使用默认配置:', error.message)
}

// 环境变量覆盖
const frontendHost = process.env.FRONTEND_HOST || config.frontend.host
const frontendPort = parseInt(process.env.FRONTEND_PORT) || config.frontend.port
const backendHost = process.env.BACKEND_HOST || config.backend.host
const backendPort = parseInt(process.env.BACKEND_PORT) || config.backend.port

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    host: frontendHost,
    port: frontendPort,
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: `http://${backendHost}:${backendPort}`,
        changeOrigin: true,
        secure: false
      },
      '/v1': {
        target: `http://${backendHost}:${backendPort}`,
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: config.frontend?.build?.outDir || 'dist',
    assetsDir: config.frontend?.build?.assetsDir || 'assets'
  }
})
