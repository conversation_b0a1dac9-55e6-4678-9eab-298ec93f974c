// 家医管理卡片模块
import type { CardModule } from '../index'

// 家医管理统计数据
export const familyDoctorStats = {
  totalFamilies: 156,
  activePlans: 89,
  monthlyVisits: 234,
  satisfaction: 96
}

// 家医管理卡片配置
export const familyDoctorCard: CardModule = {
  key: 'family-doctor',
  name: '家医管理',
  route: '/family-doctor',
  icon: 'HomeFilled',
  color: '#32D74B',
  info: '家庭医生服务',
  hasDetailStats: false,
  statsData: familyDoctorStats,
  permission: 'family_doctor'
}
