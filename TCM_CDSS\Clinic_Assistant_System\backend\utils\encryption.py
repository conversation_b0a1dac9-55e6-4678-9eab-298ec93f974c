"""
加密工具
处理敏感数据的加密和解密
"""

import os
import hashlib
import secrets
import base64
from typing import Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import bcrypt

# 加密配置
ENCRYPTION_CONFIG = {
    'algorithm': 'fernet',
    'key_length': 32,
    'salt_rounds': 12
}

# 从环境变量获取密钥，如果没有则使用默认密钥（仅用于开发）
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY', 'your-32-character-secret-key-here!!')

class EncryptionUtils:
    """加密工具类"""

    @staticmethod
    def generate_key(length: int = ENCRYPTION_CONFIG['key_length']) -> str:
        """
        生成随机密钥

        Args:
            length: 密钥长度

        Returns:
            十六进制密钥
        """
        return secrets.token_hex(length)

    @staticmethod
    def _get_fernet_key(password: str, salt: bytes = None) -> Fernet:
        """获取Fernet加密实例"""
        if salt is None:
            salt = b'tcm_clinic_salt_'  # 固定盐值，生产环境应使用随机盐值

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return Fernet(key)

    @staticmethod
    def encrypt(text: str, key: str = ENCRYPTION_KEY) -> Optional[str]:
        """
        加密文本

        Args:
            text: 要加密的文本
            key: 加密密钥（可选）

        Returns:
            加密后的文本（base64编码）
        """
        try:
            if not text:
                return None

            fernet = EncryptionUtils._get_fernet_key(key)
            encrypted = fernet.encrypt(text.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as error:
            print(f'Encryption error: {error}')
            return None

    @staticmethod
    def decrypt(encrypted_text: str, key: str = ENCRYPTION_KEY) -> Optional[str]:
        """
        解密文本

        Args:
            encrypted_text: 加密的文本（base64编码）
            key: 解密密钥（可选）

        Returns:
            解密后的文本
        """
        try:
            if not encrypted_text:
                return None

            fernet = EncryptionUtils._get_fernet_key(key)
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_text.encode())
            decrypted = fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as error:
            print(f'Decryption error: {error}')
            return None

    @staticmethod
    def hash_password(password: str) -> str:
        """
        哈希密码

        Args:
            password: 明文密码

        Returns:
            哈希后的密码
        """
        try:
            salt = bcrypt.gensalt(rounds=ENCRYPTION_CONFIG['salt_rounds'])
            return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        except Exception as error:
            print(f'Password hashing error: {error}')
            raise error

    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """
        验证密码

        Args:
            password: 明文密码
            hashed_password: 哈希密码

        Returns:
            验证结果
        """
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        except Exception as error:
            print(f'Password verification error: {error}')
            return False

    @staticmethod
    def hash_phone(phone: str) -> Optional[str]:
        """
        生成手机号哈希

        Args:
            phone: 手机号

        Returns:
            哈希值
        """
        try:
            if not phone:
                return None
            return hashlib.sha256(phone.encode()).hexdigest()
        except Exception as error:
            print(f'Phone hashing error: {error}')
            return None

    @staticmethod
    def hash_id_card(id_card: str) -> Optional[str]:
        """
        生成身份证号哈希

        Args:
            id_card: 身份证号

        Returns:
            哈希值
        """
        try:
            if not id_card:
                return None
            return hashlib.sha256(id_card.encode()).hexdigest()
        except Exception as error:
            print(f'ID card hashing error: {error}')
            return None

    @staticmethod
    def mask_sensitive_data(data: str, data_type: str) -> str:
        """
        数据脱敏

        Args:
            data: 原始数据
            data_type: 数据类型（phone, idCard, name等）

        Returns:
            脱敏后的数据
        """
        try:
            if not data:
                return ''

            if data_type == 'phone':
                # 手机号脱敏：138****8000
                if len(data) >= 11:
                    return data[:3] + '****' + data[-4:]
                return data

            elif data_type == 'idCard':
                # 身份证脱敏：110101********1234
                if len(data) >= 18:
                    return data[:6] + '********' + data[-4:]
                return data

            elif data_type == 'name':
                # 姓名脱敏：张*明
                if len(data) <= 2:
                    return data[0] + '*'
                return data[0] + '*' * (len(data) - 2) + data[-1]

            elif data_type == 'address':
                # 地址脱敏：保留前6个字符
                if len(data) <= 6:
                    return data[:2] + '***'
                return data[:6] + '***'

            else:
                return data

        except Exception as error:
            print(f'Data masking error: {error}')
            return data

    @staticmethod
    def generate_token(length: int = 32) -> str:
        """
        生成安全的随机令牌

        Args:
            length: 令牌长度

        Returns:
            随机令牌
        """
        return secrets.token_hex(length)

    @staticmethod
    def generate_jwt_secret() -> str:
        """
        生成JWT密钥

        Returns:
            JWT密钥
        """
        return secrets.token_hex(64)
