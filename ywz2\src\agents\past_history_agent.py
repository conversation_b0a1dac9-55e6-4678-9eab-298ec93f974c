from typing import Dict, Any, List
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from src.agents.base_agent import BaseAgent


class PastHistoryAgent(BaseAgent):
    """既往史采集智能体"""
    
    def __init__(self):
        super().__init__(
            name="既往史采集智能体",
            description="收集患者既往疾病、手术史、过敏史等信息",
            agent_type="past_history"
        )
        
        # 既往史类别和当前索引
        self.history_categories = [
            "既往疾病史", "手术史", "外伤史", "输血史", 
            "传染病史", "过敏史", "用药史", "住院史"
        ]
        self.current_category_index = 0
    
    def _create_prompt_template(self) -> PromptTemplate:
        """创建支持历史的提示模板"""
        return PromptTemplate(
            input_variables=["patient_input", "context", "history", "current_category"],
            template="""
你是一名专业的中医医生，负责采集患者的既往史信息。

当前询问的既往史类别: {current_category}
[对话历史]
{history}

[患者描述]
{patient_input}

请提取以下信息:
1. 疾病名称或手术名称
2. 发生时间
3. 严重程度
4. 治疗情况
5. 恢复情况
6. 是否治愈

请严格只输出如下JSON格式，不要输出任何其他内容或解释：

{{
    "history_category": "既往史类别",
    "condition_name": "疾病或手术名称",
    "occurrence_time": "发生时间",
    "severity": "严重程度",
    "treatment": "治疗情况",
    "recovery": "恢复情况",
    "is_cured": true/false,
    "confidence": 0.95
}}

注意：如果患者描述不完整，请根据已有信息填写，缺失字段用null表示。
"""
        )
    
    def _create_output_parser(self) -> JsonOutputParser:
        """创建输出解析器"""
        return JsonOutputParser()
    
    def ask_has_or_not(self, context: Dict[str, Any]) -> str:
        """询问当前既往史类别有无"""
        current_category = self.history_categories[self.current_category_index]
        
        questions = {
            "既往疾病史": "您以前患过什么疾病吗？",
            "手术史": "您做过什么手术吗？",
            "外伤史": "您受过什么外伤吗？",
            "输血史": "您输过血吗？",
            "传染病史": "您患过传染病吗？比如肝炎、结核等？",
            "过敏史": "您对什么药物、食物或环境过敏吗？",
            "用药史": "您长期服用过什么药物吗？",
            "住院史": "您住过院吗？"
        }
        
        return questions.get(current_category, "您有其他既往史吗？")
    
    def ask_details_if_has(self, context: Dict[str, Any], has_result: Dict[str, Any]) -> str:
        """如果有既往史，追问详情"""
        current_category = self.history_categories[self.current_category_index]
        
        detail_questions = {
            "既往疾病史": """
            请详细说明：
            1. 具体是什么疾病？
            2. 什么时候患病的？
            3. 当时有什么症状？
            4. 是如何治疗的？
            5. 现在是否已经治愈？
            """,
            "手术史": """
            请详细说明：
            1. 是什么手术？
            2. 什么时候做的手术？
            3. 手术的原因是什么？
            4. 手术是否成功？
            5. 术后恢复如何？
            """,
            "外伤史": """
            请详细说明：
            1. 是什么外伤？
            2. 什么时候受的伤？
            3. 受伤的部位在哪里？
            4. 当时是如何处理的？
            5. 现在是否还有后遗症？
            """,
            "输血史": """
            请详细说明：
            1. 什么时候输的血？
            2. 输血的原因是什么？
            3. 输了多少血？
            4. 输血后有什么反应吗？
            5. 是否知道血型？
            """,
            "传染病史": """
            请详细说明：
            1. 具体是什么传染病？
            2. 什么时候患病的？
            3. 当时有什么症状？
            4. 是如何治疗的？
            5. 现在是否已经治愈？
            6. 是否有传染给他人？
            """,
            "过敏史": """
            请详细说明：
            1. 对什么过敏？（药物、食物、环境等）
            2. 过敏的表现是什么？
            3. 过敏的严重程度如何？
            4. 如何避免过敏？
            5. 是否有过敏反应史？
            """,
            "用药史": """
            请详细说明：
            1. 长期服用什么药物？
            2. 服用了多长时间？
            3. 服用的原因是什么？
            4. 现在是否还在服用？
            5. 有什么副作用吗？
            """,
            "住院史": """
            请详细说明：
            1. 什么时候住院的？
            2. 住院的原因是什么？
            3. 住了多长时间？
            4. 在哪个医院？
            5. 治疗效果如何？
            """
        }
        
        return detail_questions.get(current_category, "请详细描述一下。")
    
    def next_category(self):
        """移动到下一个既往史类别"""
        if self.current_category_index < len(self.history_categories) - 1:
            self.current_category_index += 1
            return True
        return False
    
    def reset_categories(self):
        """重置既往史类别索引"""
        self.current_category_index = 0
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力"""
        return [
            "既往疾病史采集",
            "手术史采集", 
            "外伤史采集",
            "输血史采集",
            "传染病史采集",
            "过敏史采集",
            "用药史采集",
            "住院史采集"
        ]
    
    def get_required_inputs(self) -> List[str]:
        """获取所需输入"""
        return [
            "patient_input",
            "context"
        ]
    
    def get_outputs(self) -> List[str]:
        """获取输出"""
        return [
            "history_category",
            "condition_name", 
            "occurrence_time",
            "severity",
            "treatment",
            "recovery",
            "is_cured"
        ]
    
    required_fields = ["history_category", "condition_name", "occurrence_time", "severity", "treatment", "recovery", "is_cured"]
    def generate_followup(self, missing_fields):
        mapping = {
            "history_category": "请说明既往史类别。",
            "condition_name": "请说明具体疾病或手术名称。",
            "occurrence_time": "请说明发生时间。",
            "severity": "请说明严重程度。",
            "treatment": "请说明治疗情况。",
            "recovery": "请说明恢复情况。",
            "is_cured": "请说明是否治愈。"
        }
        return "；".join([mapping.get(f, f"请补充{f}") for f in missing_fields])
    def generate_summary(self, extracted_data):
        return f"既往史: {extracted_data}" 