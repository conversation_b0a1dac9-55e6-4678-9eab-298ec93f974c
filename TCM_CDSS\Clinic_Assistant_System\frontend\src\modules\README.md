# Modules 模块化系统

模块化系统用于管理应用的功能模块，提供模块的注册、安装和管理功能。

## 目录结构

### installer/ - 模块安装器
- **installer.ts** - 安装器逻辑
- **types.ts** - 类型定义
- **utils.ts** - 工具函数

### registry/ - 模块注册表
- **registry.ts** - 注册表逻辑
- **types.ts** - 类型定义
- **utils.ts** - 工具函数

### cards/ - 功能卡片模块
- **index.ts** - 卡片模块统一导出
- **appointments/** - 预约中心卡片
- **patients/** - 患者管理卡片
- **workstation/** - 门诊工作站卡片
- **billing/** - 划价收费卡片
- **pharmacy/** - 药房管理卡片
- **inventory/** - 库存管理卡片
- **scheduling/** - 排班管理卡片
- **staff/** - 员工管理卡片
- **reports/** - 统计报表卡片
- **family-doctor/** - 家医管理卡片
- **research-center/** - 研学中心卡片
- **app-center/** - 应用中心卡片
- **mall-management/** - 商城管理卡片

## 功能说明

### 模块注册
- 提供统一的模块注册接口
- 支持模块的动态加载和卸载
- 管理模块间的依赖关系

### 卡片配置
- 定义功能中心的各种卡片
- 提供统计数据和详细统计组件
- 支持权限控制和动态显示

### 安装器
- 提供模块的安装和卸载功能
- 处理模块的依赖关系
- 管理模块的版本和更新
