<!--
  应用根组件
  
  主要功能：
  1. 作为整个应用的根容器
  2. 提供路由视图容器
  3. 设置全局样式和字体
  
  组件结构：
  - router-view: 路由视图容器，用于显示当前路由对应的组件
  
  样式说明：
  - 使用系统默认字体栈
  - 启用字体平滑渲染
  - 设置全屏高度，防止页面滚动
  
  <AUTHOR>
  @version 1.0.0
  @since 2024-01-01
-->

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// App root component
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  overflow: hidden;
}
</style> 