"""
LLM客户端模块

整合OpenRouter和Ollama客户端，提供统一的LLM调用接口。
支持多种模型选择和智能切换。
"""

import logging
import asyncio
from typing import List, Dict, Optional, Any, Union
from abc import ABC, abstractmethod
import httpx
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.outputs import ChatResult, ChatGeneration
from langchain_ollama import ChatOllama

from ..core.config import get_settings, get_llm_config

logger = logging.getLogger(__name__)
settings = get_settings()


class LLMClient(ABC):
    """LLM客户端抽象基类"""
    
    @abstractmethod
    async def chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """聊天完成接口"""
        pass
    
    @abstractmethod
    def get_llm(self) -> BaseChatModel:
        """获取LangChain兼容的LLM实例"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        pass


class OpenRouterClient(LLMClient):
    """OpenRouter客户端"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        self.api_key = api_key or settings.openrouter_api_key
        self.base_url = base_url or settings.openrouter_base_url
        self.default_model = settings.default_model
        self.timeout = settings.llm_timeout
        
        # 创建异步HTTP客户端
        self.client = httpx.AsyncClient(
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://yuwenzhen.tcm-cdss.com",
                "X-Title": "YUWENZHEN TCM Consultation System"
            },
            timeout=self.timeout
        )
        
        logger.info(f"OpenRouter客户端已初始化: {self.base_url}")
    
    async def chat_completion(
        self, 
        messages: List[Dict], 
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Dict:
        """调用OpenRouter聊天接口"""
        model = model or self.default_model
        temperature = temperature or settings.llm_temperature
        max_tokens = max_tokens or settings.llm_max_tokens
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            **kwargs
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                json=payload
            )
            response.raise_for_status()
            return response.json()
            
        except httpx.HTTPStatusError as e:
            logger.error(f"OpenRouter API HTTP错误: {e.response.status_code} - {e.response.text}")
            raise Exception(f"OpenRouter API调用失败: HTTP {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"OpenRouter API请求错误: {e}")
            raise Exception(f"OpenRouter API请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"OpenRouter API未知错误: {e}")
            raise Exception(f"OpenRouter API调用失败: {str(e)}")
    
    async def get_available_models(self) -> List[Dict]:
        """获取可用模型列表"""
        try:
            response = await self.client.get(f"{self.base_url}/models")
            response.raise_for_status()
            data = response.json()
            return data.get("data", [])
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []
    
    async def get_model_info(self, model_id: str) -> Optional[Dict]:
        """获取特定模型信息"""
        models = await self.get_available_models()
        for model in models:
            if model.get("id") == model_id:
                return model
        return None
    
    def get_llm(self) -> BaseChatModel:
        """获取LangChain兼容的LLM实例"""
        return OpenRouterChatModel(
            client=self,
            model_name=self.default_model,
            temperature=settings.llm_temperature,
            max_tokens=settings.llm_max_tokens
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 测试简单的聊天请求
            test_messages = [{"role": "user", "content": "Hello"}]
            response = await self.chat_completion(test_messages, max_tokens=10)
            
            return {
                "service": "openrouter",
                "status": "healthy",
                "model": self.default_model,
                "base_url": self.base_url
            }
        except Exception as e:
            return {
                "service": "openrouter",
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


class OllamaClient(LLMClient):
    """Ollama客户端"""
    
    def __init__(self, base_url: Optional[str] = None, model: Optional[str] = None):
        self.base_url = base_url or settings.ollama_base_url
        self.model = model or settings.ollama_model
        self.timeout = settings.llm_timeout
        
        # 创建异步HTTP客户端
        self.client = httpx.AsyncClient(
            timeout=self.timeout
        )
        
        logger.info(f"Ollama客户端已初始化: {self.base_url}")
    
    async def chat_completion(
        self, 
        messages: List[Dict], 
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> Dict:
        """调用Ollama聊天接口"""
        model = model or self.model
        temperature = temperature or settings.llm_temperature
        
        # 转换消息格式为Ollama格式
        prompt = self._convert_messages_to_prompt(messages)
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": temperature,
                **kwargs
            }
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/generate",
                json=payload
            )
            response.raise_for_status()
            ollama_response = response.json()
            
            # 转换为OpenAI格式的响应
            return {
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": ollama_response.get("response", "")
                    },
                    "finish_reason": "stop"
                }],
                "model": model,
                "usage": {
                    "prompt_tokens": ollama_response.get("prompt_eval_count", 0),
                    "completion_tokens": ollama_response.get("eval_count", 0),
                    "total_tokens": ollama_response.get("prompt_eval_count", 0) + ollama_response.get("eval_count", 0)
                }
            }
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Ollama API HTTP错误: {e.response.status_code} - {e.response.text}")
            raise Exception(f"Ollama API调用失败: HTTP {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"Ollama API请求错误: {e}")
            raise Exception(f"Ollama API请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"Ollama API未知错误: {e}")
            raise Exception(f"Ollama API调用失败: {str(e)}")
    
    def _convert_messages_to_prompt(self, messages: List[Dict]) -> str:
        """将消息列表转换为Ollama提示格式"""
        prompt_parts = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"Human: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        prompt_parts.append("Assistant:")
        return "\n\n".join(prompt_parts)
    
    async def get_available_models(self) -> List[Dict]:
        """获取可用模型列表"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            data = response.json()
            return data.get("models", [])
        except Exception as e:
            logger.error(f"获取Ollama模型列表失败: {e}")
            return []
    
    def get_llm(self) -> BaseChatModel:
        """获取LangChain兼容的LLM实例"""
        return ChatOllama(
            base_url=self.base_url,
            model=self.model,
            temperature=settings.llm_temperature
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 测试获取模型列表
            models = await self.get_available_models()
            
            return {
                "service": "ollama",
                "status": "healthy",
                "model": self.model,
                "base_url": self.base_url,
                "available_models": len(models)
            }
        except Exception as e:
            return {
                "service": "ollama",
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


class OpenRouterChatModel(BaseChatModel):
    """OpenRouter聊天模型包装器"""
    
    def __init__(self, client: OpenRouterClient, model_name: str, temperature: float = 0.7, max_tokens: int = 2000):
        super().__init__()
        self.client = client
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
    
    @property
    def _llm_type(self) -> str:
        return "openrouter"
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs
    ) -> ChatResult:
        """同步生成聊天响应"""
        # 使用异步方法的同步包装
        return asyncio.run(self._agenerate(messages, stop, run_manager, **kwargs))
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs
    ) -> ChatResult:
        """异步生成聊天响应"""
        # 转换消息格式
        openrouter_messages = []
        for message in messages:
            if isinstance(message, SystemMessage):
                openrouter_messages.append({"role": "system", "content": message.content})
            elif isinstance(message, HumanMessage):
                openrouter_messages.append({"role": "user", "content": message.content})
            elif isinstance(message, AIMessage):
                openrouter_messages.append({"role": "assistant", "content": message.content})
        
        # 调用OpenRouter API
        response = await self.client.chat_completion(
            messages=openrouter_messages,
            model=self.model_name,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            **kwargs
        )
        
        # 解析响应
        if "choices" in response and len(response["choices"]) > 0:
            choice = response["choices"][0]
            message_content = choice["message"]["content"]
            
            # 创建AIMessage
            ai_message = AIMessage(content=message_content)
            
            # 创建ChatGeneration
            generation = ChatGeneration(message=ai_message)
            
            return ChatResult(generations=[generation])
        else:
            raise Exception("OpenRouter响应格式错误")


class LLMClientManager:
    """LLM客户端管理器"""
    
    def __init__(self):
        self.clients = {}
        self.current_client = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化客户端"""
        llm_config = get_llm_config()
        
        if llm_config["service"] == "openrouter":
            self.current_client = OpenRouterClient()
        elif llm_config["service"] == "ollama":
            self.current_client = OllamaClient()
        else:
            # 默认使用OpenRouter
            self.current_client = OpenRouterClient()
        
        logger.info(f"LLM客户端管理器已初始化，当前服务: {llm_config['service']}")
    
    def get_client(self) -> LLMClient:
        """获取当前客户端"""
        return self.current_client
    
    def get_llm(self) -> BaseChatModel:
        """获取LangChain兼容的LLM实例"""
        return self.current_client.get_llm()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return await self.current_client.health_check()
    
    async def close(self):
        """关闭所有客户端"""
        if self.current_client:
            await self.current_client.close()


# 全局LLM客户端管理器实例
llm_manager = LLMClientManager()


# 便捷函数
def get_llm_client() -> LLMClient:
    """获取LLM客户端"""
    return llm_manager.get_client()


def get_llm() -> BaseChatModel:
    """获取LangChain兼容的LLM实例"""
    return llm_manager.get_llm()


async def get_llm_health() -> Dict[str, Any]:
    """获取LLM健康状态"""
    return await llm_manager.health_check()


async def close_llm_clients():
    """关闭LLM客户端"""
    await llm_manager.close()


# 导出主要接口
__all__ = [
    "LLMClient",
    "OpenRouterClient",
    "OllamaClient",
    "LLMClientManager",
    "llm_manager",
    "get_llm_client",
    "get_llm",
    "get_llm_health",
    "close_llm_clients"
]
