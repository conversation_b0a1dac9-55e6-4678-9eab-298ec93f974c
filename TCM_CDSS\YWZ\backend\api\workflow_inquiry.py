"""
工作流程问诊API路由
"""

import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
from services.workflow_inquiry_service import workflow_inquiry_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/workflow", tags=["工作流程问诊"])

# 请求模型
class StartWorkflowInquiryRequest(BaseModel):
    patient_name: str
    patient_age: Optional[int] = None
    patient_gender: Optional[str] = None
    contact: Optional[str] = None

class WorkflowMessageRequest(BaseModel):
    session_id: str
    message: str

# 响应模型
class WorkflowResponse(BaseModel):
    session_id: Optional[str] = None
    message: str
    progress: Optional[Dict[str, Any]] = None
    status: str
    error: Optional[str] = None

@router.post("/start", response_model=WorkflowResponse)
async def start_workflow_inquiry(request: StartWorkflowInquiryRequest):
    """开始工作流程预问诊"""
    try:
        logger.info(f"开始工作流程问诊: {request.patient_name}")
        
        # 构建患者信息
        patient_info = {
            "name": request.patient_name,
            "age": request.patient_age,
            "gender": request.patient_gender,
            "contact": request.contact
        }
        
        # 启动工作流程
        result = workflow_inquiry_service.start_inquiry(patient_info)
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return WorkflowResponse(
            session_id=result["session_id"],
            message=result["message"],
            progress=result["progress"],
            status=result["status"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动工作流程问诊失败: {e}")
        raise HTTPException(status_code=500, detail="启动问诊失败")

@router.post("/chat", response_model=WorkflowResponse)
async def workflow_chat(request: WorkflowMessageRequest):
    """工作流程问诊对话"""
    try:
        logger.info(f"工作流程对话: {request.session_id}")
        
        # 处理消息
        result = workflow_inquiry_service.process_message(
            request.session_id, 
            request.message
        )
        
        if "error" in result:
            if result["error"] == "会话不存在":
                raise HTTPException(status_code=404, detail="会话不存在或已过期")
            else:
                raise HTTPException(status_code=500, detail=result["error"])
        
        return WorkflowResponse(
            session_id=result["session_id"],
            message=result["message"],
            progress=result["progress"],
            status=result["status"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"工作流程对话失败: {e}")
        raise HTTPException(status_code=500, detail="处理消息失败")

@router.get("/session/{session_id}")
async def get_workflow_session(session_id: str):
    """获取工作流程会话信息"""
    try:
        logger.info(f"获取工作流程会话: {session_id}")
        
        result = workflow_inquiry_service.get_session_info(session_id)
        
        if "error" in result:
            if result["error"] == "会话不存在":
                raise HTTPException(status_code=404, detail="会话不存在")
            else:
                raise HTTPException(status_code=500, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取会话信息失败")

@router.get("/history/{session_id}")
async def get_workflow_history(session_id: str):
    """获取工作流程对话历史"""
    try:
        logger.info(f"获取工作流程历史: {session_id}")
        
        result = workflow_inquiry_service.get_conversation_history(session_id)
        
        if "error" in result:
            if result["error"] == "会话不存在":
                raise HTTPException(status_code=404, detail="会话不存在")
            else:
                raise HTTPException(status_code=500, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取对话历史失败")

@router.get("/sessions")
async def get_active_workflow_sessions():
    """获取活跃的工作流程会话列表"""
    try:
        logger.info("获取活跃工作流程会话列表")
        
        result = workflow_inquiry_service.get_active_sessions()
        return result
        
    except Exception as e:
        logger.error(f"获取活跃会话失败: {e}")
        raise HTTPException(status_code=500, detail="获取会话列表失败")

@router.post("/cleanup")
async def cleanup_workflow_sessions(max_age_hours: int = 2):
    """清理过期的工作流程会话"""
    try:
        logger.info(f"清理过期工作流程会话: {max_age_hours}小时")
        
        result = workflow_inquiry_service.cleanup_expired_sessions(max_age_hours)
        return result
        
    except Exception as e:
        logger.error(f"清理过期会话失败: {e}")
        raise HTTPException(status_code=500, detail="清理会话失败")

@router.get("/progress/{session_id}")
async def get_workflow_progress(session_id: str):
    """获取工作流程进度"""
    try:
        logger.info(f"获取工作流程进度: {session_id}")
        
        session_info = workflow_inquiry_service.get_session_info(session_id)
        
        if "error" in session_info:
            if session_info["error"] == "会话不存在":
                raise HTTPException(status_code=404, detail="会话不存在")
            else:
                raise HTTPException(status_code=500, detail=session_info["error"])
        
        return {
            "session_id": session_id,
            "progress": session_info["progress"],
            "status": session_info["status"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取进度失败: {e}")
        raise HTTPException(status_code=500, detail="获取进度失败")

@router.get("/demo")
async def workflow_demo():
    """工作流程问诊演示信息"""
    return {
        "title": "多智能体协同预问诊工作流程",
        "description": "基于智能体工作流程的结构化中医预问诊系统",
        "features": [
            "🤖 多智能体协同工作",
            "📋 结构化信息采集", 
            "🔍 信息完整性验证",
            "📊 智能体工作报告",
            "📝 最终结构化病历"
        ],
        "workflow_stages": [
            {
                "stage": 1,
                "name": "主诉采集智能体",
                "description": "采集主要症状、持续时间、伴随症状",
                "required_elements": ["主要症状", "持续时间"],
                "optional_elements": ["伴随症状", "严重程度"]
            },
            {
                "stage": 2,
                "name": "现病史采集智能体", 
                "description": "了解疾病发展过程和影响因素",
                "required_elements": ["发病诱因", "症状变化", "影响因素"],
                "optional_elements": ["治疗情况"]
            },
            {
                "stage": 3,
                "name": "既往史采集智能体",
                "description": "了解既往健康背景",
                "required_elements": ["既往疾病", "过敏史"],
                "optional_elements": ["手术史", "用药史"]
            }
        ],
        "usage": {
            "start": "POST /api/workflow/start",
            "chat": "POST /api/workflow/chat",
            "progress": "GET /api/workflow/progress/{session_id}",
            "history": "GET /api/workflow/history/{session_id}"
        }
    }
