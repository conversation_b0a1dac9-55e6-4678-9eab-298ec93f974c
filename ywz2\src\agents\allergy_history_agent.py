from typing import Dict, Any, List
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from src.agents.base_agent import BaseAgent


class AllergyHistoryAgent(BaseAgent):
    """过敏史采集智能体"""
    
    def __init__(self):
        super().__init__(
            name="过敏史采集智能体",
            description="收集患者药物、食物、环境等过敏信息",
            agent_type="allergy_history"
        )
        
        # 过敏类别和当前索引
        self.allergy_categories = [
            "药物过敏", "食物过敏", "环境过敏", "接触过敏",
            "吸入过敏", "昆虫过敏", "其他过敏"
        ]
        self.current_category_index = 0
    
    def _create_prompt_template(self) -> PromptTemplate:
        """创建支持历史的提示模板"""
        return PromptTemplate(
            input_variables=["patient_input", "context", "history", "current_category"],
            template="""
你是一名专业的中医医生，负责采集患者的过敏史信息。

当前询问的过敏类别: {current_category}
[对话历史]
{history}

[患者描述]
{patient_input}

请提取以下信息:
1. 过敏源
2. 过敏反应表现
3. 首次发作时间
4. 严重程度
5. 治疗情况
6. 是否有家族史

请严格只输出如下JSON格式，不要输出任何其他内容或解释：

{{
    "allergy_category": "过敏类别",
    "allergen": "过敏源",
    "reaction": "过敏反应表现",
    "onset_time": "首次发作时间",
    "severity": "严重程度",
    "treatment": "治疗情况",
    "family_history": true/false,
    "confidence": 0.95
}}

注意：如果患者描述不完整，请根据已有信息填写，缺失字段用null表示。
"""
        )
    
    def _create_output_parser(self) -> JsonOutputParser:
        """创建输出解析器"""
        return JsonOutputParser()
    
    def ask_has_or_not(self, context: Dict[str, Any]) -> str:
        """询问当前过敏类别有无"""
        current_category = self.allergy_categories[self.current_category_index]
        
        questions = {
            "药物过敏": "您对什么药物过敏吗？比如青霉素、阿司匹林等？",
            "食物过敏": "您对什么食物过敏吗？比如海鲜、坚果、牛奶等？",
            "环境过敏": "您对环境因素过敏吗？比如花粉、灰尘、霉菌等？",
            "接触过敏": "您对什么物质接触过敏吗？比如金属、橡胶、化妆品等？",
            "吸入过敏": "您对什么吸入物过敏吗？比如动物毛发、羽毛等？",
            "昆虫过敏": "您对昆虫叮咬过敏吗？比如蜜蜂、蚊子等？",
            "其他过敏": "您还有其他过敏情况吗？"
        }
        
        return questions.get(current_category, "您有其他过敏情况吗？")
    
    def ask_details_if_has(self, context: Dict[str, Any], has_result: Dict[str, Any]) -> str:
        """如果有过敏史，追问详情"""
        current_category = self.allergy_categories[self.current_category_index]
        
        detail_questions = {
            "药物过敏": """
            请详细说明：
            1. 具体对什么药物过敏？
            2. 过敏的表现是什么？（皮疹、呼吸困难、休克等）
            3. 过敏的严重程度如何？
            4. 什么时候发现的过敏？
            5. 过敏反应持续多长时间？
            6. 是否需要紧急处理？
            7. 现在是否还在服用该药物？
            """,
            "食物过敏": """
            请详细说明：
            1. 具体对什么食物过敏？
            2. 过敏的表现是什么？（皮疹、腹痛、呼吸困难等）
            3. 过敏的严重程度如何？
            4. 什么时候发现的过敏？
            5. 过敏反应持续多长时间？
            6. 是否需要紧急处理？
            7. 现在是否还在食用该食物？
            """,
            "环境过敏": """
            请详细说明：
            1. 具体对什么环境因素过敏？
            2. 过敏的表现是什么？（打喷嚏、流鼻涕、眼睛痒等）
            3. 过敏的严重程度如何？
            4. 什么时候发现的过敏？
            5. 过敏反应持续多长时间？
            6. 在什么季节或环境下容易发作？
            7. 如何避免过敏？
            """,
            "接触过敏": """
            请详细说明：
            1. 具体对什么物质接触过敏？
            2. 过敏的表现是什么？（皮疹、瘙痒、红肿等）
            3. 过敏的严重程度如何？
            4. 什么时候发现的过敏？
            5. 过敏反应持续多长时间？
            6. 接触后多久出现反应？
            7. 如何避免接触？
            """,
            "吸入过敏": """
            请详细说明：
            1. 具体对什么吸入物过敏？
            2. 过敏的表现是什么？（咳嗽、呼吸困难、胸闷等）
            3. 过敏的严重程度如何？
            4. 什么时候发现的过敏？
            5. 过敏反应持续多长时间？
            6. 在什么环境下容易发作？
            7. 如何避免过敏？
            """,
            "昆虫过敏": """
            请详细说明：
            1. 具体对什么昆虫叮咬过敏？
            2. 过敏的表现是什么？（局部红肿、全身皮疹、休克等）
            3. 过敏的严重程度如何？
            4. 什么时候发现的过敏？
            5. 过敏反应持续多长时间？
            6. 是否需要紧急处理？
            7. 如何避免被叮咬？
            """,
            "其他过敏": """
            请详细说明：
            1. 具体对什么过敏？
            2. 过敏的表现是什么？
            3. 过敏的严重程度如何？
            4. 什么时候发现的过敏？
            5. 过敏反应持续多长时间？
            6. 是否需要特殊处理？
            7. 如何避免过敏？
            """
        }
        
        return detail_questions.get(current_category, "请详细描述一下。")
    
    def next_category(self):
        """移动到下一个过敏类别"""
        if self.current_category_index < len(self.allergy_categories) - 1:
            self.current_category_index += 1
            return True
        return False
    
    def reset_categories(self):
        """重置过敏类别索引"""
        self.current_category_index = 0
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力"""
        return [
            "药物过敏史采集",
            "食物过敏史采集",
            "环境过敏史采集", 
            "接触过敏史采集",
            "吸入过敏史采集",
            "昆虫过敏史采集",
            "其他过敏史采集"
        ]
    
    def get_required_inputs(self) -> List[str]:
        """获取所需输入"""
        return [
            "patient_input",
            "context"
        ]
    
    def get_outputs(self) -> List[str]:
        """获取输出"""
        return [
            "allergy_category",
            "allergen_type",
            "allergen_name",
            "reaction_symptoms",
            "severity",
            "occurrence_time",
            "duration",
            "needs_emergency"
        ]
    
    required_fields = ["allergy_category", "allergen", "reaction", "onset_time", "severity", "treatment", "family_history"]
    def generate_followup(self, missing_fields):
        mapping = {
            "allergy_category": "请说明过敏类别。",
            "allergen": "请说明过敏源。",
            "reaction": "请说明过敏反应表现。",
            "onset_time": "请说明首次发作时间。",
            "severity": "请说明严重程度。",
            "treatment": "请说明治疗情况。",
            "family_history": "请说明是否有家族史。"
        }
        return "；".join([mapping.get(f, f"请补充{f}") for f in missing_fields])
    def generate_summary(self, extracted_data):
        return f"过敏史: {extracted_data}" 