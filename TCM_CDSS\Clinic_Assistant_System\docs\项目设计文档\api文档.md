### API功能交互说明
#### 一、 认证与用户
- 员工登录功能：提供手机号和密码，验证用户身份以登录系统。交互：成功后，后端返回用户的身份令牌（Token）和基本信息（姓名、角色），前端需保存此令牌用于后续所有操作。
- 获取当前用户信息功能：每次刷新页面或进入系统时，使用已保存的令牌向后端确认登录状态，并获取当前用户的详细信息及权限。
- 员工登出功能：通知后端当前用户已登出，后端将使其身份令牌失效。
#### 二、 工作台 (首页)
- 获取工作台摘要数据功能：获取用于显示在工作台首页的核心统计数据，如今日预约人数、待收费数量、今日收入总览等。
- 获取待办事项列表功能：获取与当前登录用户相关的待办列表，例如医师的“今日待就诊预约”或前台的“待划价收费列表”。
#### 三、 患者管理
- 获取患者列表功能：从后端获取一份完整的患者名单。支持通过姓名、手机号等关键词进行搜索，并支持分页浏览。
- 创建新患者功能：将新患者的个人档案信息（如姓名、性别、出生日期、联系方式等）发送给后端，进行存档。
- 获取指定患者的详细信息功能：根据患者的唯一标识，从后端获取其完整的个人档案以及所有关联信息，如历史就诊记录、历史处方等。
- 更新患者信息功能：将修改后的患者个人档案信息发送给后端进行更新。
- 获取指定患者的就诊历史功能：根据患者标识，单独获取该患者的所有历史就诊记录列表。
#### 四、 预约管理
- 获取预约日历数据功能：从后端获取指定时间范围（例如一个月）和指定医师的所有预约安排，用于在前端日历上进行展示。
- 创建新预约功能：将新预约的信息（包括选择的患者、医师、预约时间点）发送给后端进行创建。
- 更新预约功能：修改一个已存在的预约，例如更改预约时间、或更新预约状态（如“患者已确认”）。
- 取消预约功能：根据预约的唯一标识，通知后端取消该预约。
#### 五、 就诊与电子病历
- 开始就诊（挂号/报到）功能：为一位到诊的患者创建一次新的就诊记录，标志着诊疗流程的开始。
- 获取完整的就诊详情功能：在医师接诊时，根据就诊记录的唯一标识，从后端获取本次就诊的全部相关信息，包括患者信息、病历内容、已开处方等。
- 保存或更新电子病历功能：将医师填写的电子病历内容（主诉、四诊信息、辨证论治、医嘱等）发送给后端进行保存或更新。这个交互会非常频繁，前端可能需要实现自动保存功能。
#### 六、 处方与治疗
- 开立新处方/治疗项目功能：在电子病历页面，将新开立的处方（包含所有药品明细）或治疗项目（如针灸、推拿）发送给后端，与当前就诊记录关联。
- 获取就诊中的所有处方/治疗功能：获取当前就诊已开立的所有处方和治疗项目列表。
- 修改或删除处方/治疗功能：在收费前，允许医师修改或删除已开立但尚未执行的处方或治疗项目。
#### 七、 划价收费
- 获取待收费列表功能：获取所有已完成就诊但尚未支付的账单列表。
- 获取单次就诊的费用明细功能：根据就诊记录，向后端请求生成详细的费用清单，包含诊金、药品费、治疗费等。
- 记录支付功能：将一笔成功的支付信息（包含支付金额、支付方式）发送给后端，后端会更新对应账单的支付状态。
#### 八、 药房管理
- 获取待发药处方列表功能：药房人员从后端获取所有已收费但尚未发药的处方列表。
- 执行发药功能：药房人员完成配药后，通知后端将指定处方的状态更新为“已发药”，此操作会同步扣减库存。
#### 九、 库存与产品管理
- 获取所有药品/服务列表功能：获取系统内定义的所有中药饮片、成药、服务项目等。此功能在开处方时的药品搜索/自动补全功能上至关重要。
- 管理产品信息功能：支持新增、修改或删除系统中的药品和服务项目及其价格。
- 获取库存信息功能：获取所有需要库存管理的物品的当前库存量、预警线等信息。
- 调整库存功能：向后端发送库存调整指令，用于处理药品入库、出库、盘点等操作。
#### 十、 报表与统计
- 获取财务报表数据功能：根据指定的时间范围，从后端获取用于生成财务报表的统计数据（如每日收入、各类目收入占比等）。
- 获取临床分析数据功能：从后端获取临床相关的统计数据，如最常见病种分析、使用频率最高的药品排行等。
#### 十一、 系统设置
- 获取和更新诊所信息功能：读取或修改诊所的基本信息，如名称、地址、联系电话等。
- 获取和管理员工及角色功能：支持查看所有员工列表、新增员工、修改员工的角色和权限。