<template>
  <div class="staff-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">员工管理</h1>
      <div class="add-button" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
      </div>
    </header>
    <div class="search-section">
      <div class="search-left">
        <div class="search-container">
          <el-icon class="search-icon"><Search /></el-icon>
          <input
            v-model="searchQuery"
            type="text"
            class="search-input"
            placeholder="搜索员工姓名或工号"
            @input="handleSearch"
          />
        </div>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
      <div class="filter-right">
        <div class="filter-tabs">
          <div
            v-for="tab in filterTabs"
            :key="tab.value"
            :class="['filter-tab', { active: currentFilter === tab.value }]"
            @click="currentFilter = tab.value"
          >
            {{ tab.label }}
          </div>
        </div>
      </div>
    </div>
    <div class="staff-list">
      <div
        v-for="staff in filteredStaffList"
        :key="staff.id"
        class="staff-item"
        @click="viewStaffDetail(staff)"
      >
        <div class="staff-avatar">
          {{ staff.name.charAt(0) }}
        </div>
        <div class="staff-info">
          <div class="staff-name">{{ staff.name }}</div>
          <div class="staff-position">{{ staff.position }}</div>
          <div class="staff-contact">
            <span class="phone">{{ staff.phone }}</span>
            <span class="status" :class="staff.status">
              {{ getStatusText(staff.status) }}
            </span>
          </div>
        </div>
        <div class="staff-actions">
          <el-button
            type="primary"
            size="small"
            @click.stop="editStaff(staff)"
          >
            编辑
          </el-button>
          <el-button
            type="info"
            size="small"
            @click.stop="viewDetail(staff)"
          >
            详情
          </el-button>
        </div>
      </div>
    </div>
    <div v-if="filteredStaffList.length === 0" class="empty-state">
      <el-icon size="64" color="#C7C7CC"><UserFilled /></el-icon>
      <p>暂无员工数据</p>
    </div>
    <el-dialog
      v-model="showAddDialog"
      :title="editMode ? '编辑员工' : '新增员工'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="staffFormRef"
        :model="staffForm"
        :rules="staffRules"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="staffForm.name" placeholder="请输入员工姓名" />
        </el-form-item>
        <el-form-item label="工号" prop="employeeId">
          <el-input v-model="staffForm.employeeId" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-select v-model="staffForm.position" placeholder="请选择职位" style="width: 100%">
            <el-option label="医师" value="医师" />
            <el-option label="护士" value="护士" />
            <el-option label="药师" value="药师" />
            <el-option label="前台" value="前台" />
            <el-option label="管理员" value="管理员" />
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="staffForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="staffForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="staffForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="在职" value="active" />
            <el-option label="离职" value="inactive" />
            <el-option label="请假" value="leave" />
          </el-select>
        </el-form-item>
        <el-form-item label="入职时间" prop="hireDate">
          <el-date-picker
            v-model="staffForm.hireDate"
            type="date"
            style="width: 100%"
            placeholder="请选择入职时间"
          />
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            v-model="staffForm.note"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息..."
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveStaff">保存</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDetailDialog"
      title="员工详情"
      width="600px"
    >
      <div v-if="selectedStaff" class="detail-content">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-row">
            <span class="label">姓名：</span>
            <span>{{ selectedStaff.name }}</span>
          </div>
          <div class="detail-row">
            <span class="label">工号：</span>
            <span>{{ selectedStaff.employeeId }}</span>
          </div>
          <div class="detail-row">
            <span class="label">职位：</span>
            <span>{{ selectedStaff.position }}</span>
          </div>
          <div class="detail-row">
            <span class="label">状态：</span>
            <span class="status" :class="selectedStaff.status">
              {{ getStatusText(selectedStaff.status) }}
            </span>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>联系信息</h4>
          <div class="detail-row">
            <span class="label">手机号：</span>
            <span>{{ selectedStaff.phone }}</span>
          </div>
          <div class="detail-row">
            <span class="label">邮箱：</span>
            <span>{{ selectedStaff.email || '-' }}</span>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>工作信息</h4>
          <div class="detail-row">
            <span class="label">入职时间：</span>
            <span>{{ formatDate(selectedStaff.hireDate) }}</span>
          </div>
          <div class="detail-row">
            <span class="label">备注：</span>
            <span>{{ selectedStaff.note || '-' }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Plus,
  Search,
  UserFilled
} from '@element-plus/icons-vue'

const router = useRouter()

// 搜索相关
const searchQuery = ref('')
const currentFilter = ref('all')

// 筛选标签
const filterTabs = [
  { label: '全部', value: 'all' },
  { label: '在职', value: 'active' },
  { label: '离职', value: 'inactive' },
  { label: '请假', value: 'leave' }
]

// 模拟员工数据
const staffList = ref([
  {
    id: 1,
    name: '张医生',
    employeeId: 'EMP001',
    position: '医师',
    phone: '138****1234',
    email: '<EMAIL>',
    status: 'active',
    hireDate: new Date('2023-01-15'),
    note: '主任医师，擅长内科疾病'
  },
  {
    id: 2,
    name: '王护士',
    employeeId: 'EMP002',
    position: '护士',
    phone: '139****5678',
    email: '<EMAIL>',
    status: 'active',
    hireDate: new Date('2023-03-20'),
    note: '主管护士，负责护理工作'
  },
  {
    id: 3,
    name: '李药师',
    employeeId: 'EMP003',
    position: '药师',
    phone: '137****9012',
    email: '<EMAIL>',
    status: 'active',
    hireDate: new Date('2023-02-10'),
    note: '主管药师，负责药房管理'
  },
  {
    id: 4,
    name: '赵前台',
    employeeId: 'EMP004',
    position: '前台',
    phone: '136****3456',
    email: '<EMAIL>',
    status: 'leave',
    hireDate: new Date('2023-04-05'),
    note: '前台接待，负责患者接待'
  }
])

// 过滤后的员工列表
const filteredStaffList = computed(() => {
  let filtered = staffList.value

  // 按状态筛选
  if (currentFilter.value !== 'all') {
    filtered = filtered.filter(item => item.status === currentFilter.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value) {
    filtered = filtered.filter(item =>
      item.name.includes(searchQuery.value) ||
      item.employeeId.includes(searchQuery.value)
    )
  }

  return filtered
})

// 弹窗控制
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const selectedStaff = ref(null)
const editMode = ref(false)

// 表单数据
const staffFormRef = ref()
const staffForm = ref({
  name: '',
  employeeId: '',
  position: '',
  phone: '',
  email: '',
  status: 'active',
  hireDate: '',
  note: ''
})

const staffRules = {
  name: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' }
  ],
  employeeId: [
    { required: true, message: '请输入工号', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请选择职位', trigger: 'change' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  hireDate: [
    { required: true, message: '请选择入职时间', trigger: 'change' }
  ]
}

// 返回功能中心
const goBack = () => router.push('/dashboard')

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    active: '在职',
    inactive: '离职',
    leave: '请假'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (date: Date) => {
  if (!date) return ''
  return date.toLocaleDateString('zh-CN')
}

// 查看员工详情
const viewStaffDetail = (staff: any) => {
  selectedStaff.value = staff
  showDetailDialog.value = true
}

// 编辑员工
const editStaff = (staff: any) => {
  selectedStaff.value = staff
  editMode.value = true
  staffForm.value = { ...staff }
  showAddDialog.value = true
}

// 查看详情
const viewDetail = (staff: any) => {
  selectedStaff.value = staff
  showDetailDialog.value = true
}

// 保存员工
const saveStaff = async () => {
  if (!staffFormRef.value) return

  try {
    await staffFormRef.value.validate()
    
    if (editMode.value) {
      // 编辑模式
      const index = staffList.value.findIndex(item => item.id === selectedStaff.value.id)
      if (index > -1) {
        staffList.value[index] = { ...selectedStaff.value, ...staffForm.value }
      }
      ElMessage.success('员工信息更新成功')
    } else {
      // 新增模式
      const newStaff = {
        id: Date.now(),
        ...staffForm.value,
        hireDate: staffForm.value.hireDate
      }
      staffList.value.unshift(newStaff)
      ElMessage.success('员工添加成功')
    }
    
    showAddDialog.value = false
    editMode.value = false
    
    // 重置表单
    staffForm.value = {
      name: '',
      employeeId: '',
      position: '',
      phone: '',
      email: '',
      status: 'active',
      hireDate: '',
      note: ''
    }
  } catch (error) {
    ElMessage.error('请检查表单信息')
  }
}
</script>

<style scoped>
.staff-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}
.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}
.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}
.back-button:hover {
  color: var(--primary-dark);
}
.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}
.add-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: white;
}
.add-button:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}
.search-section {
  padding: var(--spacing-sm) var(--spacing-xl);
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}
.search-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.search-container {
  position: relative;
  width: 300px;
}
.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}
.search-input {
  width: 100%;
  height: 36px;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 44px;
  border: 1px solid var(--border-color);
  border-radius: 18px;
  background: var(--background-color);
  font-size: var(--font-size-md);
  outline: none;
  transition: all var(--transition-fast);
}
.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}
.search-input::placeholder {
  color: var(--text-secondary);
}
.filter-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.filter-tabs {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}
.filter-tab {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.filter-tab:hover {
  color: var(--primary-color);
}
.filter-tab.active {
  background: var(--primary-color);
  color: white;
}
.staff-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
}
.staff-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-fast);
}
.staff-item:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}
.staff-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-right: var(--spacing-lg);
}
.staff-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.staff-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}
.staff-position {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
}
.staff-contact {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.phone {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
}
.status.active {
  background: #d4edda;
  color: #155724;
}
.status.inactive {
  background: #f8d7da;
  color: #721c24;
}
.status.leave {
  background: #fff3cd;
  color: #856404;
}
.staff-actions {
  display: flex;
  gap: var(--spacing-sm);
}
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  gap: var(--spacing-md);
}
.empty-state p {
  font-size: var(--font-size-md);
  margin: 0;
}
.detail-content {
  padding: var(--spacing-md);
}
.detail-section {
  margin-bottom: var(--spacing-lg);
}
.detail-section h4 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}
.detail-row {
  display: flex;
  margin-bottom: var(--spacing-sm);
}
.detail-row .label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 100px;
}
.detail-row .status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
}
</style> 