"""
智能体基类

整合四个项目的智能体功能，提供统一的智能体接口和基础功能。
支持OpenRouter和Ollama双模式，包括LCEL管道、实体提取、追问生成等。
"""

import logging
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import BaseOutputParser
from langchain_core.runnables import Runnable
from pydantic import BaseModel, Field

from ..core.config import get_settings
from ..utils.llm_client import LLMClient, get_llm_client

logger = logging.getLogger(__name__)
settings = get_settings()


class AgentResponse(BaseModel):
    """智能体响应模型"""
    
    response: str = Field(description="智能体的回复内容")
    extracted_data: Dict[str, Any] = Field(default_factory=dict, description="提取的结构化数据")
    entities: List[Dict[str, Any]] = Field(default_factory=list, description="提取的医疗实体")
    confidence: float = Field(default=0.8, description="置信度", ge=0.0, le=1.0)
    next_question: Optional[str] = Field(default=None, description="下一个问题")
    is_complete: bool = Field(default=False, description="当前阶段是否完成")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    summary: Optional[str] = Field(default=None, description="结构化结论报告")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MedicalEntity(BaseModel):
    """医疗实体模型"""
    
    entity_type: str = Field(description="实体类型：症状|疾病|药物|部位|时间|程度")
    entity_value: str = Field(description="实体值")
    confidence: float = Field(description="置信度", ge=0.0, le=1.0)
    position: Optional[int] = Field(default=None, description="在文本中的位置")
    context: Optional[str] = Field(default=None, description="上下文")


class AgentOutputParser(BaseOutputParser):
    """智能体输出解析器"""
    
    def parse(self, text: str) -> Union[Dict[str, Any], List[Any], str]:
        """解析输出文本"""
        try:
            # 清理文本
            text = text.strip()
            
            # 尝试解析JSON
            if text.startswith('{') or text.startswith('['):
                return json.loads(text)
            
            # 尝试提取JSON块
            if '```json' in text:
                start = text.find('```json') + 7
                end = text.find('```', start)
                if end != -1:
                    json_text = text[start:end].strip()
                    return json.loads(json_text)
            
            # 如果不是JSON，返回原始文本
            return {"text": text}
            
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}, 原始文本: {text[:100]}...")
            return {"text": text, "parse_error": str(e)}
    
    def get_format_instructions(self) -> str:
        """获取格式说明"""
        return """
        请以JSON格式输出结果。
        如果是列表，请使用数组格式。
        如果是对象，请使用键值对格式。
        示例：{"key": "value"} 或 [{"item1": "value1"}, {"item2": "value2"}]
        """


class BaseAgent(ABC):
    """智能体基类 - 整合版本"""
    
    # 子类需要覆盖的字段
    required_fields: List[str] = []
    optional_fields: List[str] = []
    agent_type: str = "base"
    
    def __init__(self, name: str, description: str, agent_type: Optional[str] = None):
        """
        初始化智能体
        
        Args:
            name: 智能体名称
            description: 智能体描述
            agent_type: 智能体类型
        """
        self.name = name
        self.description = description
        self.agent_type = agent_type or self.agent_type
        
        # 初始化LLM客户端
        self.llm_client = get_llm_client()
        
        # 初始化组件
        self.prompt_template = self._create_prompt_template()
        self.output_parser = self._create_output_parser()
        self.chain = self._create_chain()
        
        # 状态管理
        self.collected_data = {}
        self.conversation_history = []
        self.is_completed = False
        
        logger.info(f"初始化智能体: {self.name} (类型: {self.agent_type})")
    
    @abstractmethod
    def _create_prompt_template(self) -> PromptTemplate:
        """创建提示模板 - 子类必须实现"""
        pass
    
    def _create_output_parser(self) -> BaseOutputParser:
        """创建输出解析器"""
        return AgentOutputParser()
    
    def _create_chain(self) -> Runnable:
        """创建LCEL管道"""
        return self.prompt_template | self.llm_client.get_llm() | self.output_parser
    
    @abstractmethod
    def ask_has_or_not(self, context: Dict[str, Any]) -> str:
        """询问有无 - 第一步，子类必须实现"""
        pass
    
    @abstractmethod
    def ask_details_if_has(self, context: Dict[str, Any], has_result: Dict[str, Any]) -> str:
        """如果有则追问详情 - 第二步，子类必须实现"""
        pass
    
    def parse_has_or_not_response(self, response: str) -> Dict[str, Any]:
        """解析患者对"有无"的回答"""
        prompt = PromptTemplate(
            input_variables=["patient_response", "agent_type"],
            template="""
            你是一名专业的中医医生，请分析患者的回答，判断是否有相关的{agent_type}症状或情况。
            
            患者回答: {patient_response}
            
            请仔细分析患者的回答，判断是否明确表达了有相关症状或情况。
            
            请以JSON格式输出:
            {{
                "has": true/false,
                "confidence": 0.0-1.0,
                "reasoning": "分析理由",
                "keywords": ["关键词1", "关键词2"]
            }}
            """
        )
        
        try:
            chain = prompt | self.llm_client.get_llm() | self.output_parser
            result = chain.invoke({
                "patient_response": response,
                "agent_type": self.agent_type
            })
            
            if isinstance(result, dict):
                return result
            else:
                return {"has": False, "confidence": 0.5, "reasoning": "解析失败"}
                
        except Exception as e:
            logger.error(f"解析患者回答失败: {e}")
            return {"has": False, "confidence": 0.0, "reasoning": f"解析错误: {str(e)}"}
    
    def extract_entities(self, text: str, context: Optional[Dict[str, Any]] = None) -> List[MedicalEntity]:
        """提取医疗实体"""
        prompt = PromptTemplate(
            input_variables=["text", "agent_type"],
            template="""
            请从以下文本中提取与{agent_type}相关的医疗实体信息:
            
            文本: {text}
            
            请识别以下类型的实体：
            - 症状：患者描述的不适感受
            - 疾病：明确的疾病名称
            - 药物：提到的药物名称
            - 部位：身体部位或器官
            - 时间：时间相关的描述
            - 程度：严重程度、频率等
            
            请以JSON格式输出实体列表:
            [
                {{
                    "entity_type": "症状|疾病|药物|部位|时间|程度",
                    "entity_value": "实体值",
                    "confidence": 0.0-1.0,
                    "context": "上下文"
                }}
            ]
            """
        )
        
        try:
            chain = prompt | self.llm_client.get_llm() | self.output_parser
            result = chain.invoke({
                "text": text,
                "agent_type": self.agent_type
            })
            
            entities = []
            if isinstance(result, list):
                for item in result:
                    if isinstance(item, dict):
                        entities.append(MedicalEntity(**item))
            
            return entities
            
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return []
    
    def structure_data(self, response: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """结构化数据提取"""
        prompt = PromptTemplate(
            input_variables=["text", "agent_type", "required_fields"],
            template="""
            请将以下文本中的信息结构化，根据{agent_type}智能体的要求提取相关信息:
            
            文本: {text}
            
            需要提取的字段: {required_fields}
            
            请仔细分析文本内容，提取结构化信息。如果某个字段在文本中没有明确提到，请设置为null。
            
            请以JSON格式输出结构化数据:
            {{
                "field1": "value1 或 null",
                "field2": "value2 或 null",
                ...
            }}
            """
        )
        
        try:
            chain = prompt | self.llm_client.get_llm() | self.output_parser
            result = chain.invoke({
                "text": response,
                "agent_type": self.agent_type,
                "required_fields": ", ".join(self.required_fields)
            })
            
            return result if isinstance(result, dict) else {}
            
        except Exception as e:
            logger.error(f"数据结构化失败: {e}")
            return {}
    
    def is_complete(self, extracted_data: Dict[str, Any]) -> bool:
        """检查是否收集完整"""
        return all(
            extracted_data.get(field) is not None and extracted_data.get(field) != ""
            for field in self.required_fields
        )
    
    def get_missing_fields(self, extracted_data: Dict[str, Any]) -> List[str]:
        """获取缺失的字段"""
        return [
            field for field in self.required_fields
            if not extracted_data.get(field)
        ]
    
    def generate_followup(self, missing_fields: List[str], context: Optional[Dict[str, Any]] = None) -> str:
        """生成追问问题"""
        if not missing_fields:
            return "信息收集完整，无需追问。"
        
        # 子类可以覆盖此方法以提供更具体的追问
        return f"请补充以下信息：{', '.join(missing_fields)}"
    
    def generate_summary(self, extracted_data: Dict[str, Any]) -> str:
        """生成结构化总结"""
        # 子类可以覆盖此方法以提供更具体的总结
        summary_parts = []
        for field, value in extracted_data.items():
            if value:
                summary_parts.append(f"{field}: {value}")
        
        return "; ".join(summary_parts) if summary_parts else "无有效信息"
    
    async def agent_workflow(self, context: Dict[str, Any]) -> AgentResponse:
        """智能体工作流程"""
        try:
            # 获取对话历史
            history = context.get("conversation_history", [])
            history_text = "\n".join([
                f"{msg.get('role', 'user')}: {msg.get('content', '')}"
                for msg in history[-5:]  # 只保留最近5轮对话
            ]) if history else ""
            
            # 获取患者输入
            patient_input = (
                context.get("detailed_response") or 
                context.get("patient_response") or 
                context.get("message") or 
                ""
            )
            
            if not patient_input:
                return AgentResponse(
                    response="请提供您的症状描述。",
                    is_complete=False,
                    confidence=0.0
                )
            
            # 使用LCEL链提取结构化数据
            chain_input = {
                "patient_input": patient_input,
                "context": self.agent_type,
                "history": history_text
            }
            
            # 执行链式处理
            result = await self.chain.ainvoke(chain_input)
            
            # 确保result是字典格式
            if not isinstance(result, dict):
                result = {"text": str(result)}
            
            # 提取实体
            entities = self.extract_entities(patient_input, context)
            
            # 检查完整性
            is_complete = self.is_complete(result)
            missing_fields = self.get_missing_fields(result)
            
            # 生成响应
            if not is_complete and missing_fields:
                followup = self.generate_followup(missing_fields, context)
                return AgentResponse(
                    response=followup,
                    extracted_data=result,
                    entities=[entity.dict() for entity in entities],
                    confidence=0.8,
                    is_complete=False,
                    next_question=followup,
                    metadata={
                        "missing_fields": missing_fields,
                        "agent_type": self.agent_type
                    }
                )
            else:
                summary = self.generate_summary(result)
                return AgentResponse(
                    response="本轮信息采集已完成。",
                    extracted_data=result,
                    entities=[entity.dict() for entity in entities],
                    confidence=0.95,
                    is_complete=True,
                    summary=summary,
                    metadata={
                        "agent_type": self.agent_type,
                        "completion_time": datetime.now().isoformat()
                    }
                )
                
        except Exception as e:
            logger.error(f"智能体工作流程执行失败: {e}")
            return AgentResponse(
                response=f"处理过程中出现错误，请重新描述您的症状。",
                is_complete=False,
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    async def run(self, context: Dict[str, Any]) -> AgentResponse:
        """运行智能体"""
        return await self.agent_workflow(context)
    
    def update_conversation_history(self, role: str, content: str):
        """更新对话历史"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # 保持历史记录在合理范围内
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
    
    def get_agent_info(self) -> Dict[str, Any]:
        """获取智能体信息"""
        return {
            "name": self.name,
            "description": self.description,
            "agent_type": self.agent_type,
            "required_fields": self.required_fields,
            "optional_fields": self.optional_fields,
            "is_completed": self.is_completed,
            "collected_data": self.collected_data
        }
