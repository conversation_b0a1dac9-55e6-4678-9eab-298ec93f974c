/**
 * 用户数据管理
 * 
 * 主要功能：
 * 1. 存储系统所有用户的账号信息
 * 2. 提供用户验证的数据源
 * 3. 定义用户角色和权限
 * 
 * 用户角色说明：
 * - admin: 管理员，拥有系统设置权限
 * - doctor: 医生，拥有业务操作权限
 * 
 * 数据结构：
 * - id: 用户唯一标识
 * - username: 登录用户名（手机号或工号）
 * - password: 登录密码（明文存储，仅用于演示）
 * - name: 用户真实姓名
 * - role: 用户角色
 * - avatar: 用户头像（暂未使用）
 * 
 * 注意事项：
 * - 当前为演示版本，密码明文存储
 * - 生产环境应使用加密存储和数据库
 * - 用户数据应与后端API对接
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

export const users = [
  {
    id: 1,
    username: 'admin',
    password: 'admin',
    name: '管理员',
    role: 'admin',
    avatar: ''
  },
  {
    id: 2,
    username: '13800138000', // 示例医生账号，使用手机号登录
    password: 'password123',
    name: '李时珍',
    role: 'doctor',
    avatar: ''
  },
  {
    id: 3,
    username: '13900139000', // 示例医生账号
    password: 'password123',
    name: '华佗',
    role: 'doctor',
    avatar: ''
  }
]; 