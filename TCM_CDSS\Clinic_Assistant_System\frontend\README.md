# 观建在-中医诊所管理系统前端

## 项目简介

这是一个基于Vue 3 + TypeScript + Vite + Pinia + Element Plus的中医诊所管理系统前端项目，采用iOS风格设计，专为桌面端使用。

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript超集
- **Vite** - 下一代前端构建工具
- **Pinia** - Vue状态管理库
- **Element Plus** - Vue 3组件库
- **Vue Router** - Vue官方路由管理器

## 项目结构

```
frontend/
├── src/
│   ├── views/           # 页面组件
│   │   ├── Login.vue    # 登录页面
│   │   ├── Dashboard.vue # 功能中心
│   │   ├── appointments/ # 预约挂号模块
│   │   ├── patients/    # 患者管理模块
│   │   ├── emr/         # 电子病历模块
│   │   ├── billing/     # 划价收费模块
│   │   ├── pharmacy/    # 药房管理模块
│   │   ├── inventory/   # 库存管理模块
│   │   ├── staff/       # 员工管理模块
│   │   ├── reports/     # 统计报表模块
│   │   └── settings/    # 系统设置模块
│   ├── router/          # 路由配置
│   ├── stores/          # 状态管理
│   ├── styles/          # 样式文件
│   ├── App.vue          # 根组件
│   └── main.ts          # 入口文件
├── public/              # 静态资源
├── index.html           # HTML模板
├── package.json         # 项目配置
├── vite.config.ts       # Vite配置
├── tsconfig.json        # TypeScript配置
└── README.md           # 项目说明
```

## 环境要求

- **Node.js** >= 16.0.0
- **npm** >= 8.0.0 或 **yarn** >= 1.22.0

## 安装和运行

### 1. 安装依赖

```bash
# 进入前端项目目录
cd frontend

# 安装依赖
npm install
```

### 2. 启动开发服务器

```bash
# 启动开发服务器
npm run dev
```

启动成功后，终端会显示：
```
  VITE v5.x.x  ready in xxx ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: use --host to expose
```

### 3. 访问应用

在浏览器中打开 `http://localhost:3000` 即可访问应用。

## 开发指南

### 登录系统

- 用户名：任意输入
- 密码：任意输入（至少6位）
- 点击登录按钮即可进入系统

### 功能模块

1. **功能中心** - 系统主页面，展示所有功能模块
2. **预约挂号** - 管理患者预约信息
3. **患者管理** - 管理患者档案信息
4. **电子病历** - 中医特色电子病历系统
5. **划价收费** - 处理患者费用结算
6. **药房管理** - 管理药品发放
7. **库存管理** - 管理药品库存
8. **员工管理** - 管理系统用户
9. **统计报表** - 查看各类统计数据
10. **系统设置** - 系统配置管理

### 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产构建
npm run preview

# 代码检查
npm run lint
```

## 项目特性

### 设计风格
- **iOS风格** - 采用iOS设计语言，界面简洁美观
- **响应式布局** - 适配不同屏幕尺寸
- **现代化UI** - 使用Element Plus组件库

### 功能特性
- **模块化架构** - 清晰的模块划分
- **状态管理** - 使用Pinia进行状态管理
- **路由管理** - 完整的路由配置和守卫
- **类型安全** - 完整的TypeScript支持

## 开发注意事项

### 1. 代码规范
- 使用TypeScript进行类型检查
- 遵循Vue 3 Composition API规范
- 使用ESLint进行代码检查

### 2. 样式规范
- 使用CSS变量定义主题色彩
- 遵循iOS设计规范
- 保持组件样式的一致性

### 3. 组件开发
- 组件命名使用PascalCase
- 文件命名使用kebab-case
- 保持组件的单一职责

## 部署说明

### 构建生产版本

```bash
npm run build
```

构建完成后，`dist`目录包含可部署的静态文件。

### 部署到服务器

将`dist`目录中的文件部署到Web服务器即可。

## 常见问题

### 1. 端口被占用
如果3000端口被占用，可以在`vite.config.ts`中修改端口：

```typescript
export default defineConfig({
  server: {
    port: 3001 // 修改为其他端口
  }
})
```

### 2. 依赖安装失败
```bash
# 清除缓存后重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 3. 类型错误
确保TypeScript配置正确，检查`tsconfig.json`文件。

## 技术支持

如有问题，请检查：
1. Node.js版本是否符合要求
2. 依赖是否正确安装
3. 端口是否被占用
4. 浏览器控制台是否有错误信息

## 更新日志

### v1.0.0
- 初始版本发布
- 完成基础功能模块
- 实现iOS风格界面设计 