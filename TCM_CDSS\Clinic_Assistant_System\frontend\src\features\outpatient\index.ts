// 门诊工作站子页面组件导出
export { default as PrescriptionDetail } from './PrescriptionDetail.vue'
export { default as MedicalRecordDetail } from './MedicalRecordDetail.vue'
export { default as PatientDetail } from './PatientDetail.vue'
export { default as SymptomAnalysis } from './SymptomAnalysis.vue'
export { default as DiagnosisSuggestion } from './DiagnosisSuggestion.vue'

// 组件类型定义
export interface OutpatientComponent {
  name: string
  component: any
  props?: Record<string, any>
}

// 可用的子页面组件列表
export const outpatientComponents: OutpatientComponent[] = [
  {
    name: 'PrescriptionDetail',
    component: () => import('./PrescriptionDetail.vue')
  },
  {
    name: 'MedicalRecordDetail', 
    component: () => import('./MedicalRecordDetail.vue')
  },
  {
    name: 'PatientDetail',
    component: () => import('./PatientDetail.vue')
  },
  {
    name: 'SymptomAnalysis',
    component: () => import('./SymptomAnalysis.vue')
  },
  {
    name: 'DiagnosisSuggestion',
    component: () => import('./DiagnosisSuggestion.vue')
  }
]
