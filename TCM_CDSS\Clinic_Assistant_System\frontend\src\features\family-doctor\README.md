# 家医管理模块

## 功能描述
家医管理模块负责管理家庭医生服务，包括家医团队管理、患者签约、健康档案等功能。

## 目录结构
```
family-doctor/
├── views/                    # 页面视图
│   └── FamilyDoctorList.vue  # 家医管理页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 家医团队管理
- 患者签约管理
- 健康档案管理
- 随访计划

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 