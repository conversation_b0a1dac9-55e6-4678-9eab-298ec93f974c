#!/usr/bin/env python3
"""
工作流程问诊功能测试脚本
"""

import requests
import json
import time
from datetime import datetime

# 配置
API_BASE = "http://localhost:8002/api"
WORKFLOW_API = f"{API_BASE}/workflow"

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n{step}. {description}")
    print("-" * 40)

def test_workflow_demo():
    """测试工作流程演示信息"""
    print_step("1", "获取工作流程演示信息")
    
    try:
        response = requests.get(f"{WORKFLOW_API}/demo")
        if response.status_code == 200:
            data = response.json()
            print("✅ 工作流程演示信息获取成功")
            print(f"标题: {data['title']}")
            print(f"描述: {data['description']}")
            print("工作流程阶段:")
            for stage in data['workflow_stages']:
                print(f"  {stage['stage']}. {stage['name']}")
                print(f"     必需信息: {stage['required_elements']}")
                print(f"     可选信息: {stage['optional_elements']}")
            return True
        else:
            print(f"❌ 获取演示信息失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_start_workflow():
    """测试启动工作流程问诊"""
    print_step("2", "启动工作流程问诊")
    
    patient_data = {
        "patient_name": "张三",
        "patient_age": 35,
        "patient_gender": "男"
    }
    
    try:
        response = requests.post(f"{WORKFLOW_API}/start", json=patient_data)
        if response.status_code == 200:
            data = response.json()
            print("✅ 工作流程问诊启动成功")
            print(f"会话ID: {data['session_id']}")
            print(f"状态: {data['status']}")
            print(f"进度: {data['progress']['current_agent']}/{data['progress']['total_agents']}")
            print(f"当前智能体: {data['progress']['current_agent_name']}")
            print(f"欢迎消息: {data['message'][:100]}...")
            return data['session_id']
        else:
            print(f"❌ 启动工作流程失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_workflow_conversation(session_id):
    """测试工作流程对话"""
    print_step("3", "测试工作流程对话")
    
    # 模拟患者回复序列
    patient_responses = [
        "我头痛三天了，主要在太阳穴附近",
        "是因为最近工作压力大，经常熬夜",
        "症状逐渐加重，休息时会缓解一些",
        "没有接受过治疗",
        "以前没有得过什么大病",
        "没有药物过敏史"
    ]
    
    for i, message in enumerate(patient_responses, 1):
        print(f"\n第{i}轮对话:")
        print(f"患者: {message}")
        
        try:
            response = requests.post(f"{WORKFLOW_API}/chat", json={
                "session_id": session_id,
                "message": message
            })
            
            if response.status_code == 200:
                data = response.json()
                print(f"AI回复: {data['message'][:150]}...")
                print(f"进度: {data['progress']['progress_percentage']:.1f}%")
                print(f"状态: {data['status']}")
                
                if data['status'] == 'completed':
                    print("🎉 工作流程问诊完成！")
                    return True
                    
                # 等待一下再继续
                time.sleep(1)
                
            else:
                print(f"❌ 对话失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 对话请求失败: {e}")
            return False
    
    return True

def test_get_session_info(session_id):
    """测试获取会话信息"""
    print_step("4", "获取会话信息")
    
    try:
        response = requests.get(f"{WORKFLOW_API}/session/{session_id}")
        if response.status_code == 200:
            data = response.json()
            print("✅ 会话信息获取成功")
            print(f"患者: {data['patient_info']['name']}")
            print(f"开始时间: {data['start_time']}")
            print(f"状态: {data['status']}")
            print(f"智能体报告数量: {len(data['agent_reports'])}")
            return True
        else:
            print(f"❌ 获取会话信息失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_get_conversation_history(session_id):
    """测试获取对话历史"""
    print_step("5", "获取对话历史")
    
    try:
        response = requests.get(f"{WORKFLOW_API}/history/{session_id}")
        if response.status_code == 200:
            data = response.json()
            print("✅ 对话历史获取成功")
            print(f"总消息数: {data['total_messages']}")
            print("最近3条消息:")
            for msg in data['history'][-3:]:
                role = "用户" if msg['role'] == 'user' else "AI"
                print(f"  {role}: {msg['content'][:50]}...")
            return True
        else:
            print(f"❌ 获取对话历史失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_active_sessions():
    """测试获取活跃会话"""
    print_step("6", "获取活跃会话列表")
    
    try:
        response = requests.get(f"{WORKFLOW_API}/sessions")
        if response.status_code == 200:
            data = response.json()
            print("✅ 活跃会话列表获取成功")
            print(f"活跃会话数: {data['total_count']}")
            for session in data['active_sessions']:
                print(f"  会话: {session['session_id'][:20]}...")
                print(f"  患者: {session['patient_name']}")
                print(f"  当前智能体: {session['current_agent']}")
            return True
        else:
            print(f"❌ 获取活跃会话失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print_header("工作流程问诊功能测试")
    
    # 检查服务器状态
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code != 200:
            print("❌ 服务器未运行，请先启动服务器")
            return
        print("✅ 服务器运行正常")
    except:
        print("❌ 无法连接到服务器，请先启动服务器")
        return
    
    # 执行测试
    test_results = []
    
    # 1. 测试演示信息
    result1 = test_workflow_demo()
    test_results.append(("工作流程演示信息", result1))
    
    # 2. 启动工作流程
    session_id = test_start_workflow()
    test_results.append(("启动工作流程问诊", session_id is not None))
    
    if session_id:
        # 3. 测试对话
        result3 = test_workflow_conversation(session_id)
        test_results.append(("工作流程对话", result3))
        
        # 4. 获取会话信息
        result4 = test_get_session_info(session_id)
        test_results.append(("获取会话信息", result4))
        
        # 5. 获取对话历史
        result5 = test_get_conversation_history(session_id)
        test_results.append(("获取对话历史", result5))
    
    # 6. 获取活跃会话
    result6 = test_active_sessions()
    test_results.append(("获取活跃会话", result6))
    
    # 输出测试结果
    print_header("测试结果汇总")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有工作流程功能测试通过！")
        print("\n🌐 访问工作流程演示页面:")
        print("http://localhost:8002/pages/workflow")
        print("http://localhost:8002/workflow")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")

if __name__ == "__main__":
    main()
