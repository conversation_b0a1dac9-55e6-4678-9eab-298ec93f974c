/**
 * API状态管理组合式函数
 * 
 * 主要功能：
 * 1. 管理API请求状态（loading、error、data）
 * 2. 提供统一的错误处理
 * 3. 支持数据缓存和刷新
 * 4. 提供便捷的API调用方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// API状态接口
export interface ApiState<T = any> {
  data: T | null
  loading: boolean
  error: string | null
  lastUpdated: number | null
}

// API选项接口
export interface ApiOptions {
  immediate?: boolean
  cache?: boolean
  cacheTime?: number
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
  showErrorMessage?: boolean
  showSuccessMessage?: boolean
  successMessage?: string
}

/**
 * 使用API的组合式函数
 */
export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: ApiOptions = {}
) {
  const {
    immediate = false,
    cache = false,
    cacheTime = 5 * 60 * 1000, // 5分钟
    onSuccess,
    onError,
    showErrorMessage = true,
    showSuccessMessage = false,
    successMessage = '操作成功'
  } = options

  // 状态管理
  const state = reactive<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null
  })

  // 缓存管理
  const cacheKey = ref<string>('')
  const cacheData = new Map<string, { data: T; timestamp: number }>()

  /**
   * 执行API请求
   */
  async function execute(...args: any[]): Promise<T | null> {
    // 生成缓存键
    if (cache) {
      cacheKey.value = JSON.stringify(args)
      
      // 检查缓存
      const cached = cacheData.get(cacheKey.value)
      if (cached && Date.now() - cached.timestamp < cacheTime) {
        state.data = cached.data
        state.lastUpdated = cached.timestamp
        return cached.data
      }
    }

    state.loading = true
    state.error = null

    try {
      const result = await apiFunction(...args)
      
      // 更新状态
      state.data = result
      state.lastUpdated = Date.now()
      
      // 缓存数据
      if (cache && cacheKey.value) {
        cacheData.set(cacheKey.value, {
          data: result,
          timestamp: state.lastUpdated
        })
      }

      // 成功回调
      if (onSuccess) {
        onSuccess(result)
      }

      // 显示成功消息
      if (showSuccessMessage) {
        ElMessage.success(successMessage)
      }

      return result
    } catch (error: any) {
      console.error('API Error:', error)
      
      // 更新错误状态
      state.error = error.message || '请求失败'
      
      // 错误回调
      if (onError) {
        onError(error)
      }

      // 显示错误消息
      if (showErrorMessage) {
        ElMessage.error(state.error)
      }

      return null
    } finally {
      state.loading = false
    }
  }

  /**
   * 刷新数据
   */
  async function refresh(...args: any[]): Promise<T | null> {
    // 清除缓存
    if (cache && cacheKey.value) {
      cacheData.delete(cacheKey.value)
    }
    
    return execute(...args)
  }

  /**
   * 重置状态
   */
  function reset(): void {
    state.data = null
    state.loading = false
    state.error = null
    state.lastUpdated = null
  }

  /**
   * 清除缓存
   */
  function clearCache(): void {
    cacheData.clear()
  }

  // 计算属性
  const isLoading = computed(() => state.loading)
  const hasError = computed(() => !!state.error)
  const hasData = computed(() => !!state.data)
  const isEmpty = computed(() => !state.loading && !state.error && !state.data)

  // 立即执行
  if (immediate) {
    execute()
  }

  return {
    // 状态
    ...state,
    
    // 计算属性
    isLoading,
    hasError,
    hasData,
    isEmpty,
    
    // 方法
    execute,
    refresh,
    reset,
    clearCache
  }
}

/**
 * 使用分页API的组合式函数
 */
export function usePaginatedApi<T = any>(
  apiFunction: (params: any) => Promise<{ data: T[]; total: number; page: number; limit: number }>,
  options: ApiOptions = {}
) {
  const pagination = reactive({
    page: 1,
    limit: 10,
    total: 0
  })

  const { execute: baseExecute, ...rest } = useApi(apiFunction, options)

  /**
   * 执行分页请求
   */
  async function execute(params: any = {}) {
    const result = await baseExecute({
      ...params,
      page: pagination.page,
      limit: pagination.limit
    })

    if (result) {
      pagination.total = result.total
      pagination.page = result.page
      pagination.limit = result.limit
    }

    return result
  }

  /**
   * 跳转到指定页
   */
  async function goToPage(page: number) {
    pagination.page = page
    return execute()
  }

  /**
   * 改变每页数量
   */
  async function changePageSize(limit: number) {
    pagination.limit = limit
    pagination.page = 1 // 重置到第一页
    return execute()
  }

  /**
   * 下一页
   */
  async function nextPage() {
    if (pagination.page * pagination.limit < pagination.total) {
      return goToPage(pagination.page + 1)
    }
  }

  /**
   * 上一页
   */
  async function prevPage() {
    if (pagination.page > 1) {
      return goToPage(pagination.page - 1)
    }
  }

  // 计算属性
  const hasNextPage = computed(() => pagination.page * pagination.limit < pagination.total)
  const hasPrevPage = computed(() => pagination.page > 1)
  const totalPages = computed(() => Math.ceil(pagination.total / pagination.limit))

  return {
    ...rest,
    
    // 分页状态
    pagination,
    
    // 计算属性
    hasNextPage,
    hasPrevPage,
    totalPages,
    
    // 方法
    execute,
    goToPage,
    changePageSize,
    nextPage,
    prevPage
  }
}

/**
 * 使用列表API的组合式函数
 */
export function useListApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T[]>,
  options: ApiOptions = {}
) {
  const { execute: baseExecute, data, ...rest } = useApi<T[]>(apiFunction, options)

  // 计算属性
  const list = computed(() => data || [])
  const count = computed(() => list.value.length)
  const isEmpty = computed(() => count.value === 0)

  /**
   * 添加项目到列表
   */
  function addItem(item: T) {
    if (data) {
      data.push(item)
    }
  }

  /**
   * 从列表中移除项目
   */
  function removeItem(predicate: (item: T) => boolean) {
    if (data) {
      const index = data.findIndex(predicate)
      if (index > -1) {
        data.splice(index, 1)
      }
    }
  }

  /**
   * 更新列表中的项目
   */
  function updateItem(predicate: (item: T) => boolean, updates: Partial<T>) {
    if (data) {
      const index = data.findIndex(predicate)
      if (index > -1) {
        Object.assign(data[index], updates)
      }
    }
  }

  return {
    ...rest,
    data,
    
    // 计算属性
    list,
    count,
    isEmpty,
    
    // 方法
    execute: baseExecute,
    addItem,
    removeItem,
    updateItem
  }
}

export default useApi
