"""
主诉采集智能体

负责采集患者的主要症状和持续时间，是问诊流程的第一步。
"""

import re
from typing import Dict, List, Optional, Any
from datetime import datetime

from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from src.agents.base_agent import BaseAgent, AgentContext, AgentResponse


class ChiefComplaintAgent(BaseAgent):
    """主诉采集智能体"""
    
    required_fields = ["chief_complaint", "duration", "concomitant_symptoms"]
    
    def __init__(self):
        super().__init__(
            name="主诉采集智能体",
            description="采集患者主要症状和持续时间",
            agent_type="chief_complaint"
        )
        self.collected_fields = set()
    
    def _create_prompt_template(self) -> PromptTemplate:
        """创建支持历史的提示模板"""
        return PromptTemplate(
            input_variables=["patient_input", "context", "history"],
            template="""
你是一名专业的中医医生，负责采集患者的主诉信息。

[对话历史]
{history}

[患者描述]
{patient_input}

请提取以下信息:
1. 主要症状
2. 症状持续时间
3. 症状性质
4. 症状部位
5. 症状程度

请严格只输出如下JSON格式，不要输出任何其他内容或解释：

{{
    "chief_complaint": "主要症状描述",
    "duration": "持续时间",
    "nature": "症状性质",
    "location": "症状部位", 
    "severity": "症状程度",
    "confidence": 0.95
}}

注意：如果患者描述不完整，请根据已有信息填写，缺失字段用null表示。
"""
        )
    
    def _create_output_parser(self) -> JsonOutputParser:
        """创建输出解析器"""
        return JsonOutputParser()
    
    def ask_has_or_not(self, context: Dict[str, Any]) -> str:
        """询问有无主诉"""
        return "您有什么不适症状吗？请详细描述一下。"
    
    def ask_details_if_has(self, context: Dict[str, Any], has_result: Dict[str, Any]) -> str:
        """如果有主诉，追问详情"""
        return """
        请详细说明以下信息：
        1. 主要症状是什么？
        2. 症状持续多长时间了？
        3. 症状在哪个部位？
        4. 症状的严重程度如何？
        5. 什么情况下会加重或减轻？
        """
    
    def extract_entities(self, response: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取医疗实体"""
        entities = []
        
        # 提取症状实体
        symptom_patterns = [
            r"头痛|头晕|眩晕|恶心|呕吐|发热|咳嗽|胸闷|心悸|腹痛|腰痛|关节痛|失眠|多梦|食欲不振|口干|口苦|便秘|腹泻|尿频|尿急|尿痛",
            r"疼痛|不适|酸胀|麻木|瘙痒|灼热|寒冷|出汗|乏力|疲劳|焦虑|抑郁|烦躁|易怒|健忘|注意力不集中"
        ]
        
        for pattern in symptom_patterns:
            matches = re.findall(pattern, response)
            for match in matches:
                entity = {
                    "entity_id": f"symptom_{len(entities)}",
                    "entity_type": "symptom",
                    "entity_value": match,
                    "confidence": 0.9,
                    "source_agent": self.name,
                    "extracted_at": datetime.now().isoformat()
                }
                entities.append(entity)
        
        # 提取时间实体
        time_patterns = [
            r"(\d+)\s*(天|周|月|年)",
            r"(昨天|今天|前天|上周|本月|今年)",
            r"(\d+)\s*(小时|分钟)"
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, response)
            for match in matches:
                if isinstance(match, tuple):
                    time_value = "".join(match)
                else:
                    time_value = match
                
                entity = {
                    "entity_id": f"time_{len(entities)}",
                    "entity_type": "time",
                    "entity_value": time_value,
                    "confidence": 0.8,
                    "source_agent": self.name,
                    "extracted_at": datetime.now().isoformat()
                }
                entities.append(entity)
        
        # 提取部位实体
        location_patterns = [
            r"头部|颈部|胸部|腹部|腰部|背部|四肢|上肢|下肢|左|右|前|后|上|下",
            r"心脏|肝脏|脾脏|肺脏|肾脏|胃|肠|膀胱|子宫|卵巢|前列腺"
        ]
        
        for pattern in location_patterns:
            matches = re.findall(pattern, response)
            for match in matches:
                entity = {
                    "entity_id": f"location_{len(entities)}",
                    "entity_type": "location",
                    "entity_value": match,
                    "confidence": 0.9,
                    "source_agent": self.name,
                    "extracted_at": datetime.now().isoformat()
                }
                entities.append(entity)
        
        return entities
    
    def generate_follow_up(self, missing_fields):
        mapping = {
            "chief_complaint": "请详细描述您的主要不适症状是什么？",
            "duration": "这个症状持续多长时间了？",
            "concomitant_symptoms": "除了主要症状，还有其他伴随症状吗？"
        }
        return "；".join([mapping.get(f, f"请补充{f}") for f in missing_fields])
    
    def generate_summary(self, extracted_data):
        cc = extracted_data.get("chief_complaint", "")
        duration = extracted_data.get("duration", "")
        cs = extracted_data.get("concomitant_symptoms", "")
        return f"患者主诉: {cc}，持续{duration}，伴有{cs}"
    
    def is_complete(self, extracted_data: Dict[str, Any]) -> bool:
        """判断当前阶段是否完成"""
        # 检查必需字段是否完整
        required_complete = all(
            extracted_data.get(field) for field in self.required_fields
        )
        
        # 检查是否有足够的信息进行下一步
        has_sufficient_info = (
            extracted_data.get("chief_complaint") and 
            extracted_data.get("duration")
        )
        
        return required_complete and has_sufficient_info
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力列表"""
        return [
            "症状识别和分类",
            "时间信息提取",
            "症状严重程度评估",
            "症状性质分析",
            "部位定位",
            "智能追问"
        ]
    
    def get_required_inputs(self) -> List[str]:
        """获取所需输入列表"""
        return [
            "患者症状描述",
            "症状持续时间",
            "症状严重程度"
        ]
    
    def get_outputs(self) -> List[str]:
        """获取输出列表"""
        return [
            "主要症状",
            "持续时间",
            "症状性质",
            "症状部位",
            "症状程度",
            "诱发因素",
            "缓解因素",
            "发病时间",
            "病情发展过程"
        ]
    
    def validate_symptom_description(self, description: str) -> Dict[str, Any]:
        """验证症状描述"""
        validation_result = {
            "is_valid": True,
            "issues": [],
            "suggestions": []
        }
        
        if not description or len(description.strip()) < 5:
            validation_result["is_valid"] = False
            validation_result["issues"].append("症状描述过于简单")
            validation_result["suggestions"].append("请详细描述症状的具体表现")
        
        if len(description) > 500:
            validation_result["issues"].append("症状描述过于冗长")
            validation_result["suggestions"].append("请简洁明了地描述主要症状")
        
        return validation_result
    
    def parse_duration(self, duration_text: str) -> Dict[str, Any]:
        """解析持续时间"""
        duration_info = {
            "value": None,
            "unit": None,
            "description": duration_text
        }
        
        # 解析数字+单位格式
        patterns = [
            (r"(\d+)\s*天", "天"),
            (r"(\d+)\s*周", "周"),
            (r"(\d+)\s*月", "月"),
            (r"(\d+)\s*年", "年"),
            (r"(\d+)\s*小时", "小时"),
            (r"(\d+)\s*分钟", "分钟")
        ]
        
        for pattern, unit in patterns:
            match = re.search(pattern, duration_text)
            if match:
                duration_info["value"] = int(match.group(1))
                duration_info["unit"] = unit
                break
        
        return duration_info 