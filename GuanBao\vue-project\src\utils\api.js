// API 通信模块 - 适配GuanBao后端API
// 支持手机号验证和流式响应

import { getCurrentUser } from './authApi.js';

// API 配置
const API_CONFIG = {
  // GuanBao 后端服务地址
  BASE_URL: "http://192.168.0.92:82",
  // 请求超时时间（毫秒）
  TIMEOUT: 30000,
  // 最大重试次数
  MAX_RETRIES: 3
};

// 生成请求ID
const generateRequestId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

export const fetchChatResponse = async function* (messages) {
  const API_URL = `${API_CONFIG.BASE_URL}/v1/chat/completions`;

  // 获取当前用户信息
  const userInfo = getCurrentUser();
  if (!userInfo) {
    throw new Error('用户未登录，请先登录');
  }

  // 生成请求ID
  const requestId = Date.now().toString() + Math.random().toString(36).substring(2, 9);

  // 提取最后一条用户消息作为query
  const lastUserMessage = messages.filter(msg => msg.role === 'user').pop();
  if (!lastUserMessage) {
    throw new Error('没有找到用户消息');
  }

  // 构建符合GuanBao API格式的请求体
  const requestBody = {
    request_id: requestId,
    phone_number: userInfo.phoneNumber,
    query: lastUserMessage.content,
    api_key: userInfo.apiKey
  };

  console.log('发送GuanBao API请求:', {
    url: API_URL,
    requestId: requestId,
    phoneNumber: userInfo.phoneNumber,
    queryLength: lastUserMessage.content.length
  });

  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      // 设置请求超时
      signal: AbortSignal.timeout(API_CONFIG.TIMEOUT)
    });

    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text();
      console.error('GuanBao API请求失败:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      throw new Error(`API请求失败: ${response.status} ${response.statusText}\n${errorText}`);
    }

    console.log('GuanBao API响应成功，开始处理流式数据...');

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let fullResponse = "";
    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('流式响应完成，总长度:', fullResponse.length);
          break;
        }

        // 解码数据块
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 按行处理数据
        const lines = buffer.split('\n');
        buffer = lines.pop() || ""; // 保留最后一个不完整的行

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (!trimmedLine) continue;

          try {
            // 解析GuanBao API返回的JSON格式
            const json = JSON.parse(trimmedLine);

            // 检查是否有错误
            if (json.error) {
              throw new Error(json.error);
            }

            // 验证响应格式
            if (json.request_id === requestId && json.phone_number === userInfo.phoneNumber) {
              const content = json.response || "";

              if (content) {
                fullResponse += content;
                console.log('收到内容片段:', content.length, '字符');
                // 返回累积的响应内容
                yield fullResponse;
              } else if (json.response === "") {
                // 空响应表示结束
                console.log('收到结束标记（空响应）');
                return fullResponse;
              }
            }

          } catch (parseError) {
            console.warn('解析JSON失败:', trimmedLine, parseError);
            // 忽略解析失败的数据，继续处理
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return fullResponse;

  } catch (error) {
    console.error('API通信错误:', error);

    // 提供更友好的错误信息
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或稍后重试');
    } else if (error.message.includes('Failed to fetch')) {
      throw new Error('无法连接到服务器，请检查服务器是否正在运行');
    } else {
      throw error;
    }
  }
};

// 检查 GuanBao 后端服务健康状态
export const checkServerHealth = async () => {
  try {
    const healthUrl = `${API_CONFIG.BASE_URL}/api/health`;
    const response = await fetch(healthUrl, {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    if (response.ok) {
      const data = await response.json();
      console.log('GuanBao 后端服务健康检查通过:', data);
      return true;
    } else {
      console.warn('GuanBao 后端服务健康检查失败:', response.status);
      return false;
    }
  } catch (error) {
    console.error('GuanBao 后端服务健康检查错误:', error);
    return false;
  }
};

// 测试GuanBao聊天接口
export const testChatCompletion = async (message = "你好") => {
  const userInfo = getCurrentUser();
  if (!userInfo) {
    throw new Error('用户未登录，请先登录');
  }

  const API_URL = `${API_CONFIG.BASE_URL}/v1/chat/completions`;
  const requestId = Date.now().toString() + Math.random().toString(36).substring(2, 9);

  const requestBody = {
    request_id: requestId,
    phone_number: userInfo.phoneNumber,
    query: message,
    api_key: userInfo.apiKey
  };

  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(15000)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`测试请求失败: ${response.status}\n${errorText}`);
    }

    // 对于流式响应，我们只读取第一个响应块
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const { value } = await reader.read();
    const chunk = decoder.decode(value);

    try {
      const data = JSON.parse(chunk.trim());
      console.log('GuanBao测试对话成功:', data);
      return data;
    } catch (parseError) {
      console.log('GuanBao测试对话响应:', chunk);
      return { message: "测试成功", response: chunk };
    }

  } catch (error) {
    console.error('GuanBao测试对话错误:', error);
    throw error;
  }
};

// 更新API配置
export const updateApiConfig = (newConfig) => {
  Object.assign(API_CONFIG, newConfig);
  console.log('API配置已更新:', API_CONFIG);
};

// 获取当前API配置
export const getApiConfig = () => {
  return { ...API_CONFIG };
};