// 预约中心卡片模块
import type { CardModule } from '../index'

// 预约中心统计数据
export const appointmentsStats = {
  todayAppointments: 15,
  tomorrowAppointments: 12,
  weekAppointments: 89,
  cancelledToday: 2,
  completedToday: 8,
  pendingToday: 5
}

// 预约中心卡片配置
export const appointmentsCard: CardModule = {
  key: 'appointments',
  name: '预约中心',
  route: '/appointments',
  icon: 'Calendar',
  color: '#007AFF',
  info: `今日有 ${appointmentsStats.todayAppointments} 个预约`,
  hasDetailStats: false,
  statsData: appointmentsStats,
  permission: 'appointment_manage'
}

// 预约挂号详细统计数据获取函数
export const getAppointmentsDetailStats = () => {
  return {
    today: appointmentsStats.todayAppointments,
    tomorrow: appointmentsStats.tomorrowAppointments,
    thisWeek: appointmentsStats.weekAppointments,
    cancelled: appointmentsStats.cancelledToday,
    completed: appointmentsStats.completedToday,
    pending: appointmentsStats.pendingToday
  }
}

// 预约挂号卡片信息更新函数
export const updateAppointmentsInfo = () => {
  return `今日有 ${appointmentsStats.todayAppointments} 个预约`
}
