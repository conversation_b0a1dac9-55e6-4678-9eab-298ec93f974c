// 用户相关类型
export interface User {
  id: string
  username: string
  name: string
  role: 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'receptionist'
  avatar?: string
  email?: string
  phone?: string
  department?: string
  permissions?: string[]
}

// 患者相关类型
export interface Patient {
  id: string
  name: string
  gender: '男' | '女'
  age: number
  phone: string
  address?: string
  status: 'active' | 'inactive'
  visitCount?: number
  nextAppointment?: string
  lastVisit?: string
  medicalHistory?: string[]
  allergies?: string[]
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }
}

// 模块配置类型
export interface ModuleConfig {
  key: string
  name: string
  route: string
  icon: string
  color: string
  info: string | (() => string)
  hasDetailStats?: boolean
  permission?: string
  statsData?: ModuleStats
}

// 模块统计数据类型
export interface ModuleStats {
  [key: string]: number | string | object
}

// 通知类型
export interface Notification {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message: string
  timestamp: Date
  read: boolean
}

// 权限类型
export interface Permission {
  [key: string]: boolean
}

// 角色权限映射
export interface RolePermissions {
  [role: string]: Permission
}

// 组件配置类型
export interface ComponentConfig {
  component: any
  props?: Record<string, any>
} 