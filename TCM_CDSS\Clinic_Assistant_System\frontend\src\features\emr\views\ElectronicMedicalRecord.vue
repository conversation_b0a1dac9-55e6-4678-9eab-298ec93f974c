<template>
  <div class="emr-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">电子病历</h1>
    </header>

    <div class="patient-banner">
      <div class="patient-info">
        <span class="patient-name">{{ currentPatient.name }}</span>
        <span class="patient-gender">{{ currentPatient.gender }}</span>
        <span class="patient-age">{{ currentPatient.age }}岁</span>
      </div>
    </div>

    <div class="content-area">
      <div class="medical-record">
        <div class="record-section">
          <h3 class="section-title">主诉</h3>
          <el-input
            v-model="medicalRecord.chiefComplaint"
            type="textarea"
            :rows="3"
            placeholder="请描述患者的主要症状和不适..."
            class="section-input"
          />
        </div>

        <div class="record-section">
          <h3 class="section-title">病史</h3>
          <el-input
            v-model="medicalRecord.history"
            type="textarea"
            :rows="3"
            placeholder="请描述患者的病史信息..."
            class="section-input"
          />
        </div>

        <div class="record-section">
          <h3 class="section-title">四诊信息</h3>
          
          <div class="diagnosis-item">
            <h4 class="diagnosis-subtitle">望诊</h4>
            <div class="tongue-section">
              <div class="tongue-color">
                <span class="label">舌色：</span>
                <div class="color-options">
                  <div
                    v-for="color in tongueColors"
                    :key="color.value"
                    :class="['color-option', { active: medicalRecord.tongueColor === color.value }]"
                    @click="medicalRecord.tongueColor = color.value"
                  >
                    {{ color.label }}
                  </div>
                </div>
              </div>
              <div class="tongue-coating">
                <span class="label">舌苔厚薄：</span>
                <el-slider
                  v-model="medicalRecord.tongueCoating"
                  :min="1"
                  :max="5"
                  :marks="{ 1: '薄', 3: '中等', 5: '厚' }"
                  class="coating-slider"
                />
              </div>
            </div>
          </div>

          <div class="diagnosis-item">
            <h4 class="diagnosis-subtitle">闻诊</h4>
            <el-input
              v-model="medicalRecord.auscultation"
              placeholder="请描述闻诊情况..."
              class="section-input"
            />
          </div>

          <div class="diagnosis-item">
            <h4 class="diagnosis-subtitle">问诊</h4>
            <div class="symptom-tags">
              <div
                v-for="symptom in commonSymptoms"
                :key="symptom.value"
                :class="['symptom-tag', { active: medicalRecord.symptoms.includes(symptom.value) }]"
                @click="toggleSymptom(symptom.value)"
              >
                {{ symptom.label }}
              </div>
            </div>
          </div>

          <div class="diagnosis-item">
            <h4 class="diagnosis-subtitle">切诊</h4>
            <div class="pulse-section">
              <div
                v-for="pulse in pulseTypes"
                :key="pulse.value"
                :class="['pulse-tag', { active: medicalRecord.pulses.includes(pulse.value) }]"
                @click="togglePulse(pulse.value)"
              >
                {{ pulse.label }}
              </div>
            </div>
          </div>
        </div>

        <div class="record-section">
          <h3 class="section-title">诊断与治法</h3>
          <el-input
            v-model="medicalRecord.diagnosis"
            type="textarea"
            :rows="3"
            placeholder="请描述诊断结果和治疗原则..."
            class="section-input"
          />
        </div>

        <div class="record-section">
          <div class="prescription-header">
            <h3 class="section-title">中药处方</h3>
            <el-button type="primary" size="small" @click="addMedicine">
              <el-icon><Plus /></el-icon>
              添加药品
            </el-button>
          </div>
          
          <div class="prescription-list">
            <div
              v-for="(medicine, index) in medicalRecord.prescription"
              :key="index"
              class="prescription-item"
            >
              <div class="medicine-info">
                <el-input
                  v-model="medicine.name"
                  placeholder="药品名称"
                  class="medicine-name"
                />
                <el-input-number
                  v-model="medicine.amount"
                  :min="1"
                  :max="100"
                  placeholder="克数"
                  class="medicine-amount"
                />
                <el-input
                  v-model="medicine.note"
                  placeholder="备注"
                  class="medicine-note"
                />
                <el-button
                  type="danger"
                  size="small"
                  @click="removeMedicine(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <div class="prescription-summary">
            共 {{ medicalRecord.prescription.length }} 味药，合计金额：¥{{ totalAmount }}
          </div>
        </div>
      </div>
    </div>

    <div class="bottom-bar">
      <el-button type="primary" size="large" @click="saveRecord">
        保存病历
      </el-button>
    </div>

    <div class="ai-assistant" @click="toggleAI">
      <el-icon size="24"><ChatDotRound /></el-icon>
    </div>

    <div v-if="showAI" class="ai-window">
      <div class="ai-header">
        <h3>智能助手</h3>
        <el-button type="text" @click="toggleAI">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="ai-content">
        <div class="quick-actions">
          <el-button size="small" @click="analyzeRecord">分析当前病历</el-button>
          <el-button size="small" @click="recommendPrescription">推荐方剂</el-button>
          <el-button size="small" @click="checkContraindications">检查配伍禁忌</el-button>
        </div>
        
        <div class="chat-history">
          <div
            v-for="(message, index) in aiMessages"
            :key="index"
            :class="['chat-message', message.type]"
          >
            {{ message.content }}
          </div>
        </div>
        
        <div class="chat-input">
          <el-input
            v-model="aiInput"
            placeholder="请输入您的问题或指令..."
            @keyup.enter="sendMessage"
          />
          <el-button type="primary" @click="sendMessage">发送</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Plus,
  Delete,
  ChatDotRound,
  Close
} from '@element-plus/icons-vue'

const router = useRouter()

const currentPatient = ref({
  name: '李小明',
  gender: '男',
  age: 34
})

const medicalRecord = ref({
  chiefComplaint: '',
  history: '',
  tongueColor: '',
  tongueCoating: 3,
  auscultation: '',
  symptoms: [] as string[],
  pulses: [] as string[],
  diagnosis: '',
  prescription: [] as any[]
})

const tongueColors = [
  { label: '淡红', value: '淡红' },
  { label: '红', value: '红' },
  { label: '绛', value: '绛' },
  { label: '紫', value: '紫' }
]

const commonSymptoms = [
  { label: '怕冷', value: '怕冷' },
  { label: '口干', value: '口干' },
  { label: '多汗', value: '多汗' },
  { label: '失眠', value: '失眠' },
  { label: '头痛', value: '头痛' },
  { label: '腹痛', value: '腹痛' },
  { label: '恶心', value: '恶心' },
  { label: '呕吐', value: '呕吐' }
]

const pulseTypes = [
  { label: '浮脉', value: '浮脉' },
  { label: '沉脉', value: '沉脉' },
  { label: '弦脉', value: '弦脉' },
  { label: '滑脉', value: '滑脉' },
  { label: '涩脉', value: '涩脉' },
  { label: '数脉', value: '数脉' }
]

const showAI = ref(false)
const aiInput = ref('')
const aiMessages = ref([
  { type: 'ai', content: '您好！我是您的AI助手，可以帮助您分析病历、推荐方剂等。' }
])

const totalAmount = computed(() => {
  return medicalRecord.value.prescription.reduce((sum, medicine) => {
    return sum + (medicine.amount || 0) * 0.5
  }, 0).toFixed(2)
})

const goBack = () => router.push('/dashboard')

const toggleSymptom = (symptom: string) => {
  const index = medicalRecord.value.symptoms.indexOf(symptom)
  if (index > -1) {
    medicalRecord.value.symptoms.splice(index, 1)
  } else {
    medicalRecord.value.symptoms.push(symptom)
  }
}

const togglePulse = (pulse: string) => {
  const index = medicalRecord.value.pulses.indexOf(pulse)
  if (index > -1) {
    medicalRecord.value.pulses.splice(index, 1)
  } else {
    medicalRecord.value.pulses.push(pulse)
  }
}

const addMedicine = () => {
  medicalRecord.value.prescription.push({
    name: '',
    amount: 10,
    note: ''
  })
}

const removeMedicine = (index: number) => {
  medicalRecord.value.prescription.splice(index, 1)
}

const saveRecord = () => {
  ElMessage.success('病历保存成功')
}

const toggleAI = () => {
  showAI.value = !showAI.value
}

const analyzeRecord = () => {
  const analysis = `根据当前病历分析：
主诉：${medicalRecord.value.chiefComplaint || '未填写'}
症状：${medicalRecord.value.symptoms.join('、') || '无'}
舌象：${medicalRecord.value.tongueColor || '未选择'}
脉象：${medicalRecord.value.pulses.join('、') || '未选择'}

可能的证型：肝郁脾虚证
治疗原则：疏肝健脾，理气和中`
  
  aiMessages.value.push({ type: 'ai', content: analysis })
}

const recommendPrescription = () => {
  const recommendation = `推荐方剂：逍遥散加减
柴胡 10g，当归 10g，白芍 10g，白术 10g
茯苓 10g，炙甘草 6g，薄荷 6g，生姜 3片

功效：疏肝解郁，健脾养血
适应症：肝郁脾虚证`
  
  aiMessages.value.push({ type: 'ai', content: recommendation })
}

const checkContraindications = () => {
  const check = `配伍禁忌检查：
当前处方中未发现明显的配伍禁忌。
建议注意：
1. 避免与寒凉药物同用
2. 孕妇慎用活血化瘀类药物
3. 注意药物剂量控制`
  
  aiMessages.value.push({ type: 'ai', content: check })
}

const sendMessage = () => {
  if (!aiInput.value.trim()) return
  
  aiMessages.value.push({ type: 'user', content: aiInput.value })
  
  setTimeout(() => {
    aiMessages.value.push({ 
      type: 'ai', 
      content: `收到您的问题："${aiInput.value}"。我正在分析中，请稍候...` 
    })
  }, 500)
  
  aiInput.value = ''
}
</script>

<style scoped>
.emr-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  /* 背景图片现在由全局样式提供 */
}

.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.back-button:hover {
  color: var(--primary-dark);
}

.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.patient-banner {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
}

.patient-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.patient-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.patient-gender,
.patient-age {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
}

.medical-record {
  max-width: 800px;
  margin: 0 auto;
}

.record-section {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.section-input {
  width: 100%;
}

.diagnosis-item {
  margin-bottom: var(--spacing-lg);
}

.diagnosis-subtitle {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.tongue-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.tongue-color,
.tongue-coating {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  min-width: 80px;
}

.color-options {
  display: flex;
  gap: var(--spacing-sm);
}

.color-option {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.color-option:hover {
  border-color: var(--primary-color);
}

.color-option.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.coating-slider {
  flex: 1;
  max-width: 300px;
}

.symptom-tags,
.pulse-section {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.symptom-tag,
.pulse-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.symptom-tag:hover,
.pulse-tag:hover {
  border-color: var(--primary-color);
}

.symptom-tag.active,
.pulse-tag.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.prescription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.prescription-list {
  margin-bottom: var(--spacing-md);
}

.prescription-item {
  margin-bottom: var(--spacing-sm);
}

.medicine-info {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.medicine-name {
  flex: 2;
}

.medicine-amount {
  flex: 1;
}

.medicine-note {
  flex: 2;
}

.prescription-summary {
  padding: var(--spacing-md);
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  text-align: center;
  color: var(--text-secondary);
}

.bottom-bar {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.ai-assistant {
  position: fixed;
  right: var(--spacing-xl);
  top: 50%;
  transform: translateY(-50%);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-medium);
  color: white;
  transition: all var(--transition-fast);
  z-index: 1000;
}

.ai-assistant:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: var(--shadow-heavy);
}

.ai-window {
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100vh;
  background: var(--surface-color);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  z-index: 999;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.ai-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.ai-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-lg);
}

.quick-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  margin-bottom: var(--spacing-lg);
}

.chat-message {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-medium);
  font-size: var(--font-size-sm);
}

.chat-message.user {
  background: var(--primary-color);
  color: white;
  margin-left: 20%;
}

.chat-message.ai {
  background: var(--surface-color);
  color: var(--text-primary);
  margin-right: 20%;
}

.chat-input {
  display: flex;
  gap: var(--spacing-sm);
}

.chat-input .el-input {
  flex: 1;
}
</style> 