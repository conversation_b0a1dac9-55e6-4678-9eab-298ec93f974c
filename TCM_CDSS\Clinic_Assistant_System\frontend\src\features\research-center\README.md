# 研学中心模块

## 功能描述
研学中心模块负责管理医学研究和学习活动，包括研究项目、学术交流、培训课程等功能。

## 目录结构
```
research-center/
├── views/                    # 页面视图
│   └── ResearchCenterList.vue # 研学中心页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 研究项目管理
- 学术交流活动
- 培训课程管理
- 成果展示

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 