<template>
  <div class="patient-detail-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        患者管理
      </div>
      <h1 class="module-title">患者详情</h1>
    </header>
    <div class="content">
      <p>患者详情功能开发中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()
const goBack = () => router.push('/patients')
</script>

<style scoped>
.patient-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}
.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}
.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}
.back-button:hover {
  color: var(--primary-dark);
}
.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}
.content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}
</style> 