<template>
  <div class="research-center-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">研学中心</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showComingSoon">
          <el-icon><Plus /></el-icon>
          新建研学项目
        </el-button>
      </div>
    </header>

    <div class="content-area">
      <div class="coming-soon-container">
        <div class="coming-soon-content">
          <div class="icon-container">
            <el-icon :size="80" color="#5856D6">
              <Document />
            </el-icon>
          </div>
          <h2 class="coming-soon-title">研学中心功能</h2>
          <p class="coming-soon-subtitle">中医研学与学术平台</p>
          
          <div class="feature-sections">
            <!-- 核心功能模块 -->
            <div class="feature-section">
              <h3 class="section-title">核心功能模块</h3>
              <div class="feature-grid">
                <div class="feature-item">
                  <el-icon color="#007AFF"><Share /></el-icon>
                  <span>知识图谱</span>
                </div>
                <div class="feature-item">
                  <el-icon color="#34C759"><Document /></el-icon>
                  <span>医案学习</span>
                </div>
                <div class="feature-item">
                  <el-icon color="#FF9500"><ChatDotRound /></el-icon>
                  <span>AI辅助科研</span>
                </div>
                <div class="feature-item">
                  <el-icon color="#FF3B30"><Collection /></el-icon>
                  <span>古典书籍研究</span>
                </div>
                <div class="feature-item">
                  <el-icon color="#30B0C7"><DataAnalysis /></el-icon>
                  <span>学术数据分析</span>
                </div>
                <div class="feature-item">
                  <el-icon color="#AF52DE"><Star /></el-icon>
                  <span>名医经验传承</span>
                </div>
              </div>
            </div>

            <!-- 学习资源 -->
            <div class="feature-section">
              <h3 class="section-title">学习资源</h3>
              <div class="resource-tags">
                <el-tag type="info" size="large">《黄帝内经》</el-tag>
                <el-tag type="info" size="large">《伤寒论》</el-tag>
                <el-tag type="info" size="large">《金匮要略》</el-tag>
                <el-tag type="info" size="large">《温病条辨》</el-tag>
                <el-tag type="info" size="large">《本草纲目》</el-tag>
                <el-tag type="info" size="large">《针灸大成》</el-tag>
                <el-tag type="info" size="large">现代中医研究</el-tag>
                <el-tag type="info" size="large">临床医案集</el-tag>
              </div>
            </div>

            <!-- 研学工具 -->
            <div class="feature-section">
              <h3 class="section-title">研学工具</h3>
              <div class="tool-grid">
                <div class="tool-item">
                  <el-icon color="#5856D6"><Search /></el-icon>
                  <div class="tool-info">
                    <div class="tool-name">智能检索</div>
                    <div class="tool-desc">多维度文献检索</div>
                  </div>
                </div>
                <div class="tool-item">
                  <el-icon color="#FF6B6B"><EditPen /></el-icon>
                  <div class="tool-info">
                    <div class="tool-name">学习笔记</div>
                    <div class="tool-desc">个人研学记录</div>
                  </div>
                </div>
                <div class="tool-item">
                  <el-icon color="#32D74B"><Connection /></el-icon>
                  <div class="tool-info">
                    <div class="tool-name">知识关联</div>
                    <div class="tool-desc">概念关系图谱</div>
                  </div>
                </div>
                <div class="tool-item">
                  <el-icon color="#FF9500"><TrendCharts /></el-icon>
                  <div class="tool-info">
                    <div class="tool-name">学习进度</div>
                    <div class="tool-desc">研学轨迹追踪</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="coming-soon-badge">
            <el-tag type="warning" size="large">敬请期待</el-tag>
          </div>
          <p class="coming-soon-description">
            研学中心功能正在开发中，将为中医医生提供全面的学术研究和学习平台，
            包括知识图谱构建、医案智能分析、AI辅助科研、古典文献研究等功能，
            结合现代信息技术与传统中医学术，助力中医传承与创新发展，
            打造中医医生的专业成长和学术研究的数字化平台。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  Plus, 
  Document, 
  Book, 
  Star, 
  DataAnalysis,
  ChatDotRound, 
  Notebook,
  Collection,
  Histogram,
  User,
  Trophy,
  Search,
  EditPen,
  Connection,
  TrendCharts
} from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/dashboard')
}

const showComingSoon = () => {
  ElMessage.info('该功能正在开发中，敬请期待！')
}
</script>

<style scoped>
.research-center-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.content-area {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--spacing-xl);
  overflow-y: auto;
  min-height: 0;
}

.coming-soon-container {
  max-width: 900px;
  width: 100%;
  text-align: center;
  margin-top: var(--spacing-lg);
}

.coming-soon-content {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xxl);
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.icon-container {
  margin-bottom: var(--spacing-lg);
}

.coming-soon-title {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.coming-soon-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.feature-sections {
  margin-bottom: var(--spacing-xl);
}

.feature-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
}

.section-title {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  text-align: left;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

.tool-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.tool-info {
  text-align: left;
}

.tool-name {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.tool-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.coming-soon-badge {
  margin-bottom: var(--spacing-lg);
}

.coming-soon-description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 780px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .tool-grid {
    grid-template-columns: 1fr;
  }
  
  .coming-soon-content {
    padding: var(--spacing-lg);
  }
  
  .coming-soon-title {
    font-size: var(--font-size-xl);
  }
  
  .resource-tags {
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }
}
</style>
