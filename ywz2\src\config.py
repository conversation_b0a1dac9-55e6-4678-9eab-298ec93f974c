"""
配置管理模块

负责管理应用程序的所有配置项，包括环境变量、数据库连接、API密钥等。
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 数据库配置
    database_url: str = "postgresql://user:pass@localhost/tcm_db"
    redis_url: str = "redis://localhost:6379"
    
    # OpenRouter配置
    openrouter_api_key: str
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    default_model: str = "anthropic/claude-3.5-sonnet"
    
    # LLM配置
    llm_backend: str = Field(default="openrouter", description="LLM后端类型")
    llm_model: str = Field(default="anthropic/claude-3.5-sonnet", description="LLM模型名称")
    
    # Ollama配置
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama服务地址")
    
    # 应用配置
    app_name: str = "TCM Consultation System"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "json"
    
    # 安全配置
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 应用基础配置
    app_name: str = Field(default="TCM Consultation System", description="应用名称")
    debug: bool = Field(default=False, description="调试模式")
    host: str = Field(default="0.0.0.0", description="服务监听地址")
    port: int = Field(default=8000, description="服务监听端口")
    
    # 缓存配置
    cache_ttl: int = Field(default=3600, description="缓存TTL（秒）")
    
    # 限流配置
    rate_limit_per_minute: int = Field(default=60, description="每分钟请求限制")
    
    # 文件上传配置
    max_file_size: int = Field(default=10485760, description="最大文件大小（字节）")
    upload_dir: str = Field(default="./uploads", description="上传目录")
    
    # 邮件配置（可选）
    smtp_host: Optional[str] = Field(default=None, description="SMTP服务器地址")
    smtp_port: Optional[int] = Field(default=None, description="SMTP服务器端口")
    smtp_user: Optional[str] = Field(default=None, description="SMTP用户名")
    smtp_password: Optional[str] = Field(default=None, description="SMTP密码")
    
    # 第三方服务配置（可选）
    speech_to_text_api_key: Optional[str] = Field(default=None, description="语音识别API密钥")
    image_recognition_api_key: Optional[str] = Field(default=None, description="图像识别API密钥")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings


def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.database_url


def get_redis_url() -> str:
    """获取Redis连接URL"""
    return settings.redis_url


def get_openai_config() -> dict:
    """获取LLM配置"""
    return {
        "api_key": settings.openrouter_api_key,
        "model": settings.llm_model,
        "temperature": 0.7,
        "max_tokens": 2000,
    }


def is_debug_mode() -> bool:
    """检查是否为调试模式"""
    return settings.debug


def get_log_config() -> dict:
    """获取日志配置"""
    return {
        "level": settings.log_level,
        "format": settings.log_format,
    } 