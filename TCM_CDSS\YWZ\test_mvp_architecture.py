#!/usr/bin/env python3
"""
测试MVP架构 - 验证前后端分离功能
"""

import requests
import webbrowser
import time
import json
from pathlib import Path

def test_server_health():
    """测试服务器健康状态"""
    print("🔍 测试服务器健康状态")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8002/api/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务器健康检查通过")
            print(f"   状态: {data.get('status')}")
            print(f"   版本: {data.get('version')}")
            print(f"   LLM服务: {data.get('services', {}).get('llm_service')}")
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

def test_frontend_routes():
    """测试前端路由"""
    print("\n🌐 测试前端路由")
    print("=" * 50)
    
    routes = [
        ("/", "主页导航"),
        ("/pages/ios", "iOS演示页面"),
        ("/pages/simple", "简化演示页面"),
        ("/pages/agent", "智能体演示页面"),
        ("/pages/full", "完整演示页面")
    ]
    
    success_count = 0
    for route, description in routes:
        try:
            response = requests.get(f"http://localhost:8002{route}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {description}: {route}")
                success_count += 1
            else:
                print(f"❌ {description}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {description}: 连接失败")
    
    print(f"\n📊 前端路由测试结果: {success_count}/{len(routes)} 通过")
    return success_count == len(routes)

def test_shortcut_routes():
    """测试快捷路由"""
    print("\n🔗 测试快捷路由")
    print("=" * 50)
    
    shortcuts = [
        ("/ios", "iOS快捷路由"),
        ("/simple", "简化快捷路由"),
        ("/agent", "智能体快捷路由"),
        ("/demo", "演示快捷路由")
    ]
    
    success_count = 0
    for route, description in shortcuts:
        try:
            response = requests.get(f"http://localhost:8002{route}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {description}: {route}")
                success_count += 1
            else:
                print(f"❌ {description}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {description}: 连接失败")
    
    print(f"\n📊 快捷路由测试结果: {success_count}/{len(shortcuts)} 通过")
    return success_count == len(shortcuts)

def test_api_routes():
    """测试API路由"""
    print("\n🔌 测试API路由")
    print("=" * 50)
    
    api_routes = [
        ("/api/", "API根路径"),
        ("/api/info", "API信息"),
        ("/api/health", "健康检查"),
        ("/api/docs", "API文档")
    ]
    
    success_count = 0
    for route, description in api_routes:
        try:
            response = requests.get(f"http://localhost:8002{route}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {description}: {route}")
                success_count += 1
            else:
                print(f"❌ {description}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {description}: 连接失败")
    
    print(f"\n📊 API路由测试结果: {success_count}/{len(api_routes)} 通过")
    return success_count == len(api_routes)

def test_chat_api():
    """测试聊天API"""
    print("\n💬 测试聊天API")
    print("=" * 50)
    
    try:
        # 测试聊天接口
        chat_data = {
            "message": "我头痛"
        }
        
        response = requests.post(
            "http://localhost:8002/api/chat",
            json=chat_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 聊天API测试通过")
            print(f"   用户消息: {chat_data['message']}")
            print(f"   AI回复: {data.get('response', '无回复')}")
            return True
        else:
            print(f"❌ 聊天API失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天API测试失败: {e}")
        return False

def test_inquiry_api():
    """测试问诊API"""
    print("\n🏥 测试问诊API")
    print("=" * 50)
    
    try:
        # 测试开始问诊接口
        inquiry_data = {
            "patient_name": "张三",
            "patient_age": 30,
            "patient_gender": "男"
        }
        
        response = requests.post(
            "http://localhost:8002/api/inquiry/start",
            json=inquiry_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 问诊API测试通过")
            print(f"   患者: {inquiry_data['patient_name']}")
            print(f"   会话ID: {data.get('session_id', '无')}")
            print(f"   欢迎消息: {data.get('message', '无')}")
            return True
        else:
            print(f"❌ 问诊API失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 问诊API测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试MVP架构文件结构")
    print("=" * 50)
    
    required_files = [
        "backend/app.py",
        "backend/config/settings.py",
        "backend/api/routes.py",
        "backend/services/llm_service.py",
        "frontend/index.html",
        "frontend/pages/ios.html",
        "frontend/pages/simple.html",
        "frontend/pages/agent.html",
        "frontend/pages/full.html"
    ]
    
    success_count = 0
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
            success_count += 1
        else:
            print(f"❌ {file_path} (缺失)")
    
    print(f"\n📊 文件结构测试结果: {success_count}/{len(required_files)} 通过")
    return success_count == len(required_files)

def demo_mvp_navigation():
    """演示MVP架构导航"""
    print("\n🚀 演示MVP架构导航功能")
    print("=" * 50)
    
    pages = [
        ("http://localhost:8002", "主页导航"),
        ("http://localhost:8002/pages/ios", "iOS演示页面"),
        ("http://localhost:8002/pages/simple", "简化演示页面"),
        ("http://localhost:8002/pages/agent", "智能体演示页面"),
        ("http://localhost:8002/api/docs", "API文档")
    ]
    
    for url, description in pages:
        print(f"🌐 打开: {description}")
        webbrowser.open(url)
        time.sleep(1)
    
    print("\n💡 现在您可以:")
    print("1. 在主页选择不同的演示版本")
    print("2. 在子页面点击返回按钮回到主页")
    print("3. 使用快捷URL直接访问页面")
    print("4. 查看API文档了解接口详情")

def generate_test_report():
    """生成测试报告"""
    print("\n📋 MVP架构测试报告")
    print("=" * 60)
    
    tests = [
        ("服务器健康", test_server_health),
        ("前端路由", test_frontend_routes),
        ("快捷路由", test_shortcut_routes),
        ("API路由", test_api_routes),
        ("聊天API", test_chat_api),
        ("问诊API", test_inquiry_api),
        ("文件结构", test_file_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 MVP架构测试全部通过！")
        print("\n🏗️ MVP架构特色:")
        print("✅ 前后端分离设计")
        print("✅ 统一服务器部署")
        print("✅ RESTful API接口")
        print("✅ 现代化前端界面")
        print("✅ 完整的路由系统")
        print("✅ 响应式设计")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")
    
    return passed == total

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            demo_mvp_navigation()
        elif sys.argv[1] == "--report":
            generate_test_report()
        else:
            print("用法:")
            print("  python test_mvp_architecture.py          # 运行所有测试")
            print("  python test_mvp_architecture.py --demo   # 演示导航功能")
            print("  python test_mvp_architecture.py --report # 生成详细报告")
    else:
        # 运行所有测试
        success = generate_test_report()
        
        if success:
            print("\n💡 提示:")
            print("运行 'python test_mvp_architecture.py --demo' 可以在浏览器中演示MVP架构")

if __name__ == "__main__":
    main()
