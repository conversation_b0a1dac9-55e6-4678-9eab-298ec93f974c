/**
 * 应用入口文件
 * 
 * 主要功能：
 * 1. 创建Vue应用实例
 * 2. 配置全局插件（Pinia状态管理、Vue Router路由、Element Plus UI库）
 * 3. 注册Element Plus图标组件
 * 4. 导入全局样式文件
 * 5. 挂载应用到DOM
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './core/router'
import './shared/styles/variables.css'
import './shared/styles/global.css'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app') 