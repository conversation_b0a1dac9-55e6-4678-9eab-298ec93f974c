# 🏥 YUWENZHEN - 中医预问诊智能体系统

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![LangChain](https://img.shields.io/badge/LangChain-0.2+-orange.svg)](https://langchain.com)
[![LangGraph](https://img.shields.io/badge/LangGraph-0.2+-purple.svg)](https://langgraph.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于最新LangChain和LangGraph技术栈开发的现代化中医预问诊智能体系统，整合四个项目的优点，采用前后端分离架构，支持OpenRouter和Ollama双模式LLM调用。

## ✨ 项目特色

### 🏗️ 现代化架构
- **前后端分离**: 清晰的职责划分，易于维护和扩展
- **微服务化**: 模块化设计，支持独立部署和扩展
- **异步架构**: 全异步设计，高性能并发处理
- **容器化**: 支持Docker部署，环境一致性保障

### 🤖 智能体系统
- **多智能体协同**: 10个专业智能体分工协作
- **统一工作流程**: "先问有无，若有则追问，无则结束"的标准化流程
- **LCEL管道**: 使用LangChain Expression Language构建数据处理管道
- **实体提取**: 自动提取医疗实体，生成结构化病历

### 🔧 技术优势
- **双模式LLM**: 支持OpenRouter云端和Ollama本地部署
- **多数据库**: PostgreSQL生产环境 + SQLite开发环境
- **智能缓存**: Redis分布式缓存 + 本地缓存降级
- **安全认证**: JWT认证 + 权限控制 + 密码安全

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
│  React/Vue.js + TypeScript + Vite                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (API Gateway)                  │
│  FastAPI + CORS + 认证中间件                                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   业务服务层 (Services)                     │
│  问诊服务 | 智能体管理 | 会话管理 | 病历服务                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 智能体协同层 (Agent Layer)                   │
│  LangGraph工作流引擎 + 多智能体协同                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   LLM调用层 (LLM Layer)                     │
│  OpenRouter客户端 (主要) + Ollama客户端 (备选)              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   数据存储层 (Data Layer)                   │
│  PostgreSQL + Redis + SQLite + 文件存储                     │
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
YUWENZHEN/
├── 📁 frontend/                     # 前端应用
│   ├── 📁 src/
│   │   ├── 📁 components/           # 可复用组件
│   │   ├── 📁 pages/                # 页面组件
│   │   ├── 📁 services/             # API服务
│   │   └── 📁 stores/               # 状态管理
│   └── package.json
├── 📁 backend/                      # 后端应用
│   ├── 📁 app/
│   │   ├── 📁 core/                 # 核心模块
│   │   ├── 📁 agents/               # 智能体模块
│   │   ├── 📁 api/                  # API路由
│   │   ├── 📁 services/             # 业务服务
│   │   ├── 📁 models/               # 数据模型
│   │   ├── 📁 workflows/            # 工作流
│   │   └── 📁 utils/                # 工具模块
│   └── requirements.txt
├── 📁 tests/                        # 测试代码
├── 📁 docs/                         # 项目文档
├── 📁 scripts/                      # 脚本文件
├── 📁 config/                       # 配置文件
├── 📁 docker/                       # Docker配置
└── README.md
```

## 🚀 快速开始

### 环境要求

- Python 3.9+
- Node.js 16+
- PostgreSQL 13+ (可选，支持SQLite)
- Redis 6+ (可选，支持本地缓存)

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd YUWENZHEN

# 后端依赖
cd backend
pip install -r requirements.txt

# 前端依赖
cd ../frontend
npm install
```

### 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 启动服务

```bash
# 启动后端服务
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端服务
cd frontend
npm run dev
```

### 访问应用

- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 🔧 配置说明

### LLM配置

支持两种LLM服务模式：

#### OpenRouter模式 (推荐)
```env
LLM_SERVICE=openrouter
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=anthropic/claude-3.5-sonnet
```

#### Ollama模式 (本地部署)
```env
LLM_SERVICE=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:32b
```

### 数据库配置

#### PostgreSQL (生产环境)
```env
DATABASE_URL=postgresql://user:password@localhost:5432/yuwenzhen
```

#### SQLite (开发环境)
```env
DATABASE_URL=sqlite:///./data/yuwenzhen.db
```

## 🤖 智能体系统

### 核心智能体

| 智能体 | 职责 | 主要功能 |
|--------|------|----------|
| 主诉采集智能体 | 采集主要症状 | 询问症状性质、部位、持续时间等 |
| 现病史采集智能体 | 详细追问当前疾病 | 按症状类别系统询问（寒热、汗症、疼痛等） |
| 既往史采集智能体 | 收集既往疾病史 | 询问既往疾病、手术史、过敏史等 |
| 家族史采集智能体 | 了解家族疾病史 | 询问家族遗传疾病情况 |
| 过敏史采集智能体 | 收集过敏信息 | 询问药物、食物、环境过敏等 |

### 工作流程

1. **询问有无**: 智能体首先询问患者是否有相关症状或情况
2. **解析回答**: 使用LLM解析患者的回答，判断是否有相关情况
3. **追问详情**: 如果有，则生成详细的追问问题
4. **提取信息**: 从患者回答中提取结构化信息和医疗实体
5. **完成判断**: 判断当前智能体是否完成，决定下一步行动

## 📚 API文档

### 主要接口

#### 健康检查
```http
GET /api/health
```

#### 开始问诊
```http
POST /api/consultation/start
Content-Type: application/json

{
  "patient_name": "张三",
  "patient_age": 35,
  "patient_gender": "男"
}
```

#### 问诊对话
```http
POST /api/consultation/chat
Content-Type: application/json

{
  "session_id": "session-uuid",
  "message": "我最近总是头痛"
}
```

#### WebSocket实时对话
```javascript
const ws = new WebSocket('ws://localhost:8000/api/consultation/ws');
ws.send(JSON.stringify({
  "session_id": "session-uuid",
  "message": "患者消息"
}));
```

## 🧪 测试

### 运行测试

```bash
# 单元测试
pytest tests/unit/

# 集成测试
pytest tests/integration/

# 端到端测试
pytest tests/e2e/

# 测试覆盖率
pytest --cov=app tests/
```

### 测试配置

```bash
# 测试环境配置
export ENVIRONMENT=testing
export DATABASE_URL=sqlite:///./test.db
```

## 🐳 Docker部署

### 构建镜像

```bash
# 构建后端镜像
docker build -f docker/Dockerfile.backend -t yuwenzhen-backend .

# 构建前端镜像
docker build -f docker/Dockerfile.frontend -t yuwenzhen-frontend .
```

### 使用Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📊 监控和日志

### 健康检查端点

- 应用健康: `/api/health`
- 数据库健康: `/api/health/database`
- 缓存健康: `/api/health/cache`
- LLM健康: `/api/health/llm`

### 日志配置

```env
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/app.log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: <EMAIL>

## 🙏 致谢

感谢以下项目的贡献：
- ywz2 - 提供了完整的智能体架构
- YUWENZHEN-AUGMENT - 提供了优秀的前端界面
- YUWENZHEN-CURSOR - 提供了技术参考
- YWZ - 提供了MVP架构思路

---

**版本**: 2.0.0  
**最后更新**: 2025-08-15
