# 🏥 YUWENZHEN - 中医预问诊智能体系统

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Vue](https://img.shields.io/badge/Vue-3.4+-green.svg)](https://vuejs.org)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3+-blue.svg)](https://typescriptlang.org)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.4+-blue.svg)](https://element-plus.org)
[![LangChain](https://img.shields.io/badge/LangChain-0.2+-orange.svg)](https://langchain.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

简洁易用的中医预问诊智能体系统，基于LangChain和LangGraph技术栈，采用前后端分离架构，专注于开发测试环境，干净简洁，清晰易懂。

## ✨ 项目特色

### 🎯 简洁设计
- **干净简洁**: 专注核心功能，避免过度设计
- **清晰易懂**: 代码结构清晰，易于理解和维护
- **开发友好**: 专为开发测试环境优化，快速上手

### 🏗️ 技术架构
- **前后端分离**: Vue 3 + TypeScript + Element Plus 前端，FastAPI 后端
- **轻量级数据库**: 统一使用SQLite，无需复杂数据库配置
- **本地缓存**: 内存缓存，无需Redis等外部依赖
- **智能体系统**: 基于LangChain的多智能体协同工作流

### 🤖 AI能力
- **本地优先**: 优先使用Ollama本地LLM，保护数据隐私
- **云端备选**: 支持OpenRouter云端LLM作为备选方案
- **智能问诊**: 多轮对话式中医问诊，自动提取结构化信息
- **实体识别**: 自动识别医疗实体，生成结构化病历

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
│  Vue 3 + TypeScript + Element Plus + Vite                  │
│  ├── 问诊界面 (Consultation)                                │
│  ├── 记录查看 (Records)                                     │
│  └── 系统管理 (Admin)                                       │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/WebSocket
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    API服务层 (Backend)                      │
│  FastAPI + CORS + 简化认证                                  │
│  ├── 问诊API (/consultation)                                │
│  ├── 记录API (/records)                                     │
│  └── 健康检查 (/health)                                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 智能体协同层 (Agent Layer)                   │
│  LangGraph工作流 + 多智能体协同                              │
│  ├── 主诉采集智能体                                         │
│  ├── 现病史采集智能体                                       │
│  ├── 既往史采集智能体                                       │
│  └── 家族史采集智能体                                       │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   LLM调用层 (LLM Layer)                     │
│  Ollama客户端 (主要) + OpenRouter客户端 (备选)              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   数据存储层 (Data Layer)                   │
│  SQLite数据库 + 本地缓存 + 文件存储                          │
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
YUWENZHEN/
├── 📁 frontend/                     # 前端应用
│   ├── 📁 src/
│   │   ├── 📁 components/           # 可复用组件
│   │   ├── 📁 pages/                # 页面组件
│   │   ├── 📁 services/             # API服务
│   │   └── 📁 stores/               # 状态管理
│   └── package.json
├── 📁 backend/                      # 后端应用
│   ├── 📁 app/
│   │   ├── 📁 core/                 # 核心模块
│   │   ├── 📁 agents/               # 智能体模块
│   │   ├── 📁 api/                  # API路由
│   │   ├── 📁 services/             # 业务服务
│   │   ├── 📁 models/               # 数据模型
│   │   ├── 📁 workflows/            # 工作流
│   │   └── 📁 utils/                # 工具模块
│   └── requirements.txt
├── 📁 tests/                        # 测试代码
├── 📁 docs/                         # 项目文档
├── 📁 scripts/                      # 脚本文件
├── 📁 config/                       # 配置文件
├── 📁 docker/                       # Docker配置
└── README.md
```

## 🚀 快速开始

### 环境要求

- **Python 3.9+** (后端开发)
- **Node.js 16+** (前端开发)
- **Ollama** (推荐，本地LLM服务) 或 **OpenRouter API密钥** (云端LLM)

### 一键启动 (推荐)

```bash
# 1. 克隆项目
git clone <repository-url>
cd YUWENZHEN

# 2. 安装依赖
pip install -r backend/requirements.txt
cd frontend && npm install && cd ..

# 3. 配置环境
cp .env.example .env
# 编辑 .env 文件，配置LLM服务

# 4. 启动项目
python scripts/start.py dev
```

### 手动启动

```bash
# 后端服务
cd backend
python -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000

# 前端服务 (新终端)
cd frontend
npm run dev
```

### 访问应用

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 🔧 配置说明

### LLM配置

#### Ollama模式 (推荐，本地部署)
```env
LLM_SERVICE=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:7b
```

**安装Ollama:**
```bash
# 下载并安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull qwen2.5:7b
```

#### OpenRouter模式 (云端服务)
```env
LLM_SERVICE=openrouter
OPENROUTER_API_KEY=your-openrouter-api-key
DEFAULT_MODEL=anthropic/claude-3.5-sonnet
```

### 数据库配置

项目统一使用SQLite数据库，无需额外配置：
```env
DATABASE_URL=sqlite:///./data/yuwenzhen.db
```

### 缓存配置

使用本地内存缓存，无需Redis：
```env
ENABLE_REDIS=false
CACHE_TTL=1800
```

## 🤖 智能体系统

### 核心智能体

| 智能体 | 职责 | 主要功能 |
|--------|------|----------|
| 主诉采集智能体 | 采集主要症状 | 询问症状性质、部位、持续时间等 |
| 现病史采集智能体 | 详细追问当前疾病 | 按症状类别系统询问（寒热、汗症、疼痛等） |
| 既往史采集智能体 | 收集既往疾病史 | 询问既往疾病、手术史、过敏史等 |
| 家族史采集智能体 | 了解家族疾病史 | 询问家族遗传疾病情况 |
| 过敏史采集智能体 | 收集过敏信息 | 询问药物、食物、环境过敏等 |

### 工作流程

1. **询问有无**: 智能体首先询问患者是否有相关症状或情况
2. **解析回答**: 使用LLM解析患者的回答，判断是否有相关情况
3. **追问详情**: 如果有，则生成详细的追问问题
4. **提取信息**: 从患者回答中提取结构化信息和医疗实体
5. **完成判断**: 判断当前智能体是否完成，决定下一步行动

## 📚 API文档

### 主要接口

#### 健康检查
```http
GET /api/health
```

#### 开始问诊
```http
POST /api/consultation/start
Content-Type: application/json

{
  "patient_name": "张三",
  "patient_age": 35,
  "patient_gender": "男"
}
```

#### 问诊对话
```http
POST /api/consultation/chat
Content-Type: application/json

{
  "session_id": "session-uuid",
  "message": "我最近总是头痛"
}
```

#### WebSocket实时对话
```javascript
const ws = new WebSocket('ws://localhost:8000/api/consultation/ws');
ws.send(JSON.stringify({
  "session_id": "session-uuid",
  "message": "患者消息"
}));
```

## 🧪 测试

### 运行测试

```bash
# 单元测试
pytest tests/unit/

# 集成测试
pytest tests/integration/

# 端到端测试
pytest tests/e2e/

# 测试覆盖率
pytest --cov=app tests/
```

### 测试配置

```bash
# 测试环境配置
export ENVIRONMENT=testing
export DATABASE_URL=sqlite:///./test.db
```

## 🐳 Docker部署

### 构建镜像

```bash
# 构建后端镜像
docker build -f docker/Dockerfile.backend -t yuwenzhen-backend .

# 构建前端镜像
docker build -f docker/Dockerfile.frontend -t yuwenzhen-frontend .
```

### 使用Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📊 监控和日志

### 健康检查端点

- 应用健康: `/api/health`
- 数据库健康: `/api/health/database`
- 缓存健康: `/api/health/cache`
- LLM健康: `/api/health/llm`

### 日志配置

```env
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/app.log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: <EMAIL>

## 🙏 致谢

感谢以下项目的贡献：
- ywz2 - 提供了完整的智能体架构
- YUWENZHEN-AUGMENT - 提供了优秀的前端界面
- YUWENZHEN-CURSOR - 提供了技术参考
- YWZ - 提供了MVP架构思路

---

**版本**: 2.0.0  
**最后更新**: 2025-08-15
