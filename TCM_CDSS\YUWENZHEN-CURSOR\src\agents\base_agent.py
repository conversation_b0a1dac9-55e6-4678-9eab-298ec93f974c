"""
智能体基类

定义所有智能体的通用接口和基础功能，包括LCEL管道创建、实体提取、追问生成等。
"""

import logging
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import BaseOutputParser
from langchain_openai import ChatOpenAI
from langchain.schema.runnable import Runnable
from pydantic import BaseModel, Field

from src.config import get_openai_config
from src.models.medical_record import MedicalEntity
from src.utils.openrouter_client import OpenRouterChatModel, ModelSelector


logger = logging.getLogger(__name__)


class AgentResponse(BaseModel):
    """智能体响应模型"""
    
    response: str = Field(description="智能体的回复内容")
    extracted_data: Dict[str, Any] = Field(description="提取的结构化数据")
    entities: List[Dict[str, Any]] = Field(description="提取的医疗实体")
    confidence: float = Field(description="置信度", ge=0.0, le=1.0)
    next_question: Optional[str] = Field(default=None, description="下一个问题")
    is_complete: bool = Field(description="当前阶段是否完成")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    summary: Optional[str] = Field(default=None, description="结构化结论报告")


class AgentContext(BaseModel):
    """智能体上下文模型"""
    
    patient_id: str = Field(description="患者ID")
    session_id: str = Field(description="会话ID")
    current_step: str = Field(description="当前步骤")
    collected_data: Dict[str, Any] = Field(description="已收集的数据")
    conversation_history: List[Dict[str, str]] = Field(description="对话历史")
    patient_info: Optional[Dict[str, Any]] = Field(default=None, description="患者信息")


class BaseAgent(ABC):
    """智能体基类"""
    
    required_fields: List[str] = []  # 子类需覆盖
    
    def __init__(self, name: str, description: str, agent_type: str):
        """
        初始化智能体
        
        Args:
            name: 智能体名称
            description: 智能体描述
            agent_type: 智能体类型
        """
        self.name = name
        self.description = description
        self.agent_type = agent_type
        
        # 初始化OpenRouter模型
        self.model_selector = ModelSelector()
        self.llm = self.model_selector.get_model_for_agent(agent_type)
        
        # 初始化提示模板和输出解析器
        self.prompt_template = self._create_prompt_template()
        self.output_parser = self._create_output_parser()
        
        # 创建LCEL管道
        self.chain = self._create_chain()
        
        logger.info(f"初始化智能体: {self.name}")
    
    def _create_llm(self) -> ChatOpenAI:
        """创建语言模型实例"""
        config = get_openai_config()
        return ChatOpenAI(
            api_key=config["api_key"],
            model=config["model"],
            temperature=config["temperature"],
            max_tokens=config["max_tokens"]
        )
    
    @abstractmethod
    def _create_prompt_template(self) -> PromptTemplate:
        """创建支持会话历史的提示模板"""
        return PromptTemplate(
            input_variables=["patient_input", "context", "history"],
            template="""
            你是一名专业的中医医生，负责采集患者的{context}信息。
            
            [对话历史]
            {history}
            
            [患者描述]
            {patient_input}
            
            请根据历史和当前输入，提取关键信息并以JSON格式输出。
            """
        )
    
    def _create_output_parser(self) -> BaseOutputParser:
        """创建输出解析器"""
        return AgentOutputParser()
    
    def _create_chain(self) -> Runnable:
        """创建LCEL管道"""
        return self.prompt_template | self.llm | self.output_parser
    
    @abstractmethod
    def ask_has_or_not(self, context: Dict[str, Any]) -> str:
        """询问有无 - 第一步"""
        pass
    
    @abstractmethod
    def ask_details_if_has(self, context: Dict[str, Any], has_result: Dict[str, Any]) -> str:
        """如果有则追问详情 - 第二步"""
        pass
    
    def parse_has_or_not_response(self, response: str) -> Dict[str, Any]:
        """解析患者对"有无"的回答"""
        # 使用LLM解析患者回答
        prompt = PromptTemplate(
            input_variables=["patient_response"],
            template="""
            请分析患者的回答，判断是否有相关症状或情况。
            
            患者回答: {patient_response}
            
            请以JSON格式输出:
            {{
                "has": true/false,
                "confidence": 0.0-1.0,
                "reasoning": "分析理由"
            }}
            """
        )
        
        chain = prompt | self.llm | self.output_parser
        result = chain.invoke({"patient_response": response})
        
        return result
    
    def extract_entities(self, response: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取医疗实体"""
        # 使用LLM提取实体
        prompt = PromptTemplate(
            input_variables=["text"],
            template="""
            请从以下文本中提取医疗相关的实体信息:
            
            文本: {text}
            
            请以JSON格式输出实体列表:
            [
                {{
                    "entity_type": "症状|疾病|药物|部位|时间",
                    "entity_value": "实体值",
                    "confidence": 0.0-1.0
                }}
            ]
            """
        )
        
        chain = prompt | self.llm | self.output_parser
        result = chain.invoke({"text": response})
        
        return result if isinstance(result, list) else []
    
    def structure_data(self, response: str) -> Dict[str, Any]:
        """结构化数据"""
        # 使用LLM结构化数据
        prompt = PromptTemplate(
            input_variables=["text", "agent_type"],
            template="""
            请将以下文本中的信息结构化，根据智能体类型提取相关信息:
            
            智能体类型: {agent_type}
            文本: {text}
            
            请以JSON格式输出结构化数据。
            """
        )
        
        chain = prompt | self.llm | self.output_parser
        result = chain.invoke({"text": response, "agent_type": self.agent_type})
        
        return result if isinstance(result, dict) else {}
    
    def is_complete(self, extracted_data: Dict[str, Any]) -> bool:
        return all(extracted_data.get(f) for f in self.required_fields)
    
    def get_missing_fields(self, extracted_data: Dict[str, Any]) -> List[str]:
        return [f for f in self.required_fields if not extracted_data.get(f)]
    
    def generate_followup(self, missing_fields: List[str]) -> str:
        # 针对缺失字段生成追问，子类可覆盖
        return "请补充以下信息: " + ", ".join(missing_fields)
    
    def generate_summary(self, extracted_data: Dict[str, Any]) -> str:
        # 结构化结论，子类可覆盖
        return str(extracted_data)
    
    def agent_workflow(self, context: Dict[str, Any]) -> AgentResponse:
        # 取历史
        history = context.get("conversation_history", [])
        history_text = "\n".join([
            f"{msg['role']}: {msg['content']}" for msg in history[-5:]
        ]) if history else ""
        # 1. 获取患者输入
        patient_input = context.get("detailed_response") or context.get("patient_response") or ""
        # 2. LCEL链式抽取结构化数据
        chain_input = {
            "patient_input": patient_input,
            "context": self.agent_type,
            "history": history_text
        }
        result = self.chain.invoke(chain_input)
        # 3. 判断采集完整性
        is_complete = self.is_complete(result)
        missing_fields = self.get_missing_fields(result)
        # 4. 追问或结论
        if not is_complete:
            followup = self.generate_followup(missing_fields)
            return AgentResponse(
                response=followup,
                extracted_data=result,
                entities=self.extract_entities(patient_input, context),
                confidence=0.8,
                is_complete=False,
                summary=None
            )
        else:
            summary = self.generate_summary(result)
            return AgentResponse(
                response="本轮采集已完成。",
                extracted_data=result,
                entities=self.extract_entities(patient_input, context),
                confidence=0.95,
                is_complete=True,
                summary=summary
            )
    
    def run(self, context: Dict[str, Any]) -> AgentResponse:
        """运行智能体"""
        return self.agent_workflow(context)
    
    def get_agent_info(self) -> Dict[str, Any]:
        """获取智能体信息"""
        return {
            "name": self.name,
            "description": self.description,
            "agent_type": self.agent_type,
            "capabilities": self.get_capabilities(),
            "required_inputs": self.get_required_inputs(),
            "outputs": self.get_outputs()
        }
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """获取智能体能力"""
        pass
    
    @abstractmethod
    def get_required_inputs(self) -> List[str]:
        """获取所需输入"""
        pass
    
    @abstractmethod
    def get_outputs(self) -> List[str]:
        """获取输出"""
        pass


class AgentOutputParser(BaseOutputParser):
    """智能体输出解析器"""
    
    def parse(self, text: str) -> Dict[str, Any]:
        """解析输出文本"""
        try:
            # 尝试解析JSON
            if text.strip().startswith('{') or text.strip().startswith('['):
                return json.loads(text)
            else:
                # 如果不是JSON，返回原始文本
                return {"text": text}
        except json.JSONDecodeError:
            # JSON解析失败，返回原始文本
            return {"text": text}
    
    def get_format_instructions(self) -> str:
        """获取格式说明"""
        return """
        请以JSON格式输出结果。
        如果是列表，请使用数组格式。
        如果是对象，请使用键值对格式。
        """ 