### 一、 登录页面 (Login Screen)
整体氛围与视觉
这是一个极其简洁、宁静且专业的界面，旨在让用户不受干扰地完成登录操作。整个屏幕的视觉焦点被严格控制在中央。
背景
屏幕背景是一张高分辨率的摄影作品，呈现了一个现代、干净的中医诊所室内一角。画面中包含原木色调的家具、绿色的植物和充足的自然光线，但整张照片被施加了高强度的模糊效果，使其柔和地退为背景，仅保留了色彩氛围，不会干扰前景元素。
布局与元素
- 垂直居中布局：所有元素都在屏幕的垂直和水平中轴线上对齐。
- 诊所Logo：在屏幕中央偏上的位置，是一个设计简约的诊所标志。这个标志由单色的线条构成，颜色为深灰绿色，下方是诊所的全名，字体为优雅的无衬线体。
- 账号输入框：在Logo下方，留有足够的空白间距后，是第一个输入框。它没有四周边框，只有一个位于底部的、1像素高的实心线条。输入框内部有浅灰色的提示文字"请输入手机号或工号"。当用户点击输入时，底部的线条会从灰色变为明亮的品牌主色（例如，一种温暖的蓝色），并且提示文字会平滑地缩小并移动到输入框的左上角。
- 密码输入框：在账号输入框正下方，间隔一段距离，是密码输入框。它的设计与账号输入框完全一致，内部的提示文字是"请输入密码"。输入框的最右侧有一个小眼睛图标，点击可以切换密码的可见性。
- 登录按钮：在密码输入框下方，经过一段更大的间距后，是一个尺寸宽大的登录按钮。这个按钮拥有完全圆润的边角，填充着明亮的品牌主色（与输入框激活时的颜色相同）。按钮内部是两个白色的、加粗的汉字："登录"。按钮下方投射出非常柔和、几乎难以察觉的阴影，使其具有轻微的立体感。
- 辅助链接：在登录按钮下方最底部的位置，是一行小号的、浅灰色的文字链接："忘记密码？"，为用户提供找回密码的入口。
---
### 二、 功能中心页面 (Function Hub Screen)
整体氛围与视觉
这是一个明亮、开阔、有序的界面，如同一个专属的应用桌面。它给人的感觉是掌控全局，同时又轻松愉悦。
背景
屏幕背景是纯粹的、非常浅的灰色（接近于白色，但不是纯白），以确保前景的卡片元素能够清晰地凸显出来。
布局与元素
1. 顶部状态栏：屏幕最顶端是一条纤细的横向状态栏。
    1. 左侧：是诊所的名称，字体为中等粗细的黑色无衬线体。
    2. 右侧：是一个圆形的、包含用户头像的个人资料入口。如果用户没有上传头像，则显示其姓名的第一个字。点击这个圆形入口，会从其下方平滑地展开一个小的下拉菜单，提供"个人中心"、"修改密码"和"退出登录"三个选项。对于管理员账号，此处还会额外显示一个"系统设置"选项。点击"系统设置"后，会弹出一个对话框，要求输入管理员密码进行验证，验证通过后方可进入系统设置页面。
2. 网格化卡片布局：在顶部状态栏下方，整个工作区被一个不可见的网格所规范。一系列功能卡片在这个网格上均匀、整齐地排列。卡片与卡片之间、卡片与屏幕边缘之间，都留有非常宽敞的间距。
    1. 功能卡片：每一张卡片都是一个独立的功能入口，采用3:4的宽高比，垂直布局设计。
    2. 形状与外观：卡片是白色的，拥有显著的圆角。卡片的下方和周围投射出柔和、弥散的阴影，使其看起来像是轻轻地漂浮在浅灰色的背景之上。
    3. 卡片内部结构（垂直居中布局）：
        1. 图标区域：卡片中央是一个64px×64px的图标，带有圆形背景。例如，"预约中心"卡片是一个日历图标，"患者管理"是一个人形档案图标，"门诊工作站"是一个听诊器和纸笔的组合图标。
        2. 文本区域：图标下方包含模块名称和动态信息，上下分布。
           - 模块名称：使用加粗的、黑色的无衬线字体，例如"预约中心"。
           - 动态信息：是一行小号的、浅灰色的文字，用于显示该模块的实时摘要信息。例如，"今日有 15 个预约"，"当前有 38 位患者档案"，"3 个处方待发药"。
    4. "灵机管理"卡片：智能诊疗辅助功能入口，采用魔法棒图标(MagicStick)，颜色为#FF6B6B（珊瑚红色）。卡片信息显示"智能诊疗辅助"，点击进入灵机管理页面。当前为预留功能，页面显示"敬请期待"状态，展示功能预览包括智能病历分析、AI诊疗建议、数据智能分析、个性化推荐等。
    5. "家医管理"卡片：家庭医生服务管理功能入口，采用房屋图标(House)，颜色为#32D74B（绿色）。卡片信息显示"家庭医生服务"，点击进入家医管理页面。当前为预留功能，页面显示"敬请期待"状态，展示功能预览包括签约患者管理、健康档案建立、远程健康咨询、健康监测跟踪、健康提醒服务、健康数据分析、上门服务管理、健康教育资源、服务包配置等，提供基本医疗服务、健康管理服务、慢病管理服务、康复指导服务、健康教育服务、中医养生服务等家庭医生服务内容。
    6. "研学中心"卡片：中医研学与学术平台功能入口，采用阅读图标(Reading)，颜色为#5856D6（紫色）。卡片信息显示"中医研学平台"，点击进入研学中心页面。当前为预留功能，页面显示"敬请期待"状态，展示功能预览包括知识图谱、医案学习、AI辅助科研、古典书籍研究、学术数据分析、名医经验传承等核心功能模块，提供《黄帝内经》、《伤寒论》、《金匮要略》等经典文献学习资源，以及智能检索、学习笔记、知识关联、学习进度等研学工具，为中医医生提供专业的学术研究和学习平台。
    5. "系统设置"卡片：在所有业务功能卡片的最后，会有一张"系统设置"卡片。它的图标是一个齿轮，用于区分于业务模块。这张卡片没有动态信息行，只有图标和模块名称。这张卡片仅对拥有管理员权限的用户可见。
3. 交互反馈：当鼠标光标悬停在任何一张功能卡片上时，该卡片会非常轻微地向上浮起，同时其下方的阴影会变得稍微加深和集中。卡片内容会从垂直居中布局动态切换为水平布局，图标和文本左右分布，整体移到卡片顶端。对于患者管理卡片，还会在卡片底部显示详细的统计信息。这个动态的动画效果为用户提供了清晰的、可交互的视觉反馈，同时展示了更多的功能信息。
---
### 三、 模块内部页面（以"患者列表"为例）
整体氛围与视觉
这是一个专注于任务、信息清晰、操作便捷的工作界面。延续了简洁和大量留白的设计风格，采用卡片式布局提供更丰富的患者信息展示。

布局与元素
1. 模块头部导航栏：当从功能中心点击卡片进入模块后，屏幕顶部会出现一个专属于此模块的头部导航栏。
    1. 左上角：是一个标志性的返回控件。它由一个向左的尖括号图标"<"和紧随其后的文字"功能中心"组成。整个控件的颜色为品牌主色（蓝色），点击它会将用户带回到功能中心页面。
    2. 正中央：是当前模块的标题，使用加粗的黑色字体，例如"患者管理"。
    3. 右上角：是一个主要操作按钮。对于列表页，这通常是一个加号"+"图标，被一个与品牌主色相同的圆形背景包裹，用于"新增患者"。
2. 搜索栏：在头部导航栏下方，是一个独立的、横跨整个内容区域的搜索栏。它的背景色比主背景色稍深一点，高度经过优化以增加工作区占比。
    1. 搜索区域（左侧）：包含一个胶囊形状的搜索框和一个确认按钮。
        1. 搜索框：宽度为250px，高度为36px，左侧有放大镜图标，内部有提示文字"搜索患者姓名或手机号"。
        2. 确认按钮：紧邻搜索框右侧，为品牌主色的"搜索"按钮，点击执行搜索操作。
    2. 筛选区域（右侧）：包含统一的筛选和排序组件，采用简化设计：
        1. 二级筛选器：级联选择器，宽度200px，将原有的多个筛选条件合并为统一的二级下拉菜单：
           - 一级分类：
             * 患者属性：包含性别和年龄段筛选
             * 就诊状态：包含活跃状态和预约状态筛选
             * 中医证型：包含各种证型筛选
           - 二级选项：
             * 患者属性 → 男性患者、女性患者、青年(25-35岁)、中年(36-45岁)、中老年(46岁以上)
             * 就诊状态 → 活跃患者、非活跃患者、有预约、无预约
             * 中医证型 → 肝郁脾虚证、气血两虚证、肾阳虚证、痰湿阻肺证、心脾两虚证、肝阳上亢证、脾胃虚弱证、阴虚火旺证
        2. 预约时间筛选：下拉框，宽度120px，专门用于按时间筛选预约患者：
           - 全部：显示所有患者
           - 今日预约：筛选今天有预约的患者（默认选项）
           - 明日预约：筛选明天有预约的患者
           - 本周预约：筛选本周有预约的患者
        3. 排序按钮：下拉菜单，宽度120px，支持多种排序方式：
           - 姓名排序：A-Z、Z-A
           - 年龄排序：小-大、大-小
           - 就诊次数排序：少-多、多-少
           - 最后就诊时间排序：早-晚、晚-早
           - 下次预约时间排序：早-晚、晚-早
    3. 布局：搜索栏使用flex布局，搜索区域固定宽度，筛选区域占据剩余空间并右对齐。筛选区域包含二级筛选器、预约时间筛选和排序按钮，三个组件在一行显示，响应式设计下自动调整布局。
3. 患者卡片网格：搜索栏下方是患者卡片的主体区域。
    1. 网格布局：采用固定的三行六列网格布局，每行显示6张卡片，共18张卡片，超出部分支持垂直滚动。
    2. 响应式设计：根据屏幕尺寸自动调整列数：
       - 大屏幕（>1200px）：6列
       - 中等屏幕（900px-1200px）：4列
       - 小屏幕（768px-900px）：3列
       - 平板（480px-768px）：2列
       - 手机（<480px）：1列
    3. 卡片设计：每个患者信息以独立卡片形式展示，卡片具有圆角、阴影和悬停效果，采用紧凑型设计以适应更多卡片显示，卡片高度经过优化以增加信息密度。
    4. 卡片结构（自上而下）：
        1. 卡片头部：包含患者头像（显示姓名首字母，32px圆形）、基本信息（姓名、性别、年龄）和状态标签（活跃/非活跃）。
        2. 卡片内容：包含患者详细信息，每行信息配有相应图标：
           - 联系电话：手机图标 + 电话号码
           - 诊断结果：文档图标 + 中医证型诊断（如"肝郁脾虚证"）
           - 最后就诊时间：日历图标 + 最后就诊日期
           - 下次预约时间：时钟图标 + 下次预约日期（如有）
        3. 卡片底部：包含就诊次数统计和"查看详情"按钮。
    5. 交互效果：卡片悬停时轻微上浮，阴影加深，边框变为品牌主色。
    6. 信息展示：诊断结果以品牌主色显示，下次预约时间以橙色显示，便于医生快速识别重要信息。

**注意：此搜索栏布局适用于所有包含搜索功能的模块页面，包括患者管理、划价收费、药房管理、库存管理、员工管理等页面。**
---
### 四、 模块内部页面（以"门诊工作站"为例）
整体氛围与视觉
这是一个高度专业、信息密度高但组织有序的工作界面。它像一个精密的数字仪器，所有设计都为医师的高效、准确操作服务，并引入了智能化的AI辅助。
布局与元素
1. 固定的头部与底部：
    1. 头部导航栏：与列表页类似，左上角是返回功能中心的按钮，中央是标题"门诊工作站"。
    2. 患者信息横幅：在头部导航栏下方，有一个固定的、不会随页面滚动的横幅区域。这个区域用浅灰色的背景与下方内容区分开。横幅内清晰地显示着当前患者的核心信息："李小明 男 34岁"，确保医师在任何时候都不会混淆患者。
    3. 底部操作栏：屏幕的最底部，也固定着一个操作栏，其中包含一个尺寸宽大、颜色醒目的"保存病历"按钮，确保医师在完成所有录入后可以方便地保存。
2. 可滚动的内容区：在固定的患者信息横幅和底部操作栏之间，是可垂直滚动的病历内容区。内容区被划分为多个逻辑清晰的区块。
    1. 区块设计：每个区块都由一个加粗的区块标题（如"主诉"、"四诊信息"）和下方的输入区域组成。区块之间有明显的垂直间距。
    2. 主诉与病史区块：这些区块包含的是一个宽大的、支持多行输入的文本区域，有浅灰色的边框，内部有提示文字。
    3. 四诊信息区块：这是交互最丰富的区域。
        1. 望诊：提供一个"舌象"区域，用户可以点击预设的、带有文字描述的颜色块（如"淡红"、"红"、"绛"）来选择舌色；下方是带有示意图的滑块，用于选择舌苔的厚薄。
        2. 闻诊：提供一个文本输入框。
        3. 问诊：提供一系列可勾选的常见症状标签（如"怕冷"、"口干"、"多汗"），方便快速录入。
        4. 切诊：提供一个"脉象"区域，用户可以点击多个预设的、描述性的标签（如"浮脉"、"沉脉"、"弦脉"、"滑脉"），支持多选。
        5. 诊断与治法区块：与主诉区块类似，是大的文本输入区域。
        6. 处方区块：标题为"中药处方"，右侧有一个"+" 添加药品"的按钮。点击按钮后，下方会动态增加一行。该行包含一个药品搜索框，医师输入药品名称时，会实时弹出匹配的药品列表（包含库存信息）供选择。选择药品后，该行会变为一个包含药品名称、一个用于输入克数的数字框、一个用于备注的文本框和删除按钮的条目。所有已添加的药品会形成一个列表。列表的底部会实时计算并显示"共 X 味药，合计金额：¥XXX.XX"。
3. 常驻AI助手入口：
    1. 图标位置：在屏幕的右侧边缘垂直居中的位置，始终悬浮着一个AI助手图标，这个图标是一个圆形的、带有柔和阴影的按钮，背景色是渐变的蓝紫色，中间是一个白色的、风格化的"AI"字母或一个大脑/星云的抽象图标。这个图标会固定在屏幕上，即使用户滚动病历内容，它也保持位置不变，但是当用户点击长按住图标，图标可上下移动，松手后固定位置。
    2. 点击交互：当用户点击这个AI助手图标时，会触发一个流畅的动画。
4. AI助手窗口：
    1. 窗口滑出：一个独立的AI助手窗口会从屏幕的右侧平滑地滑入，最终占据屏幕右侧约三分之一的宽度。原有的病历内容区会被优雅地向左压缩，但仍然可见和可操作。
    2. 窗口布局：
        1. 头部：窗口顶部有标题"智能助手"，右侧有一个"X"关闭按钮，点击后窗口会平滑地滑出屏幕，病历区恢复原状。
        2. 对话历史区：占据窗口大部分区域的是一个对话式的聊天记录区，显示用户与AI的交互历史。
        3. 快捷操作按钮：在对话历史的上方或下方，有一排预设的、胶囊形状的快捷操作按钮，例如："分析当前病历"、"推荐方剂"、"检查配伍禁忌"。
        4. 输入框：窗口最底部是一个聊天输入框，内部有提示文字"请输入您的问题或指令..."，右侧有一个发送按钮。
    3. AI功能示例：点击"分析当前病历"后，AI会自动读取左侧已填写的病历信息，并在窗口中输出结构化的摘要、可能的证型诊断和治疗原则建议。医师在输入框中提问"该患者失眠，有何加减建议？"，AI会根据当前病历和处方，给出具体的用药调整建议。
---
### 五、 模块内部页面（以"排班管理"为例）
整体氛围与视觉
这是一个功能性强、信息直观、便于规划的界面。它主要以日历视图为核心，帮助管理者清晰地掌握医生的排班情况，并能快速进行调整。

布局与元素
1.  **模块头部导航栏**：与其它模块保持一致。
    1.  左上角：返回控件"< 功能中心"。
    2.  正中央：模块标题"排班管理"。
    3.  右上角：包含一组主要操作按钮，例如"导出本周排班"和"新增排班"。

2.  **工具栏**：在头部导航栏下方，提供视图控制和筛选功能。
    1.  **视图切换**：一组按钮，用于在"月视图"、"周视图"和"日视图"之间切换（当前版本以月视图为核心）。
    2.  **医生筛选**：一个下拉选择框，可以按特定医生筛选排班表，默认显示"所有医生"。
    3.  **日期导航**：日历组件自带向前和向后翻页的箭头按钮，以及一个"今天"按钮，可快速返回当前日期的视图。

3.  **日历核心区**：占据页面的主要部分。
    1.  **网格布局**：根据所选视图（月/周/日），显示相应的日历网格。
    2.  **排班条目**：在日期格内，每个排班都以一个带颜色的信息条形式存在。信息条上简洁地显示医生姓名和排班时间（如"李医生 09:00 - 12:00"）。不同医生的排班条可以使用不同的颜色以示区分。
    3.  **交互**：
        *   点击日期格的空白处或顶部的"新增排班"按钮，会弹出"新增排班"对话框。
        *   点击一个已存在的排班条目，会弹出"编辑排班"对话框，其中预先填入了该排班的信息。

4.  **新增/编辑排班对话框**：一个模态弹窗，用于创建或修改排班信息。
    1.  **标题**："新增排班"或"编辑排班"。
    2.  **表单项**：
        *   **选择医生**：一个必填的下拉框，列出所有可排班的医生。
        *   **排班日期**：一个日期选择器。
        *   **开始时间**：一个时间选择器。
        *   **结束时间**：一个时间选择器。
        *   **班次类型**：一组单选按钮（如：上午班、下午班、夜班、全天）。
        *   **备注**：一个可选的文本输入区域。
    3.  **操作按钮**：
        *   "保存"按钮。
        *   "取消"按钮。
        *   在编辑模式下，还会出现一个红色的"删除排班"按钮。

---

## 页面布局修改规范

### 修改原则
1. **文档同步原则**：任何页面布局、样式、交互的修改都必须同步更新到本页面描述文档中
2. **详细记录原则**：修改内容需要详细记录，包括具体的尺寸、颜色、间距、交互方式等
3. **适用范围标注**：明确标注修改内容适用的页面范围
4. **版本记录原则**：重要修改需要标注修改时间和版本信息

### 修改流程
1. **开发前**：先更新页面描述文档，明确修改内容
2. **开发中**：按照文档描述进行开发
3. **开发后**：验证实现与文档描述的一致性
4. **发布前**：最终确认文档与代码的同步性

### 文档更新要求
1. **搜索栏相关修改**：必须更新搜索栏的详细描述，包括布局、尺寸、交互方式
2. **新增页面**：必须添加完整的页面描述，包括整体氛围、布局元素、交互方式
3. **样式调整**：必须更新相关的样式描述，包括颜色、尺寸、间距等
4. **功能变更**：必须更新相关的功能描述和交互方式

### 当前版本信息
- **文档版本**：v3.5
- **最后更新**：2025-01-21
- **主要更新**：门诊工作台页面开发，实现三栏式布局的智能诊疗工作站
- **影响页面**：门诊工作台页面、功能中心页面、路由配置

### 修改记录
- **2025-01-21 v3.5**：门诊工作台页面开发
  - 创建门诊工作台页面组件 OutpatientWorkstation.vue
  - 采用三栏式布局设计：左侧流程控制面板(20%)、中间电子病历工单画布(60%)、右侧参考信息面板(20%)
  - 左侧栏功能：门诊单搜索框、接诊按钮、患者队列预览、工具区(叫号、门诊看板、预约看板、用药助手)
  - 中间栏功能：顶部操作栏(费用预览、保存病历、继续接诊、打印)、基本信息与就诊属性区、病历区(主诉、现病史、既往史、四诊信息、诊断)、适宜技术区、处方医嘱区、医嘱事项区、审计校验区、工单信息展示区
  - 右侧栏功能：历史标签页(患者历次就诊记录)、报告标签页(化验单检查报告)、影像标签页(X光CT等影像资料)、AI诊断标签页(证型辨析、方剂推荐、智能问答)
  - 四诊信息区域集成AI辅助功能，每个子项都有"一键从灵机获取信息"按钮
  - 适宜技术区支持动态添加技术卡片，包含技术名称、操作部位、具体穴位等字段
  - 处方医嘱区提供中成药处方、输注处方、中药处方、历史处方、处方模板等功能入口
  - 审计校验区实时显示方剂审核和医保合规性审核结果
  - AI诊断功能包括证型辨析、方剂与治法推荐、智能问答等
  - 患者队列支持状态标签显示(待诊、诊中、回诊、已诊、未收费)，诊中状态带有微光动画
  - 更新路由配置，添加/outpatient-workstation路由
  - 更新功能中心页面，门诊工作站卡片链接指向新路由
  - 页面采用专业、冷静的现代设计风格，以灰白色为基调，蓝绿色作为高亮和交互色
  - 遵循门诊工作台页面原型文档的设计规范，实现高效智能的单页工作站
  - 为医生提供完整的诊疗工作流程支持，最大化诊疗效率

- **2025-06-21 v3.4**：功能中心页面新增"研学中心"卡片
  - 在功能中心页面添加"研学中心"功能卡片
  - 卡片采用阅读图标(Reading)，颜色为#5856D6（紫色）
  - 卡片信息显示"中医研学平台"
  - 添加路由配置 /research-center，指向研学中心页面
  - 创建研学中心页面组件 ResearchCenterList.vue
  - 页面采用"敬请期待"设计，展示功能预览
  - 核心功能模块包括：知识图谱、医案学习、AI辅助科研、古典书籍研究、学术数据分析、名医经验传承
  - 学习资源涵盖：《黄帝内经》、《伤寒论》、《金匮要略》、《温病条辨》、《本草纲目》、《针灸大成》、现代中医研究、临床医案集
  - 研学工具包括：智能检索、学习笔记、知识关联、学习进度追踪
  - 页面布局采用分区设计：核心功能模块（3x2网格）、学习资源（标签展示）、研学工具（2x2网格）
  - 响应式设计适配移动端，为中医医生提供专业的学术研究和学习平台
  - 预留后期开发空间，当前为占位页面

- **2025-06-21 v3.3**：将"慢病管理"调整为"家医管理"
  - 将慢病管理卡片调整为家医管理卡片
  - 卡片名称从"慢病管理"改为"家医管理"
  - 卡片图标从趋势图表(TrendCharts)改为房屋图标(House)
  - 卡片信息从"慢性病跟踪管理"改为"家庭医生服务"
  - 路由从 /chronic 改为 /family-doctor
  - 删除原慢病管理页面组件 ChronicList.vue
  - 创建新的家医管理页面组件 FamilyDoctorList.vue
  - 功能预览调整为家庭医生服务相关功能：
    - 签约患者管理、健康档案建立、远程健康咨询
    - 健康监测跟踪、健康提醒服务、健康数据分析
    - 上门服务管理、健康教育资源、服务包配置
  - 服务内容展示：基本医疗服务、健康管理服务、慢病管理服务、康复指导服务、健康教育服务、中医养生服务
  - 采用3x3网格布局展示9个核心功能特性
  - 更符合家庭医生服务的工作场景和需求

- **2025-06-21 v3.2**：功能中心页面新增"慢病管理"卡片
  - 在功能中心页面添加"慢病管理"功能卡片
  - 卡片采用趋势图表图标(TrendCharts)，颜色为#32D74B（绿色）
  - 卡片信息显示"慢性病跟踪管理"
  - 添加路由配置 /chronic，指向慢病管理页面
  - 创建慢病管理页面组件 ChronicList.vue
  - 页面采用"敬请期待"设计，展示功能预览
  - 功能预览包括：患者档案管理、健康指标监测、用药提醒服务、病情趋势分析、异常预警系统、随访计划管理
  - 支持慢病类型展示：高血压、糖尿病、冠心病、慢性肾病、慢性阻塞性肺病、脑卒中
  - 页面布局遵循系统设计规范，包含标准的模块头部导航栏
  - 采用3x2网格布局展示功能特性，响应式设计适配移动端
  - 预留后期开发空间，当前为占位页面

- **2025-06-21 v3.1**：功能中心页面新增"灵机管理"卡片
  - 在功能中心页面添加"灵机管理"功能卡片
  - 卡片采用魔法棒图标(MagicStick)，颜色为#FF6B6B
  - 卡片信息显示"智能诊疗辅助"
  - 添加路由配置 /lingji，指向灵机管理页面
  - 创建灵机管理页面组件 LingjiList.vue
  - 页面采用"敬请期待"设计，展示功能预览
  - 功能预览包括：智能病历分析、AI诊疗建议、数据智能分析、个性化推荐
  - 页面布局遵循系统设计规范，包含标准的模块头部导航栏
  - 预留后期开发空间，当前为占位页面

- **2025-06-21 v3.0**：患者管理页面筛选功能重构
  - 将原有的4个独立筛选控件（状态、性别、证型、预约）合并为一个二级级联选择器
  - 二级筛选器宽度200px，采用级联下拉菜单设计
  - 一级分类：患者属性、就诊状态、中医证型
  - 二级选项：具体筛选条件，包括新增的年龄段筛选（青年、中年、中老年）
  - 移除日历控件，简化时间筛选为预约时间筛选下拉框
  - 预约时间筛选包含：全部、今日预约、明日预约、本周预约
  - 默认显示今日预约患者，符合日常使用习惯
  - 筛选区域从4个控件简化为3个控件：二级筛选器 + 预约时间筛选 + 排序按钮
  - 优化用户体验，操作更直观，界面更简洁
  - 保留所有原有筛选功能，增强年龄段筛选能力
  - 更新患者测试数据，包含2025年6月的预约时间，便于测试时间筛选功能

- **2024-01-15 v2.5**：功能中心卡片悬停布局优化
  - 减少悬停时图标和文字之间的间距，从md调整为sm
  - 优化卡片内容的垂直分布，文字区域采用flex-start对齐
  - 减少标题和信息之间的间距，从2px调整为1px
  - 减少统计信息区域的内边距和外边距
  - 调整统计信息区域的定位，减少与卡片内容的间距
  - 优化文字行高，使信息显示更紧凑
  - 整体布局更加紧凑，减少不必要的留白空间

- **2024-01-15 v2.4**：子页面统计信息高度进一步优化
  - 将子页面统计信息的高度从80px增加到107px（增加1/3）
  - 为更多统计数据的展示提供充足空间
  - 改善统计信息的视觉比例和内容分布
  - 保持紧凑布局的同时优化信息展示效果

- **2024-01-15 v2.3**：子页面统计信息高度优化
  - 增加鼠标悬停时子页面统计信息的高度
  - 设置最小高度为80px，确保统计信息有足够的显示空间
  - 增加内边距从sm调整为md，提供更好的内容呼吸空间
  - 改善统计信息的视觉层次和可读性
  - 为更多统计数据的展示预留空间

- **2024-01-15 v2.0**：功能中心卡片动态布局实现
  - 实现卡片内容的动态布局切换效果
  - 默认状态：图标、标题、信息垂直居中分布，采用3:4宽高比
  - 悬停状态：图标和文本左右分布，文本上下分布，整体移到卡片顶端
  - 悬停时图标尺寸从56px缩小到48px，适应水平布局
  - 悬停时文字大小相应调整，标题从lg调整为md，信息从sm调整为11px
  - 患者管理卡片悬停时在底部显示详细统计信息
  - 统计信息采用绝对定位，从底部滑入显示
  - 所有过渡效果使用CSS动画，提供流畅的用户体验
  - 保持原有的悬停上浮和阴影效果

- **2024-01-15 v1.9**：功能中心卡片宽高比调整
  - 将卡片宽高比调整为3:4，采用垂直布局
  - 调整网格最小宽度从280px减少到240px，适应新的宽高比
  - 卡片内容采用垂直居中布局，图标、标题、信息上下分布
  - 增大图标尺寸到64px×64px，在垂直布局中更突出
  - 优化文字间距和行高，提升垂直布局的可读性
  - 调整患者统计信息布局，改为3列网格，适应卡片宽度
  - 统计信息字体和间距相应调整，保持整体协调
  - 添加文本换行处理，确保长文本在卡片中正确显示

- **2024-01-15 v1.6**：功能中心模块名称调整
  - 将"电子病历"模块名称改为"门诊工作站"
  - 保持原有路由和功能不变，仅调整显示名称
  - 更准确地反映该模块的实际功能，包含病历录入、处方开具等门诊工作流程
  - 提升用户体验，使模块名称更符合医生的工作习惯

- **2024-01-15 v1.5**：功能中心患者统计信息增强
  - 在患者管理卡片上显示实时统计信息
  - 基础信息显示：总患者数、活跃状态、性别分布、预约情况
  - 悬停显示详细统计：包含状态、性别、年龄分布、是否预约、就诊次数等
  - 统计信息包括：
    - 总患者数、活跃/非活跃患者数
    - 男性/女性患者分布
    - 有预约/无预约患者数
    - 平均就诊次数
    - 年龄分组统计（青年25-35岁、中年36-45岁、老年46岁以上）
  - 使用不同颜色区分各类统计数据，提升可读性
  - 悬停动画效果，平滑显示详细统计信息
  - 集成模拟数据库服务，实时获取患者数据
  - 为医生提供快速了解患者整体状况的入口

- **2024-01-15 v1.4**：患者数据扩展
  - 新增20个患者数据，总患者数达到38人
  - 新增患者涵盖不同年龄、性别、证型、状态和预约情况
  - 性别分布平衡：男19人，女19人
  - 年龄分布：25-58岁，覆盖各年龄段
  - 证型分布丰富：8种常见中医证型
  - 状态分布：活跃24人，非活跃14人
  - 预约情况：有预约24人，无预约14人
  - 更新模拟数据库文档，添加详细的数据统计信息
  - 为患者管理页面的筛选和排序功能提供更丰富的测试数据

- **2024-01-15 v1.3**：数据库架构重构
  - 创建模拟数据库文件夹结构：`frontend/src/mock/database/`
  - 将患者数据从组件中分离，创建独立的患者表文件 `patients.ts`
  - 实现完整的患者数据服务接口，包括CRUD操作
  - 添加18个真实患者数据，涵盖不同年龄、性别、证型、状态和预约情况
  - 实现模拟网络延迟和异步操作，更真实地模拟数据库行为
  - 创建数据库统一导出文件 `index.ts`，便于后续扩展其他数据表
  - 修改患者列表页面，使用模拟数据库服务替代本地数据
  - 创建详细的模拟数据库使用文档 `README.md`
  - 为下一阶段真实数据库开发做好准备，保持接口兼容性

- **2024-01-15 v1.2**：患者管理页面卡片布局优化
  - 将患者列表从行式布局改为卡片网格布局
  - 增加患者信息字段：性别、年龄、诊断结果、最后就诊时间、下次预约时间、就诊次数、状态
  - 卡片采用固定的三行六列网格布局，每行6张卡片，共18张卡片，超出部分支持垂直滚动
  - 响应式设计：根据屏幕尺寸自动调整列数（6/4/3/2/1列）
  - 采用紧凑型设计，减小卡片内间距和字体大小
  - 进一步优化卡片高度，头像尺寸调整为32px，减少各元素间距
  - 新增排序功能：支持按姓名、年龄、就诊次数、最后就诊时间、下次预约时间正序倒序排序
  - 新增筛选功能：支持按性别、证型筛选，增强状态筛选
  - 新增是否预约筛选：支持按"全部、有预约、无预约"筛选患者
  - 优化筛选控件布局：所有筛选控件在一行显示，统一宽度为120px，搜索框宽度调整为250px
  - 添加卡片悬停效果和视觉反馈
  - 诊断结果以品牌主色显示，下次预约时间以橙色显示，便于医生快速识别重要信息
  - 搜索功能支持按诊断结果搜索
  - 状态筛选功能支持按活跃/非活跃状态筛选

- **2024-01-15 v1.1**：搜索栏布局优化
  - 搜索框宽度调整为300px（原宽度的1/2）
  - 添加确认按钮
  - 搜索框和按钮靠左对齐
  - 条件筛选组件放在右侧
  - 降低搜索栏高度以增加工作区占比
  - 适用于所有包含搜索功能的模块页面

- **2025-01-21 v3.6**：功能中心页面灵机管理功能迁移
  - 将灵机管理功能从功能中心页面迁移到系统设置页面
  - 在系统设置页面新增"灵机管理"标签页，位于权限设置和系统配置之间
  - 灵机管理功能包含：系统状态监控、功能配置、使用统计
  - 系统状态：显示AI服务状态、模型版本、响应时间、今日调用次数
  - 功能配置：智能诊断、方剂推荐、配伍检查、病历分析开关，置信度阈值调节，响应模式选择
  - 使用统计：本月调用次数、准确率、平均响应时间、用户满意度等关键指标
  - 从功能中心页面移除灵机管理卡片，简化功能中心布局
  - 灵机管理作为系统级功能更适合放在系统设置中统一管理
  - 保持灵机管理页面的路由和组件文件，以备后续独立页面使用
  - 优化系统设置的标签页布局，新增MagicStick图标支持

- **2025-06-24 v3.7**：功能中心新增"数据中心"模块
  - 在功能中心页面添加"数据中心"功能卡片
  - 卡片采用数据分析图标(DataAnalysis)，颜色为#722ED1（紫色）
  - 卡片信息动态显示今日就诊人次和收入统计
  - 添加路由配置 /data-center，指向数据中心页面
  - 创建数据中心页面组件 DataCenterList.vue
  - 页面采用"功能开发中"设计，展示功能预览和数据概览
  - 核心功能模块包括：数据趋势分析、统计报表生成、实时数据看板、科研数据支持、历史数据查询、数据配置管理
  - 数据统计范围涵盖：患者数据、诊疗数据、处方数据、收费数据、库存数据、AI数据
  - 页面布局采用概览卡片 + 功能预览的设计，响应式适配移动端
  - 为研学中心科研功能提供数据支撑，包括证型分布、方剂使用频率、疾病趋势等
  - 创建数据统计服务 dataStatistics.ts，提供完整的数据分析API
  - 支持数据导出功能，便于科研分析和报表生成
  - 权限配置：管理员和医师可访问，护士、药师、前台无权限
  - 预留后期开发空间，当前为占位页面展示功能规划
  - 卡片具有详细统计信息功能，悬停时显示今日核心指标、模块数据概览、数据趋势等

- **2025-06-24 v3.8**：功能中心新增卡片编辑功能
  - 在功能中心页面头部添加"编辑布局"按钮，支持切换编辑模式
  - 编辑模式下卡片支持拖拽调整位置，实时保存用户自定义布局
  - 添加布局预设选择器，提供"默认布局"、"医生专用"、"管理员专用"三种预设
  - 创建布局管理服务 layoutService.ts，统一管理卡片布局数据
  - 支持布局数据本地存储，按用户ID区分不同用户的布局配置
  - 编辑模式下显示拖拽指示器和操作提示，提升用户体验
  - 编辑模式下禁用卡片点击导航和悬停统计信息，避免误操作
  - 支持重置为默认布局功能，一键恢复系统推荐布局
  - 布局数据包含版本控制，确保不同版本间的兼容性
  - 预设布局针对不同角色优化：医生专用突出诊疗功能，管理员专用突出管理功能
  - 拖拽过程中提供视觉反馈：半透明、旋转、阴影等效果
  - 响应式设计适配移动端，编辑模式在小屏幕设备上优化显示
  - 布局服务支持导入导出功能，便于配置备份和迁移

- **2025-06-24 v3.9**：预约中心页面重新设计
  - 采用三栏式布局设计，参考门诊工作台的布局结构
  - 左侧栏：搜索筛选功能，包含患者搜索、日期筛选、医师筛选、状态筛选、预约来源筛选
  - 中间栏：预约患者列表，只显示有预约的患者，不显示无预约患者
  - 右侧栏：选中预约的详细信息，包含患者信息、预约信息、历史就诊记录
  - 支持按日期筛选：今日预约、明日预约、本周预约、自定义日期
  - 日期筛选参考患者管理页面的简化设计，提供快捷选项
  - 预约卡片显示患者头像、姓名、手机号、医师、时间、来源、状态等关键信息
  - 支持预约状态管理：待就诊、已就诊、已取消、未到
  - 支持预约来源区分：线上预约、现场挂号、电话预约
  - 新建预约功能增强：支持患者基本信息录入（姓名、手机、性别、年龄）
  - 预约表单增加预约来源选择和更详细的验证规则
  - 左侧栏显示今日预约统计：总预约数、待就诊数、已完成数、已取消数
  - 支持从预约列表直接开始接诊，跳转到门诊工作台
  - 支持预约取消功能，带确认提示避免误操作
  - 右侧栏显示患者历史就诊记录，便于医师了解患者病史
  - 预约详情支持编辑、接诊、取消、打印等操作
  - 响应式设计适配不同屏幕尺寸，移动端采用垂直布局
  - 页面专注于前台创建预约工单的核心功能

- **2025-06-24 v3.10**：预约中心页面与患者数据库联动
  - 创建预约数据库模拟文件 appointments.ts，包含完整的预约数据结构和服务API
  - 创建患者选择器组件 PatientSelector.vue，支持搜索现有患者和新建患者
  - 预约表单集成患者选择器，支持从患者数据库中选择现有患者创建预约
  - 患者选择器支持模糊搜索：按姓名、手机号、诊断信息搜索患者
  - 新建预约时自动关联患者ID，建立预约与患者的数据关联关系
  - 预约数据包含患者冗余字段（姓名、手机、性别、年龄），便于列表显示和筛选
  - 支持在预约表单中直接新建患者，新建后自动选中该患者
  - 患者信息在预约表单中以描述列表形式展示，包含基本信息和诊断信息
  - 预约操作（创建、编辑、取消）自动同步更新患者的下次预约时间
  - 右侧栏患者历史记录从预约数据库中动态加载，显示该患者的历史就诊记录
  - 预约统计数据从预约数据库实时计算，支持今日预约数量统计
  - 所有预约操作通过 appointmentService API 进行，支持异步数据处理
  - 患者选择器支持远程搜索，提升大量患者数据下的搜索性能
  - 预约列表支持按患者ID关联查询，实现预约与患者数据的完整联动
  - 数据加载状态管理，提供加载指示器和错误处理机制
  - 预约表单验证规则优化，重点验证患者选择和预约时间
  - 支持编辑预约时自动加载关联的患者信息
  - 取消预约时自动清除患者的下次预约时间，保持数据一致性