<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医预问诊智能体系统 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .back-button {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.05);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background: #e8f5e8;
            border-left: 4px solid #4CAF50;
        }
        
        .error {
            background: #ffe8e8;
            border-left-color: #f44336;
        }
        
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.healthy {
            background: #4CAF50;
            color: white;
        }
        
        .status.unhealthy {
            background: #f44336;
            color: white;
        }
        
        .chat-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            background: white;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
        }
        
        .message.user {
            background: #e3f2fd;
            margin-left: 20px;
        }
        
        .message.assistant {
            background: #f1f8e9;
            margin-right: 20px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-button">← 返回主页</a>
            <h1>🏥 中医预问诊智能体系统</h1>
            <p>完整功能演示 - 基于多智能体协同的智能问诊平台</p>
        </div>
        
        <div class="content">
            <!-- 系统状态 -->
            <div class="section">
                <h3>🔍 系统状态</h3>
                <button class="btn" onclick="checkHealth()">检查系统健康状态</button>
                <button class="btn" onclick="testConnection()" style="margin-left: 10px;">测试连接</button>
                <div id="healthStatus"></div>
            </div>
            
            <!-- 开始问诊 -->
            <div class="section">
                <h3>🚀 开始问诊</h3>
                <div class="form-group">
                    <label for="patientName">患者姓名：</label>
                    <input type="text" id="patientName" placeholder="请输入患者姓名" value="张三">
                </div>
                <div class="form-group">
                    <label for="patientAge">年龄：</label>
                    <input type="number" id="patientAge" placeholder="请输入年龄" value="30" min="1" max="150">
                </div>
                <div class="form-group">
                    <label for="patientGender">性别：</label>
                    <select id="patientGender">
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
                <button class="btn" onclick="startInquiry()">开始问诊</button>
                <div id="inquiryResult"></div>
            </div>
            
            <!-- 简单聊天 -->
            <div class="section">
                <h3>💬 智能对话</h3>
                <div class="chat-container" id="chatContainer">
                    <div class="message assistant">
                        <strong>中医助手：</strong>您好！我是您的中医问诊助手，请告诉我您的症状。
                    </div>
                </div>
                <div class="form-group">
                    <textarea id="chatMessage" placeholder="请描述您的症状或问题..."></textarea>
                </div>
                <button class="btn" onclick="sendMessage()">发送消息</button>
                <div class="loading" id="chatLoading">
                    <div class="spinner"></div>
                    <p>AI正在思考中...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        // 添加全局错误处理
        window.addEventListener('error', function(e) {
            console.error('全局错误:', e.error);
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise拒绝:', e.reason);
        });
        
        // 检查系统健康状态
        async function checkHealth() {
            try {
                console.log('检查健康状态...');

                const response = await fetch(`${API_BASE}/health`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                console.log('健康检查响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('健康检查数据:', data);

                const statusDiv = document.getElementById('healthStatus');
                const statusClass = data.status === 'healthy' ? 'healthy' : 'unhealthy';
                const llmStatus = data.services?.llm || 'unknown';

                statusDiv.innerHTML = `
                    <div class="response">
                        <strong>系统状态：</strong>
                        <span class="status ${statusClass}">${data.status}</span>
                        <br><br>
                        <strong>LLM服务：</strong> ${llmStatus}<br>
                        <strong>版本：</strong> ${data.version || 'unknown'}<br>
                        <strong>检查时间：</strong> ${new Date().toLocaleString()}
                    </div>
                `;
            } catch (error) {
                console.error('健康检查错误:', error);
                document.getElementById('healthStatus').innerHTML = `
                    <div class="response error">
                        <strong>错误：</strong>无法连接到服务器 (${API_BASE})<br>
                        <strong>详情：</strong>${error.message}<br>
                        <small>请确保服务器正在运行在端口8001</small>
                    </div>
                `;
            }
        }

        // 测试连接
        async function testConnection() {
            const statusDiv = document.getElementById('healthStatus');
            statusDiv.innerHTML = '<div class="response">正在测试连接...</div>';

            try {
                // 测试基本连接
                const response = await fetch(`${API_BASE}/`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    statusDiv.innerHTML = `
                        <div class="response">
                            <strong>✅ 连接成功！</strong><br>
                            <strong>服务器响应：</strong>${data.message}<br>
                            <strong>版本：</strong>${data.version}<br>
                            <strong>API基础地址：</strong>${API_BASE}
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="response error">
                        <strong>❌ 连接失败</strong><br>
                        <strong>错误：</strong>${error.message}<br>
                        <strong>建议：</strong>请确保服务器正在运行在 ${API_BASE}
                    </div>
                `;
            }
        }

        // 开始问诊
        async function startInquiry() {
            const name = document.getElementById('patientName').value;
            const age = document.getElementById('patientAge').value;
            const gender = document.getElementById('patientGender').value;
            
            if (!name || !age) {
                alert('请填写完整的患者信息');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/inquiry/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        patient_name: name,
                        patient_age: parseInt(age),
                        patient_gender: gender
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('inquiryResult').innerHTML = `
                        <div class="response">
                            <strong>问诊已开始！</strong><br>
                            <strong>会话ID：</strong>${data.session_id}<br>
                            <strong>欢迎消息：</strong>${data.message}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '启动问诊失败');
                }
            } catch (error) {
                document.getElementById('inquiryResult').innerHTML = `
                    <div class="response error">
                        <strong>错误：</strong>${error.message}
                    </div>
                `;
            }
        }
        
        // 发送聊天消息
        async function sendMessage() {
            const message = document.getElementById('chatMessage').value.trim();
            if (!message) {
                alert('请输入消息');
                return;
            }

            // 显示用户消息
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML += `
                <div class="message user">
                    <strong>患者：</strong>${message}
                </div>
            `;

            // 清空输入框并显示加载状态
            document.getElementById('chatMessage').value = '';
            document.getElementById('chatLoading').style.display = 'block';

            try {
                console.log('发送消息:', message);

                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message
                    })
                });

                console.log('响应状态:', response.status);

                const data = await response.json();
                console.log('响应数据:', data);

                if (response.ok && data.status === 'success') {
                    chatContainer.innerHTML += `
                        <div class="message assistant">
                            <strong>中医助手：</strong>${data.response || '抱歉，我没有收到有效的响应。'}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || data.detail || '发送消息失败');
                }
            } catch (error) {
                console.error('聊天错误:', error);
                chatContainer.innerHTML += `
                    <div class="message assistant error">
                        <strong>错误：</strong>${error.message}<br>
                        <small>请检查服务器是否正常运行，或稍后重试。</small>
                    </div>
                `;
            } finally {
                document.getElementById('chatLoading').style.display = 'none';
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }
        
        // 页面加载时自动检查健康状态
        window.onload = function() {
            checkHealth();
        };
        
        // 回车键发送消息
        document.getElementById('chatMessage').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>
