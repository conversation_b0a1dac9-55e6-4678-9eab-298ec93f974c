<template>
  <div class="mall-management-container">
    <!-- 页面头部 -->
    <div class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        <span>返回</span>
      </div>
      <h1 class="module-title">商城管理</h1>
    </div>

    <!-- 页面内容 -->
    <div class="content-area">
      <div class="mall-management-overview">
        <!-- 统计概览 -->
        <div class="stats-section">
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon :size="32" color="#007AFF"><ShoppingBag /></el-icon>
              </div>
              <div class="stat-content">
                <h3>商品总数</h3>
                <p class="stat-number">{{ mallStats.totalProducts }}</p>
                <span class="stat-trend positive">+12 本月新增</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon :size="32" color="#34C759"><Money /></el-icon>
              </div>
              <div class="stat-content">
                <h3>本月销售额</h3>
                <p class="stat-number">¥{{ mallStats.monthlySales.toLocaleString() }}</p>
                <span class="stat-trend positive">+15.6% 环比上月</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon :size="32" color="#FF9500"><User /></el-icon>
              </div>
              <div class="stat-content">
                <h3>活跃用户</h3>
                <p class="stat-number">{{ mallStats.activeUsers }}</p>
                <span class="stat-trend positive">+8.3% 环比上月</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon :size="32" color="#5856D6"><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <h3>订单数量</h3>
                <p class="stat-number">{{ mallStats.totalOrders }}</p>
                <span class="stat-trend positive">+22.1% 环比上月</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能导航 -->
        <div class="function-nav">
          <div class="nav-grid">
            <div class="nav-card" @click="navigateToProducts">
              <div class="nav-icon">
                <el-icon :size="40" color="#007AFF"><Box /></el-icon>
              </div>
              <h3>商品管理</h3>
              <p>添加、编辑和管理商城商品</p>
            </div>
            <div class="nav-card" @click="navigateToOrders">
              <div class="nav-icon">
                <el-icon :size="40" color="#34C759"><Document /></el-icon>
              </div>
              <h3>订单管理</h3>
              <p>查看和处理客户订单</p>
            </div>
            <div class="nav-card" @click="navigateToCustomers">
              <div class="nav-icon">
                <el-icon :size="40" color="#FF9500"><UserFilled /></el-icon>
              </div>
              <h3>客户管理</h3>
              <p>管理客户信息和会员等级</p>
            </div>
            <div class="nav-card" @click="navigateToPromotions">
              <div class="nav-icon">
                <el-icon :size="40" color="#5856D6"><Present /></el-icon>
              </div>
              <h3>营销活动</h3>
              <p>创建和管理促销活动</p>
            </div>
            <div class="nav-card" @click="navigateToInventory">
              <div class="nav-icon">
                <el-icon :size="40" color="#AF52DE"><Grid /></el-icon>
              </div>
              <h3>库存管理</h3>
              <p>监控和管理商品库存</p>
            </div>
            <div class="nav-card" @click="navigateToAnalytics">
              <div class="nav-icon">
                <el-icon :size="40" color="#FF3B30"><DataAnalysis /></el-icon>
              </div>
              <h3>数据分析</h3>
              <p>查看销售数据和分析报告</p>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <h3>快速操作</h3>
          <div class="actions-grid">
            <el-button type="primary" size="large" @click="addProduct">
              <el-icon><Plus /></el-icon>
              添加商品
            </el-button>
            <el-button type="success" size="large" @click="processOrders">
              <el-icon><Check /></el-icon>
              处理订单
            </el-button>
            <el-button type="warning" size="large" @click="createPromotion">
              <el-icon><Star /></el-icon>
              创建活动
            </el-button>
            <el-button type="info" size="large" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activities">
          <h3>最近活动</h3>
          <div class="activity-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon :color="activity.iconColor">
                  <component :is="activity.icon" />
                </el-icon>
              </div>
              <div class="activity-content">
                <p class="activity-title">{{ activity.title }}</p>
                <p class="activity-time">{{ activity.time }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft,
  ShoppingBag,
  Money,
  User,
  TrendCharts,
  Box,
  Document,
  UserFilled,
  Present,
  Grid,
  DataAnalysis,
  Plus,
  Check,
  Star,
  Download,
  ShoppingCart,
  Edit,
  Delete
} from '@element-plus/icons-vue'

const router = useRouter()

// 商城统计数据
const mallStats = ref({
  totalProducts: 156,
  monthlySales: 89650,
  activeUsers: 1234,
  totalOrders: 567
})

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '新增商品：三七粉胶囊',
    time: '2小时前',
    icon: 'Plus',
    iconColor: '#34C759'
  },
  {
    id: 2,
    title: '处理订单：#20250121001',
    time: '4小时前',
    icon: 'Check',
    iconColor: '#007AFF'
  },
  {
    id: 3,
    title: '创建促销活动：春节特惠',
    time: '6小时前',
    icon: 'Star',
    iconColor: '#FF9500'
  },
  {
    id: 4,
    title: '库存预警：当归片库存不足',
    time: '8小时前',
    icon: 'Warning',
    iconColor: '#FF3B30'
  }
])

// 方法
const goBack = () => {
  router.push('/dashboard')
}

const navigateToProducts = () => {
  ElMessage.info('跳转到商品管理页面')
}

const navigateToOrders = () => {
  ElMessage.info('跳转到订单管理页面')
}

const navigateToCustomers = () => {
  ElMessage.info('跳转到客户管理页面')
}

const navigateToPromotions = () => {
  ElMessage.info('跳转到营销活动页面')
}

const navigateToInventory = () => {
  ElMessage.info('跳转到库存管理页面')
}

const navigateToAnalytics = () => {
  ElMessage.info('跳转到数据分析页面')
}

const addProduct = () => {
  ElMessage.success('打开添加商品对话框')
}

const processOrders = () => {
  ElMessage.success('打开订单处理页面')
}

const createPromotion = () => {
  ElMessage.success('打开创建活动对话框')
}

const exportData = () => {
  ElMessage.success('开始导出数据')
}

onMounted(() => {
  // 页面初始化
})
</script>

<style scoped>
.mall-management-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.back-button:hover {
  color: var(--primary-dark);
}

.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.content-area {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--spacing-xl);
  overflow-y: auto;
  min-height: 0;
}

.mall-management-overview {
  max-width: 1200px;
  width: 100%;
}

.stats-section {
  margin-bottom: var(--spacing-xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.stat-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-md);
}

.stat-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
}

.stat-content h3 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
}

.stat-number {
  font-size: var(--font-size-xxl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.stat-trend {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.stat-trend.positive {
  color: #52c41a;
}

.function-nav {
  margin-bottom: var(--spacing-xl);
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.nav-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
}

.nav-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.nav-icon {
  margin-bottom: var(--spacing-md);
}

.nav-card h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.nav-card p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.quick-actions {
  margin-bottom: var(--spacing-xl);
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.quick-actions h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.recent-activities {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.recent-activities h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.activity-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
}

.activity-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin: 0;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .nav-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .stats-grid,
  .nav-grid,
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
