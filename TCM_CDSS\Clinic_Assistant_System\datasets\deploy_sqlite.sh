#!/bin/bash

# =====================================================
# 中医诊所助手系统 - SQLite数据库部署脚本 (Linux/Mac)
# =====================================================

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置变量
DATABASE_FILE="tcm_clinic_system.db"

echo -e "${BLUE}=====================================================${NC}"
echo -e "${BLUE}中医诊所助手系统 - SQLite数据库部署脚本${NC}"
echo -e "${BLUE}=====================================================${NC}"
echo

# 检查SQLite是否已安装
if ! command -v sqlite3 &> /dev/null; then
    echo -e "${YELLOW}[信息] 未检测到SQLite，正在安装...${NC}"
    
    # 检测操作系统并安装SQLite
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y sqlite3
        elif command -v yum &> /dev/null; then
            sudo yum install -y sqlite
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y sqlite
        elif command -v pacman &> /dev/null; then
            sudo pacman -S sqlite
        else
            echo -e "${RED}[错误] 无法自动安装SQLite，请手动安装${NC}"
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install sqlite
        else
            echo -e "${YELLOW}[信息] 建议安装Homebrew后再运行此脚本${NC}"
            echo "安装命令: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
            exit 1
        fi
    else
        echo -e "${RED}[错误] 不支持的操作系统${NC}"
        exit 1
    fi
    
    # 再次检查SQLite是否安装成功
    if ! command -v sqlite3 &> /dev/null; then
        echo -e "${RED}[错误] SQLite安装失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}[成功] SQLite安装完成${NC}"
else
    echo -e "${GREEN}[信息] 检测到SQLite工具${NC}"
fi

# 显示SQLite版本
SQLITE_VERSION=$(sqlite3 -version | cut -d' ' -f1)
echo -e "${YELLOW}SQLite版本: $SQLITE_VERSION${NC}"
echo

# 检查数据库文件是否已存在
if [ -f "$DATABASE_FILE" ]; then
    echo -e "${YELLOW}[警告] 数据库文件 $DATABASE_FILE 已存在${NC}"
    read -p "是否覆盖现有数据库? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}[信息] 操作已取消${NC}"
        exit 0
    fi
    rm "$DATABASE_FILE"
    echo -e "${YELLOW}[信息] 已删除现有数据库文件${NC}"
fi

echo
echo -e "${YELLOW}[信息] 开始创建SQLite数据库...${NC}"

# 创建数据库和表结构
echo -e "${YELLOW}[步骤 1/3] 创建数据库表结构...${NC}"
if ! sqlite3 "$DATABASE_FILE" < sqlite/schema/create_database.sql; then
    echo -e "${RED}[错误] 数据库表结构创建失败${NC}"
    exit 1
fi
echo -e "${GREEN}[成功] 数据库表结构创建完成${NC}"

# 插入基础数据
echo -e "${YELLOW}[步骤 2/3] 插入基础数据...${NC}"
if ! sqlite3 "$DATABASE_FILE" < sqlite/data/basic_data.sql; then
    echo -e "${RED}[错误] 基础数据插入失败${NC}"
    exit 1
fi
echo -e "${GREEN}[成功] 基础数据插入完成${NC}"

# 询问是否插入示例数据
echo
read -p "是否插入示例数据? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}[步骤 3/3] 插入示例数据...${NC}"
    if ! sqlite3 "$DATABASE_FILE" < sqlite/data/sample_data.sql; then
        echo -e "${RED}[错误] 示例数据插入失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}[成功] 示例数据插入完成${NC}"
else
    echo -e "${YELLOW}[步骤 3/3] 跳过示例数据插入${NC}"
fi

# 验证数据库
echo
echo -e "${YELLOW}[验证] 检查数据库完整性...${NC}"
sqlite3 "$DATABASE_FILE" "PRAGMA integrity_check;"

echo "数据库表列表:"
sqlite3 "$DATABASE_FILE" "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"

echo
echo -e "${BLUE}=====================================================${NC}"
echo -e "${GREEN}SQLite数据库部署完成！${NC}"
echo -e "${BLUE}=====================================================${NC}"
echo
echo -e "${YELLOW}数据库信息:${NC}"
echo "  - 数据库文件: $DATABASE_FILE"
echo "  - 文件位置: $(pwd)/$DATABASE_FILE"
echo "  - 数据库类型: SQLite 3"
echo "  - 文件大小: $(du -h "$DATABASE_FILE" | cut -f1)"
echo
echo -e "${YELLOW}默认管理员账号:${NC}"
echo "  - 用户名: 13000000000"
echo "  - 密码: password"
echo "  - 角色: 管理员"
echo
echo -e "${YELLOW}数据库连接示例:${NC}"
echo "  - Node.js: sqlite3 $DATABASE_FILE"
echo "  - Python: sqlite3.connect('$DATABASE_FILE')"
echo "  - 命令行: sqlite3 $DATABASE_FILE"
echo
echo -e "${RED}注意事项:${NC}"
echo "  1. 生产环境请立即修改默认密码"
echo "  2. 建议定期备份数据库文件"
echo "  3. SQLite适合小型应用，大型应用建议使用MySQL/PostgreSQL"
echo "  4. 数据库文件可以直接复制到其他环境使用"
echo
echo -e "${YELLOW}下一步:${NC}"
echo "  1. 配置前端应用的数据库连接"
echo "  2. 启动应用服务"
echo "  3. 访问系统进行测试"
echo

# 询问是否打开数据库
echo
read -p "是否打开数据库进行查看? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    echo -e "${YELLOW}正在打开SQLite数据库...${NC}"
    echo "输入 .tables 查看所有表"
    echo "输入 .schema 查看表结构"
    echo "输入 .quit 退出数据库"
    echo
    sqlite3 "$DATABASE_FILE"
fi

echo
echo -e "${GREEN}部署完成！${NC}"
