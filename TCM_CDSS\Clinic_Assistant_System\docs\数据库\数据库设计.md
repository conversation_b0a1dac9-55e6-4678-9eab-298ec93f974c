# 中医诊所助手系统 - 数据库设计文档

## 1. 概述

本文档基于前端 `frontend/src/features` 模块分析，设计了完整的数据库表结构，涵盖了中医诊所管理系统的所有核心业务功能。

## 2. 数据库设计原则

- **业务驱动**: 基于前端功能模块的实际需求设计表结构
- **中医特色**: 突出中医诊疗的特殊需求（四诊、证型、方剂等）
- **数据完整性**: 确保数据的一致性和完整性约束
- **性能优化**: 合理设计索引和关联关系
- **扩展性**: 预留未来功能扩展的空间

## 3. 数据库表分组

### 3.1 用户与权限组 (User & Permission)
- `staff` - 员工表
- `roles` - 角色表
- `permissions` - 权限表
- `role_permissions` - 角色权限关联表

### 3.2 患者与就诊核心组 (Patient & Visit Core)
- `patients` - 患者信息表
- `appointments` - 预约记录表
- `visits` - 就诊记录表
- `patient_contacts` - 患者联系人表

### 3.3 电子病历组 (EMR - Electronic Medical Record)
- `medical_records` - 电子病历表
- `prescriptions` - 处方主表
- `prescription_items` - 处方明细表
- `medical_images` - 医疗影像表

### 3.4 药品与库存组 (Pharmacy & Inventory)
- `products` - 产品表（药品、耗材、服务）
- `inventory_stock` - 库存表
- `inventory_transactions` - 库存变动记录表
- `suppliers` - 供应商表

### 3.5 财务组 (Financial)
- `billings` - 账单表
- `billing_items` - 账单明细表
- `payments` - 支付记录表
- `refunds` - 退款记录表

### 3.6 排班与员工管理组 (Scheduling & Staff)
- `schedules` - 排班表
- `attendance` - 考勤记录表
- `staff_departments` - 科室表

### 3.7 统计与报表组 (Statistics & Reports)
- `daily_statistics` - 日统计表
- `module_usage_stats` - 模块使用统计表
- `ai_usage_stats` - AI使用统计表

### 3.8 系统配置组 (System Configuration)
- `system_settings` - 系统设置表
- `lingji_settings` - 灵机管理设置表
- `clinic_info` - 诊所信息表

### 3.9 研学中心组 (Research & Study)
- `knowledge_base` - 知识库表
- `case_studies` - 医案研究表
- `research_data` - 研究数据表
- `study_materials` - 学习资料表

### 3.10 应用与商城组 (App & Mall)
- `applications` - 应用表
- `app_downloads` - 应用下载记录表
- `mall_products` - 商城商品表
- `mall_orders` - 商城订单表

## 4. 详细表结构设计

### 4.1 用户与权限组

#### 4.1.1 staff (员工表)
**用途**: 存储所有诊所员工信息，包括医师、前台、药房人员、管理员等

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| employee_number | VARCHAR(20) | UNIQUE, NOT NULL, 员工工号 |
| full_name | VARCHAR(100) | NOT NULL, 员工姓名 |
| phone | VARCHAR(20) | UNIQUE, NOT NULL, 手机号（用于登录） |
| password_hash | VARCHAR(255) | NOT NULL, 密码哈希 |
| role_id | INT | FK -> roles.id, NOT NULL |
| department_id | INT | FK -> staff_departments.id, NULL |
| title | VARCHAR(50) | NULL, 职称/职位 |
| specialty | TEXT | NULL, 医师擅长领域 |
| avatar | VARCHAR(255) | NULL, 头像URL |
| email | VARCHAR(100) | NULL, 邮箱 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE |
| last_login_at | DATETIME | NULL, 最后登录时间 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_staff_phone` (phone)
- `idx_staff_role` (role_id)
- `idx_staff_department` (department_id)

#### 4.1.2 roles (角色表)
**用途**: 定义系统中的角色

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| name | VARCHAR(50) | UNIQUE, NOT NULL, 角色名称 |
| display_name | VARCHAR(100) | NOT NULL, 显示名称 |
| description | TEXT | NULL, 角色描述 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**预设数据**:
- admin (管理员)
- doctor (医生)
- nurse (护士)
- pharmacist (药师)
- receptionist (前台)

#### 4.1.3 permissions (权限表)
**用途**: 定义系统权限点

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| name | VARCHAR(100) | UNIQUE, NOT NULL, 权限标识 |
| display_name | VARCHAR(100) | NOT NULL, 显示名称 |
| module | VARCHAR(50) | NOT NULL, 所属模块 |
| description | TEXT | NULL, 权限描述 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

#### 4.1.4 role_permissions (角色权限关联表)
**用途**: 角色与权限的多对多关联

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| role_id | INT | FK -> roles.id, NOT NULL |
| permission_id | INT | FK -> permissions.id, NOT NULL |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**主键**: (role_id, permission_id)

### 4.2 患者与就诊核心组

#### 4.2.1 patients (患者信息表)
**用途**: 存储患者的个人档案信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| patient_number | VARCHAR(50) | UNIQUE, NOT NULL, 病历号 |
| full_name | VARCHAR(100) | NOT NULL, 姓名 |
| gender | ENUM('男', '女', '未知') | NOT NULL |
| date_of_birth | DATE | NOT NULL, 出生日期 |
| phone | VARCHAR(20) | NULL, 手机号 |
| id_card_number | VARCHAR(18) | NULL, 身份证号 |
| address | VARCHAR(255) | NULL, 联系地址 |
| allergies | TEXT | NULL, 过敏史 |
| medical_history | TEXT | NULL, 既往病史 |
| family_history | TEXT | NULL, 家族病史 |
| status | ENUM('active', 'inactive') | NOT NULL, DEFAULT 'active' |
| last_visit_date | DATE | NULL, 最后就诊日期 |
| next_appointment_date | DATE | NULL, 下次预约日期 |
| visit_count | INT | NOT NULL, DEFAULT 0, 就诊次数 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_patients_phone` (phone)
- `idx_patients_name` (full_name)
- `idx_patients_number` (patient_number)

#### 4.2.2 patient_contacts (患者联系人表)
**用途**: 存储患者紧急联系人信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| patient_id | INT | FK -> patients.id, NOT NULL |
| name | VARCHAR(100) | NOT NULL, 联系人姓名 |
| phone | VARCHAR(20) | NOT NULL, 联系电话 |
| relationship | VARCHAR(50) | NOT NULL, 关系 |
| is_emergency | BOOLEAN | NOT NULL, DEFAULT FALSE, 是否紧急联系人 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_patient_contacts_patient` (patient_id)

#### 4.2.3 appointments (预约记录表)
**用途**: 存储所有的预约信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| appointment_number | VARCHAR(50) | UNIQUE, NOT NULL, 预约号 |
| patient_id | INT | FK -> patients.id, NOT NULL |
| doctor_id | INT | FK -> staff.id, NOT NULL |
| appointment_date | DATE | NOT NULL, 预约日期 |
| appointment_time | TIME | NOT NULL, 预约时间 |
| status | ENUM('pending', 'confirmed', 'completed', 'cancelled', 'absent') | NOT NULL, DEFAULT 'pending' |
| source | ENUM('online', 'offline', 'phone') | NOT NULL, 预约来源 |
| chief_complaint | TEXT | NULL, 主诉 |
| note | TEXT | NULL, 备注信息 |
| created_by | INT | FK -> staff.id, NOT NULL, 创建人 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_appointments_patient` (patient_id)
- `idx_appointments_doctor` (doctor_id)
- `idx_appointments_date` (appointment_date)
- `idx_appointments_status` (status)

#### 4.2.4 visits (就诊记录表)
**用途**: 存储每次就诊的基本信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| visit_number | VARCHAR(50) | UNIQUE, NOT NULL, 就诊号 |
| patient_id | INT | FK -> patients.id, NOT NULL |
| doctor_id | INT | FK -> staff.id, NOT NULL |
| appointment_id | INT | FK -> appointments.id, NULL, 关联预约 |
| visit_date | DATE | NOT NULL, 就诊日期 |
| visit_start_time | DATETIME | NOT NULL, 就诊开始时间 |
| visit_end_time | DATETIME | NULL, 就诊结束时间 |
| status | ENUM('waiting', 'in_progress', 'completed', 'billed') | NOT NULL, DEFAULT 'waiting' |
| visit_type | ENUM('first_visit', 'follow_up', 'emergency') | NOT NULL, DEFAULT 'first_visit' |
| chief_complaint | TEXT | NULL, 主诉 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_visits_patient` (patient_id)
- `idx_visits_doctor` (doctor_id)
- `idx_visits_date` (visit_date)
- `idx_visits_status` (status)

### 4.3 电子病历组

#### 4.3.1 medical_records (电子病历表)
**用途**: 存储每次就诊的详细病历内容（中医特色）

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| visit_id | INT | FK -> visits.id, UNIQUE, NOT NULL |
| patient_id | INT | FK -> patients.id, NOT NULL |
| doctor_id | INT | FK -> staff.id, NOT NULL |
| chief_complaint | TEXT | NULL, 主诉 |
| present_illness | TEXT | NULL, 现病史 |
| past_history | TEXT | NULL, 既往史 |
| family_history | TEXT | NULL, 家族史 |
| personal_history | TEXT | NULL, 个人史 |
| inspection | TEXT | NULL, 望诊（神、色、形、态、舌象等） |
| auscultation | TEXT | NULL, 闻诊（语音、呼吸、咳嗽等） |
| inquiry | TEXT | NULL, 问诊（寒热、汗、痛、睡眠、二便等） |
| palpation | TEXT | NULL, 切诊（脉象、按诊等） |
| tcm_diagnosis | TEXT | NULL, 中医诊断 |
| tcm_syndrome | TEXT | NULL, 中医证型 |
| western_diagnosis | TEXT | NULL, 西医诊断 |
| treatment_principle | TEXT | NULL, 治法 |
| doctors_orders | TEXT | NULL, 医嘱 |
| follow_up_plan | TEXT | NULL, 随访计划 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_medical_records_visit` (visit_id)
- `idx_medical_records_patient` (patient_id)
- `idx_medical_records_doctor` (doctor_id)

#### 4.3.2 prescriptions (处方主表)
**用途**: 存储一张完整的处方信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| prescription_number | VARCHAR(50) | UNIQUE, NOT NULL, 处方号 |
| visit_id | INT | FK -> visits.id, NOT NULL |
| patient_id | INT | FK -> patients.id, NOT NULL |
| doctor_id | INT | FK -> staff.id, NOT NULL |
| prescription_type | ENUM('herbal', 'patent', 'western', 'injection') | NOT NULL, 处方类型 |
| status | ENUM('draft', 'confirmed', 'billed', 'dispensed', 'cancelled') | NOT NULL, DEFAULT 'draft' |
| dosage_instructions | TEXT | NULL, 服法（如：每日一剂，水煎服，分两次服用） |
| decoction_method | TEXT | NULL, 煎煮方法（如：先煎...后下...） |
| total_doses | INT | NULL, 总剂数 |
| total_amount | DECIMAL(10,2) | NULL, 处方总金额 |
| note | TEXT | NULL, 备注 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_prescriptions_visit` (visit_id)
- `idx_prescriptions_patient` (patient_id)
- `idx_prescriptions_doctor` (doctor_id)
- `idx_prescriptions_status` (status)

#### 4.3.3 prescription_items (处方明细表)
**用途**: 存储一张处方中包含的每一个药品

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| prescription_id | INT | FK -> prescriptions.id, NOT NULL |
| product_id | INT | FK -> products.id, NOT NULL |
| quantity | DECIMAL(10,2) | NOT NULL, 数量 |
| unit | VARCHAR(20) | NOT NULL, 单位 |
| unit_price | DECIMAL(10,2) | NOT NULL, 单价 |
| total_price | DECIMAL(10,2) | NOT NULL, 小计 |
| usage_method | VARCHAR(100) | NULL, 用法（如：先煎、后下、包煎等） |
| note | TEXT | NULL, 备注 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_prescription_items_prescription` (prescription_id)
- `idx_prescription_items_product` (product_id)

#### 4.3.4 medical_images (医疗影像表)
**用途**: 存储医疗影像文件信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| visit_id | INT | FK -> visits.id, NOT NULL |
| patient_id | INT | FK -> patients.id, NOT NULL |
| image_type | ENUM('xray', 'ct', 'mri', 'ultrasound', 'photo', 'other') | NOT NULL |
| file_name | VARCHAR(255) | NOT NULL, 文件名 |
| file_path | VARCHAR(500) | NOT NULL, 文件路径 |
| file_size | BIGINT | NOT NULL, 文件大小（字节） |
| mime_type | VARCHAR(100) | NOT NULL, MIME类型 |
| description | TEXT | NULL, 描述 |
| created_by | INT | FK -> staff.id, NOT NULL |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_medical_images_visit` (visit_id)
- `idx_medical_images_patient` (patient_id)

### 4.4 药品与库存组

#### 4.4.1 products (产品表)
**用途**: 定义所有可销售、可使用的项目，包括中药饮片、成药、医疗服务等

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| product_code | VARCHAR(50) | UNIQUE, NOT NULL, 产品编码 |
| name | VARCHAR(100) | NOT NULL, 项目名称 |
| pinyin_code | VARCHAR(50) | NULL, 拼音码，用于快速搜索 |
| product_type | ENUM('herbal', 'patent', 'western', 'service', 'consumable') | NOT NULL, 产品类型 |
| category | VARCHAR(50) | NULL, 分类 |
| specifications | VARCHAR(100) | NULL, 规格 |
| unit | VARCHAR(20) | NOT NULL, 基本单位 |
| cost_price | DECIMAL(10,2) | NULL, 成本价 |
| selling_price | DECIMAL(10,2) | NOT NULL, 销售价 |
| supplier_id | INT | FK -> suppliers.id, NULL |
| manufacturer | VARCHAR(100) | NULL, 生产厂家 |
| approval_number | VARCHAR(100) | NULL, 批准文号 |
| expiry_days | INT | NULL, 有效期天数 |
| storage_condition | VARCHAR(100) | NULL, 储存条件 |
| is_prescription | BOOLEAN | NOT NULL, DEFAULT FALSE, 是否处方药 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_products_code` (product_code)
- `idx_products_name` (name)
- `idx_products_pinyin` (pinyin_code)
- `idx_products_type` (product_type)

#### 4.4.2 suppliers (供应商表)
**用途**: 存储供应商信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| name | VARCHAR(100) | NOT NULL, 供应商名称 |
| contact_person | VARCHAR(50) | NULL, 联系人 |
| phone | VARCHAR(20) | NULL, 联系电话 |
| address | VARCHAR(255) | NULL, 地址 |
| email | VARCHAR(100) | NULL, 邮箱 |
| business_license | VARCHAR(100) | NULL, 营业执照号 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

#### 4.4.3 inventory_stock (库存表)
**用途**: 跟踪实体物品的库存水平

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| product_id | INT | PK, FK -> products.id |
| quantity_on_hand | DECIMAL(10,2) | NOT NULL, DEFAULT 0, 当前库存量 |
| reserved_quantity | DECIMAL(10,2) | NOT NULL, DEFAULT 0, 预留数量 |
| available_quantity | DECIMAL(10,2) | GENERATED ALWAYS AS (quantity_on_hand - reserved_quantity), 可用数量 |
| location | VARCHAR(100) | NULL, 存储位置 |
| low_stock_threshold | DECIMAL(10,2) | NULL, 低库存预警阈值 |
| last_updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_inventory_stock_low` (low_stock_threshold, quantity_on_hand)

#### 4.4.4 inventory_transactions (库存变动记录表)
**用途**: 记录所有库存变动

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| product_id | INT | FK -> products.id, NOT NULL |
| transaction_type | ENUM('in', 'out', 'adjust', 'expired', 'damaged') | NOT NULL |
| quantity | DECIMAL(10,2) | NOT NULL, 变动数量（正数为入库，负数为出库） |
| unit_cost | DECIMAL(10,2) | NULL, 单位成本 |
| reference_type | ENUM('purchase', 'prescription', 'adjustment', 'waste') | NULL, 关联类型 |
| reference_id | INT | NULL, 关联ID |
| batch_number | VARCHAR(50) | NULL, 批次号 |
| expiry_date | DATE | NULL, 有效期 |
| note | TEXT | NULL, 备注 |
| operator_id | INT | FK -> staff.id, NOT NULL, 操作人 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_inventory_transactions_product` (product_id)
- `idx_inventory_transactions_type` (transaction_type)
- `idx_inventory_transactions_date` (created_at)

### 4.5 财务组

#### 4.5.1 billings (账单表)
**用途**: 为每一次就诊生成一张总账单

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| billing_number | VARCHAR(50) | UNIQUE, NOT NULL, 账单号 |
| visit_id | INT | FK -> visits.id, UNIQUE, NOT NULL |
| patient_id | INT | FK -> patients.id, NOT NULL |
| total_amount | DECIMAL(10,2) | NOT NULL, 总金额 |
| paid_amount | DECIMAL(10,2) | NOT NULL, DEFAULT 0, 已付金额 |
| discount_amount | DECIMAL(10,2) | NOT NULL, DEFAULT 0, 优惠金额 |
| status | ENUM('pending', 'partial', 'paid', 'cancelled') | NOT NULL, DEFAULT 'pending' |
| billing_date | DATE | NOT NULL, 账单日期 |
| due_date | DATE | NULL, 到期日期 |
| note | TEXT | NULL, 备注 |
| created_by | INT | FK -> staff.id, NOT NULL |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_billings_visit` (visit_id)
- `idx_billings_patient` (patient_id)
- `idx_billings_status` (status)
- `idx_billings_date` (billing_date)

#### 4.5.2 billing_items (账单明细表)
**用途**: 记录一张账单中包含的所有费用项

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| billing_id | INT | FK -> billings.id, NOT NULL |
| item_type | ENUM('service', 'prescription', 'material', 'other') | NOT NULL, 项目类型 |
| item_name | VARCHAR(100) | NOT NULL, 项目名称 |
| product_id | INT | FK -> products.id, NULL, 关联产品 |
| prescription_id | INT | FK -> prescriptions.id, NULL, 关联处方 |
| quantity | DECIMAL(10,2) | NOT NULL, 数量 |
| unit_price | DECIMAL(10,2) | NOT NULL, 单价 |
| total_price | DECIMAL(10,2) | NOT NULL, 小计 |
| discount_rate | DECIMAL(5,2) | NOT NULL, DEFAULT 0, 折扣率 |
| discount_amount | DECIMAL(10,2) | NOT NULL, DEFAULT 0, 折扣金额 |
| final_amount | DECIMAL(10,2) | NOT NULL, 最终金额 |
| note | TEXT | NULL, 备注 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_billing_items_billing` (billing_id)
- `idx_billing_items_product` (product_id)
- `idx_billing_items_prescription` (prescription_id)

#### 4.5.3 payments (支付记录表)
**用途**: 记录所有支付信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| payment_number | VARCHAR(50) | UNIQUE, NOT NULL, 支付单号 |
| billing_id | INT | FK -> billings.id, NOT NULL |
| payment_method | ENUM('cash', 'card', 'wechat', 'alipay', 'bank_transfer') | NOT NULL |
| amount | DECIMAL(10,2) | NOT NULL, 支付金额 |
| transaction_id | VARCHAR(100) | NULL, 第三方交易号 |
| status | ENUM('pending', 'success', 'failed', 'cancelled') | NOT NULL, DEFAULT 'pending' |
| payment_date | DATETIME | NOT NULL, 支付时间 |
| note | TEXT | NULL, 备注 |
| operator_id | INT | FK -> staff.id, NOT NULL, 操作员 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_payments_billing` (billing_id)
- `idx_payments_date` (payment_date)
- `idx_payments_method` (payment_method)

#### 4.5.4 refunds (退款记录表)
**用途**: 记录退款信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| refund_number | VARCHAR(50) | UNIQUE, NOT NULL, 退款单号 |
| payment_id | INT | FK -> payments.id, NOT NULL |
| billing_id | INT | FK -> billings.id, NOT NULL |
| refund_amount | DECIMAL(10,2) | NOT NULL, 退款金额 |
| refund_reason | TEXT | NOT NULL, 退款原因 |
| status | ENUM('pending', 'approved', 'rejected', 'completed') | NOT NULL, DEFAULT 'pending' |
| refund_date | DATETIME | NULL, 退款时间 |
| approved_by | INT | FK -> staff.id, NULL, 审批人 |
| operator_id | INT | FK -> staff.id, NOT NULL, 操作员 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_refunds_payment` (payment_id)
- `idx_refunds_billing` (billing_id)
- `idx_refunds_status` (status)

### 4.6 排班与员工管理组

#### 4.6.1 staff_departments (科室表)
**用途**: 定义诊所的科室信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| name | VARCHAR(50) | UNIQUE, NOT NULL, 科室名称 |
| description | TEXT | NULL, 科室描述 |
| head_id | INT | FK -> staff.id, NULL, 科室主任 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

#### 4.6.2 schedules (排班表)
**用途**: 存储员工排班信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| staff_id | INT | FK -> staff.id, NOT NULL |
| schedule_date | DATE | NOT NULL, 排班日期 |
| shift_type | ENUM('morning', 'afternoon', 'evening', 'night', 'full_day') | NOT NULL, 班次类型 |
| start_time | TIME | NOT NULL, 开始时间 |
| end_time | TIME | NOT NULL, 结束时间 |
| max_appointments | INT | NULL, 最大预约数 |
| status | ENUM('scheduled', 'confirmed', 'cancelled', 'completed') | NOT NULL, DEFAULT 'scheduled' |
| note | TEXT | NULL, 备注 |
| created_by | INT | FK -> staff.id, NOT NULL |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_schedules_staff_date` (staff_id, schedule_date)
- `idx_schedules_date` (schedule_date)
- `idx_schedules_status` (status)

#### 4.6.3 attendance (考勤记录表)
**用途**: 记录员工考勤信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| staff_id | INT | FK -> staff.id, NOT NULL |
| attendance_date | DATE | NOT NULL, 考勤日期 |
| check_in_time | DATETIME | NULL, 签到时间 |
| check_out_time | DATETIME | NULL, 签退时间 |
| work_hours | DECIMAL(4,2) | NULL, 工作小时数 |
| status | ENUM('present', 'absent', 'late', 'early_leave', 'overtime') | NOT NULL |
| note | TEXT | NULL, 备注 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_attendance_staff_date` (staff_id, attendance_date)
- `idx_attendance_date` (attendance_date)

### 4.7 统计与报表组

#### 4.7.1 daily_statistics (日统计表)
**用途**: 存储每日业务统计数据

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| stat_date | DATE | UNIQUE, NOT NULL, 统计日期 |
| patient_count | INT | NOT NULL, DEFAULT 0, 患者数量 |
| new_patient_count | INT | NOT NULL, DEFAULT 0, 新患者数量 |
| visit_count | INT | NOT NULL, DEFAULT 0, 就诊次数 |
| appointment_count | INT | NOT NULL, DEFAULT 0, 预约数量 |
| prescription_count | INT | NOT NULL, DEFAULT 0, 处方数量 |
| total_revenue | DECIMAL(12,2) | NOT NULL, DEFAULT 0, 总收入 |
| service_revenue | DECIMAL(12,2) | NOT NULL, DEFAULT 0, 服务收入 |
| medicine_revenue | DECIMAL(12,2) | NOT NULL, DEFAULT 0, 药品收入 |
| avg_visit_duration | INT | NULL, 平均就诊时长（分钟） |
| ai_usage_count | INT | NOT NULL, DEFAULT 0, AI使用次数 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_daily_statistics_date` (stat_date)

#### 4.7.2 module_usage_stats (模块使用统计表)
**用途**: 统计各功能模块的使用情况

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| module_name | VARCHAR(50) | NOT NULL, 模块名称 |
| stat_date | DATE | NOT NULL, 统计日期 |
| usage_count | INT | NOT NULL, DEFAULT 0, 使用次数 |
| unique_users | INT | NOT NULL, DEFAULT 0, 独立用户数 |
| avg_session_duration | INT | NULL, 平均会话时长（秒） |
| error_count | INT | NOT NULL, DEFAULT 0, 错误次数 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_module_usage_stats_module_date` (module_name, stat_date)

#### 4.7.3 ai_usage_stats (AI使用统计表)
**用途**: 统计AI功能的使用情况和效果

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| feature_name | VARCHAR(50) | NOT NULL, AI功能名称 |
| stat_date | DATE | NOT NULL, 统计日期 |
| usage_count | INT | NOT NULL, DEFAULT 0, 使用次数 |
| success_count | INT | NOT NULL, DEFAULT 0, 成功次数 |
| accuracy_rate | DECIMAL(5,2) | NULL, 准确率 |
| avg_response_time | INT | NULL, 平均响应时间（毫秒） |
| user_satisfaction | DECIMAL(3,2) | NULL, 用户满意度 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_ai_usage_stats_feature_date` (feature_name, stat_date)

### 4.8 系统配置组

#### 4.8.1 clinic_info (诊所信息表)
**用途**: 存储诊所基本信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| name | VARCHAR(100) | NOT NULL, 诊所名称 |
| license_number | VARCHAR(100) | NULL, 医疗机构执业许可证号 |
| legal_person | VARCHAR(50) | NULL, 法人代表 |
| phone | VARCHAR(20) | NULL, 联系电话 |
| address | VARCHAR(255) | NULL, 地址 |
| business_hours_start | TIME | NULL, 营业开始时间 |
| business_hours_end | TIME | NULL, 营业结束时间 |
| logo_url | VARCHAR(255) | NULL, 诊所Logo |
| description | TEXT | NULL, 诊所简介 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

#### 4.8.2 system_settings (系统设置表)
**用途**: 存储系统配置参数

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| setting_key | VARCHAR(100) | UNIQUE, NOT NULL, 设置键 |
| setting_value | TEXT | NULL, 设置值 |
| setting_type | ENUM('string', 'number', 'boolean', 'json') | NOT NULL, 数据类型 |
| category | VARCHAR(50) | NOT NULL, 分类 |
| description | TEXT | NULL, 描述 |
| is_public | BOOLEAN | NOT NULL, DEFAULT FALSE, 是否公开 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_system_settings_category` (category)

#### 4.8.3 lingji_settings (灵机管理设置表)
**用途**: 存储AI灵机功能的配置参数

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| feature_name | VARCHAR(50) | UNIQUE, NOT NULL, 功能名称 |
| is_enabled | BOOLEAN | NOT NULL, DEFAULT TRUE, 是否启用 |
| confidence_threshold | DECIMAL(3,2) | NULL, 置信度阈值 |
| response_mode | ENUM('fast', 'balanced', 'accurate') | NOT NULL, DEFAULT 'balanced' |
| max_suggestions | INT | NULL, 最大建议数 |
| config_json | JSON | NULL, 详细配置 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

### 4.9 研学中心组

#### 4.9.1 knowledge_base (知识库表)
**用途**: 存储中医知识库内容

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| title | VARCHAR(200) | NOT NULL, 标题 |
| content_type | ENUM('syndrome', 'formula', 'herb', 'acupoint', 'theory', 'case') | NOT NULL |
| content | LONGTEXT | NOT NULL, 内容 |
| keywords | TEXT | NULL, 关键词 |
| category | VARCHAR(50) | NULL, 分类 |
| source | VARCHAR(100) | NULL, 来源 |
| author | VARCHAR(50) | NULL, 作者 |
| difficulty_level | ENUM('beginner', 'intermediate', 'advanced') | NOT NULL, DEFAULT 'intermediate' |
| view_count | INT | NOT NULL, DEFAULT 0, 浏览次数 |
| like_count | INT | NOT NULL, DEFAULT 0, 点赞次数 |
| is_published | BOOLEAN | NOT NULL, DEFAULT TRUE |
| created_by | INT | FK -> staff.id, NOT NULL |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_knowledge_base_type` (content_type)
- `idx_knowledge_base_category` (category)
- `idx_knowledge_base_keywords` (keywords(100))

#### 4.9.2 case_studies (医案研究表)
**用途**: 存储医案研究数据

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| title | VARCHAR(200) | NOT NULL, 医案标题 |
| patient_info | TEXT | NOT NULL, 患者信息（脱敏） |
| chief_complaint | TEXT | NOT NULL, 主诉 |
| diagnosis_process | LONGTEXT | NOT NULL, 诊断过程 |
| treatment_plan | LONGTEXT | NOT NULL, 治疗方案 |
| follow_up | TEXT | NULL, 随访记录 |
| outcome | TEXT | NULL, 治疗结果 |
| analysis | LONGTEXT | NULL, 医案分析 |
| teaching_points | TEXT | NULL, 教学要点 |
| tags | TEXT | NULL, 标签 |
| difficulty_level | ENUM('beginner', 'intermediate', 'advanced') | NOT NULL |
| is_classic | BOOLEAN | NOT NULL, DEFAULT FALSE, 是否经典医案 |
| created_by | INT | FK -> staff.id, NOT NULL |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_case_studies_tags` (tags(100))
- `idx_case_studies_level` (difficulty_level)

#### 4.9.3 study_materials (学习资料表)
**用途**: 存储学习资料信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| title | VARCHAR(200) | NOT NULL, 资料标题 |
| material_type | ENUM('video', 'audio', 'document', 'image', 'link') | NOT NULL |
| file_path | VARCHAR(500) | NULL, 文件路径 |
| file_size | BIGINT | NULL, 文件大小 |
| duration | INT | NULL, 时长（秒） |
| description | TEXT | NULL, 描述 |
| category | VARCHAR(50) | NULL, 分类 |
| tags | TEXT | NULL, 标签 |
| difficulty_level | ENUM('beginner', 'intermediate', 'advanced') | NOT NULL |
| download_count | INT | NOT NULL, DEFAULT 0, 下载次数 |
| is_free | BOOLEAN | NOT NULL, DEFAULT TRUE, 是否免费 |
| price | DECIMAL(8,2) | NULL, 价格 |
| created_by | INT | FK -> staff.id, NOT NULL |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_study_materials_type` (material_type)
- `idx_study_materials_category` (category)

#### 4.9.4 research_data (研究数据表)
**用途**: 存储用于科研的统计数据

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| research_type | VARCHAR(50) | NOT NULL, 研究类型 |
| data_source | VARCHAR(100) | NOT NULL, 数据来源 |
| time_period_start | DATE | NOT NULL, 时间段开始 |
| time_period_end | DATE | NOT NULL, 时间段结束 |
| data_content | JSON | NOT NULL, 数据内容 |
| analysis_result | TEXT | NULL, 分析结果 |
| created_by | INT | FK -> staff.id, NOT NULL |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_research_data_type` (research_type)
- `idx_research_data_period` (time_period_start, time_period_end)

### 4.10 应用与商城组

#### 4.10.1 applications (应用表)
**用途**: 存储应用中心的应用信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| name | VARCHAR(100) | NOT NULL, 应用名称 |
| package_name | VARCHAR(100) | UNIQUE, NOT NULL, 包名 |
| version | VARCHAR(20) | NOT NULL, 版本号 |
| description | TEXT | NULL, 应用描述 |
| icon_url | VARCHAR(255) | NULL, 图标URL |
| download_url | VARCHAR(500) | NOT NULL, 下载链接 |
| file_size | BIGINT | NOT NULL, 文件大小 |
| category | VARCHAR(50) | NULL, 分类 |
| developer | VARCHAR(100) | NULL, 开发者 |
| is_free | BOOLEAN | NOT NULL, DEFAULT TRUE, 是否免费 |
| price | DECIMAL(8,2) | NULL, 价格 |
| rating | DECIMAL(2,1) | NULL, 评分 |
| download_count | INT | NOT NULL, DEFAULT 0, 下载次数 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_applications_category` (category)
- `idx_applications_package` (package_name)

#### 4.10.2 app_downloads (应用下载记录表)
**用途**: 记录应用下载历史

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| application_id | INT | FK -> applications.id, NOT NULL |
| user_id | INT | FK -> staff.id, NOT NULL |
| download_time | DATETIME | NOT NULL, 下载时间 |
| ip_address | VARCHAR(45) | NULL, IP地址 |
| user_agent | TEXT | NULL, 用户代理 |
| status | ENUM('started', 'completed', 'failed') | NOT NULL, DEFAULT 'started' |

**索引**:
- `idx_app_downloads_app` (application_id)
- `idx_app_downloads_user` (user_id)
- `idx_app_downloads_time` (download_time)

#### 4.10.3 mall_products (商城商品表)
**用途**: 存储商城商品信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| product_code | VARCHAR(50) | UNIQUE, NOT NULL, 商品编码 |
| name | VARCHAR(100) | NOT NULL, 商品名称 |
| description | TEXT | NULL, 商品描述 |
| category | VARCHAR(50) | NULL, 商品分类 |
| brand | VARCHAR(50) | NULL, 品牌 |
| specifications | VARCHAR(100) | NULL, 规格 |
| unit | VARCHAR(20) | NOT NULL, 单位 |
| price | DECIMAL(10,2) | NOT NULL, 价格 |
| stock_quantity | INT | NOT NULL, DEFAULT 0, 库存数量 |
| min_order_quantity | INT | NOT NULL, DEFAULT 1, 最小订购量 |
| images | JSON | NULL, 商品图片 |
| supplier_id | INT | FK -> suppliers.id, NULL |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_mall_products_category` (category)
- `idx_mall_products_code` (product_code)

#### 4.10.4 mall_orders (商城订单表)
**用途**: 存储商城订单信息

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| order_number | VARCHAR(50) | UNIQUE, NOT NULL, 订单号 |
| user_id | INT | FK -> staff.id, NOT NULL, 下单用户 |
| total_amount | DECIMAL(10,2) | NOT NULL, 订单总金额 |
| shipping_fee | DECIMAL(8,2) | NOT NULL, DEFAULT 0, 运费 |
| discount_amount | DECIMAL(8,2) | NOT NULL, DEFAULT 0, 优惠金额 |
| final_amount | DECIMAL(10,2) | NOT NULL, 最终金额 |
| status | ENUM('pending', 'paid', 'shipped', 'delivered', 'cancelled') | NOT NULL, DEFAULT 'pending' |
| shipping_address | TEXT | NOT NULL, 收货地址 |
| contact_phone | VARCHAR(20) | NOT NULL, 联系电话 |
| note | TEXT | NULL, 备注 |
| order_date | DATETIME | NOT NULL, 下单时间 |
| payment_date | DATETIME | NULL, 支付时间 |
| shipping_date | DATETIME | NULL, 发货时间 |
| delivery_date | DATETIME | NULL, 收货时间 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

**索引**:
- `idx_mall_orders_user` (user_id)
- `idx_mall_orders_status` (status)
- `idx_mall_orders_date` (order_date)

#### 4.10.5 mall_order_items (商城订单明细表)
**用途**: 存储订单商品明细

| 字段名 | 数据类型 | 约束/备注 |
|--------|----------|-----------|
| id | INT | PK, AUTO_INCREMENT |
| order_id | INT | FK -> mall_orders.id, NOT NULL |
| product_id | INT | FK -> mall_products.id, NOT NULL |
| quantity | INT | NOT NULL, 数量 |
| unit_price | DECIMAL(10,2) | NOT NULL, 单价 |
| total_price | DECIMAL(10,2) | NOT NULL, 小计 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**索引**:
- `idx_mall_order_items_order` (order_id)
- `idx_mall_order_items_product` (product_id)

## 5. 模块与表的关系映射

### 5.1 前端模块对应的数据表

#### appointments (预约挂号模块)
**相关表**:
- `appointments` - 预约记录表（主表）
- `patients` - 患者信息表
- `staff` - 员工表（医生信息）
- `schedules` - 排班表

**业务关系**:
- 预约关联患者和医生
- 预约时间需要检查医生排班
- 预约状态管理完整生命周期

#### patients (患者管理模块)
**相关表**:
- `patients` - 患者信息表（主表）
- `patient_contacts` - 患者联系人表
- `visits` - 就诊记录表
- `medical_records` - 电子病历表
- `appointments` - 预约记录表

**业务关系**:
- 患者基本信息管理
- 关联就诊历史和病历
- 管理紧急联系人信息

#### outpatient (门诊工作站模块)
**相关表**:
- `visits` - 就诊记录表（主表）
- `medical_records` - 电子病历表
- `prescriptions` - 处方主表
- `prescription_items` - 处方明细表
- `patients` - 患者信息表
- `appointments` - 预约记录表

**业务关系**:
- 管理就诊流程
- 录入病历信息
- 开具处方

#### billing (划价收费模块)
**相关表**:
- `billings` - 账单表（主表）
- `billing_items` - 账单明细表
- `payments` - 支付记录表
- `visits` - 就诊记录表
- `prescriptions` - 处方主表

**业务关系**:
- 根据就诊和处方生成账单
- 处理支付流程
- 管理收费状态

#### pharmacy (药房管理模块)
**相关表**:
- `prescriptions` - 处方主表（主表）
- `prescription_items` - 处方明细表
- `products` - 产品表
- `inventory_stock` - 库存表
- `inventory_transactions` - 库存变动记录表

**业务关系**:
- 处方配药管理
- 库存扣减
- 发药记录

#### inventory (库存管理模块)
**相关表**:
- `products` - 产品表（主表）
- `inventory_stock` - 库存表
- `inventory_transactions` - 库存变动记录表
- `suppliers` - 供应商表

**业务关系**:
- 药品库存管理
- 进销存记录
- 供应商管理

#### staff (员工管理模块)
**相关表**:
- `staff` - 员工表（主表）
- `roles` - 角色表
- `permissions` - 权限表
- `role_permissions` - 角色权限关联表
- `staff_departments` - 科室表
- `attendance` - 考勤记录表

**业务关系**:
- 员工档案管理
- 权限分配
- 考勤管理

#### scheduling (排班管理模块)
**相关表**:
- `schedules` - 排班表（主表）
- `staff` - 员工表
- `appointments` - 预约记录表

**业务关系**:
- 医生排班安排
- 预约时间冲突检查
- 班次管理

#### reports (统计报表模块)
**相关表**:
- `daily_statistics` - 日统计表（主表）
- `module_usage_stats` - 模块使用统计表
- `ai_usage_stats` - AI使用统计表
- 所有业务表（用于统计分析）

**业务关系**:
- 业务数据统计
- 报表生成
- 趋势分析

#### settings (系统设置模块)
**相关表**:
- `system_settings` - 系统设置表（主表）
- `lingji_settings` - 灵机管理设置表
- `clinic_info` - 诊所信息表
- `roles` - 角色表
- `permissions` - 权限表

**业务关系**:
- 系统参数配置
- 权限管理
- AI功能设置

#### family-doctor (家医管理模块)
**相关表**:
- `patients` - 患者信息表（主表）
- `visits` - 就诊记录表
- `medical_records` - 电子病历表
- `staff` - 员工表（家庭医生）

**业务关系**:
- 家庭医生与患者绑定
- 慢病管理
- 健康档案

#### research-center (研学中心模块)
**相关表**:
- `knowledge_base` - 知识库表（主表）
- `case_studies` - 医案研究表
- `study_materials` - 学习资料表
- `research_data` - 研究数据表

**业务关系**:
- 知识库管理
- 医案研究
- 学习资源

#### app-center (应用中心模块)
**相关表**:
- `applications` - 应用表（主表）
- `app_downloads` - 应用下载记录表
- `staff` - 员工表（用户）

**业务关系**:
- 应用管理
- 下载记录
- 用户权限

#### mall-management (商城管理模块)
**相关表**:
- `mall_products` - 商城商品表（主表）
- `mall_orders` - 商城订单表
- `mall_order_items` - 商城订单明细表
- `suppliers` - 供应商表

**业务关系**:
- 商品管理
- 订单处理
- 供应链管理

#### data-center (数据中心模块)
**相关表**:
- `daily_statistics` - 日统计表（主表）
- `module_usage_stats` - 模块使用统计表
- `ai_usage_stats` - AI使用统计表
- 所有业务表（数据源）

**业务关系**:
- 数据收集
- 统计分析
- 为研学中心提供数据支持

### 5.2 核心业务流程的表关系

#### 完整就诊流程
1. **预约阶段**: `appointments` ← `patients` + `staff` + `schedules`
2. **就诊阶段**: `visits` ← `appointments` + `patients` + `staff`
3. **病历阶段**: `medical_records` ← `visits`
4. **处方阶段**: `prescriptions` + `prescription_items` ← `visits` + `products`
5. **收费阶段**: `billings` + `billing_items` ← `visits` + `prescriptions`
6. **支付阶段**: `payments` ← `billings`
7. **发药阶段**: `inventory_transactions` ← `prescriptions` + `inventory_stock`

#### 权限管理流程
1. **角色定义**: `roles` + `permissions` + `role_permissions`
2. **用户分配**: `staff` ← `roles`
3. **权限验证**: 通过 `staff.role_id` → `role_permissions` → `permissions`

#### 统计分析流程
1. **数据收集**: 从各业务表收集数据
2. **日统计**: 汇总到 `daily_statistics`
3. **模块统计**: 记录到 `module_usage_stats`
4. **AI统计**: 记录到 `ai_usage_stats`
5. **研究分析**: 存储到 `research_data`

## 6. 索引优化建议

### 6.1 高频查询索引
- 患者姓名、手机号搜索
- 预约日期、医生查询
- 就诊状态筛选
- 处方状态查询
- 库存预警查询

### 6.2 复合索引
- `(patient_id, visit_date)` - 患者就诊历史
- `(doctor_id, appointment_date)` - 医生预约查询
- `(product_id, transaction_date)` - 库存变动查询

### 6.3 性能优化
- 大表分区（按日期）
- 历史数据归档
- 读写分离
- 缓存热点数据

## 7. 数据完整性约束

### 7.1 外键约束
- 所有关联表都设置了外键约束
- 级联删除策略需要谨慎设计
- 重要数据采用软删除

### 7.2 业务规则约束
- 预约时间不能冲突
- 库存不能为负数
- 账单金额必须匹配明细
- 处方必须有医生签名

### 7.3 数据验证
- 手机号格式验证
- 身份证号校验
- 金额精度控制
- 日期范围检查

## 8. 扩展性考虑

### 8.1 预留字段
- 各主要表都预留了扩展字段
- JSON字段用于灵活配置
- 状态枚举可扩展

### 8.2 模块化设计
- 表按业务模块分组
- 模块间松耦合
- 便于功能扩展

### 8.3 多租户支持
- 预留租户ID字段
- 数据隔离设计
- 权限体系支持多租户

---

**文档版本**: v1.0
**创建日期**: 2025-06-24
**最后更新**: 2025-06-24
**维护人员**: 开发团队