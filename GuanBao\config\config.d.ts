/**
 * GuanBao AI Chat 配置类型定义
 */

export interface ProjectConfig {
  name: string;
  version: string;
  description: string;
}

export interface FrontendDevConfig {
  host: string;
  port: number;
  open: boolean;
  cors: boolean;
}

export interface FrontendBuildConfig {
  outDir: string;
  assetsDir: string;
}

export interface FrontendConfig {
  host: string;
  port: number;
  dev: FrontendDevConfig;
  build: FrontendBuildConfig;
}

export interface BackendDevConfig {
  host: string;
  port: number;
  debug: boolean;
  reload: boolean;
}

export interface BackendProdConfig {
  host: string;
  port: number;
  debug: boolean;
  workers: number;
}

export interface BackendConfig {
  host: string;
  port: number;
  dev: BackendDevConfig;
  prod: BackendProdConfig;
}

export interface ApiEndpoints {
  health: string;
  send_verification_code: string;
  verify_login: string;
  chat_completions: string;
}

export interface ApiConfig {
  base_url: string;
  endpoints: ApiEndpoints;
  timeout: number;
  retry_attempts: number;
}

export interface DatabaseConfig {
  type: string;
  url: string;
  pool_size: number;
  max_overflow: number;
}

export interface SecurityConfig {
  secret_key: string;
  api_key: string;
  session_timeout: number;
  cors_origins: string[];
}

export interface LoggingConfig {
  level: string;
  format: string;
  file: string;
  max_size: string;
  backup_count: number;
}

export interface FeaturesConfig {
  registration_enabled: boolean;
  invited_users_only: boolean;
  max_sessions_per_user: number;
  max_message_length: number;
  stream_response: boolean;
}

export interface AppConfig {
  project: ProjectConfig;
  frontend: FrontendConfig;
  backend: BackendConfig;
  api: ApiConfig;
  database: DatabaseConfig;
  security: SecurityConfig;
  logging: LoggingConfig;
  features: FeaturesConfig;
}

export interface DevServerConfig {
  host: string;
  port: number;
  open: boolean;
  cors: boolean;
  proxy: {
    [key: string]: {
      target: string;
      changeOrigin: boolean;
      secure: boolean;
    };
  };
}

export declare class FrontendConfig {
  constructor(configFile?: string);
  
  get(key: string, defaultValue?: any): any;
  set(key: string, value: any): void;
  save(): void;
  
  get frontendHost(): string;
  get frontendPort(): number;
  get backendHost(): string;
  get backendPort(): number;
  get apiBaseUrl(): string;
  get apiEndpoints(): ApiEndpoints;
  get corsOrigins(): string[];
  
  getApiUrl(endpoint: string): string;
  getDevServerConfig(): DevServerConfig;
  getBuildConfig(): FrontendBuildConfig;
  toString(): string;
}

export declare const config: FrontendConfig;
export default config;
