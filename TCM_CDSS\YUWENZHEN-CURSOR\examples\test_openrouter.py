#!/usr/bin/env python3
"""
OpenRouter集成测试脚本
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 动态将项目根目录加入 sys.path
ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)

from src.utils.openrouter_client import OpenRouterClient, OpenRouterChatModel
from agents.chief_complaint_agent import ChiefComplaintAgent
from agents.present_illness_agent import PresentIllnessAgent


def test_openrouter_client():
    """测试OpenRouter客户端"""
    print("=== 测试OpenRouter客户端 ===")
    
    try:
        client = OpenRouterClient()
        
        # 测试获取可用模型
        models = client.get_available_models()
        print(f"可用模型数量: {len(models)}")
        
        # 测试聊天接口
        messages = [
            {"role": "user", "content": "你好，请简单介绍一下自己。"}
        ]
        
        response = client.chat_completion(messages)
        print("聊天响应:", response.get("choices", [{}])[0].get("message", {}).get("content", ""))
        
        print("✅ OpenRouter客户端测试通过")
        
    except Exception as e:
        print(f"❌ OpenRouter客户端测试失败: {e}")


def test_chief_complaint_agent():
    """测试主诉采集智能体"""
    print("\n=== 测试主诉采集智能体 ===")
    
    try:
        agent = ChiefComplaintAgent()
        
        # 测试询问有无
        context = {}
        has_question = agent.ask_has_or_not(context)
        print(f"询问有无: {has_question}")
        
        # 测试追问详情
        has_result = {"has": True, "confidence": 0.9}
        details_question = agent.ask_details_if_has(context, has_result)
        print(f"追问详情: {details_question}")
        
        # 测试工作流程
        context = {
            "patient_response": "我最近头痛得厉害",
            "detailed_response": "头痛在太阳穴，持续3天了，晚上加重"
        }
        
        response = agent.agent_workflow(context)
        print(f"工作流响应: {response.response}")
        print(f"提取数据: {response.extracted_data}")
        print(f"实体: {response.entities}")
        
        print("✅ 主诉采集智能体测试通过")
        
    except Exception as e:
        print(f"❌ 主诉采集智能体测试失败: {e}")


def test_present_illness_agent():
    """测试现病史采集智能体"""
    print("\n=== 测试现病史采集智能体 ===")
    
    try:
        agent = PresentIllnessAgent()
        
        # 测试询问有无
        context = {}
        has_question = agent.ask_has_or_not(context)
        print(f"询问有无: {has_question}")
        
        # 测试追问详情
        has_result = {"has": True, "confidence": 0.9}
        details_question = agent.ask_details_if_has(context, has_result)
        print(f"追问详情: {details_question}")
        
        # 测试下一个类别
        agent.next_category()
        next_question = agent.ask_has_or_not(context)
        print(f"下一个类别: {next_question}")
        
        print("✅ 现病史采集智能体测试通过")
        
    except Exception as e:
        print(f"❌ 现病史采集智能体测试失败: {e}")


def main():
    """主函数"""
    print("开始OpenRouter集成测试...")
    
    # 检查环境变量
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key or api_key == "your-openrouter-api-key-here":
        print("❌ 请设置OPENROUTER_API_KEY环境变量")
        return
    
    # 运行测试
    test_openrouter_client()
    test_chief_complaint_agent()
    test_present_illness_agent()
    
    print("\n🎉 所有测试完成！")


if __name__ == "__main__":
    main() 