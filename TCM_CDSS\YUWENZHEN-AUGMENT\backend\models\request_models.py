"""
请求数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional

class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息", min_length=1, max_length=1000)
    session_id: Optional[str] = Field(None, description="会话ID")

class InquiryStartRequest(BaseModel):
    """开始问诊请求模型"""
    patient_name: str = Field(..., description="患者姓名", min_length=1, max_length=50)
    patient_age: int = Field(..., description="患者年龄", ge=1, le=150)
    patient_gender: str = Field(..., description="患者性别", pattern="^(男|女)$")
    
class HealthCheckRequest(BaseModel):
    """健康检查请求模型"""
    detailed: bool = Field(False, description="是否返回详细信息")
