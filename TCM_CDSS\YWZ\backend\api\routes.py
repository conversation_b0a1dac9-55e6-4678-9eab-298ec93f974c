"""
API路由定义
"""

from fastapi import APIRouter, HTTPException
from datetime import datetime

# 导入子路由
from .health import router as health_router
from .chat import router as chat_router
from .inquiry import router as inquiry_router
from .workflow_inquiry import router as workflow_router

# 创建主路由
router = APIRouter()

# 注册子路由
router.include_router(health_router, tags=["健康检查"])
router.include_router(chat_router, tags=["聊天"])
router.include_router(inquiry_router, prefix="/inquiry", tags=["问诊"])
router.include_router(workflow_router, tags=["工作流程问诊"])

@router.get("/")
async def api_root():
    """API根路径"""
    return {
        "message": "中医预问诊智能体系统 API",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "health": "/api/health",
            "chat": "/api/chat",
            "inquiry": "/api/inquiry/start",
            "workflow": "/api/workflow/start",
            "docs": "/api/docs"
        }
    }

@router.get("/info")
async def api_info():
    """API信息"""
    from config.settings import settings
    
    return {
        "system": "中医预问诊智能体系统",
        "version": "2.0.0",
        "architecture": "MVP - 前后端分离",
        "llm_service": settings.llm_service,
        "llm_config": {
            "service": settings.llm_service,
            "model": settings.ollama_model if settings.is_ollama_enabled else settings.default_model,
            "base_url": settings.ollama_base_url if settings.is_ollama_enabled else settings.openrouter_base_url
        },
        "endpoints": {
            "frontend": {
                "home": "/",
                "ios": "/pages/ios",
                "simple": "/pages/simple",
                "agent": "/pages/agent",
                "full": "/pages/full"
            },
            "api": {
                "health": "/api/health",
                "chat": "/api/chat",
                "inquiry_start": "/api/inquiry/start",
                "workflow_start": "/api/workflow/start",
                "workflow_demo": "/api/workflow/demo",
                "docs": "/api/docs"
            }
        }
    }
