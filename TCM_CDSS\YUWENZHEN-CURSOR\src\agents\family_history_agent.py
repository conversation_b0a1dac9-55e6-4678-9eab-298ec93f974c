from typing import Dict, Any, List
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from src.agents.base_agent import BaseAgent


class FamilyHistoryAgent(BaseAgent):
    """家族史采集智能体"""
    
    def __init__(self):
        super().__init__(
            name="家族史采集智能体",
            description="了解患者家族疾病史和遗传病史",
            agent_type="family_history"
        )
        
        # 家族史类别和当前索引
        self.family_categories = [
            "遗传性疾病", "慢性疾病", "肿瘤疾病", "精神疾病",
            "心血管疾病", "代谢性疾病", "免疫性疾病", "其他疾病"
        ]
        self.current_category_index = 0
    
    def _create_prompt_template(self) -> PromptTemplate:
        """创建支持历史的提示模板"""
        return PromptTemplate(
            input_variables=["patient_input", "context", "history", "current_category"],
            template="""
你是一名专业的中医医生，负责采集患者的家族史信息。

当前询问的家族史类别: {current_category}
[对话历史]
{history}

[患者描述]
{patient_input}

请提取以下信息:
1. 家族成员关系
2. 疾病名称
3. 发病年龄
4. 疾病严重程度
5. 治疗情况
6. 是否遗传相关

请严格只输出如下JSON格式，不要输出任何其他内容或解释：

{{
    "family_category": "家族史类别",
    "family_member": "家族成员关系",
    "disease_name": "疾病名称",
    "onset_age": "发病年龄",
    "severity": "疾病严重程度",
    "treatment": "治疗情况",
    "is_hereditary": true/false,
    "confidence": 0.95
}}

注意：如果患者描述不完整，请根据已有信息填写，缺失字段用null表示。
"""
        )
    
    def _create_output_parser(self) -> JsonOutputParser:
        """创建输出解析器"""
        return JsonOutputParser()
    
    def ask_has_or_not(self, context: Dict[str, Any]) -> str:
        """询问当前家族史类别有无"""
        current_category = self.family_categories[self.current_category_index]
        
        questions = {
            "遗传性疾病": "您的家族中有遗传性疾病吗？比如血友病、色盲等？",
            "慢性疾病": "您的家族中有慢性疾病吗？比如高血压、糖尿病等？",
            "肿瘤疾病": "您的家族中有肿瘤疾病吗？比如癌症等？",
            "精神疾病": "您的家族中有精神疾病吗？比如抑郁症、精神分裂症等？",
            "心血管疾病": "您的家族中有心血管疾病吗？比如心脏病、脑卒中等？",
            "代谢性疾病": "您的家族中有代谢性疾病吗？比如甲状腺疾病等？",
            "免疫性疾病": "您的家族中有免疫性疾病吗？比如类风湿关节炎等？",
            "其他疾病": "您的家族中还有其他疾病吗？"
        }
        
        return questions.get(current_category, "您的家族中有其他疾病吗？")
    
    def ask_details_if_has(self, context: Dict[str, Any], has_result: Dict[str, Any]) -> str:
        """如果有家族史，追问详情"""
        current_category = self.family_categories[self.current_category_index]
        
        detail_questions = {
            "遗传性疾病": """
            请详细说明：
            1. 具体是什么遗传性疾病？
            2. 是哪个家族成员患的？
            3. 什么时候发病的？
            4. 疾病的严重程度如何？
            5. 是如何治疗的？
            6. 现在的情况如何？
            """,
            "慢性疾病": """
            请详细说明：
            1. 具体是什么慢性疾病？
            2. 是哪个家族成员患的？
            3. 什么时候开始患病的？
            4. 疾病的严重程度如何？
            5. 是如何治疗的？
            6. 现在控制得如何？
            """,
            "肿瘤疾病": """
            请详细说明：
            1. 具体是什么肿瘤疾病？
            2. 是哪个家族成员患的？
            3. 什么时候发现的？
            4. 肿瘤的类型和分期？
            5. 是如何治疗的？
            6. 现在的情况如何？
            """,
            "精神疾病": """
            请详细说明：
            1. 具体是什么精神疾病？
            2. 是哪个家族成员患的？
            3. 什么时候发病的？
            4. 疾病的严重程度如何？
            5. 是如何治疗的？
            6. 现在的情况如何？
            """,
            "心血管疾病": """
            请详细说明：
            1. 具体是什么心血管疾病？
            2. 是哪个家族成员患的？
            3. 什么时候发病的？
            4. 疾病的严重程度如何？
            5. 是如何治疗的？
            6. 现在控制得如何？
            """,
            "代谢性疾病": """
            请详细说明：
            1. 具体是什么代谢性疾病？
            2. 是哪个家族成员患的？
            3. 什么时候发病的？
            4. 疾病的严重程度如何？
            5. 是如何治疗的？
            6. 现在控制得如何？
            """,
            "免疫性疾病": """
            请详细说明：
            1. 具体是什么免疫性疾病？
            2. 是哪个家族成员患的？
            3. 什么时候发病的？
            4. 疾病的严重程度如何？
            5. 是如何治疗的？
            6. 现在的情况如何？
            """,
            "其他疾病": """
            请详细说明：
            1. 具体是什么疾病？
            2. 是哪个家族成员患的？
            3. 什么时候发病的？
            4. 疾病的严重程度如何？
            5. 是如何治疗的？
            6. 现在的情况如何？
            """
        }
        
        return detail_questions.get(current_category, "请详细描述一下。")
    
    def next_category(self):
        """移动到下一个家族史类别"""
        if self.current_category_index < len(self.family_categories) - 1:
            self.current_category_index += 1
            return True
        return False
    
    def reset_categories(self):
        """重置家族史类别索引"""
        self.current_category_index = 0
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力"""
        return [
            "遗传性疾病史采集",
            "慢性疾病史采集",
            "肿瘤疾病史采集",
            "精神疾病史采集",
            "心血管疾病史采集",
            "代谢性疾病史采集",
            "免疫性疾病史采集",
            "其他疾病史采集"
        ]
    
    def get_required_inputs(self) -> List[str]:
        """获取所需输入"""
        return [
            "patient_input",
            "context"
        ]
    
    def get_outputs(self) -> List[str]:
        """获取输出"""
        return [
            "family_category",
            "family_member",
            "disease_name",
            "onset_age",
            "severity",
            "treatment",
            "is_hereditary"
        ]
    
    required_fields = ["family_category", "family_member", "disease_name", "onset_age", "severity", "treatment", "is_hereditary"]
    def generate_followup(self, missing_fields):
        mapping = {
            "family_category": "请说明家族史类别。",
            "family_member": "请说明家族成员关系。",
            "disease_name": "请说明疾病名称。",
            "onset_age": "请说明发病年龄。",
            "severity": "请说明疾病严重程度。",
            "treatment": "请说明治疗情况。",
            "is_hereditary": "请说明是否遗传相关。"
        }
        return "；".join([mapping.get(f, f"请补充{f}") for f in missing_fields])
    def generate_summary(self, extracted_data):
        return f"家族史: {extracted_data}" 