// 数据统计服务
// 为研学中心和其他模块提供数据支持

export interface DailyStatistics {
  date: string;
  patientCount: number;
  visitCount: number;
  prescriptionCount: number;
  revenue: number;
  aiUsageCount: number;
  avgDiagnosisTime: number;
}

export interface ModuleStatistics {
  moduleName: string;
  dailyUsage: number;
  weeklyUsage: number;
  monthlyUsage: number;
  userSatisfaction: number;
  errorRate: number;
}

export interface AIStatistics {
  syndromeAccuracy: number;
  formulaRecommendationRate: number;
  safetyCheckCount: number;
  userFeedbackScore: number;
  improvementSuggestions: string[];
}

export interface ResearchData {
  // 证型分布数据
  syndromeDistribution: Array<{
    syndrome: string;
    count: number;
    percentage: number;
  }>;
  
  // 方剂使用频率
  formulaUsage: Array<{
    formula: string;
    usageCount: number;
    efficacyRate: number;
  }>;
  
  // 疾病趋势分析
  diseaseTrends: Array<{
    disease: string;
    monthlyData: number[];
    trend: 'increasing' | 'decreasing' | 'stable';
  }>;
  
  // 治疗效果统计
  treatmentEffectiveness: Array<{
    treatment: string;
    successRate: number;
    averageTreatmentDays: number;
    patientSatisfaction: number;
  }>;
}

class DataStatisticsService {
  // 获取每日统计数据
  async getDailyStatistics(startDate: string, endDate: string): Promise<DailyStatistics[]> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const statistics: DailyStatistics[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      statistics.push({
        date: dateStr,
        patientCount: Math.floor(Math.random() * 200) + 50,
        visitCount: Math.floor(Math.random() * 250) + 80,
        prescriptionCount: Math.floor(Math.random() * 180) + 60,
        revenue: Math.floor(Math.random() * 50000) + 10000,
        aiUsageCount: Math.floor(Math.random() * 150) + 30,
        avgDiagnosisTime: Math.round((Math.random() * 10 + 8) * 10) / 10
      });
    }
    
    return statistics;
  }
  
  // 获取模块使用统计
  async getModuleStatistics(): Promise<ModuleStatistics[]> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return [
      {
        moduleName: '患者管理',
        dailyUsage: 156,
        weeklyUsage: 892,
        monthlyUsage: 3456,
        userSatisfaction: 4.5,
        errorRate: 0.02
      },
      {
        moduleName: '门诊工作台',
        dailyUsage: 189,
        weeklyUsage: 1156,
        monthlyUsage: 4523,
        userSatisfaction: 4.7,
        errorRate: 0.01
      },
      {
        moduleName: '处方开具',
        dailyUsage: 142,
        weeklyUsage: 987,
        monthlyUsage: 3892,
        userSatisfaction: 4.3,
        errorRate: 0.03
      },
      {
        moduleName: 'AI辅助诊断',
        dailyUsage: 98,
        weeklyUsage: 654,
        monthlyUsage: 2567,
        userSatisfaction: 4.6,
        errorRate: 0.05
      },
      {
        moduleName: '药房管理',
        dailyUsage: 87,
        weeklyUsage: 543,
        monthlyUsage: 2134,
        userSatisfaction: 4.2,
        errorRate: 0.02
      }
    ];
  }
  
  // 获取AI功能统计
  async getAIStatistics(): Promise<AIStatistics> {
    await new Promise(resolve => setTimeout(resolve, 400));
    
    return {
      syndromeAccuracy: 0.876,
      formulaRecommendationRate: 0.823,
      safetyCheckCount: 142,
      userFeedbackScore: 4.6,
      improvementSuggestions: [
        '增加更多经典医案数据训练',
        '优化证型辨识算法准确性',
        '完善方剂配伍检查规则',
        '提升用户界面交互体验',
        '加强AI解释性和可信度'
      ]
    };
  }
  
  // 获取研学中心科研数据
  async getResearchData(): Promise<ResearchData> {
    await new Promise(resolve => setTimeout(resolve, 600));
    
    return {
      syndromeDistribution: [
        { syndrome: '肝郁脾虚证', count: 234, percentage: 18.5 },
        { syndrome: '气血两虚证', count: 198, percentage: 15.6 },
        { syndrome: '肾阳虚证', count: 167, percentage: 13.2 },
        { syndrome: '痰湿阻肺证', count: 145, percentage: 11.4 },
        { syndrome: '心脾两虚证', count: 123, percentage: 9.7 },
        { syndrome: '肝阳上亢证', count: 112, percentage: 8.8 },
        { syndrome: '脾胃虚弱证', count: 98, percentage: 7.7 },
        { syndrome: '阴虚火旺证', count: 87, percentage: 6.9 },
        { syndrome: '其他证型', count: 102, percentage: 8.2 }
      ],
      
      formulaUsage: [
        { formula: '逍遥散加减', usageCount: 89, efficacyRate: 0.85 },
        { formula: '四君子汤', usageCount: 76, efficacyRate: 0.82 },
        { formula: '六味地黄丸', usageCount: 65, efficacyRate: 0.78 },
        { formula: '补中益气汤', usageCount: 58, efficacyRate: 0.80 },
        { formula: '甘麦大枣汤', usageCount: 52, efficacyRate: 0.83 },
        { formula: '小柴胡汤', usageCount: 47, efficacyRate: 0.79 },
        { formula: '归脾汤', usageCount: 43, efficacyRate: 0.81 },
        { formula: '温胆汤', usageCount: 39, efficacyRate: 0.77 }
      ],
      
      diseaseTrends: [
        {
          disease: '失眠症',
          monthlyData: [45, 52, 48, 56, 61, 58, 63, 59, 67, 72, 68, 74],
          trend: 'increasing'
        },
        {
          disease: '慢性胃炎',
          monthlyData: [78, 82, 75, 79, 73, 76, 71, 74, 69, 72, 68, 70],
          trend: 'decreasing'
        },
        {
          disease: '高血压',
          monthlyData: [123, 125, 127, 124, 126, 128, 125, 127, 124, 126, 125, 127],
          trend: 'stable'
        },
        {
          disease: '糖尿病',
          monthlyData: [89, 92, 87, 94, 91, 96, 93, 98, 95, 101, 97, 103],
          trend: 'increasing'
        }
      ],
      
      treatmentEffectiveness: [
        {
          treatment: '中药汤剂',
          successRate: 0.85,
          averageTreatmentDays: 14,
          patientSatisfaction: 4.5
        },
        {
          treatment: '中成药',
          successRate: 0.78,
          averageTreatmentDays: 21,
          patientSatisfaction: 4.2
        },
        {
          treatment: '针灸治疗',
          successRate: 0.82,
          averageTreatmentDays: 10,
          patientSatisfaction: 4.6
        },
        {
          treatment: '推拿按摩',
          successRate: 0.76,
          averageTreatmentDays: 7,
          patientSatisfaction: 4.4
        },
        {
          treatment: '中西医结合',
          successRate: 0.88,
          averageTreatmentDays: 12,
          patientSatisfaction: 4.7
        }
      ]
    };
  }
  
  // 导出数据为CSV格式
  async exportDataToCSV(dataType: string, startDate?: string, endDate?: string): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟CSV数据生成
    let csvContent = '';
    
    switch (dataType) {
      case 'daily':
        csvContent = '日期,患者数,就诊次数,处方数,收入,AI使用次数,平均诊疗时间\n';
        csvContent += '2025-06-24,156,189,142,28650,98,12.5\n';
        csvContent += '2025-06-23,143,167,128,25430,87,13.2\n';
        break;
      case 'module':
        csvContent = '模块名称,日使用量,周使用量,月使用量,用户满意度,错误率\n';
        csvContent += '患者管理,156,892,3456,4.5,0.02\n';
        csvContent += '门诊工作台,189,1156,4523,4.7,0.01\n';
        break;
      case 'research':
        csvContent = '证型,数量,占比\n';
        csvContent += '肝郁脾虚证,234,18.5%\n';
        csvContent += '气血两虚证,198,15.6%\n';
        break;
      default:
        csvContent = '暂无数据\n';
    }
    
    return csvContent;
  }
}

// 导出单例实例
export const dataStatisticsService = new DataStatisticsService();
