#!/usr/bin/env python3
"""
中医预问诊智能体系统 - 统一服务器
MVP架构 - 前后端分离
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, HTMLResponse
import uvicorn
import logging

# 导入API路由
from api.routes import router as api_router
from config.settings import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="中医预问诊智能体系统",
    description="MVP架构 - 前后端分离的中医问诊系统",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
frontend_path = project_root / "frontend"
app.mount("/assets", StaticFiles(directory=str(frontend_path / "assets")), name="assets")
app.mount("/components", StaticFiles(directory=str(frontend_path / "components")), name="components")

# 注册API路由
app.include_router(api_router, prefix="/api")

# 前端页面路由
@app.get("/")
async def home():
    """主页导航"""
    return FileResponse(str(frontend_path / "index.html"))

@app.get("/pages/{page_name}")
async def serve_page(page_name: str):
    """服务子页面"""
    page_file = frontend_path / "pages" / f"{page_name}.html"
    if page_file.exists():
        return FileResponse(str(page_file))
    else:
        raise HTTPException(status_code=404, detail=f"页面 {page_name} 不存在")

# 快捷路由 (向后兼容)
@app.get("/ios")
async def ios_shortcut():
    """iOS页面快捷路由"""
    return FileResponse(str(frontend_path / "pages" / "ios.html"))

@app.get("/simple")
async def simple_shortcut():
    """简化页面快捷路由"""
    return FileResponse(str(frontend_path / "pages" / "simple.html"))

@app.get("/agent")
async def agent_shortcut():
    """智能体页面快捷路由"""
    return FileResponse(str(frontend_path / "pages" / "agent.html"))

@app.get("/workflow")
async def workflow_shortcut():
    """工作流程页面快捷路由"""
    return FileResponse(str(frontend_path / "pages" / "workflow.html"))

@app.get("/demo")
async def demo_shortcut():
    """演示页面快捷路由 - 默认跳转到iOS版本"""
    return FileResponse(str(frontend_path / "pages" / "ios.html"))

# 兼容旧路由
@app.get("/demo/{demo_type}")
async def demo_compat(demo_type: str):
    """兼容旧的demo路由"""
    page_map = {
        "ios": "ios.html",
        "simple": "simple.html", 
        "agent": "agent.html",
        "full": "full.html"
    }
    
    if demo_type in page_map:
        return FileResponse(str(frontend_path / "pages" / page_map[demo_type]))
    else:
        raise HTTPException(status_code=404, detail=f"演示页面 {demo_type} 不存在")

# 错误处理
@app.exception_handler(404)
async def not_found_handler(request, exc):
    """404错误处理"""
    return HTMLResponse(
        content=f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>页面未找到</title>
            <style>
                body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
                .error {{ color: #f44336; }}
                .back-link {{ color: #2196F3; text-decoration: none; }}
            </style>
        </head>
        <body>
            <h1 class="error">404 - 页面未找到</h1>
            <p>请求的页面不存在</p>
            <a href="/" class="back-link">← 返回主页</a>
        </body>
        </html>
        """,
        status_code=404
    )

def create_app():
    """创建应用实例"""
    return app

if __name__ == "__main__":
    print("🏥 中医预问诊智能体系统 - MVP架构")
    print("=" * 50)
    print(f"前端目录: {frontend_path}")
    print(f"LLM服务: {settings.llm_service}")
    print(f"Ollama地址: {settings.ollama_base_url}")
    print(f"使用模型: {settings.ollama_model}")
    
    print("🚀 启动统一服务器...")
    
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
