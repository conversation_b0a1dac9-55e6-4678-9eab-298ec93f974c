# 员工管理模块

## 功能描述
员工管理模块负责管理诊所的员工信息，包括员工档案、权限管理、绩效考核等功能。

## 目录结构
```
staff/
├── views/                    # 页面视图
│   └── StaffList.vue         # 员工管理页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 员工档案管理
- 权限分配管理
- 绩效考核
- 培训记录

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 