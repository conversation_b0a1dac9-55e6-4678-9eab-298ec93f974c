<template>
  <div class="symptom-analysis-page">
    <div class="page-header">
      <h2>症状分析</h2>
      <el-button type="text" class="close-btn" @click="$emit('close')">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <div class="page-content">
      <div class="analysis-container">
        <div class="input-section">
          <h3>症状输入</h3>
          <div class="symptom-input">
            <el-input
              v-model="symptomText"
              type="textarea"
              :rows="4"
              placeholder="请描述患者的症状，如：头痛、发热、咳嗽等..."
              @input="onSymptomInput"
            />
            <div class="input-actions">
              <el-button type="primary" @click="analyzeSymptoms" :loading="analyzing">
                <el-icon><Search /></el-icon>
                分析症状
              </el-button>
              <el-button @click="clearSymptoms">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </div>
        </div>
        
        <div class="quick-symptoms">
          <h4>常见症状快选</h4>
          <div class="symptom-tags">
            <el-tag
              v-for="symptom in commonSymptoms"
              :key="symptom"
              :type="selectedSymptoms.includes(symptom) ? 'primary' : ''"
              class="symptom-tag"
              @click="toggleSymptom(symptom)"
            >
              {{ symptom }}
            </el-tag>
          </div>
        </div>
        
        <div v-if="analysisResult" class="analysis-result">
          <h3>分析结果</h3>
          
          <div class="result-section">
            <h4>可能的疾病</h4>
            <div class="disease-list">
              <div
                v-for="disease in analysisResult.possibleDiseases"
                :key="disease.name"
                class="disease-item"
              >
                <div class="disease-header">
                  <span class="disease-name">{{ disease.name }}</span>
                  <el-progress
                    :percentage="disease.probability"
                    :color="getProbabilityColor(disease.probability)"
                    :show-text="false"
                    class="probability-bar"
                  />
                  <span class="probability-text">{{ disease.probability }}%</span>
                </div>
                <p class="disease-description">{{ disease.description }}</p>
              </div>
            </div>
          </div>
          
          <div class="result-section">
            <h4>建议检查</h4>
            <div class="examination-list">
              <el-tag
                v-for="exam in analysisResult.suggestedExaminations"
                :key="exam"
                type="info"
                class="exam-tag"
              >
                {{ exam }}
              </el-tag>
            </div>
          </div>
          
          <div class="result-section">
            <h4>中医辨证</h4>
            <div class="tcm-analysis">
              <div class="syndrome-item">
                <label>证型：</label>
                <span>{{ analysisResult.tcmSyndrome?.type || '待进一步辨证' }}</span>
              </div>
              <div class="syndrome-item">
                <label>病机：</label>
                <span>{{ analysisResult.tcmSyndrome?.pathogenesis || '待进一步分析' }}</span>
              </div>
              <div class="syndrome-item">
                <label>治法：</label>
                <span>{{ analysisResult.tcmSyndrome?.treatment || '待制定治疗方案' }}</span>
              </div>
            </div>
          </div>
          
          <div class="result-section">
            <h4>注意事项</h4>
            <div class="precautions">
              <ul>
                <li v-for="precaution in analysisResult.precautions" :key="precaution">
                  {{ precaution }}
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <div v-if="!analysisResult && !analyzing" class="empty-state">
          <el-empty description="请输入症状进行分析" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Close, Search, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

defineEmits<{
  close: []
}>()

const symptomText = ref('')
const analyzing = ref(false)
const selectedSymptoms = ref<string[]>([])
const analysisResult = ref<any>(null)

const commonSymptoms = [
  '头痛', '发热', '咳嗽', '胸闷', '腹痛', '恶心', '呕吐', '腹泻',
  '便秘', '失眠', '乏力', '食欲不振', '心悸', '气短', '眩晕', '耳鸣'
]

const onSymptomInput = () => {
  // 实时输入处理
}

const toggleSymptom = (symptom: string) => {
  const index = selectedSymptoms.value.indexOf(symptom)
  if (index > -1) {
    selectedSymptoms.value.splice(index, 1)
  } else {
    selectedSymptoms.value.push(symptom)
  }
  
  // 更新症状文本
  symptomText.value = selectedSymptoms.value.join('、')
}

const analyzeSymptoms = async () => {
  if (!symptomText.value.trim()) {
    ElMessage.warning('请输入症状描述')
    return
  }
  
  analyzing.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟分析结果
    analysisResult.value = {
      possibleDiseases: [
        {
          name: '感冒',
          probability: 85,
          description: '上呼吸道感染，常见症状包括发热、咳嗽、鼻塞等'
        },
        {
          name: '支气管炎',
          probability: 65,
          description: '支气管黏膜的炎症，主要表现为咳嗽、咳痰'
        },
        {
          name: '肺炎',
          probability: 35,
          description: '肺部感染性疾病，需要进一步检查确诊'
        }
      ],
      suggestedExaminations: [
        '血常规', '胸部X光', '体温测量', '血氧饱和度'
      ],
      tcmSyndrome: {
        type: '风寒感冒',
        pathogenesis: '风寒外袭，肺气失宣',
        treatment: '疏风散寒，宣肺解表'
      },
      precautions: [
        '注意休息，避免劳累',
        '多饮温水，保持室内通风',
        '如症状加重，及时就医',
        '避免接触其他人，防止传染'
      ]
    }
    
    ElMessage.success('症状分析完成')
  } catch (error) {
    ElMessage.error('分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

const clearSymptoms = () => {
  symptomText.value = ''
  selectedSymptoms.value = []
  analysisResult.value = null
}

const getProbabilityColor = (probability: number) => {
  if (probability >= 80) return '#67C23A'
  if (probability >= 60) return '#E6A23C'
  if (probability >= 40) return '#F56C6C'
  return '#909399'
}
</script>

<style scoped>
.symptom-analysis-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--background-color);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

.page-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.close-btn:hover {
  color: var(--text-primary);
}

.page-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.analysis-container {
  max-width: 900px;
  margin: 0 auto;
}

.input-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.input-section h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.symptom-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.input-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.quick-symptoms {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.quick-symptoms h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.symptom-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.symptom-tag {
  cursor: pointer;
  transition: all var(--transition-fast);
}

.symptom-tag:hover {
  transform: translateY(-1px);
}

.analysis-result {
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
}

.analysis-result h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.result-section {
  margin-bottom: var(--spacing-lg);
}

.result-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.disease-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.disease-item {
  padding: var(--spacing-md);
  background: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.disease-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xs);
}

.disease-name {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 80px;
}

.probability-bar {
  flex: 1;
}

.probability-text {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 40px;
}

.disease-description {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.examination-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.exam-tag {
  margin: 0;
}

.tcm-analysis {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.syndrome-item {
  display: flex;
  align-items: center;
}

.syndrome-item label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: var(--spacing-xs);
  min-width: 60px;
}

.syndrome-item span {
  color: var(--text-secondary);
}

.precautions ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.precautions li {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-xs);
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

@media (max-width: 768px) {
  .page-content {
    padding: var(--spacing-md);
  }

  .disease-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .probability-bar {
    width: 100%;
  }
}
</style>
