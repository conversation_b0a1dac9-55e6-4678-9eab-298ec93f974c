import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 可以在这里添加token等认证信息
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  (error) => {
    console.error('响应错误:', error)
    
    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 可以在这里处理登录跳转
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API接口定义
export interface ConsultationRequest {
  patient_name: string
  patient_age: number
  patient_gender: string
}

export interface ChatRequest {
  session_id: string
  message: string
}

export interface ChatResponse {
  response: string
  session_id: string
  is_complete: boolean
  extracted_data?: any
  next_question?: string
}

export interface HealthResponse {
  status: string
  timestamp: string
  version: string
}

// API方法
export const apiService = {
  // 健康检查
  health(): Promise<HealthResponse> {
    return api.get('/health')
  },

  // 开始问诊
  startConsultation(data: ConsultationRequest): Promise<any> {
    return api.post('/consultation/start', data)
  },

  // 发送消息
  sendMessage(data: ChatRequest): Promise<ChatResponse> {
    return api.post('/consultation/chat', data)
  },

  // 获取问诊记录
  getRecords(): Promise<any[]> {
    return api.get('/consultation/records')
  },

  // 获取特定问诊记录
  getRecord(sessionId: string): Promise<any> {
    return api.get(`/consultation/records/${sessionId}`)
  },

  // 删除问诊记录
  deleteRecord(sessionId: string): Promise<void> {
    return api.delete(`/consultation/records/${sessionId}`)
  }
}

export default api
