-- =====================================================
-- 中医诊所助手系统 - 电子病历组表结构
-- =====================================================
-- 文件: 04_emr_tables.sql
-- 描述: 创建电子病历相关表
-- 版本: v1.0
-- 创建日期: 2025-06-24
-- =====================================================

USE `tcm_clinic_system`;

-- =====================================================
-- 4.3 电子病历组 (EMR - Electronic Medical Record)
-- =====================================================

-- 4.3.1 电子病历表（中医特色）
CREATE TABLE `medical_records` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '病历ID',
    `visit_id` INT NOT NULL COMMENT '就诊ID',
    `patient_id` INT NOT NULL COMMENT '患者ID',
    `doctor_id` INT NOT NULL COMMENT '医生ID',
    `chief_complaint` TEXT NULL COMMENT '主诉',
    `present_illness` TEXT NULL COMMENT '现病史',
    `past_history` TEXT NULL COMMENT '既往史',
    `family_history` TEXT NULL COMMENT '家族史',
    `personal_history` TEXT NULL COMMENT '个人史',
    -- 中医四诊
    `inspection` TEXT NULL COMMENT '望诊（神、色、形、态、舌象等）',
    `auscultation` TEXT NULL COMMENT '闻诊（语音、呼吸、咳嗽等）',
    `inquiry` TEXT NULL COMMENT '问诊（寒热、汗、痛、睡眠、二便等）',
    `palpation` TEXT NULL COMMENT '切诊（脉象、按诊等）',
    -- 中医诊断
    `tcm_diagnosis` TEXT NULL COMMENT '中医诊断',
    `tcm_syndrome` TEXT NULL COMMENT '中医证型',
    `western_diagnosis` TEXT NULL COMMENT '西医诊断',
    `treatment_principle` TEXT NULL COMMENT '治法',
    `doctors_orders` TEXT NULL COMMENT '医嘱',
    `follow_up_plan` TEXT NULL COMMENT '随访计划',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_medical_records_visit` (`visit_id`),
    KEY `idx_medical_records_patient` (`patient_id`),
    KEY `idx_medical_records_doctor` (`doctor_id`),
    KEY `idx_medical_records_created` (`created_at`),
    CONSTRAINT `fk_medical_records_visit` FOREIGN KEY (`visit_id`) REFERENCES `visits` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_medical_records_patient` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`),
    CONSTRAINT `fk_medical_records_doctor` FOREIGN KEY (`doctor_id`) REFERENCES `staff` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电子病历表';

-- 4.3.2 处方主表
CREATE TABLE `prescriptions` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '处方ID',
    `prescription_number` VARCHAR(50) NOT NULL COMMENT '处方号',
    `visit_id` INT NOT NULL COMMENT '就诊ID',
    `patient_id` INT NOT NULL COMMENT '患者ID',
    `doctor_id` INT NOT NULL COMMENT '医生ID',
    `prescription_type` ENUM('herbal', 'patent', 'western', 'injection') NOT NULL COMMENT '处方类型',
    `status` ENUM('draft', 'confirmed', 'billed', 'dispensed', 'cancelled') NOT NULL DEFAULT 'draft' COMMENT '处方状态',
    `dosage_instructions` TEXT NULL COMMENT '服法（如：每日一剂，水煎服，分两次服用）',
    `decoction_method` TEXT NULL COMMENT '煎煮方法（如：先煎...后下...）',
    `total_doses` INT NULL COMMENT '总剂数',
    `total_amount` DECIMAL(10,2) NULL COMMENT '处方总金额',
    `note` TEXT NULL COMMENT '备注',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_prescriptions_number` (`prescription_number`),
    KEY `idx_prescriptions_visit` (`visit_id`),
    KEY `idx_prescriptions_patient` (`patient_id`),
    KEY `idx_prescriptions_doctor` (`doctor_id`),
    KEY `idx_prescriptions_status` (`status`),
    KEY `idx_prescriptions_type` (`prescription_type`),
    KEY `idx_prescriptions_created` (`created_at`),
    CONSTRAINT `fk_prescriptions_visit` FOREIGN KEY (`visit_id`) REFERENCES `visits` (`id`),
    CONSTRAINT `fk_prescriptions_patient` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`),
    CONSTRAINT `fk_prescriptions_doctor` FOREIGN KEY (`doctor_id`) REFERENCES `staff` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处方主表';

-- 4.3.3 处方明细表
CREATE TABLE `prescription_items` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '处方明细ID',
    `prescription_id` INT NOT NULL COMMENT '处方ID',
    `product_id` INT NOT NULL COMMENT '产品ID',
    `quantity` DECIMAL(10,2) NOT NULL COMMENT '数量',
    `unit` VARCHAR(20) NOT NULL COMMENT '单位',
    `unit_price` DECIMAL(10,2) NOT NULL COMMENT '单价',
    `total_price` DECIMAL(10,2) NOT NULL COMMENT '小计',
    `usage_method` VARCHAR(100) NULL COMMENT '用法（如：先煎、后下、包煎等）',
    `note` TEXT NULL COMMENT '备注',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_prescription_items_prescription` (`prescription_id`),
    KEY `idx_prescription_items_product` (`product_id`),
    CONSTRAINT `fk_prescription_items_prescription` FOREIGN KEY (`prescription_id`) REFERENCES `prescriptions` (`id`) ON DELETE CASCADE
    -- 注意：product_id的外键约束将在产品表创建后添加
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处方明细表';

-- 4.3.4 医疗影像表
CREATE TABLE `medical_images` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '影像ID',
    `visit_id` INT NOT NULL COMMENT '就诊ID',
    `patient_id` INT NOT NULL COMMENT '患者ID',
    `image_type` ENUM('xray', 'ct', 'mri', 'ultrasound', 'photo', 'other') NOT NULL COMMENT '影像类型',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
    `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
    `mime_type` VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    `description` TEXT NULL COMMENT '描述',
    `created_by` INT NOT NULL COMMENT '上传人',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_medical_images_visit` (`visit_id`),
    KEY `idx_medical_images_patient` (`patient_id`),
    KEY `idx_medical_images_type` (`image_type`),
    KEY `idx_medical_images_created_by` (`created_by`),
    CONSTRAINT `fk_medical_images_visit` FOREIGN KEY (`visit_id`) REFERENCES `visits` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_medical_images_patient` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`),
    CONSTRAINT `fk_medical_images_created_by` FOREIGN KEY (`created_by`) REFERENCES `staff` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医疗影像表';

-- =====================================================
-- 创建触发器：自动生成处方编号
-- =====================================================

DELIMITER $$
CREATE TRIGGER `tr_prescriptions_generate_number` 
BEFORE INSERT ON `prescriptions`
FOR EACH ROW
BEGIN
    IF NEW.prescription_number IS NULL OR NEW.prescription_number = '' THEN
        SET NEW.prescription_number = CONCAT('R', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(LAST_INSERT_ID() + 1, 4, '0'));
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 创建触发器：处方金额计算
-- =====================================================

-- 处方明细插入时更新处方总金额
DELIMITER $$
CREATE TRIGGER `tr_prescription_items_insert_amount` 
AFTER INSERT ON `prescription_items`
FOR EACH ROW
BEGIN
    UPDATE `prescriptions` 
    SET `total_amount` = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM `prescription_items` 
        WHERE `prescription_id` = NEW.prescription_id
    ),
    `updated_at` = NOW()
    WHERE `id` = NEW.prescription_id;
END$$
DELIMITER ;

-- 处方明细更新时更新处方总金额
DELIMITER $$
CREATE TRIGGER `tr_prescription_items_update_amount` 
AFTER UPDATE ON `prescription_items`
FOR EACH ROW
BEGIN
    UPDATE `prescriptions` 
    SET `total_amount` = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM `prescription_items` 
        WHERE `prescription_id` = NEW.prescription_id
    ),
    `updated_at` = NOW()
    WHERE `id` = NEW.prescription_id;
END$$
DELIMITER ;

-- 处方明细删除时更新处方总金额
DELIMITER $$
CREATE TRIGGER `tr_prescription_items_delete_amount` 
AFTER DELETE ON `prescription_items`
FOR EACH ROW
BEGIN
    UPDATE `prescriptions` 
    SET `total_amount` = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM `prescription_items` 
        WHERE `prescription_id` = OLD.prescription_id
    ),
    `updated_at` = NOW()
    WHERE `id` = OLD.prescription_id;
END$$
DELIMITER ;

-- =====================================================
-- 创建视图：完整病历信息
-- =====================================================

CREATE VIEW `v_complete_medical_records` AS
SELECT 
    mr.id,
    mr.visit_id,
    v.visit_number,
    v.visit_date,
    p.patient_number,
    p.full_name AS patient_name,
    p.gender,
    p.phone AS patient_phone,
    s.full_name AS doctor_name,
    s.title AS doctor_title,
    mr.chief_complaint,
    mr.present_illness,
    mr.past_history,
    mr.inspection,
    mr.auscultation,
    mr.inquiry,
    mr.palpation,
    mr.tcm_diagnosis,
    mr.tcm_syndrome,
    mr.western_diagnosis,
    mr.treatment_principle,
    mr.doctors_orders,
    mr.follow_up_plan,
    mr.created_at,
    mr.updated_at
FROM `medical_records` mr
JOIN `visits` v ON mr.visit_id = v.id
JOIN `patients` p ON mr.patient_id = p.id
JOIN `staff` s ON mr.doctor_id = s.id;

-- =====================================================
-- 创建视图：处方详情
-- =====================================================

CREATE VIEW `v_prescription_details` AS
SELECT 
    p.id AS prescription_id,
    p.prescription_number,
    p.visit_id,
    v.visit_number,
    v.visit_date,
    pt.patient_number,
    pt.full_name AS patient_name,
    pt.phone AS patient_phone,
    s.full_name AS doctor_name,
    p.prescription_type,
    p.status,
    p.dosage_instructions,
    p.decoction_method,
    p.total_doses,
    p.total_amount,
    p.note,
    p.created_at,
    p.updated_at
FROM `prescriptions` p
JOIN `visits` v ON p.visit_id = v.id
JOIN `patients` pt ON p.patient_id = pt.id
JOIN `staff` s ON p.doctor_id = s.id;

-- =====================================================
-- 插入示例数据
-- =====================================================

-- 插入示例预约数据（需要先有预约才能有就诊）
INSERT INTO `appointments` (`appointment_number`, `patient_id`, `doctor_id`, `appointment_date`, `appointment_time`, `status`, `source`, `chief_complaint`, `created_by`) VALUES
('A20250624001', 1, 2, '2025-06-24', '09:00:00', 'completed', 'online', '头痛，失眠', 4),
('A20250624002', 2, 2, '2025-06-24', '10:00:00', 'completed', 'offline', '胃痛，消化不良', 4),
('A20250624003', 3, 3, '2025-06-24', '14:00:00', 'pending', 'phone', '腰痛，活动受限', 4);

-- 插入示例就诊数据
INSERT INTO `visits` (`visit_number`, `patient_id`, `doctor_id`, `appointment_id`, `visit_date`, `visit_start_time`, `visit_end_time`, `status`, `visit_type`, `chief_complaint`) VALUES
('V20250624001', 1, 2, 1, '2025-06-24', '2025-06-24 09:00:00', '2025-06-24 09:30:00', 'completed', 'first_visit', '头痛，失眠'),
('V20250624002', 2, 2, 2, '2025-06-24', '2025-06-24 10:00:00', '2025-06-24 10:25:00', 'completed', 'follow_up', '胃痛，消化不良');

-- 插入示例病历数据
INSERT INTO `medical_records` (`visit_id`, `patient_id`, `doctor_id`, `chief_complaint`, `present_illness`, `inspection`, `inquiry`, `palpation`, `tcm_diagnosis`, `tcm_syndrome`, `treatment_principle`) VALUES
(1, 1, 2, '头痛，失眠', '患者近1月来头痛，夜间失眠，工作压力大', '面色略黄，舌淡红苔薄白', '头痛以胀痛为主，夜间难以入睡，多梦', '脉弦细', '头痛病', '肝郁脾虚证', '疏肝健脾，安神定志'),
(2, 2, 2, '胃痛，消化不良', '患者胃脘疼痛2周，饭后加重，伴嗳气', '面色萎黄，舌淡苔白腻', '胃脘胀痛，食后加重，嗳气频作', '脉濡缓', '胃痛病', '脾胃虚弱证', '健脾和胃，理气止痛');

-- 注意：处方数据将在产品表创建后插入
