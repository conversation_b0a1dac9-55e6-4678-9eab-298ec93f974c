// 应用中心卡片模块
import type { CardModule } from '../index'
import { ref } from 'vue'

// 应用中心统计数据
export const appCenterStats = ref({
  totalApps: 24,
  installedApps: 8,
  availableUpdates: 3,
  downloadToday: 5,
  categories: {
    medical: 8,
    management: 6,
    analytics: 4,
    education: 6
  },
  recentDownloads: [
    { name: '智能诊断助手', time: '2小时前' },
    { name: '电子病历系统', time: '4小时前' },
    { name: '数据分析平台', time: '6小时前' }
  ]
})

// 应用中心卡片配置
export const appCenterCard: CardModule = {
  key: 'app-center',
  name: '应用中心',
  route: '/app-center',
  icon: 'Grid',
  color: '#007AFF',
  info: '下载和管理应用',
  hasDetailStats: true,
  statsData: appCenterStats,
  permission: 'app_center'
}

// 应用中心详细统计数据获取函数
export const getAppCenterDetailStats = () => {
  return {
    totalApps: appCenterStats.value.totalApps,
    installedApps: appCenterStats.value.installedApps,
    availableUpdates: appCenterStats.value.availableUpdates,
    downloadToday: appCenterStats.value.downloadToday,
    categories: appCenterStats.value.categories,
    recentDownloads: appCenterStats.value.recentDownloads
  }
}

// 更新应用中心统计数据
export const updateAppCenterStats = (newStats: any) => {
  appCenterStats.value = { ...appCenterStats.value, ...newStats }
}

// 模拟下载应用
export const downloadApp = (appName: string) => {
  appCenterStats.value.installedApps += 1
  appCenterStats.value.downloadToday += 1
  appCenterStats.value.recentDownloads.unshift({
    name: appName,
    time: '刚刚'
  })
  // 保持最近下载记录不超过5条
  if (appCenterStats.value.recentDownloads.length > 5) {
    appCenterStats.value.recentDownloads.pop()
  }
}

// 模拟更新应用
export const updateApp = (appName: string) => {
  if (appCenterStats.value.availableUpdates > 0) {
    appCenterStats.value.availableUpdates -= 1
  }
  appCenterStats.value.recentDownloads.unshift({
    name: `${appName} (更新)`,
    time: '刚刚'
  })
  // 保持最近下载记录不超过5条
  if (appCenterStats.value.recentDownloads.length > 5) {
    appCenterStats.value.recentDownloads.pop()
  }
}
