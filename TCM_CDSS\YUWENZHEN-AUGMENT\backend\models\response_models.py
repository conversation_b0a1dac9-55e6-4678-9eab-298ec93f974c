"""
响应数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class BaseResponse(BaseModel):
    """基础响应模型"""
    status: str = Field(..., description="响应状态")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="时间戳")

class ChatResponse(BaseResponse):
    """聊天响应模型"""
    response: str = Field(..., description="AI回复")
    session_id: Optional[str] = Field(None, description="会话ID")

class InquiryStartResponse(BaseResponse):
    """开始问诊响应模型"""
    session_id: str = Field(..., description="会话ID")
    message: str = Field(..., description="欢迎消息")

class HealthResponse(BaseResponse):
    """健康检查响应模型"""
    version: str = Field(..., description="系统版本")
    services: Dict[str, Any] = Field(..., description="服务状态")
    
class ErrorResponse(BaseResponse):
    """错误响应模型"""
    error: str = Field(..., description="错误信息")
    detail: Optional[str] = Field(None, description="错误详情")
