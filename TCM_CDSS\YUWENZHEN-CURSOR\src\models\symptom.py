"""
症状数据模型

定义症状相关的数据结构，包括症状实体、症状分类等。
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class SymptomEntity(BaseModel):
    """症状实体模型"""
    
    symptom_id: str = Field(description="症状唯一标识符")
    symptom_name: str = Field(description="症状名称")
    symptom_type: str = Field(description="症状类型")
    location: Optional[str] = Field(default=None, description="症状部位")
    nature: Optional[str] = Field(default=None, description="症状性质")
    severity: Optional[str] = Field(default=None, description="症状程度")
    duration: Optional[str] = Field(default=None, description="持续时间")
    frequency: Optional[str] = Field(default=None, description="发作频率")
    triggers: List[str] = Field(default_factory=list, description="诱发因素")
    relievers: List[str] = Field(default_factory=list, description="缓解因素")
    onset_time: Optional[datetime] = Field(default=None, description="发病时间")
    course: Optional[str] = Field(default=None, description="病情发展过程")
    
    # 系统信息
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    confidence: float = Field(default=0.8, description="置信度")
    
    class Config:
        json_schema_extra = {
            "example": {
                "symptom_id": "SYM001",
                "symptom_name": "头痛",
                "symptom_type": "疼痛",
                "location": "头部",
                "nature": "胀痛",
                "severity": "中等",
                "duration": "3天",
                "frequency": "持续性",
                "triggers": ["劳累", "熬夜"],
                "relievers": ["休息", "按摩"],
                "onset_time": "2024-12-01T10:00:00",
                "course": "逐渐加重",
                "confidence": 0.9
            }
        }


class SymptomCategory(BaseModel):
    """症状分类模型"""
    
    category_id: str = Field(description="分类唯一标识符")
    category_name: str = Field(description="分类名称")
    category_description: str = Field(description="分类描述")
    parent_category: Optional[str] = Field(default=None, description="父分类")
    symptoms: List[str] = Field(default_factory=list, description="包含的症状")
    
    class Config:
        json_schema_extra = {
            "example": {
                "category_id": "CAT001",
                "category_name": "寒热症状",
                "category_description": "与寒热相关的症状",
                "parent_category": None,
                "symptoms": ["发热", "怕冷", "寒战", "恶寒"]
            }
        }


class SymptomPattern(BaseModel):
    """症状模式模型"""
    
    pattern_id: str = Field(description="模式唯一标识符")
    pattern_name: str = Field(description="模式名称")
    pattern_description: str = Field(description="模式描述")
    symptoms: List[str] = Field(description="相关症状")
    syndrome: Optional[str] = Field(default=None, description="对应证候")
    treatment_principle: Optional[str] = Field(default=None, description="治疗原则")
    
    class Config:
        json_schema_extra = {
            "example": {
                "pattern_id": "PAT001",
                "pattern_name": "肝阳上亢",
                "pattern_description": "肝阳上亢的症状模式",
                "symptoms": ["头痛", "眩晕", "耳鸣", "失眠"],
                "syndrome": "肝阳上亢证",
                "treatment_principle": "平肝潜阳"
            }
        } 