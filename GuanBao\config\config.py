#!/usr/bin/env python3
"""
GuanBao AI Chat 配置管理模块
统一管理前端和后端的配置信息
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional

class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径，默认为 config/app.config.json
        """
        if config_file is None:
            # 获取项目根目录
            project_root = Path(__file__).parent.parent
            config_file = project_root / "config" / "app.config.json"
        
        self.config_file = Path(config_file)
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 环境变量覆盖
            self._apply_env_overrides(config)
            
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _apply_env_overrides(self, config: Dict[str, Any]):
        """应用环境变量覆盖"""
        # 前端配置
        if os.getenv('FRONTEND_HOST'):
            config['frontend']['host'] = os.getenv('FRONTEND_HOST')
        if os.getenv('FRONTEND_PORT'):
            config['frontend']['port'] = int(os.getenv('FRONTEND_PORT'))
        
        # 后端配置
        if os.getenv('BACKEND_HOST'):
            config['backend']['host'] = os.getenv('BACKEND_HOST')
        if os.getenv('BACKEND_PORT'):
            config['backend']['port'] = int(os.getenv('BACKEND_PORT'))
        
        # API配置
        if os.getenv('API_BASE_URL'):
            config['api']['base_url'] = os.getenv('API_BASE_URL')
        
        # 安全配置
        if os.getenv('SECRET_KEY'):
            config['security']['secret_key'] = os.getenv('SECRET_KEY')
        if os.getenv('API_KEY'):
            config['security']['api_key'] = os.getenv('API_KEY')
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，如 'backend.host' 或 'frontend.port'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self._config, f, indent=2, ensure_ascii=False)
    
    # 便捷属性访问
    @property
    def frontend_host(self) -> str:
        """前端主机地址"""
        return self.get('frontend.host', 'localhost')
    
    @property
    def frontend_port(self) -> int:
        """前端端口"""
        return self.get('frontend.port', 5173)
    
    @property
    def backend_host(self) -> str:
        """后端主机地址"""
        return self.get('backend.host', '0.0.0.0')
    
    @property
    def backend_port(self) -> int:
        """后端端口"""
        return self.get('backend.port', 82)
    
    @property
    def api_base_url(self) -> str:
        """API基础URL"""
        return self.get('api.base_url', f'http://localhost:{self.backend_port}')
    
    @property
    def secret_key(self) -> str:
        """密钥"""
        return self.get('security.secret_key', 'GuanBao_2024_SECRET_KEY')
    
    @property
    def api_key(self) -> str:
        """API密钥"""
        return self.get('security.api_key', 'GuanBao_2024_API_KEY')
    
    @property
    def cors_origins(self) -> list:
        """CORS允许的源"""
        return self.get('security.cors_origins', [f'http://localhost:{self.frontend_port}'])
    
    def get_frontend_config(self) -> Dict[str, Any]:
        """获取前端配置"""
        return self.get('frontend', {})
    
    def get_backend_config(self) -> Dict[str, Any]:
        """获取后端配置"""
        return self.get('backend', {})
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.get('api', {})
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Config(frontend={self.frontend_host}:{self.frontend_port}, backend={self.backend_host}:{self.backend_port})"
    
    def __repr__(self) -> str:
        """调试表示"""
        return self.__str__()


# 全局配置实例
config = Config()

# 便捷函数
def get_config() -> Config:
    """获取全局配置实例"""
    return config

def reload_config():
    """重新加载配置"""
    global config
    config = Config()

if __name__ == '__main__':
    # 测试配置加载
    print("GuanBao AI Chat 配置信息:")
    print(f"前端: {config.frontend_host}:{config.frontend_port}")
    print(f"后端: {config.backend_host}:{config.backend_port}")
    print(f"API: {config.api_base_url}")
    print(f"CORS: {config.cors_origins}")
