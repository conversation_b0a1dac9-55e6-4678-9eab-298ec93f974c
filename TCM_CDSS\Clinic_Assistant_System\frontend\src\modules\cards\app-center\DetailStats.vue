<template>
  <div class="app-center-stats-detail">
    <div class="stats-grid">
      <div class="stat-item">
        <span class="stat-label">应用总数</span>
        <span class="stat-value total">{{ stats.totalApps }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已安装</span>
        <span class="stat-value installed">{{ stats.installedApps }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">可更新</span>
        <span class="stat-value updates">{{ stats.availableUpdates }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">今日下载</span>
        <span class="stat-value downloads">{{ stats.downloadToday }}</span>
      </div>
    </div>
    <div class="category-breakdown">
      <div class="category-item">
        <span class="category-label">医疗工具</span>
        <span class="category-value">{{ stats.categories.medical }}个</span>
      </div>
      <div class="category-item">
        <span class="category-label">管理系统</span>
        <span class="category-value">{{ stats.categories.management }}个</span>
      </div>
      <div class="category-item">
        <span class="category-label">数据分析</span>
        <span class="category-value">{{ stats.categories.analytics }}个</span>
      </div>
      <div class="category-item">
        <span class="category-label">教育培训</span>
        <span class="category-value">{{ stats.categories.education }}个</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { appCenterStats } from './index'

// 计算属性获取统计数据
const stats = computed(() => appCenterStats.value)
</script>

<style scoped>
.app-center-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(0, 122, 255, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(0, 122, 255, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .app-center-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.stat-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
}

.stat-value.total {
  color: #007AFF;
}

.stat-value.installed {
  color: #34C759;
}

.stat-value.updates {
  color: #FF9500;
}

.stat-value.downloads {
  color: #5856D6;
}

.category-breakdown {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.category-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.category-value {
  font-size: 11px;
  font-weight: 600;
  color: #007AFF;
}
</style>
