# 应用配置
APP_NAME=TCM Consultation System
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://tcm_user:tcm_pass@localhost:5432/tcm_db
REDIS_URL=redis://localhost:6379

# OpenRouter配置
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=anthropic/claude-3.5-sonnet

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=2000

# 安全配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 缓存配置
CACHE_TTL=3600

# 限流配置
RATE_LIMIT_PER_MINUTE=60

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# 第三方服务配置（可选）
# 语音识别服务
SPEECH_TO_TEXT_API_KEY=your_speech_api_key

# 图像识别服务
IMAGE_RECOGNITION_API_KEY=your_image_api_key 