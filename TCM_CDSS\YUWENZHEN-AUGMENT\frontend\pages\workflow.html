<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多智能体协同预问诊工作流程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .workflow-info {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .workflow-stages {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stage-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #4facfe;
        }

        .stage-number {
            background: #4facfe;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .stage-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .stage-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .elements {
            margin-bottom: 10px;
        }

        .elements-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .elements-list {
            color: #666;
            font-size: 14px;
        }

        .progress-section {
            padding: 30px;
            background: white;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
        }

        .current-agent {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .current-agent-title {
            color: #1976d2;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .chat-section {
            padding: 30px;
            background: #f8f9fa;
        }

        .patient-form {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .chat-container {
            background: white;
            border-radius: 15px;
            height: 400px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            border-bottom: 1px solid #e9ecef;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 18px;
            border-radius: 18px;
            max-width: 80%;
            line-height: 1.5;
        }

        .message.user {
            background: #4facfe;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .message.assistant {
            background: #f1f3f4;
            color: #333;
            border-bottom-left-radius: 5px;
        }

        .message.system {
            background: #e8f5e8;
            color: #2e7d32;
            text-align: center;
            border-radius: 10px;
            font-style: italic;
        }

        .chat-input-container {
            padding: 20px;
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
        }

        .chat-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .return-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border: none;
            border-radius: 50px;
            padding: 12px 20px;
            font-size: 14px;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .return-btn:hover {
            background: white;
            transform: translateY(-2px);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active {
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        .status-completed {
            background: #2196f3;
        }

        .status-waiting {
            background: #ff9800;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <button class="return-btn" onclick="window.location.href='/'">← 返回主页</button>

    <div class="container">
        <div class="header">
            <h1>🤖 多智能体协同预问诊</h1>
            <p>基于工作流程的结构化中医预问诊系统</p>
        </div>

        <div class="workflow-info">
            <h2 style="margin-bottom: 20px; color: #333;">🔄 工作流程说明</h2>
            <div class="workflow-stages">
                <div class="stage-card">
                    <div class="stage-number">1</div>
                    <div class="stage-title">主诉采集智能体</div>
                    <div class="stage-description">采集患者的主要症状信息</div>
                    <div class="elements">
                        <div class="elements-title">必需信息:</div>
                        <div class="elements-list">主要症状、持续时间</div>
                    </div>
                    <div class="elements">
                        <div class="elements-title">可选信息:</div>
                        <div class="elements-list">伴随症状、严重程度</div>
                    </div>
                </div>

                <div class="stage-card">
                    <div class="stage-number">2</div>
                    <div class="stage-title">现病史采集智能体</div>
                    <div class="stage-description">了解疾病发展过程和影响因素</div>
                    <div class="elements">
                        <div class="elements-title">必需信息:</div>
                        <div class="elements-list">发病诱因、症状变化、影响因素</div>
                    </div>
                    <div class="elements">
                        <div class="elements-title">可选信息:</div>
                        <div class="elements-list">治疗情况</div>
                    </div>
                </div>

                <div class="stage-card">
                    <div class="stage-number">3</div>
                    <div class="stage-title">既往史采集智能体</div>
                    <div class="stage-description">了解既往健康背景</div>
                    <div class="elements">
                        <div class="elements-title">必需信息:</div>
                        <div class="elements-list">既往疾病、过敏史</div>
                    </div>
                    <div class="elements">
                        <div class="elements-title">可选信息:</div>
                        <div class="elements-list">手术史、用药史</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="progress-section">
            <h2 style="margin-bottom: 20px; color: #333;">📊 问诊进度</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备开始问诊</div>
            
            <div class="current-agent hidden" id="currentAgent">
                <div class="current-agent-title" id="currentAgentTitle">
                    <span class="status-indicator status-waiting"></span>
                    当前工作智能体
                </div>
                <div id="currentAgentDescription">等待开始...</div>
            </div>
        </div>

        <div class="chat-section">
            <div class="patient-form" id="patientForm">
                <h3 style="margin-bottom: 20px; color: #333;">👤 患者信息</h3>
                <div class="form-group">
                    <label class="form-label" for="patientName">姓名 *</label>
                    <input type="text" id="patientName" class="form-input" placeholder="请输入您的姓名" required>
                </div>
                <div style="display: flex; gap: 20px;">
                    <div class="form-group" style="flex: 1;">
                        <label class="form-label" for="patientAge">年龄</label>
                        <input type="number" id="patientAge" class="form-input" placeholder="年龄" min="1" max="120">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label class="form-label" for="patientGender">性别</label>
                        <select id="patientGender" class="form-input">
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="startWorkflowInquiry()" style="width: 100%;">
                    🚀 开始工作流程问诊
                </button>
            </div>

            <div class="chat-container hidden" id="chatContainer">
                <div class="chat-messages" id="chatMessages"></div>
                <div class="chat-input-container">
                    <input type="text" id="chatInput" class="chat-input" placeholder="请输入您的回复..." 
                           onkeypress="if(event.key==='Enter') sendMessage()">
                    <button class="btn btn-primary" onclick="sendMessage()" id="sendBtn">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        let isInquiryActive = false;

        // 开始工作流程问诊
        async function startWorkflowInquiry() {
            const name = document.getElementById('patientName').value.trim();
            if (!name) {
                alert('请输入姓名');
                return;
            }

            const age = document.getElementById('patientAge').value;
            const gender = document.getElementById('patientGender').value;

            try {
                const response = await fetch('/api/workflow/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        patient_name: name,
                        patient_age: age ? parseInt(age) : null,
                        patient_gender: gender || null
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    currentSessionId = data.session_id;
                    isInquiryActive = true;
                    
                    // 隐藏表单，显示聊天界面
                    document.getElementById('patientForm').classList.add('hidden');
                    document.getElementById('chatContainer').classList.remove('hidden');
                    document.getElementById('currentAgent').classList.remove('hidden');
                    
                    // 显示欢迎消息
                    addMessage('assistant', data.message);
                    
                    // 更新进度
                    updateProgress(data.progress);
                    
                } else {
                    alert('启动问诊失败: ' + (data.detail || '未知错误'));
                }
            } catch (error) {
                console.error('启动问诊失败:', error);
                alert('启动问诊失败，请检查网络连接');
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message || !currentSessionId) return;

            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 禁用发送按钮
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = '处理中...';

            try {
                const response = await fetch('/api/workflow/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        session_id: currentSessionId,
                        message: message
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    // 显示AI回复
                    addMessage('assistant', data.message);
                    
                    // 更新进度
                    updateProgress(data.progress);
                    
                    // 检查是否完成
                    if (data.status === 'completed') {
                        isInquiryActive = false;
                        addMessage('system', '🎉 预问诊已完成！感谢您的配合。');
                        document.getElementById('chatInput').disabled = true;
                        sendBtn.disabled = true;
                        sendBtn.textContent = '已完成';
                    }
                    
                } else {
                    addMessage('system', '❌ 处理消息失败: ' + (data.detail || '未知错误'));
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('system', '❌ 发送消息失败，请检查网络连接');
            } finally {
                if (isInquiryActive) {
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送';
                }
            }
        }

        // 添加消息到聊天界面
        function addMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.textContent = content;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 更新进度
        function updateProgress(progress) {
            if (!progress) return;

            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const currentAgentTitle = document.getElementById('currentAgentTitle');
            const currentAgentDescription = document.getElementById('currentAgentDescription');

            // 更新进度条
            progressFill.style.width = progress.progress_percentage + '%';
            
            // 更新进度文本
            progressText.textContent = `进度: ${progress.current_agent}/${progress.total_agents} (${Math.round(progress.progress_percentage)}%)`;
            
            // 更新当前智能体信息
            if (progress.is_completed) {
                currentAgentTitle.innerHTML = '<span class="status-indicator status-completed"></span>所有智能体工作完成';
                currentAgentDescription.textContent = '预问诊流程已完成，已生成结构化病历';
            } else {
                currentAgentTitle.innerHTML = `<span class="status-indicator status-active"></span>当前工作: ${progress.current_agent_name}`;
                currentAgentDescription.textContent = `正在采集相关信息，请根据提示回答问题`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置回车键发送消息
            document.getElementById('chatInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html>
