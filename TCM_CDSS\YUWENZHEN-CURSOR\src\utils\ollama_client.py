import os
import requests
from typing import List, Dict, Any, Optional
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.outputs import ChatResult, ChatGeneration

class OllamaLLMClient(BaseChatModel):
    """Ollama 本地大模型客户端"""
    
    model_name: str
    temperature: float = 0.7
    max_tokens: int = 2000
    base_url: str = "http://localhost:11434"
    model_config = {"extra": "allow"}
    
    def __init__(self, model_name: str = None, base_url: str = None, **kwargs):
        # 确保在调用 super() 之前设置了 model_name
        if model_name:
            kwargs['model_name'] = model_name
        super().__init__(**kwargs)
        self.base_url = base_url or os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    
    @property
    def _llm_type(self) -> str:
        return "ollama"
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs
    ) -> ChatResult:
        """生成聊天响应"""
        # 转换消息格式
        ollama_messages = []
        for message in messages:
            if isinstance(message, SystemMessage):
                ollama_messages.append({"role": "system", "content": message.content})
            elif isinstance(message, HumanMessage):
                ollama_messages.append({"role": "user", "content": message.content})
            elif isinstance(message, AIMessage):
                ollama_messages.append({"role": "assistant", "content": message.content})
        
        # 调用 Ollama API
        payload = {
            "model": self.model_name,
            "messages": ollama_messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
        }
        payload.update(kwargs)
        
        try:
            response = requests.post(f"{self.base_url}/v1/chat/completions", json=payload)
            response.raise_for_status()
            result = response.json()
            
            # 解析响应
            if "choices" in result and len(result["choices"]) > 0:
                choice = result["choices"][0]
                message_content = choice["message"]["content"]
                
                # 创建 AIMessage
                ai_message = AIMessage(content=message_content)
                
                # 创建 ChatGeneration
                generation = ChatGeneration(message=ai_message)
                
                return ChatResult(generations=[generation])
            else:
                raise Exception("Ollama 响应格式错误")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Ollama API 调用失败: {e}")
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs
    ) -> ChatResult:
        """异步生成聊天响应"""
        # 目前使用同步方法，可以后续优化为真正的异步
        return self._generate(messages, stop, run_manager, **kwargs) 