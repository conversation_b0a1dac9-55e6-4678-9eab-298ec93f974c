-- =====================================================
-- 中医诊所助手系统 - 映射数据插入
-- =====================================================
-- 文件: insert_mappings.sql
-- 描述: 插入所有映射表的基础数据
-- 版本: v1.0
-- 创建日期: 2025-06-24
-- =====================================================

-- =====================================================
-- 基础映射数据
-- =====================================================

-- 性别映射数据
INSERT OR IGNORE INTO gender_mappings (code, display_name, description) VALUES
('M', '男', '男性'),
('F', '女', '女性'),
('U', '未知', '性别未知或不便透露');

-- 角色映射数据
INSERT OR IGNORE INTO role_mappings (code, display_name, description, level) VALUES
('ADMIN', '系统管理员', '系统管理员，拥有所有权限', 10),
('DOCTOR', '医生', '医生角色，拥有诊疗相关权限', 8),
('NURSE', '护士', '护士角色，拥有护理相关权限', 6),
('PHARMACIST', '药师', '药师角色，拥有药房管理权限', 6),
('RECEPTIONIST', '前台', '前台角色，拥有预约挂号权限', 4),
('TECHNICIAN', '技师', '医技人员，拥有检查检验权限', 5);

-- 科室映射数据
INSERT OR IGNORE INTO department_mappings (code, display_name, description, sort_order) VALUES
('TCM_INTERNAL', '中医内科', '中医内科', 1),
('TCM_SURGERY', '中医外科', '中医外科', 2),
('TCM_GYNECOLOGY', '中医妇科', '中医妇科', 3),
('TCM_PEDIATRICS', '中医儿科', '中医儿科', 4),
('ACUPUNCTURE', '针灸科', '针灸推拿科', 5),
('PHARMACY', '药房', '中药房', 6),
('ADMINISTRATION', '行政科', '行政管理科', 7),
('NURSING', '护理部', '护理部门', 8);

-- 状态映射数据
INSERT OR IGNORE INTO status_mappings (category, code, display_name, description, sort_order) VALUES
-- 患者状态
('patient', 'ACTIVE', '正常', '正常状态的患者', 1),
('patient', 'INACTIVE', '停用', '停用状态的患者', 2),
('patient', 'DECEASED', '已故', '已故患者', 3),
-- 预约状态
('appointment', 'PENDING', '待确认', '等待确认的预约', 1),
('appointment', 'CONFIRMED', '已确认', '已确认的预约', 2),
('appointment', 'COMPLETED', '已完成', '已完成的预约', 3),
('appointment', 'CANCELLED', '已取消', '已取消的预约', 4),
('appointment', 'ABSENT', '爽约', '患者未到的预约', 5),
-- 就诊状态
('visit', 'WAITING', '候诊', '等待就诊', 1),
('visit', 'IN_PROGRESS', '就诊中', '正在就诊', 2),
('visit', 'COMPLETED', '已完成', '就诊完成', 3),
('visit', 'BILLED', '已收费', '已收费完成', 4),
-- 处方状态
('prescription', 'DRAFT', '草稿', '处方草稿', 1),
('prescription', 'CONFIRMED', '已确认', '已确认的处方', 2),
('prescription', 'BILLED', '已收费', '已收费的处方', 3),
('prescription', 'DISPENSED', '已发药', '已发药的处方', 4),
('prescription', 'CANCELLED', '已取消', '已取消的处方', 5);

-- 症状映射数据（常见症状）
INSERT OR IGNORE INTO symptom_mappings (code, display_name, category, description) VALUES
('HEADACHE', '头痛', '神经系统', '头部疼痛'),
('FEVER', '发热', '全身症状', '体温升高'),
('COUGH', '咳嗽', '呼吸系统', '咳嗽症状'),
('FATIGUE', '乏力', '全身症状', '疲劳乏力'),
('INSOMNIA', '失眠', '神经系统', '睡眠障碍'),
('STOMACH_PAIN', '胃痛', '消化系统', '胃部疼痛'),
('BACK_PAIN', '腰痛', '骨骼肌肉', '腰部疼痛'),
('DIZZINESS', '头晕', '神经系统', '头晕目眩'),
('NAUSEA', '恶心', '消化系统', '恶心症状'),
('CHEST_PAIN', '胸痛', '循环系统', '胸部疼痛');

-- 诊断映射数据（常见疾病）
INSERT OR IGNORE INTO diagnosis_mappings (code, display_name, category, description) VALUES
-- 中医诊断
('TCM_HEADACHE', '头痛病', 'tcm', '中医头痛病'),
('TCM_INSOMNIA', '不寐', 'tcm', '中医失眠症'),
('TCM_STOMACH_PAIN', '胃痛病', 'tcm', '中医胃痛病'),
('TCM_BACK_PAIN', '腰痛病', 'tcm', '中医腰痛病'),
('TCM_FATIGUE', '虚劳', 'tcm', '中医虚劳症'),
-- 西医诊断
('HYPERTENSION', '高血压', 'western', '原发性高血压'),
('DIABETES', '糖尿病', 'western', '2型糖尿病'),
('GASTRITIS', '胃炎', 'western', '慢性胃炎'),
('LUMBAR_DISC', '腰椎间盘突出', 'western', '腰椎间盘突出症'),
('ANXIETY', '焦虑症', 'western', '焦虑障碍');

-- 证型映射数据（中医证型）
INSERT OR IGNORE INTO syndrome_mappings (code, display_name, category, description, treatment_principle) VALUES
('LIVER_QI_STAGNATION', '肝气郁结证', '肝系证候', '肝气郁结，疏泄失常', '疏肝理气'),
('SPLEEN_DEFICIENCY', '脾气虚证', '脾系证候', '脾气不足，运化失常', '健脾益气'),
('KIDNEY_YANG_DEFICIENCY', '肾阳虚证', '肾系证候', '肾阳不足，温煦失职', '温补肾阳'),
('HEART_BLOOD_STASIS', '心血瘀阻证', '心系证候', '心血瘀阻，血行不畅', '活血化瘀'),
('LUNG_QI_DEFICIENCY', '肺气虚证', '肺系证候', '肺气不足，宣降失常', '补肺益气'),
('STOMACH_YIN_DEFICIENCY', '胃阴虚证', '胃系证候', '胃阴不足，失于濡润', '养胃生津'),
('LIVER_KIDNEY_YIN_DEFICIENCY', '肝肾阴虚证', '肝肾证候', '肝肾阴虚，精血不足', '滋补肝肾'),
('PHLEGM_DAMPNESS', '痰湿证', '痰湿证候', '痰湿内生，阻滞气机', '化痰除湿'),
('BLOOD_STASIS', '血瘀证', '血瘀证候', '血行瘀滞，脉络不通', '活血化瘀'),
('QI_BLOOD_DEFICIENCY', '气血两虚证', '虚证', '气血俱虚，脏腑失养', '益气养血');

-- 产品类型映射数据
INSERT OR IGNORE INTO product_type_mappings (code, display_name, category, description) VALUES
('HERBAL', '中药饮片', 'medicine', '中药饮片'),
('PATENT', '中成药', 'medicine', '中成药制剂'),
('WESTERN', '西药', 'medicine', '西药制剂'),
('SERVICE', '医疗服务', 'service', '医疗服务项目'),
('CONSUMABLE', '医用耗材', 'consumable', '医用耗材');

-- 产品分类映射数据
INSERT OR IGNORE INTO product_category_mappings (code, display_name, description, sort_order) VALUES
('EXTERIOR_RELEASING', '解表药', '解表类中药', 1),
('HEAT_CLEARING', '清热药', '清热类中药', 2),
('QI_TONIFYING', '补气药', '补气类中药', 3),
('BLOOD_TONIFYING', '补血药', '补血类中药', 4),
('QI_REGULATING', '理气药', '理气类中药', 5),
('BLOOD_ACTIVATING', '活血药', '活血化瘀类中药', 6),
('CONSULTATION', '诊疗费', '诊疗服务费用', 1),
('ACUPUNCTURE_FEE', '针灸费', '针灸治疗费用', 2),
('MASSAGE_FEE', '推拿费', '推拿治疗费用', 3);

-- 用法映射数据
INSERT OR IGNORE INTO usage_mappings (code, display_name, description, instruction) VALUES
('NORMAL_DECOCTION', '正常煎煮', '常规煎煮方法', '先煎30分钟'),
('FIRST_DECOCTION', '先煎', '需要先煎的药物', '先煎60分钟后再下其他药'),
('LATER_ADDITION', '后下', '需要后下的药物', '其他药煎好前5分钟下'),
('WRAPPED_DECOCTION', '包煎', '需要包煎的药物', '用纱布包好再煎'),
('DISSOLVE', '烊化', '需要烊化的药物', '用药汁烊化后服用'),
('POWDER', '研末冲服', '研成粉末冲服', '研成细末，用温水冲服');

-- 预约来源映射数据
INSERT OR IGNORE INTO appointment_source_mappings (code, display_name, description) VALUES
('ONLINE', '线上预约', '通过网络平台预约'),
('OFFLINE', '现场预约', '现场挂号预约'),
('PHONE', '电话预约', '电话预约'),
('WECHAT', '微信预约', '微信小程序预约');

-- 就诊类型映射数据
INSERT OR IGNORE INTO visit_type_mappings (code, display_name, description) VALUES
('FIRST_VISIT', '初诊', '首次就诊'),
('FOLLOW_UP', '复诊', '复诊就诊'),
('EMERGENCY', '急诊', '急诊就诊'),
('CONSULTATION', '会诊', '多科会诊');

-- 支付方式映射数据
INSERT OR IGNORE INTO payment_method_mappings (code, display_name, description, is_online) VALUES
('CASH', '现金', '现金支付', 0),
('CARD', '银行卡', '银行卡支付', 0),
('WECHAT', '微信支付', '微信支付', 1),
('ALIPAY', '支付宝', '支付宝支付', 1),
('MEDICAL_INSURANCE', '医保', '医保支付', 0);

-- 权限模块映射数据
INSERT OR IGNORE INTO permission_module_mappings (code, display_name, description, icon, sort_order) VALUES
('PATIENT', '患者管理', '患者信息管理模块', 'user', 1),
('APPOINTMENT', '预约管理', '预约挂号管理模块', 'calendar', 2),
('OUTPATIENT', '门诊工作站', '门诊诊疗工作站', 'desktop', 3),
('PHARMACY', '药房管理', '药房处方管理模块', 'medicine-box', 4),
('BILLING', '收费管理', '收费结算管理模块', 'money-collect', 5),
('STAFF', '员工管理', '员工信息管理模块', 'team', 6),
('REPORT', '统计报表', '统计报表模块', 'bar-chart', 7),
('SYSTEM', '系统设置', '系统设置管理模块', 'setting', 8);

-- 权限操作映射数据
INSERT OR IGNORE INTO permission_action_mappings (code, display_name, description) VALUES
('VIEW', '查看', '查看权限'),
('CREATE', '新增', '新增权限'),
('EDIT', '编辑', '编辑权限'),
('DELETE', '删除', '删除权限'),
('EXPORT', '导出', '导出权限'),
('APPROVE', '审核', '审核权限'),
('CANCEL', '取消', '取消权限'),
('DISPENSE', '发药', '发药权限'),
('PAYMENT', '收费', '收费权限');

SELECT 'Mapping data inserted successfully!' AS message;

-- 显示统计信息
SELECT 
    (SELECT COUNT(*) FROM gender_mappings) AS gender_count,
    (SELECT COUNT(*) FROM role_mappings) AS role_count,
    (SELECT COUNT(*) FROM department_mappings) AS department_count,
    (SELECT COUNT(*) FROM status_mappings) AS status_count,
    (SELECT COUNT(*) FROM symptom_mappings) AS symptom_count,
    (SELECT COUNT(*) FROM diagnosis_mappings) AS diagnosis_count,
    (SELECT COUNT(*) FROM syndrome_mappings) AS syndrome_count;
