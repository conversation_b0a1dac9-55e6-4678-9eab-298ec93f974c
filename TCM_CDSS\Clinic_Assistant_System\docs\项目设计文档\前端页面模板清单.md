#### 一、 基础框架与通用页面 (约 7个页面)
这些是系统运行必不可少的基础部分。
- 主布局 (Main Layout)：这不是一个页面，而是一个外壳。包含侧边栏菜单、顶部导航栏、内容区 (<router-view>)。所有业务页面都将嵌套在这个布局中。
- 登录页 (/login): 独立的页面，不使用主布局。
- 忘记密码页 (/forgot-password): 独立页面。
- 重置密码页 (/reset-password): 独立页面。
- 个人中心页 (/profile): 用于员工修改自己的密码和信息，可以是一个简单的表单页。
- 404 Not Found 页: 路由未匹配时显示。
- 403 Forbidden 页: 无权限访问时显示。
#### 二、 可复用的核心模板 (2个核心模板)
这两个模板是开发效率的关键，它们本身是页面，但能通过配置适配多个路由。
- 通用列表页模板 (Generic List Page):适用路由: /patients, /billing/history, /pharmacy/pending-prescriptions, /inventory/herbs, /inventory/products, /inventory/alerts, /inventory/suppliers, /staff, /staff/roles 等所有以表格展示数据的列表页。实现: 一个页面模板，接收配置来动态生成。
- 通用表单页模板 (Generic Form Page):适用路由 : /patients/new, /patients/:id/profile, /staff/new, /staff/:id/edit, /inventory/suppliers/new, /settings/* 中的大部分设置页面。实现: 一个页面模板，根据配置动态渲染表单项和处理提交逻辑。
#### 三、 核心业务专属页面 (约 11-15个页面)
这些页面的布局和交互逻辑比较独特，难以用通用模板完全覆盖，需要独立开发。
- 工作台 (/dashboard): 布局独特，包含各种统计图表和快捷入口，是高度定制化的页面。
- 预约日历页 (/appointments): 核心是日历组件（如 FullCalendar），交互复杂，需要一个专属页面来承载日历视图和相关操作。
- 预约表单 (Appointment Form): 虽然是表单，但新建/编辑预约的逻辑（如选择患者、选择时间、冲突检测）比通用表单复杂，通常会做成一个独立的组件或弹窗，这里算作一个独立的开发单元。
- 患者详情页 (/patients/:id): 这是一个容器页面，通常带有标签页（Tabs）结构，用于切换显示患者的不同信息（就诊记录、历史处方等）。
- 电子病历页 (EMR Page - /visits/:visitId/emr): 系统中最复杂、最核心的页面。布局特殊，包含四诊信息、辨证论治、处方开立等多个专用区域，交互极其复杂，必须是独立页面。
- 划价收费/结账页 (/billing/:visitId/checkout): 专门的结账界面，汇总费用明细、支持多种支付方式、打印票据等，布局和逻辑都非常独特。
- 待收费列表页 (/billing): 虽然是列表，但其行操作（去收费）和状态（待收费、已收费）比较特殊，可以基于通用列表模板扩展，但有时也可能因业务逻辑复杂而独立。我们暂且将其视为通用列表。
- 药房发药详情页 (/pharmacy/prescription/:prescriptionId): 展示处方详情，并提供配药、核对、发药等一系列流程操作，是一个专用的工作流页面。
- 入库管理页 (/inventory/stock-in): 入库表单，可能涉及选择供应商、批号、有效期等，比通用表单复杂，可以算作一个独立页面。
- 出库管理页 (/inventory/stock-out): 出库表单，涉及出库原因、盘点损耗等，与入库不同，也算一个独立页面。
- 报表页面 (Reporting Pages - /reports/*): 每个报表都可能是一个独特的页面。
    - 财务报表页 (Financial Report)
    - 患者分析页 (Patient Analytics)
    - 临床分析页 (Clinical Analytics)
    - 工作量报表页 (Workload Report)
    - 估算: 至少需要 4个 独立的报表页面，因为它们的图表、数据维度和布局各不相同。