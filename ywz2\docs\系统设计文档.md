# 中医诊前预问诊智能体系统设计文档

## 1. 项目概述

### 1.1 项目目标
开发一个基于最新LangChain和LangGraph技术栈的多智能体协同中医预问诊系统，通过结构化对话采集患者的完整病史信息，为医生诊疗提供准确的患者背景资料。

### 1.2 核心功能
- 通过自然对话方式采集患者病史信息
- 提取结构化的医疗实体和关键信息
- 生成标准化的中医病历格式
- 支持智能追问和信息补全
- 多智能体协同工作流编排

### 1.3 技术栈选型
- **LangChain**: 最新版本 (0.2.x)，使用LCEL（LangChain Expression Language）管道开发
- **LangGraph**: 最新版本 (0.2.x)，用于复杂多智能体工作流编排
- **OpenRouter**: 作为大模型调用接口，支持多种LLM模型
- **Python**: 3.9+
- **Pydantic**: 数据模型定义和验证
- **FastAPI**: API接口开发
- **SQLAlchemy**: 数据持久化
- **Redis**: 会话状态管理

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
├─────────────────────────────────────────────────────────────┤
│  Web界面  │  移动端  │  语音接口  │  第三方系统集成          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    API网关层                                │
├─────────────────────────────────────────────────────────────┤
│  认证授权  │  请求路由  │  限流控制  │  日志记录              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  智能体编排层                                │
├─────────────────────────────────────────────────────────────┤
│  LangGraph工作流引擎  │  智能体调度器  │  状态管理器          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  专业智能体层                                │
├─────────────────────────────────────────────────────────────┤
│ 主诉采集 │ 现病史 │ 既往史 │ 遗传病史 │ 传染病史 │ 过敏史 │
│ 手术史   │ 婚育史 │ 家族史 │ 预防接种史 │ 经带胎产史        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  大模型调用层                                │
├─────────────────────────────────────────────────────────────┤
│  OpenRouter API  │  模型选择器  │  响应解析器              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  数据处理层                                │
├─────────────────────────────────────────────────────────────┤
│  实体提取  │  信息验证  │  数据清洗  │  格式转换              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  数据存储层                                │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL │  Redis  │  文件存储  │  日志存储              │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件说明

#### 2.2.1 智能体编排层
- **LangGraph工作流引擎**: 负责协调多个智能体的执行顺序和状态转换
- **智能体调度器**: 根据对话状态和患者信息动态调度相应的智能体
- **状态管理器**: 维护整个问诊会话的状态信息

#### 2.2.2 专业智能体层
每个智能体都是独立的LangChain组件，具有以下特性：
- 专业的领域知识
- 标准化的输入输出格式
- 智能追问能力
- 实体提取功能
- **统一的"先问有无，若有则追问，无则结束"逻辑**

#### 2.2.3 大模型调用层
- **OpenRouter API**: 统一的LLM调用接口
- **模型选择器**: 根据任务类型选择合适的模型
- **响应解析器**: 解析和验证LLM响应

## 3. 智能体详细设计

### 3.1 智能体架构模式
```python
class BaseAgent:
    """智能体基类"""
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.openrouter_client = None
        self.prompt_template = None
        self.output_parser = None
    
    def create_chain(self) -> Runnable:
        """创建LCEL管道"""
        pass
    
    def extract_entities(self, response: str) -> Dict:
        """提取医疗实体"""
        pass
    
    def generate_follow_up(self, context: Dict) -> str:
        """生成追问"""
        pass
    
    def ask_has_or_not(self, context: Dict) -> str:
        """询问有无"""
        pass
    
    def ask_details_if_has(self, context: Dict) -> str:
        """如果有则追问详情"""
        pass
```

### 3.2 智能体统一逻辑流程

#### 3.2.1 "先问有无，若有则追问，无则结束"流程
```python
def agent_workflow(self, context: Dict) -> AgentResponse:
    """智能体工作流程"""
    
    # 第一步：询问有无
    has_response = self.ask_has_or_not(context)
    
    # 解析患者回答
    has_result = self.parse_has_or_not_response(has_response)
    
    if has_result["has"]:
        # 第二步：如果有，则追问详情
        details_response = self.ask_details_if_has(context, has_result)
        
        # 第三步：提取实体和结构化信息
        entities = self.extract_entities(details_response)
        structured_data = self.structure_data(details_response)
        
        return AgentResponse(
            response=details_response,
            extracted_data=structured_data,
            entities=entities,
            is_complete=True
        )
    else:
        # 如果没有，则结束当前智能体
        return AgentResponse(
            response="好的，了解了。",
            extracted_data={"has_symptom": False},
            entities=[],
            is_complete=True
        )
```

### 3.3 核心智能体设计

#### 3.3.1 主诉采集智能体 (ChiefComplaintAgent)
**职责**: 采集患者主要症状和持续时间
**逻辑流程**:
1. 询问："您有什么不适症状吗？"
2. 如果有症状，追问：症状性质、部位、持续时间、严重程度等
3. 如果没有症状，结束

```python
class ChiefComplaintAgent(BaseAgent):
    def ask_has_or_not(self, context: Dict) -> str:
        """询问有无主诉"""
        return "您有什么不适症状吗？请详细描述一下。"
    
    def ask_details_if_has(self, context: Dict, has_result: Dict) -> str:
        """如果有主诉，追问详情"""
        return """
        请详细说明以下信息：
        1. 主要症状是什么？
        2. 症状持续多长时间了？
        3. 症状在哪个部位？
        4. 症状的严重程度如何？
        5. 什么情况下会加重或减轻？
        """
```

#### 3.3.2 现病史采集智能体 (PresentIllnessAgent)
**职责**: 详细追问当前疾病相关信息
**逻辑流程**: 对每个症状类别执行"先问有无，若有则追问，无则结束"

```python
class PresentIllnessAgent(BaseAgent):
    def __init__(self):
        self.symptom_categories = [
            "寒热症状", "汗症", "疼痛症状", "头身胸腹不适",
            "耳目症状", "睡眠情况", "饮食口味", "二便情况"
        ]
        self.current_category_index = 0
    
    def ask_has_or_not(self, context: Dict) -> str:
        """询问当前症状类别有无"""
        current_category = self.symptom_categories[self.current_category_index]
        
        questions = {
            "寒热症状": "您最近有发热或怕冷的感觉吗？",
            "汗症": "您最近出汗情况如何？",
            "疼痛症状": "您有疼痛的感觉吗？",
            "头身胸腹不适": "头部、身体、胸部或腹部有不适感吗？",
            "耳目症状": "眼睛或耳朵有异常感觉吗？",
            "睡眠情况": "您的睡眠情况如何？",
            "饮食口味": "您的食欲和口味有什么异常吗？",
            "二便情况": "大便和小便情况如何？"
        }
        
        return questions.get(current_category, "您有其他不适吗？")
    
    def ask_details_if_has(self, context: Dict, has_result: Dict) -> str:
        """如果有症状，追问详情"""
        current_category = self.symptom_categories[self.current_category_index]
        
        detail_questions = {
            "寒热症状": """
            请详细说明：
            1. 发热或怕冷的程度如何？
            2. 发热或怕冷的时间规律？
            3. 是否同时出现其他症状？
            """,
            "汗症": """
            请详细说明：
            1. 出汗的颜色和质地？
            2. 出汗的时间和量？
            3. 出汗的部位？
            """,
            # ... 其他症状类别的详细问题
        }
        
        return detail_questions.get(current_category, "请详细描述一下。")
```

#### 3.3.3 其他专业智能体
- **既往史采集智能体**: 询问既往疾病、手术史、过敏史等
- **家族史采集智能体**: 询问家族疾病史、遗传病史等
- **过敏史采集智能体**: 询问药物、食物、环境过敏等
- **手术史采集智能体**: 询问手术经历、外伤史等
- **婚育史采集智能体**: 询问婚姻、生育、月经史等
- **家族史采集智能体**: 询问家族疾病史等
- **预防接种史采集智能体**: 询问疫苗接种情况等

### 3.4 OpenRouter集成设计

#### 3.4.1 OpenRouter客户端配置
```python
class OpenRouterClient:
    """OpenRouter客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        })
    
    def chat_completion(self, messages: List[Dict], model: str = "anthropic/claude-3.5-sonnet") -> Dict:
        """调用OpenRouter聊天接口"""
        payload = {
            "model": model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        response = self.session.post(f"{self.base_url}/chat/completions", json=payload)
        return response.json()
    
    def get_available_models(self) -> List[Dict]:
        """获取可用模型列表"""
        response = self.session.get(f"{self.base_url}/models")
        return response.json()["data"]
```

#### 3.4.2 模型选择策略
```python
class ModelSelector:
    """模型选择器"""
    
    def __init__(self):
        self.model_mapping = {
            "chief_complaint": "anthropic/claude-3.5-sonnet",
            "present_illness": "anthropic/claude-3.5-sonnet",
            "past_history": "openai/gpt-4",
            "family_history": "openai/gpt-4",
            "allergy_history": "anthropic/claude-3.5-sonnet"
        }
    
    def select_model(self, agent_type: str) -> str:
        """根据智能体类型选择模型"""
        return self.model_mapping.get(agent_type, "anthropic/claude-3.5-sonnet")
```

## 4. 数据模型设计

### 4.1 核心数据模型

#### 4.1.1 患者信息模型
```python
class PatientInfo(BaseModel):
    """患者基本信息"""
    patient_id: str
    name: str
    gender: Literal["男", "女"]
    age: int
    contact: str
    created_at: datetime
    updated_at: datetime
```

#### 4.1.2 问诊会话模型
```python
class ConsultationSession(BaseModel):
    """问诊会话"""
    session_id: str
    patient_id: str
    status: Literal["进行中", "已完成", "已暂停"]
    current_agent: str
    progress: float  # 0-1
    created_at: datetime
    updated_at: datetime
```

#### 4.1.3 智能体响应模型
```python
class AgentResponse(BaseModel):
    """智能体响应"""
    response: str
    extracted_data: Dict[str, Any]
    entities: List[MedicalEntity]
    confidence: float
    next_question: Optional[str]
    is_complete: bool
    metadata: Dict[str, Any]
```

#### 4.1.4 中医病历模型
```python
class TCMMedicalRecord(BaseModel):
    """中医病历"""
    record_id: str
    patient_id: str
    session_id: str
    
    # 基本信息
    chief_complaint: str
    present_illness: Dict[str, Any]
    past_history: Dict[str, Any]
    family_history: Dict[str, Any]
    
    # 四诊信息
    inspection: Dict[str, Any]  # 望诊
    auscultation: Dict[str, Any]  # 闻诊
    inquiry: Dict[str, Any]  # 问诊
    palpation: Dict[str, Any]  # 切诊
    
    # 诊断信息
    diagnosis: List[str]
    syndrome_differentiation: List[str]
    
    created_at: datetime
    updated_at: datetime
```

## 5. LangGraph工作流设计

### 5.1 工作流状态定义
```python
class ConsultationState(TypedDict):
    """问诊状态"""
    patient_id: str
    session_id: str
    current_step: str
    collected_data: Dict[str, Any]
    conversation_history: List[Dict[str, str]]
    next_agent: Optional[str]
    is_complete: bool
    medical_record: Optional[TCMMedicalRecord]
```

### 5.2 工作流节点设计

#### 5.2.1 主控节点
```python
def consultation_controller(state: ConsultationState) -> ConsultationState:
    """主控节点 - 决定下一步执行哪个智能体"""
    current_step = state["current_step"]
    
    # 智能体执行顺序
    agent_sequence = [
        "chief_complaint",
        "present_illness", 
        "past_history",
        "family_history",
        "allergy_history",
        "surgery_history",
        "marriage_fertility",
        "vaccination_history"
    ]
    
    # 根据当前步骤确定下一个智能体
    if current_step in agent_sequence:
        current_index = agent_sequence.index(current_step)
        if current_index < len(agent_sequence) - 1:
            state["next_agent"] = agent_sequence[current_index + 1]
        else:
            state["is_complete"] = True
    
    return state
```

#### 5.2.2 智能体执行节点
```python
def execute_agent(state: ConsultationState, agent_name: str) -> ConsultationState:
    """执行指定智能体"""
    agent = get_agent(agent_name)
    
    # 获取当前对话上下文
    context = {
        "patient_id": state["patient_id"],
        "collected_data": state["collected_data"],
        "conversation_history": state["conversation_history"]
    }
    
    # 执行智能体（遵循"先问有无，若有则追问，无则结束"逻辑）
    result = agent.run(context)
    
    # 更新状态
    state["collected_data"].update(result["extracted_data"])
    state["conversation_history"].append({
        "role": "agent",
        "content": result["response"],
        "timestamp": datetime.now()
    })
    
    return state
```

### 5.3 工作流图构建
```python
def create_consultation_workflow() -> StateGraph:
    """创建问诊工作流"""
    workflow = StateGraph(ConsultationState)
    
    # 添加节点
    workflow.add_node("controller", consultation_controller)
    workflow.add_node("chief_complaint_agent", lambda s: execute_agent(s, "chief_complaint"))
    workflow.add_node("present_illness_agent", lambda s: execute_agent(s, "present_illness"))
    workflow.add_node("past_history_agent", lambda s: execute_agent(s, "past_history"))
    
    # 添加边
    workflow.add_edge("controller", "chief_complaint_agent")
    workflow.add_edge("chief_complaint_agent", "controller")
    workflow.add_edge("controller", "present_illness_agent")
    workflow.add_edge("present_illness_agent", "controller")
    
    # 设置入口和出口
    workflow.set_entry_point("controller")
    workflow.add_conditional_edges(
        "controller",
        lambda s: "end" if s["is_complete"] else s["next_agent"],
        {
            "chief_complaint_agent": "chief_complaint_agent",
            "present_illness_agent": "present_illness_agent",
            "past_history_agent": "past_history_agent",
            "end": END
        }
    )
    
    return workflow.compile()
```

## 6. API接口设计

### 6.1 RESTful API设计

#### 6.1.1 会话管理接口
```python
# 创建问诊会话
POST /api/v1/consultations
{
    "patient_id": "string",
    "patient_name": "string",
    "gender": "男|女",
    "age": "integer"
}

# 获取会话状态
GET /api/v1/consultations/{session_id}

# 发送消息
POST /api/v1/consultations/{session_id}/messages
{
    "message": "string",
    "message_type": "text|voice|image"
}
```

#### 6.1.2 智能体管理接口
```python
# 获取可用智能体列表
GET /api/v1/agents

# 获取智能体详情
GET /api/v1/agents/{agent_name}

# 手动切换智能体
POST /api/v1/consultations/{session_id}/switch-agent
{
    "agent_name": "string"
}
```

#### 6.1.3 病历管理接口
```python
# 获取病历
GET /api/v1/medical-records/{record_id}

# 导出病历
GET /api/v1/medical-records/{record_id}/export
{
    "format": "json|pdf|docx"
}

# 更新病历
PUT /api/v1/medical-records/{record_id}
```

### 6.2 WebSocket接口设计
```python
# 实时对话接口
WS /api/v1/consultations/{session_id}/chat

# 消息格式
{
    "type": "message|status|error",
    "data": {
        "content": "string",
        "timestamp": "datetime",
        "agent": "string"
    }
}
```

## 7. 技术实现方案

### 7.1 项目结构
```
ywz2/
├── docs/                    # 文档目录
│   ├── 系统设计文档.md
│   ├── API文档.md
│   └── 部署指南.md
├── src/                     # 源代码目录
│   ├── agents/             # 智能体实现
│   │   ├── __init__.py
│   │   ├── base_agent.py
│   │   ├── chief_complaint_agent.py
│   │   ├── present_illness_agent.py
│   │   └── ...
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   ├── patient.py
│   │   ├── consultation.py
│   │   └── medical_record.py
│   ├── workflows/          # 工作流定义
│   │   ├── __init__.py
│   │   └── consultation_workflow.py
│   ├── utils/              # 工具函数
│   │   ├── __init__.py
│   │   ├── entity_extractor.py
│   │   ├── medical_parser.py
│   │   └── openrouter_client.py
│   ├── api/                # API接口
│   │   ├── __init__.py
│   │   ├── routes.py
│   │   └── websocket.py
│   └── main.py             # 应用入口
├── tests/                  # 测试目录
├── requirements.txt        # 依赖管理
├── docker-compose.yml      # 容器编排
└── README.md              # 项目说明
```

### 7.2 依赖管理
```txt
# requirements.txt
langchain>=0.2.0
langgraph>=0.2.0
langchain-openai>=0.1.0
langchain-community>=0.2.0
openrouter>=0.1.0
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0
pydantic>=2.5.0
pydantic-settings>=2.1.0
sqlalchemy>=2.0.0
alembic>=1.13.0
psycopg2-binary>=2.9.0
redis>=5.0.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-dotenv>=1.0.0
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.25.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.7.0
mkdocs>=1.5.0
mkdocs-material>=9.4.0
structlog>=23.2.0
python-dateutil>=2.8.0
orjson>=3.9.0
asyncio-mqtt>=0.16.0
```

### 7.3 配置管理
```python
# config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # 数据库配置
    database_url: str = "postgresql://user:pass@localhost/tcm_db"
    redis_url: str = "redis://localhost:6379"
    
    # OpenRouter配置
    openrouter_api_key: str
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    default_model: str = "anthropic/claude-3.5-sonnet"
    
    # 应用配置
    app_name: str = "TCM Consultation System"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    
    class Config:
        env_file = ".env"
```

## 8. 部署方案

### 8.1 开发环境部署
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
    volumes:
      - .:/app
    depends_on:
      - postgres
      - redis
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: tcm_db
      POSTGRES_USER: tcm_user
      POSTGRES_PASSWORD: tcm_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

### 8.2 生产环境部署
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  app:
    build: .
    restart: unless-stopped
    environment:
      - DEBUG=false
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
    depends_on:
      - postgres
      - redis
    deploy:
      replicas: 3
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
```

## 9. 测试策略

### 9.1 单元测试
- 智能体功能测试
- 数据模型验证测试
- 工作流节点测试
- 实体提取测试
- OpenRouter集成测试

### 9.2 集成测试
- API接口测试
- 工作流集成测试
- 数据库集成测试
- 第三方服务集成测试

### 9.3 端到端测试
- 完整问诊流程测试
- 多用户并发测试
- 性能压力测试
- 用户体验测试

## 10. 监控和日志

### 10.1 监控指标
- 系统性能指标（响应时间、吞吐量）
- 智能体执行指标（成功率、准确率）
- 用户行为指标（会话时长、完成率）
- 业务指标（问诊质量、用户满意度）
- OpenRouter API调用指标

### 10.2 日志管理
- 结构化日志记录
- 日志级别分类
- 日志聚合和分析
- 异常告警机制

## 11. 安全考虑

### 11.1 数据安全
- 患者数据加密存储
- 数据传输加密
- 访问权限控制
- 数据备份和恢复

### 11.2 系统安全
- API接口认证
- 请求频率限制
- 输入数据验证
- 安全漏洞扫描
- OpenRouter API密钥保护

## 12. 后续扩展计划

### 12.1 功能扩展
- 语音识别和合成
- 图像识别（舌诊、面诊）
- 多语言支持
- 移动端应用

### 12.2 技术扩展
- 机器学习模型集成
- 知识图谱构建
- 推荐系统
- 大数据分析

## 13. 总结

本设计文档为中医诊前预问诊智能体系统提供了完整的技术方案，包括：

1. **系统架构设计**: 基于LangChain和LangGraph的多层架构，集成OpenRouter
2. **智能体设计**: 10个专业智能体，统一"先问有无，若有则追问，无则结束"逻辑
3. **数据模型**: 完整的数据结构和实体定义
4. **工作流设计**: LangGraph工作流的实现方案
5. **API设计**: RESTful和WebSocket接口规范
6. **部署方案**: 开发和生产环境配置
7. **测试策略**: 全面的测试覆盖方案

该设计文档为后续开发工作提供了清晰的技术路线图和实现指导，确保系统能够高效、稳定地运行，为中医诊疗提供有力的技术支持。 