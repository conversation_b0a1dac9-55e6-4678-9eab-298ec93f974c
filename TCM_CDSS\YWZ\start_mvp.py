#!/usr/bin/env python3
"""
中医预问诊智能体系统 - MVP架构启动脚本
前后端分离，统一服务器
"""

import os
import sys
import subprocess
import webbrowser
import time
import urllib.request
import json
from pathlib import Path
import argparse

def print_banner():
    """打印启动横幅"""
    print("🏥 中医预问诊智能体系统 - MVP架构")
    print("=" * 60)
    print("📁 前后端分离架构")
    print("🚀 统一服务器部署")
    print("🎨 现代化界面设计")
    print("=" * 60)

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本需要3.8+")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查项目结构
    required_dirs = [
        "backend",
        "frontend", 
        "frontend/pages",
        "backend/api",
        "backend/services"
    ]
    
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            print(f"❌ 缺少目录: {dir_path}")
            return False
        print(f"✅ 目录存在: {dir_path}")
    
    # 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️ .env文件不存在，创建默认配置...")
        create_default_env()
    
    print("✅ 环境配置检查完成")
    return True

def create_default_env():
    """创建默认环境配置"""
    default_env = """# LLM服务选择 (ollama 或 openrouter)
LLM_SERVICE=ollama

# Ollama配置
OLLAMA_BASE_URL=http://************:8080
OLLAMA_MODEL=qwen2.5:14b
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2048
LLM_TIMEOUT=30

# OpenRouter配置 (备用)
OPENROUTER_API_KEY=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
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=meta-llama/llama-3.2-3b-instruct:free

# 服务器配置
HOST=0.0.0.0
PORT=8002
DEBUG=true
LOG_LEVEL=INFO

# CORS配置
CORS_ORIGINS=*
CORS_CREDENTIALS=true
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(default_env)
    
    print("✅ 已创建默认.env配置文件")

def install_dependencies():
    """安装依赖"""
    print("📦 检查并安装依赖...")
    
    try:
        # 检查是否在conda环境中
        conda_env = os.environ.get('CONDA_DEFAULT_ENV')
        if conda_env:
            print(f"✅ 当前conda环境: {conda_env}")
        else:
            print("⚠️ 建议使用conda环境: conda activate llms")
        
        # 安装依赖
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ 依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def start_server(host="0.0.0.0", port=8002, dev_mode=False):
    """启动服务器"""
    print(f"🚀 启动MVP服务器...")
    print(f"   地址: http://{host}:{port}")
    print(f"   模式: {'开发模式' if dev_mode else '生产模式'}")
    
    try:
        # 切换到backend目录
        os.chdir("backend")
        
        # 构建启动命令
        if dev_mode:
            cmd = [sys.executable, "app.py"]
        else:
            cmd = [
                sys.executable, "-m", "uvicorn", 
                "app:app", 
                "--host", host, 
                "--port", str(port),
                "--reload" if dev_mode else "--no-reload"
            ]
        
        # 启动服务器进程
        process = subprocess.Popen(cmd)
        
        # 切换回原目录
        os.chdir("..")
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(8)
        
        # 检查服务器是否启动成功
        try:
            with urllib.request.urlopen(f"http://localhost:{port}/api/health", timeout=10) as response:
                data = json.loads(response.read().decode('utf-8'))
                if data.get('status') in ['healthy', 'degraded']:
                    print("✅ 服务器启动成功")
                    return process
                else:
                    print("❌ 服务器健康检查失败")
                    return None
                    
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return None

def open_browser(port=8002):
    """打开浏览器"""
    print("🌐 自动打开浏览器...")
    
    try:
        server_url = f"http://localhost:{port}"
        
        print(f"🚀 正在打开: MVP架构主页")
        webbrowser.open(server_url)
        
        print(f"✅ 已在浏览器中打开主页")
        print(f"🌐 主页地址: {server_url}")
        
        return server_url
        
    except Exception as e:
        print(f"⚠️ 自动打开失败: {e}")
        return None

def show_usage_info(port=8002):
    """显示使用说明"""
    print("\n🎉 MVP系统启动完成！")
    print("=" * 60)
    print("🌐 主页已在浏览器中打开")
    print("📱 您可以选择不同的演示页面版本")
    print("\n🔗 可用地址:")
    print(f"   主页导航: http://localhost:{port}")
    print(f"   iOS演示: http://localhost:{port}/pages/ios")
    print(f"   简化演示: http://localhost:{port}/pages/simple")
    print(f"   智能体演示: http://localhost:{port}/pages/agent")
    print(f"   完整演示: http://localhost:{port}/pages/full")
    print(f"   API文档: http://localhost:{port}/api/docs")
    print(f"   健康检查: http://localhost:{port}/api/health")
    print("\n🎯 快捷访问:")
    print(f"   iOS: http://localhost:{port}/ios")
    print(f"   简化: http://localhost:{port}/simple")
    print(f"   智能体: http://localhost:{port}/agent")
    print("\n💡 使用说明:")
    print("1. 在主页选择您喜欢的演示版本")
    print("2. 点击'开始问诊'启动AI助手")
    print("3. 手动输入症状描述")
    print("4. 跟随AI助手的问题逐步提供信息")
    print("5. 体验完整的中医问诊信息采集流程")
    print("\n🏗️ MVP架构特色:")
    print("✅ 前后端分离设计")
    print("✅ 统一服务器部署")
    print("✅ RESTful API接口")
    print("✅ 现代化前端界面")
    print("✅ 响应式设计")
    print("\n⏹️ 按 Ctrl+C 停止服务器")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="中医预问诊智能体系统 - MVP架构")
    parser.add_argument("--host", default="0.0.0.0", help="服务器地址")
    parser.add_argument("--port", type=int, default=8002, help="服务器端口")
    parser.add_argument("--dev", action="store_true", help="开发模式")
    parser.add_argument("--no-browser", action="store_true", help="不自动打开浏览器")
    
    args = parser.parse_args()
    
    print_banner()
    
    try:
        # 1. 检查环境
        if not check_environment():
            print("❌ 环境检查失败，请修复后重试")
            return 1
        
        # 2. 安装依赖
        if not install_dependencies():
            print("❌ 依赖安装失败，请手动安装: pip install -r requirements.txt")
            return 1
        
        # 3. 启动服务器
        server_process = start_server(args.host, args.port, args.dev)
        if not server_process:
            print("❌ 服务器启动失败")
            return 1
        
        # 4. 打开浏览器
        if not args.no_browser:
            open_browser(args.port)
        
        # 5. 显示使用说明
        show_usage_info(args.port)
        
        # 6. 等待用户中断
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            server_process.terminate()
            server_process.wait()
            print("✅ 服务器已停止")
        
        return 0
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
