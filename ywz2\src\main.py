from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager

from .config import settings
from .api import router, websocket_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    print(f"🚀 启动 {settings.app_name}")
    print(f"📊 配置信息:")
    print(f"   - 调试模式: {settings.debug}")
    print(f"   - 监听地址: {settings.host}:{settings.port}")
    print(f"   - 默认模型: {settings.default_model}")
    
    yield
    
    # 关闭时的清理
    print(f"👋 关闭 {settings.app_name}")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    description="基于OpenRouter + LangChain + LangGraph的中医诊前预问诊智能体系统",
    version="0.1.0",
    debug=settings.debug,
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(router)
app.include_router(websocket_router)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用 {settings.app_name}",
        "version": "0.1.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": settings.app_name,
        "timestamp": "2024-12-19T10:00:00Z"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": str(exc),
            "type": type(exc).__name__
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    ) 