"""
健康检查API
"""

from fastapi import APIRouter
from datetime import datetime
from models.response_models import HealthResponse
from services.llm_service import llm_service
from config.settings import settings

router = APIRouter()

@router.get("/health", response_model=HealthResponse)
async def health_check():
    """系统健康检查"""
    
    # 检查LLM服务
    llm_healthy = llm_service.check_health()
    
    # 确定整体状态
    overall_status = "healthy" if llm_healthy else "degraded"
    
    return HealthResponse(
        status=overall_status,
        version="2.0.0",
        services={
            "llm": "healthy" if llm_healthy else "unhealthy",
            "llm_service": settings.llm_service,
            "llm_model": settings.ollama_model if settings.is_ollama_enabled else settings.default_model,
            "llm_url": settings.ollama_base_url if settings.is_ollama_enabled else settings.openrouter_base_url,
            "frontend": "healthy",
            "api": "healthy"
        }
    )
