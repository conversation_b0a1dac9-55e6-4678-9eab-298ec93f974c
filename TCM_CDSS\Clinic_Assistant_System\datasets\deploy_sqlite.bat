@echo off
chcp 65001 >nul
echo =====================================================
echo 中医诊所助手系统 - SQLite数据库部署脚本
echo =====================================================
echo.

:: 设置变量
set DATABASE_FILE=tcm_clinic_system.db
set SQLITE_URL=https://www.sqlite.org/2023/sqlite-tools-win32-x86-3440200.zip

:: 检查SQLite是否已安装
sqlite3 -version >nul 2>&1
if errorlevel 1 (
    echo [信息] 未检测到SQLite，正在下载...
    
    :: 检查是否有curl或powershell
    curl --version >nul 2>&1
    if not errorlevel 1 (
        echo [信息] 使用curl下载SQLite...
        curl -L -o sqlite-tools.zip %SQLITE_URL%
        if errorlevel 1 (
            echo [错误] 下载失败，请手动下载SQLite
            echo 下载地址: %SQLITE_URL%
            pause
            exit /b 1
        )
    ) else (
        echo [信息] 使用PowerShell下载SQLite...
        powershell -Command "Invoke-WebRequest -Uri '%SQLITE_URL%' -OutFile 'sqlite-tools.zip'"
        if errorlevel 1 (
            echo [错误] 下载失败，请手动下载SQLite
            echo 下载地址: %SQLITE_URL%
            pause
            exit /b 1
        )
    )
    
    :: 解压SQLite
    echo [信息] 解压SQLite工具...
    powershell -Command "Expand-Archive -Path 'sqlite-tools.zip' -DestinationPath '.' -Force"
    if errorlevel 1 (
        echo [错误] 解压失败
        pause
        exit /b 1
    )
    
    :: 移动sqlite3.exe到当前目录
    move sqlite-tools-win32-x86-*\sqlite3.exe . >nul 2>&1
    
    :: 清理临时文件
    del sqlite-tools.zip >nul 2>&1
    rmdir /s /q sqlite-tools-win32-x86-* >nul 2>&1
    
    echo [成功] SQLite工具下载完成
) else (
    echo [信息] 检测到SQLite工具
)

echo.

:: 检查数据库文件是否已存在
if exist "%DATABASE_FILE%" (
    echo [警告] 数据库文件 %DATABASE_FILE% 已存在
    set /p OVERWRITE="是否覆盖现有数据库? (y/n): "
    if /i not "%OVERWRITE%"=="y" (
        echo [信息] 操作已取消
        pause
        exit /b 0
    )
    del "%DATABASE_FILE%"
    echo [信息] 已删除现有数据库文件
)

echo.
echo [信息] 开始创建SQLite数据库...

:: 创建数据库和表结构
echo [步骤 1/3] 创建数据库表结构...
sqlite3 %DATABASE_FILE% < sqlite\schema\create_database.sql
if errorlevel 1 (
    echo [错误] 数据库表结构创建失败
    pause
    exit /b 1
)
echo [成功] 数据库表结构创建完成

:: 插入基础数据
echo [步骤 2/3] 插入基础数据...
sqlite3 %DATABASE_FILE% < sqlite\data\basic_data.sql
if errorlevel 1 (
    echo [错误] 基础数据插入失败
    pause
    exit /b 1
)
echo [成功] 基础数据插入完成

:: 询问是否插入示例数据
set /p SAMPLE_DATA="是否插入示例数据? (y/n): "
if /i "%SAMPLE_DATA%"=="y" (
    echo [步骤 3/3] 插入示例数据...
    sqlite3 %DATABASE_FILE% < sqlite\data\sample_data.sql
    if errorlevel 1 (
        echo [错误] 示例数据插入失败
        pause
        exit /b 1
    )
    echo [成功] 示例数据插入完成
) else (
    echo [步骤 3/3] 跳过示例数据插入
)

:: 验证数据库
echo.
echo [验证] 检查数据库完整性...
sqlite3 %DATABASE_FILE% "PRAGMA integrity_check;"
sqlite3 %DATABASE_FILE% "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"

echo.
echo =====================================================
echo SQLite数据库部署完成！
echo =====================================================
echo.
echo 数据库信息:
echo   - 数据库文件: %DATABASE_FILE%
echo   - 文件位置: %CD%\%DATABASE_FILE%
echo   - 数据库类型: SQLite 3
echo.
echo 默认管理员账号:
echo   - 用户名: 13000000000
echo   - 密码: password
echo   - 角色: 管理员
echo.
echo 数据库连接示例:
echo   - Node.js: sqlite3 %DATABASE_FILE%
echo   - Python: sqlite3.connect('%DATABASE_FILE%')
echo   - 命令行: sqlite3 %DATABASE_FILE%
echo.
echo 注意事项:
echo   1. 生产环境请立即修改默认密码
echo   2. 建议定期备份数据库文件
echo   3. SQLite适合小型应用，大型应用建议使用MySQL/PostgreSQL
echo   4. 数据库文件可以直接复制到其他环境使用
echo.
echo 下一步:
echo   1. 配置前端应用的数据库连接
echo   2. 启动应用服务
echo   3. 访问系统进行测试
echo.

:: 询问是否打开数据库
set /p OPEN_DB="是否打开数据库进行查看? (y/n): "
if /i "%OPEN_DB%"=="y" (
    echo.
    echo 正在打开SQLite数据库...
    echo 输入 .tables 查看所有表
    echo 输入 .quit 退出数据库
    echo.
    sqlite3 %DATABASE_FILE%
)

pause
