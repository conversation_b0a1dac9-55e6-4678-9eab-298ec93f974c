# 🏥 中医预问诊智能体系统 - MVP架构

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![MVP](https://img.shields.io/badge/Architecture-MVP-brightgreen.svg)](https://en.wikipedia.org/wiki/Model%E2%80%93view%E2%80%93presenter)
[![Memory](https://img.shields.io/badge/AI-Memory-orange.svg)](https://github.com)

基于MVP架构的专业中医预问诊智能体系统，支持智能体短期记忆功能，前后端分离设计，提供专业的诊前信息采集服务。

## ✨ 核心特色

### 🏗️ MVP架构设计
- **前后端分离**: 清晰的职责划分，易于维护
- **统一服务器**: 单一部署点，简化运维
- **RESTful API**: 标准化接口设计
- **现代化前端**: 响应式设计，优秀体验

### 🤖 多智能体协同工作流程
- **智能体工作阶段管理**: 每个智能体有明确的工作目标和完成标准
- **信息完整性验证**: 智能体会验证必需信息是否收集完整
- **智能体工作报告**: 每个智能体完成工作后输出标准化报告
- **协同机制**: 严格按顺序执行，上一个完成才开始下一个
- **结构化病历**: 生成完整的预问诊病历并结束会话

### 🧠 智能体记忆功能
- **短期记忆**: AI记住对话历史，避免重复询问
- **上下文感知**: 基于历史信息提供连贯回复
- **会话管理**: 完整的会话生命周期管理
- **个性化服务**: 记住患者基本信息

### 🎯 专业化设计
- **严格职责边界**: AI只做信息采集，不进行诊断
- **中医问诊流程**: 遵循标准的中医问诊步骤
- **温和专业语气**: 让患者感到舒适和信任
- **简洁明了回复**: 每次只问1-2个相关问题

### 🎨 现代化界面
- **iOS风格设计**: 圆角、毛玻璃效果、渐变色
- **响应式布局**: 适配各种设备屏幕
- **多种演示版本**: iOS、简化、智能体、完整功能
- **流畅动画效果**: 优雅的用户体验

### 🔧 技术优势
- **Ollama本地部署**: 支持本地LLM模型
- **OpenRouter备用**: 支持云端LLM服务
- **完整测试体系**: MVP架构和记忆功能测试
- **一键启动**: 简化的部署和使用流程

## 📁 项目结构 (MVP架构)

```
YWZ/
├── 📁 backend/                      # 🔧 MVP后端服务
│   ├── app.py                      # 🚀 主服务器入口
│   ├── 📁 api/                     # 🌐 API接口层
│   │   ├── routes.py               # 路由汇总
│   │   ├── health.py               # 健康检查API
│   │   ├── chat.py                 # 聊天API (支持记忆)
│   │   ├── inquiry.py              # 问诊API (支持记忆)
│   │   └── workflow_inquiry.py     # 工作流程问诊API
│   ├── 📁 services/                # 🔄 业务服务层
│   │   ├── llm_service.py          # LLM统一服务
│   │   ├── chat_service.py         # 聊天服务 (短期记忆)
│   │   ├── inquiry_service.py      # 问诊服务 (智能体记忆)
│   │   ├── agents.py               # 智能体定义
│   │   └── workflow_inquiry_service.py # 工作流程问诊服务
│   ├── 📁 models/                  # 📊 数据模型
│   ├── 📁 config/                  # ⚙️ 配置管理
│   └── 📁 utils/                   # 🛠️ 工具函数
├── 📁 frontend/                     # 🎨 MVP前端页面
│   ├── index.html                  # 🏠 主页导航
│   └── 📁 pages/                   # 📱 子页面
│       ├── ios.html                # 🍎 iOS风格页面 (支持记忆)
│       ├── simple.html             # 🎯 简化页面 (支持记忆)
│       ├── agent.html              # 🤖 智能体页面
│       ├── workflow.html           # 🔄 工作流程演示页面
│       └── full.html               # 📋 完整功能页面
├── 📁 tests/                       # 🧪 测试文件
├── 📁 docs/                        # 📚 项目文档
├── 📁 data/                        # 💾 数据存储
├── 📁 logs/                        # 📝 日志文件
├── start_mvp.py                    # 🚀 MVP完整启动脚本
├── quick_start_mvp.py              # ⚡ MVP快速启动脚本
├── test_mvp_architecture.py        # 🧪 MVP架构测试
├── test_memory_function.py         # 🧠 记忆功能测试
├── demo_memory_function.py         # 🎭 记忆功能演示
├── test_workflow_inquiry.py        # 🧪 工作流程问诊测试
├── demo_workflow_inquiry.py        # 🎭 工作流程问诊演示
├── README.md                       # 📖 项目说明 (本文件)
├── requirements.txt                # 📦 Python依赖
└── .env                           # ⚙️ 环境配置
```

## 🚀 快速开始

### 📋 环境要求

- **Python**: 3.8+
- **Conda**: 推荐使用conda环境管理 (llms环境)
- **Ollama**: 本地LLM服务 (推荐) 或 OpenRouter API

### 🛠️ 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd YWZ
```

2. **激活conda环境**
```bash
conda activate llms
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量** (已有默认配置)
```bash
# .env文件已包含默认配置
# LLM_SERVICE=ollama
# OLLAMA_BASE_URL=http://************:8080
# OLLAMA_MODEL=qwen2.5:14b
```

5. **一键启动系统**
```bash
# 推荐：快速启动
python quick_start_mvp.py

# 或者：完整启动
python start_mvp.py --dev
```

6. **自动打开演示页面**
```bash
# 系统会自动在浏览器中打开主页导航
# http://localhost:8002
```

## 🎯 使用方式

### 🚀 方式一：一键启动 (推荐)

**快速启动**
```bash
python quick_start_mvp.py
```

**完整启动**
```bash
python start_mvp.py --dev
```

**特色功能**：
- ✅ 自动检查环境和依赖
- ✅ 自动启动MVP服务器
- ✅ 自动在浏览器中打开主页导航
- ✅ 支持智能体短期记忆功能

### 🖥️ 方式二：手动启动

1. **启动MVP服务器**
```bash
conda activate llms
python backend/app.py
```

2. **访问演示页面**
```bash
# 主页导航
http://localhost:8002

# 各种演示页面
http://localhost:8002/pages/ios      # iOS风格 (推荐)
http://localhost:8002/pages/simple   # 简化版本
http://localhost:8002/pages/agent    # 智能体演示
http://localhost:8002/pages/workflow # 工作流程演示 (新功能)
http://localhost:8002/pages/full     # 完整功能
```

3. **体验记忆功能**
   - 点击"开始问诊"启动AI助手
   - 输入症状: "我头痛"
   - 继续对话: "在太阳穴附近"
   - 观察AI如何基于历史信息提问

### 🔧 方式三：API接口调用

```python
import requests

# 启动问诊 (支持智能体记忆)
response = requests.post("http://localhost:8002/api/inquiry/start", json={
    "patient_name": "张三",
    "patient_age": 30,
    "patient_gender": "男"
})

# 发送症状信息 (自动传递会话ID)
response = requests.post("http://localhost:8002/api/chat", json={
    "message": "我头痛三天了",
    "session_id": "your-session-id"
})
```

### 🧪 方式四：功能测试

```bash
# MVP架构测试
python test_mvp_architecture.py

# 智能体记忆功能测试
python test_memory_function.py

# 记忆功能演示
python demo_memory_function.py

# 工作流程问诊测试
python test_workflow_inquiry.py

# 工作流程问诊演示
python demo_workflow_inquiry.py
```

## 📡 API使用

### 健康检查

```bash
curl "http://localhost:8002/api/health"
```

### 开始问诊

```bash
curl -X POST "http://localhost:8002/api/inquiry/start" \
  -H "Content-Type: application/json" \
  -d '{
    "patient_name": "张三",
    "patient_age": 30,
    "patient_gender": "男"
  }'
```

### 工作流程问诊 (多智能体协同)

```bash
curl -X POST "http://localhost:8002/api/workflow/start" \
  -H "Content-Type: application/json" \
  -d '{
    "patient_name": "张三",
    "patient_age": 30,
    "patient_gender": "男"
  }'
```

### 对话交互 (支持记忆)

```bash
curl -X POST "http://localhost:8002/api/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "your-session-id",
    "message": "头痛三天，伴有发热"
  }'
```

### 获取对话历史

```bash
curl "http://localhost:8002/api/chat/history/{session_id}"
```

### API文档

```bash
# 访问交互式API文档
http://localhost:8002/api/docs
```

## 🧪 测试验证

### MVP架构测试

```bash
# 完整的MVP架构测试
python test_mvp_architecture.py

# 生成详细测试报告
python test_mvp_architecture.py --report
```

### 智能体记忆功能测试

```bash
# 测试短期记忆功能
python test_memory_function.py

# 演示记忆功能
python demo_memory_function.py
```

### 测试结果

- ✅ **MVP架构测试**: 7/7 通过
- ✅ **记忆功能测试**: 100% 通过
- ✅ **前端路由测试**: 5/5 通过
- ✅ **API接口测试**: 4/4 通过

## 📊 系统监控

### 健康检查

```bash
# 系统健康状态
curl http://localhost:8002/api/health

# 系统详细信息
curl http://localhost:8002/api/info
```

### 日志管理

日志文件位置：
- 应用日志: `logs/`
- 系统会自动创建日志目录

## 🔧 配置说明

### 环境变量 (.env文件)

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `LLM_SERVICE` | LLM服务类型 | `ollama` |
| `OLLAMA_BASE_URL` | Ollama服务地址 | `http://************:8080` |
| `OLLAMA_MODEL` | 使用的模型 | `qwen2.5:14b` |
| `OPENROUTER_API_KEY` | OpenRouter API密钥 | (备用) |
| `HOST` | 服务器地址 | `0.0.0.0` |
| `PORT` | 服务器端口 | `8002` |

### 支持的LLM服务

**Ollama (推荐)**:
- `qwen2.5:14b` (推荐)
- `qwen2.5:7b`
- 其他兼容的Ollama模型

**OpenRouter (备用)**:
- 支持多种云端LLM模型

## 🚨 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 检查端口是否被占用
   netstat -an | findstr :8002

   # 重新启动服务器
   python quick_start_mvp.py
   ```

2. **Ollama连接失败**
   ```bash
   # 检查Ollama服务状态
   curl http://************:8080/api/tags

   # 检查.env配置
   cat .env | grep OLLAMA
   ```

3. **记忆功能异常**
   ```bash
   # 测试记忆功能
   python test_memory_function.py

   # 检查会话管理
   curl http://localhost:8002/api/health
   ```

4. **前端页面无法访问**
   ```bash
   # 检查路由配置
   python test_mvp_architecture.py --report

   # 直接访问主页
   http://localhost:8002
   ```

### 性能优化

1. **LLM服务优化**
   - 使用更快的模型 (qwen2.5:7b)
   - 调整温度和token限制
   - 启用本地Ollama服务

2. **记忆功能优化**
   - 限制对话历史长度
   - 定期清理过期会话
   - 优化上下文构建

## 📚 开发指南

### 扩展智能体记忆功能

1. 修改 `backend/services/chat_service.py` 中的记忆逻辑
2. 调整 `build_context_prompt` 方法
3. 在 `backend/services/inquiry_service.py` 中扩展问诊记忆
4. 添加相应的测试用例

### 自定义提示词

提示词配置位于各服务类中，可以根据需要调整：
- 聊天服务: `backend/services/chat_service.py`
- 问诊服务: `backend/services/inquiry_service.py`

### 扩展前端页面

1. 在 `frontend/pages/` 中添加新页面
2. 在 `backend/app.py` 中添加路由
3. 更新 `frontend/index.html` 主页导航

## 📖 相关文档

- 📋 [MVP架构总结](MVP_ARCHITECTURE_SUMMARY.md)
- 🧠 [记忆功能总结](MEMORY_FUNCTION_SUMMARY.md)
- 🤖 [工作流程问诊总结](WORKFLOW_INQUIRY_SUMMARY.md)
- 🏗️ [架构设计文档](ARCHITECTURE.md)
- 📁 [项目结构说明](CLEAN_PROJECT_STRUCTURE.md)
- 🧹 [项目清理总结](PROJECT_CLEANUP_SUMMARY.md)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📞 支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [项目文档](docs/)

---

**🎉 中医预问诊智能体系统 - MVP架构，支持多智能体协同工作流程和智能体短期记忆功能，为中医诊疗提供智能化支持！**
