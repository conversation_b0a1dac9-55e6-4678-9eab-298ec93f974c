# 划价收费模块

## 功能描述
划价收费模块负责管理医疗服务的费用计算和收费流程，包括费用清单、支付处理等功能。

## 目录结构
```
billing/
├── views/                    # 页面视图
│   └── BillingList.vue       # 收费列表页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 费用清单管理
- 价格计算
- 支付处理
- 收费记录

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 