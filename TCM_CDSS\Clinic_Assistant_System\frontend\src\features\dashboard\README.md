# Dashboard 功能中心模块

功能中心模块是应用的主页面，提供所有功能模块的入口。支持卡片编辑和布局自定义功能。

## 新增功能 (v3.8)

### 卡片编辑功能
- **编辑模式**: 点击"编辑布局"按钮进入编辑模式
- **拖拽调整**: 支持拖拽调整卡片位置
- **实时保存**: 自动保存用户自定义布局
- **布局预设**: 提供默认、医生专用、管理员专用三种预设

### 布局管理
- **个性化布局**: 每个用户可以自定义卡片布局
- **数据持久化**: 布局配置保存在本地存储
- **版本控制**: 支持布局数据版本管理
- **权限控制**: 基于用户角色的布局权限

## 目录结构

### views/ - 页面视图
- **Dashboard.vue** - 功能中心主页面（包含卡片编辑功能）
- **dashboard.css** - 页面样式

### components/ - 功能组件
- **FunctionCard/** - 功能卡片组件
  - **FunctionCard.vue** - 卡片结构
  - **function-card.css** - 卡片样式
  - **useFunctionCard.ts** - 卡片逻辑
- **DetailStats/** - 详细统计组件
  - **DetailStats.vue** - 统计结构
  - **detail-stats.css** - 统计样式
  - **useDetailStats.ts** - 统计逻辑
- **CardGrid/** - 卡片网格组件

### composables/ - 组合式函数
- **useDashboard.ts** - 仪表板逻辑
- **useCardConfig.ts** - 卡片配置逻辑
- **useStatistics.ts** - 统计数据逻辑

### stores/ - 状态管理
- **dashboard.ts** - 仪表板状态

### types/ - 类型定义
- **card.ts** - 卡片类型
- **statistics.ts** - 统计类型

### constants/ - 常量配置
- **cards.ts** - 卡片配置
- **permissions.ts** - 权限配置

### styles/ - 模块样式
- **dashboard.css** - 模块主样式
- **variables.css** - 模块变量

## 卡片编辑使用说明

### 进入编辑模式
1. 点击页面右上角的"编辑布局"按钮
2. 页面进入编辑模式，显示操作提示
3. 卡片显示拖拽指示器

### 调整卡片位置
1. 在编辑模式下拖拽卡片到目标位置
2. 松开鼠标完成位置调整
3. 系统自动保存新的布局配置

### 使用布局预设
1. 点击"布局预设"下拉菜单
2. 选择需要的预设布局
3. 系统自动应用选中的布局

### 重置布局
- 在预设菜单中选择"重置为默认"
- 恢复到系统默认布局配置

## 技术实现

### 核心文件
- **Dashboard.vue**: 主页面，包含编辑功能
- **layoutService.ts**: 布局管理服务 (shared/services/)

### 关键技术
- HTML5 拖拽API
- localStorage数据持久化
- Vue 3 响应式系统
- TypeScript类型安全