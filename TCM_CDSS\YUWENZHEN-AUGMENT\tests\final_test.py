#!/usr/bin/env python3
"""最终测试：验证AI返回简洁问题"""

import json
import urllib.request
import time

def test_final():
    """最终测试"""
    base_url = "http://localhost:8002"
    
    print("🎯 最终测试：验证AI返回简洁问题")
    print("=" * 50)
    
    # 等待服务器完全启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 测试健康检查
    try:
        with urllib.request.urlopen(f"{base_url}/health") as response:
            health_data = json.loads(response.read().decode('utf-8'))
            print(f"✅ 服务器健康: {health_data.get('status')}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return
    
    # 测试聊天
    test_message = "我头痛三天了"
    print(f"\n👤 测试消息: {test_message}")
    
    try:
        data = json.dumps({"message": test_message}).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/chat",
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        print("📤 发送请求...")
        with urllib.request.urlopen(req, timeout=45) as response:
            result = json.loads(response.read().decode('utf-8'))
            ai_response = result.get("response", "")
            
            print(f"🤖 AI回复: '{ai_response}'")
            print(f"📏 回复长度: {len(ai_response)} 字符")
            
            # 验证回复质量
            checks = {
                "简洁性": len(ai_response) <= 50,
                "包含问号": '？' in ai_response or '?' in ai_response,
                "无诊断词": not any(word in ai_response for word in ['可能', '诊断', '建议', '治疗']),
                "是问题": ai_response.strip().endswith('？') or ai_response.strip().endswith('?')
            }
            
            print(f"\n📊 质量检查:")
            for check, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {check}")
            
            all_passed = all(checks.values())
            if all_passed:
                print(f"\n🎉 完美！AI现在返回简洁的下一步问题了！")
                print(f"✅ 问题已修复：AI不再返回包含think部分的冗长内容")
                print(f"✅ 前端体验：用户将看到简洁的追问问题")
            else:
                print(f"\n⚠️ 还需要进一步优化")
            
            return all_passed
            
    except Exception as e:
        print(f"❌ 聊天测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_final()
    if success:
        print(f"\n🚀 系统已准备就绪，可以开始手动体验！")
        print(f"📱 打开 ios_demo.html 开始体验")
    else:
        print(f"\n🔧 需要进一步调试")
