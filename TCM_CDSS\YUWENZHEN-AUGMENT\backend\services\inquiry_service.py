"""
问诊服务 - 处理问诊流程，支持智能体记忆
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from services.llm_service import llm_service

logger = logging.getLogger(__name__)

class InquiryService:
    """问诊服务类 - 支持智能体对话历史"""

    def __init__(self):
        self.sessions: Dict[str, Dict[str, Any]] = {}

        # 智能体系统提示词模板
        self.agent_system_prompt = """你是一个专业的中医问诊助手。你的职责是采集患者的症状信息，不进行诊断或治疗建议。

患者信息：
- 姓名：{patient_name}
- 年龄：{patient_age}岁
- 性别：{patient_gender}

请遵循以下原则：
1. 只询问症状相关信息，不提供诊断
2. 每次回复控制在50字以内
3. 语气温和专业
4. 逐步深入了解症状细节
5. 根据对话历史，避免重复询问已知信息
6. 按照中医问诊的逻辑顺序：主诉→现病史→既往史→家族史
7. 不要使用<think>标签或冗长解释

{history_context}

请基于以上信息，继续进行专业的中医问诊。"""

    def start_inquiry(self, patient_name: str, patient_age: int, patient_gender: str) -> Dict[str, Any]:
        """开始问诊"""
        try:
            # 生成会话ID
            session_id = f"inquiry_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

            # 创建会话信息
            session_info = {
                "session_id": session_id,
                "patient_name": patient_name,
                "patient_age": patient_age,
                "patient_gender": patient_gender,
                "start_time": datetime.now().isoformat(),
                "messages": [],
                "current_stage": "chief_complaint",  # 当前问诊阶段
                "collected_info": {
                    "chief_complaint": [],      # 主诉
                    "present_illness": [],      # 现病史
                    "past_history": [],         # 既往史
                    "family_history": []        # 家族史
                }
            }

            # 保存会话
            self.sessions[session_id] = session_info

            # 生成个性化欢迎消息
            welcome_message = f"您好{patient_name}，我是您的中医问诊助手。请详细描述您的主要症状。"

            # 添加欢迎消息到历史
            self.add_message(session_id, "assistant", welcome_message)

            logger.info(f"Started inquiry for {patient_name}, session: {session_id}")

            return {
                "session_id": session_id,
                "message": welcome_message,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Start inquiry error: {e}")
            raise Exception(f"启动问诊失败: {str(e)}")

    def build_history_context(self, session_id: str) -> str:
        """构建对话历史上下文"""
        if session_id not in self.sessions:
            return ""

        session = self.sessions[session_id]
        messages = session.get("messages", [])

        if not messages:
            return ""

        # 构建对话历史
        history_text = "\n=== 对话历史 ===\n"
        for msg in messages[-10:]:  # 最近5轮对话
            role_name = "患者" if msg["role"] == "user" else "医生"
            history_text += f"{role_name}: {msg['content']}\n"

        # 添加已收集的信息摘要
        collected_info = session.get("collected_info", {})
        info_summary = "\n=== 已收集信息摘要 ===\n"

        for category, items in collected_info.items():
            if items:
                category_name = {
                    "chief_complaint": "主诉",
                    "present_illness": "现病史",
                    "past_history": "既往史",
                    "family_history": "家族史"
                }.get(category, category)
                info_summary += f"{category_name}: {'; '.join(items)}\n"

        return history_text + info_summary

    def process_inquiry_message(self, session_id: str, message: str) -> str:
        """处理问诊消息，使用智能体记忆"""
        try:
            if session_id not in self.sessions:
                return "会话不存在，请重新开始问诊。"

            session = self.sessions[session_id]

            # 添加用户消息到历史
            self.add_message(session_id, "user", message)

            # 构建包含历史的系统提示词
            history_context = self.build_history_context(session_id)

            system_prompt = self.agent_system_prompt.format(
                patient_name=session["patient_name"],
                patient_age=session["patient_age"],
                patient_gender=session["patient_gender"],
                history_context=history_context
            )

            # 调用LLM服务
            response = llm_service.call_llm(message, system_prompt)

            # 添加AI回复到历史
            self.add_message(session_id, "assistant", response)

            # 更新收集的信息（简单的关键词提取）
            self.update_collected_info(session_id, message)

            logger.info(f"Processed inquiry message in session {session_id}")
            return response

        except Exception as e:
            logger.error(f"Process inquiry message error: {e}")
            return "抱歉，处理您的消息时出现错误，请稍后再试。"

    def update_collected_info(self, session_id: str, user_message: str):
        """更新已收集的信息（简单实现）"""
        if session_id not in self.sessions:
            return

        session = self.sessions[session_id]
        current_stage = session.get("current_stage", "chief_complaint")

        # 简单的信息分类（实际项目中可以使用更复杂的NLP技术）
        if "头痛" in user_message or "疼痛" in user_message:
            session["collected_info"]["chief_complaint"].append(f"疼痛症状: {user_message[:20]}")
        elif "发热" in user_message or "发烧" in user_message:
            session["collected_info"]["chief_complaint"].append(f"发热症状: {user_message[:20]}")
        elif "天" in user_message or "周" in user_message or "月" in user_message:
            session["collected_info"]["present_illness"].append(f"时间信息: {user_message[:20]}")

    def get_session(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        return self.sessions.get(session_id)

    def add_message(self, session_id: str, role: str, content: str):
        """添加消息到会话"""
        if session_id in self.sessions:
            self.sessions[session_id]["messages"].append({
                "role": role,
                "content": content,
                "timestamp": datetime.now().isoformat()
            })

            # 限制消息历史长度
            if len(self.sessions[session_id]["messages"]) > 50:
                self.sessions[session_id]["messages"] = self.sessions[session_id]["messages"][-50:]

    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """获取会话摘要"""
        if session_id not in self.sessions:
            return {"error": "会话不存在"}

        session = self.sessions[session_id]
        messages = session.get("messages", [])

        return {
            "session_id": session_id,
            "patient_info": {
                "name": session["patient_name"],
                "age": session["patient_age"],
                "gender": session["patient_gender"]
            },
            "start_time": session["start_time"],
            "message_count": len(messages),
            "current_stage": session.get("current_stage"),
            "collected_info": session.get("collected_info", {}),
            "last_activity": messages[-1]["timestamp"] if messages else session["start_time"]
        }

    def get_session_count(self) -> int:
        """获取会话数量"""
        return len(self.sessions)

# 全局问诊服务实例
inquiry_service = InquiryService()
