version: '3.8'

services:
  app:
    build: .
    container_name: tcm-consultation-app
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/tcm_db
      - REDIS_URL=redis://redis:6379
      - DEBUG=false
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - tcm-network

  postgres:
    image: postgres:15-alpine
    container_name: tcm-postgres
    environment:
      POSTGRES_DB: tcm_db
      POSTGRES_USER: tcm_user
      POSTGRES_PASSWORD: tcm_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - tcm-network

  redis:
    image: redis:7-alpine
    container_name: tcm-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - tcm-network

  nginx:
    image: nginx:alpine
    container_name: tcm-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - tcm-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  tcm-network:
    driver: bridge 