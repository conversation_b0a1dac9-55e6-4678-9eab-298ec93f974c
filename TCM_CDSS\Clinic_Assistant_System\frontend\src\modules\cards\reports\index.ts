// 统计报表卡片模块
import type { CardModule } from '../index'

// 统计报表统计数据
export const reportsStats = {
  monthlyIncome: 125680,
  monthlyPatients: 456,
  growthRate: 15.6,
  reportCount: 12
}

// 统计报表卡片配置
export const reportsCard: CardModule = {
  key: 'reports',
  name: '统计报表',
  route: '/reports',
  icon: 'DataAnalysis',
  color: '#30B0C7',
  info: `本月收入 ¥${reportsStats.monthlyIncome.toLocaleString()}`,
  hasDetailStats: false,
  statsData: reportsStats,
  permission: 'report_view'
}
