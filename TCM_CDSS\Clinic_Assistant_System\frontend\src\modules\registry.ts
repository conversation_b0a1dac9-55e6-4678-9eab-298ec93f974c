// 模块注册器 - 注册所有内置和第三方模块
import { moduleInstaller, type ModulePackage } from './installer'
import { allCardModules } from './cards'

// 内置模块包定义
const builtinPackages: ModulePackage[] = [
  {
    id: 'appointments',
    name: '预约中心模块',
    version: '1.0.0',
    description: '患者预约中心管理功能',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'appointments')!,
    installScript: async () => {
      console.log('Installing appointments module...')
      // 这里可以添加模块特定的安装逻辑
    }
  },
  {
    id: 'patients',
    name: '患者管理模块',
    version: '1.0.0',
    description: '患者信息管理和统计功能',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'patients')!,
    installScript: async () => {
      console.log('Installing patients module...')
    }
  },
  {
    id: 'workstation',
    name: '门诊工作站模块',
    version: '1.0.0',
    description: '门诊医生工作台功能',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'workstation')!,
    dependencies: ['patients'],
    installScript: async () => {
      console.log('Installing workstation module...')
    }
  },
  {
    id: 'billing',
    name: '划价收费模块',
    version: '1.0.0',
    description: '医疗费用计算和收费管理',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'billing')!,
    dependencies: ['patients'],
    installScript: async () => {
      console.log('Installing billing module...')
    }
  },
  {
    id: 'pharmacy',
    name: '药房管理模块',
    version: '1.0.0',
    description: '药品管理和处方发药功能',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'pharmacy')!,
    installScript: async () => {
      console.log('Installing pharmacy module...')
    }
  },
  {
    id: 'inventory',
    name: '库存管理模块',
    version: '1.0.0',
    description: '药品和医疗用品库存管理',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'inventory')!,
    dependencies: ['pharmacy'],
    installScript: async () => {
      console.log('Installing inventory module...')
    }
  },
  {
    id: 'scheduling',
    name: '排班管理模块',
    version: '1.0.0',
    description: '医生和护士排班管理功能',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'scheduling')!,
    installScript: async () => {
      console.log('Installing scheduling module...')
    }
  },
  {
    id: 'staff',
    name: '员工管理模块',
    version: '1.0.0',
    description: '医院员工信息和权限管理',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'staff')!,
    installScript: async () => {
      console.log('Installing staff module...')
    }
  },
  {
    id: 'reports',
    name: '统计报表模块',
    version: '1.0.0',
    description: '医院运营数据统计和报表生成',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'reports')!,
    dependencies: ['patients', 'billing'],
    installScript: async () => {
      console.log('Installing reports module...')
    }
  },
  {
    id: 'family-doctor',
    name: '家医管理模块',
    version: '1.0.0',
    description: '家庭医生服务管理功能',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'family-doctor')!,
    dependencies: ['patients'],
    installScript: async () => {
      console.log('Installing family-doctor module...')
    }
  },
  {
    id: 'research-center',
    name: '研学中心模块',
    version: '1.0.0',
    description: '中医研学和教育培训平台',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'research-center')!,
    installScript: async () => {
      console.log('Installing research-center module...')
    }
  },
  {
    id: 'app-center',
    name: '应用中心模块',
    version: '1.0.0',
    description: '应用下载和管理中心',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'app-center')!,
    installScript: async () => {
      console.log('Installing app-center module...')
    }
  },
  {
    id: 'mall-management',
    name: '商城管理模块',
    version: '1.0.0',
    description: '自有商城管理和运营功能',
    author: 'TCM CDSS Team',
    cardModule: allCardModules.find(m => m.key === 'mall-management')!,
    installScript: async () => {
      console.log('Installing mall-management module...')
    }
  }
]

// 注册所有内置模块
export function registerBuiltinModules() {
  builtinPackages.forEach(pkg => {
    moduleInstaller.registerPackage(pkg)
  })
  
  console.log(`Registered ${builtinPackages.length} builtin modules`)
}

// 自动安装核心模块
export async function installCoreModules() {
  const coreModules = ['appointments', 'patients', 'workstation']
  
  for (const moduleId of coreModules) {
    if (!moduleInstaller.isInstalled(moduleId)) {
      try {
        await moduleInstaller.installModule(moduleId)
        console.log(`Core module ${moduleId} installed successfully`)
      } catch (error) {
        console.error(`Failed to install core module ${moduleId}:`, error)
      }
    }
  }
}

// 获取推荐安装的模块
export function getRecommendedModules(): string[] {
  return [
    'billing',
    'pharmacy',
    'inventory',
    'scheduling',
    'staff',
    'reports',
    'family-doctor',
    'research-center',
    'app-center',
    'mall-management'
  ]
}

// 初始化模块系统
export async function initializeModuleSystem() {
  // 注册内置模块
  registerBuiltinModules()
  
  // 安装核心模块
  await installCoreModules()
  
  // 清理无效记录
  moduleInstaller.cleanup()
  
  console.log('Module system initialized successfully')
}

// 导出模块安装器实例
export { moduleInstaller }
