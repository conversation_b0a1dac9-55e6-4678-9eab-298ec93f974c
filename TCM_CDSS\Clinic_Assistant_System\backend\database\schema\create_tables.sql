-- =====================================================
-- 中医诊所助手系统 - 主表结构
-- =====================================================
-- 文件: create_tables.sql
-- 描述: 创建主要业务表，使用映射表ID存储敏感信息
-- 版本: v1.0
-- 创建日期: 2025-06-24
-- =====================================================

PRAGMA foreign_keys = ON;
PRAGMA encoding = 'UTF-8';

-- =====================================================
-- 用户与权限表
-- =====================================================

-- 员工表（优化版）
CREATE TABLE IF NOT EXISTS staff (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_number TEXT NOT NULL UNIQUE,
    full_name_encrypted TEXT NOT NULL, -- 加密存储姓名
    phone_hash TEXT NOT NULL UNIQUE, -- 手机号哈希
    password_hash TEXT NOT NULL,
    role_mapping_id INTEGER NOT NULL, -- 引用角色映射表
    department_mapping_id INTEGER, -- 引用科室映射表
    title TEXT,
    specialty_encrypted TEXT, -- 加密存储专长
    avatar TEXT,
    email_encrypted TEXT, -- 加密存储邮箱
    is_active BOOLEAN NOT NULL DEFAULT 1,
    last_login_at DATETIME,
    login_attempts INTEGER NOT NULL DEFAULT 0, -- 登录尝试次数
    locked_until DATETIME, -- 锁定到期时间
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_mapping_id) REFERENCES role_mappings(id),
    FOREIGN KEY (department_mapping_id) REFERENCES department_mappings(id)
);

-- 权限表（优化版）
CREATE TABLE IF NOT EXISTS permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    module_mapping_id INTEGER NOT NULL, -- 引用模块映射表
    action_mapping_id INTEGER NOT NULL, -- 引用操作映射表
    resource TEXT, -- 资源标识
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (module_mapping_id) REFERENCES permission_module_mappings(id),
    FOREIGN KEY (action_mapping_id) REFERENCES permission_action_mappings(id),
    UNIQUE(module_mapping_id, action_mapping_id, resource)
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    role_mapping_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_mapping_id, permission_id),
    FOREIGN KEY (role_mapping_id) REFERENCES role_mappings(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- =====================================================
-- 患者与就诊表
-- =====================================================

-- 患者表（优化版）
CREATE TABLE IF NOT EXISTS patients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_number TEXT NOT NULL UNIQUE,
    full_name_encrypted TEXT NOT NULL, -- 加密存储姓名
    gender_mapping_id INTEGER NOT NULL, -- 引用性别映射表
    date_of_birth_encrypted TEXT NOT NULL, -- 加密存储生日
    phone_hash TEXT, -- 手机号哈希
    id_card_hash TEXT, -- 身份证号哈希
    address_encrypted TEXT, -- 加密存储地址
    region_mapping_id INTEGER, -- 引用地区映射表
    allergies_encrypted TEXT, -- 加密存储过敏史
    medical_history_encrypted TEXT, -- 加密存储病史
    family_history_encrypted TEXT, -- 加密存储家族史
    status_mapping_id INTEGER NOT NULL, -- 引用状态映射表
    last_visit_date DATE,
    next_appointment_date DATE,
    visit_count INTEGER NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (gender_mapping_id) REFERENCES gender_mappings(id),
    FOREIGN KEY (status_mapping_id) REFERENCES status_mappings(id),
    FOREIGN KEY (region_mapping_id) REFERENCES region_mappings(id)
);

-- 患者联系人表（优化版）
CREATE TABLE IF NOT EXISTS patient_contacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id INTEGER NOT NULL,
    name_encrypted TEXT NOT NULL, -- 加密存储姓名
    phone_hash TEXT NOT NULL, -- 手机号哈希
    relationship TEXT NOT NULL,
    is_emergency BOOLEAN NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
);

-- 预约表（优化版）
CREATE TABLE IF NOT EXISTS appointments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    appointment_number TEXT NOT NULL UNIQUE,
    patient_id INTEGER NOT NULL,
    doctor_id INTEGER NOT NULL,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    status_mapping_id INTEGER NOT NULL, -- 引用状态映射表
    source_mapping_id INTEGER NOT NULL, -- 引用预约来源映射表
    chief_complaint_encrypted TEXT, -- 加密存储主诉
    note_encrypted TEXT, -- 加密存储备注
    created_by INTEGER NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (doctor_id) REFERENCES staff(id),
    FOREIGN KEY (created_by) REFERENCES staff(id),
    FOREIGN KEY (status_mapping_id) REFERENCES status_mappings(id),
    FOREIGN KEY (source_mapping_id) REFERENCES appointment_source_mappings(id)
);

-- 就诊表（优化版）
CREATE TABLE IF NOT EXISTS visits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    visit_number TEXT NOT NULL UNIQUE,
    patient_id INTEGER NOT NULL,
    doctor_id INTEGER NOT NULL,
    appointment_id INTEGER,
    visit_date DATE NOT NULL,
    visit_start_time DATETIME NOT NULL,
    visit_end_time DATETIME,
    status_mapping_id INTEGER NOT NULL, -- 引用状态映射表
    visit_type_mapping_id INTEGER NOT NULL, -- 引用就诊类型映射表
    chief_complaint_encrypted TEXT, -- 加密存储主诉
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (doctor_id) REFERENCES staff(id),
    FOREIGN KEY (appointment_id) REFERENCES appointments(id),
    FOREIGN KEY (status_mapping_id) REFERENCES status_mappings(id),
    FOREIGN KEY (visit_type_mapping_id) REFERENCES visit_type_mappings(id)
);

-- =====================================================
-- 电子病历表
-- =====================================================

-- 病历表（优化版）
CREATE TABLE IF NOT EXISTS medical_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    visit_id INTEGER NOT NULL UNIQUE,
    patient_id INTEGER NOT NULL,
    doctor_id INTEGER NOT NULL,
    chief_complaint_encrypted TEXT, -- 加密存储主诉
    present_illness_encrypted TEXT, -- 加密存储现病史
    past_history_encrypted TEXT, -- 加密存储既往史
    family_history_encrypted TEXT, -- 加密存储家族史
    personal_history_encrypted TEXT, -- 加密存储个人史
    -- 中医四诊（加密存储）
    inspection_encrypted TEXT, -- 望诊
    auscultation_encrypted TEXT, -- 闻诊
    inquiry_encrypted TEXT, -- 问诊
    palpation_encrypted TEXT, -- 切诊
    -- 诊断（使用映射表ID）
    tcm_diagnosis_mapping_ids TEXT, -- JSON格式存储多个中医诊断映射ID
    tcm_syndrome_mapping_ids TEXT, -- JSON格式存储多个证型映射ID
    western_diagnosis_mapping_ids TEXT, -- JSON格式存储多个西医诊断映射ID
    treatment_principle_encrypted TEXT, -- 加密存储治法
    doctors_orders_encrypted TEXT, -- 加密存储医嘱
    follow_up_plan_encrypted TEXT, -- 加密存储随访计划
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (visit_id) REFERENCES visits(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (doctor_id) REFERENCES staff(id)
);

-- =====================================================
-- 产品与处方表
-- =====================================================

-- 产品表（优化版）
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_code TEXT NOT NULL UNIQUE,
    name_encrypted TEXT NOT NULL, -- 加密存储产品名称
    pinyin_code TEXT,
    product_type_mapping_id INTEGER NOT NULL, -- 引用产品类型映射表
    category_mapping_id INTEGER, -- 引用产品分类映射表
    specifications TEXT,
    unit TEXT NOT NULL,
    cost_price_encrypted TEXT, -- 加密存储成本价
    selling_price_encrypted TEXT, -- 加密存储销售价
    supplier_info_encrypted TEXT, -- 加密存储供应商信息
    manufacturer_encrypted TEXT, -- 加密存储生产厂家
    approval_number TEXT,
    expiry_days INTEGER,
    storage_condition TEXT,
    is_prescription BOOLEAN NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_type_mapping_id) REFERENCES product_type_mappings(id),
    FOREIGN KEY (category_mapping_id) REFERENCES product_category_mappings(id)
);

-- 处方表（优化版）
CREATE TABLE IF NOT EXISTS prescriptions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    prescription_number TEXT NOT NULL UNIQUE,
    visit_id INTEGER NOT NULL,
    patient_id INTEGER NOT NULL,
    doctor_id INTEGER NOT NULL,
    prescription_type_mapping_id INTEGER NOT NULL, -- 引用处方类型映射表
    status_mapping_id INTEGER NOT NULL, -- 引用状态映射表
    dosage_instructions_encrypted TEXT, -- 加密存储服法
    decoction_method_encrypted TEXT, -- 加密存储煎煮方法
    total_doses INTEGER,
    total_amount_encrypted TEXT, -- 加密存储总金额
    note_encrypted TEXT, -- 加密存储备注
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (visit_id) REFERENCES visits(id),
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (doctor_id) REFERENCES staff(id),
    FOREIGN KEY (prescription_type_mapping_id) REFERENCES product_type_mappings(id),
    FOREIGN KEY (status_mapping_id) REFERENCES status_mappings(id)
);

-- 处方明细表（优化版）
CREATE TABLE IF NOT EXISTS prescription_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    prescription_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity_encrypted TEXT NOT NULL, -- 加密存储数量
    unit TEXT NOT NULL,
    unit_price_encrypted TEXT NOT NULL, -- 加密存储单价
    total_price_encrypted TEXT NOT NULL, -- 加密存储小计
    usage_mapping_id INTEGER, -- 引用用法映射表
    note_encrypted TEXT, -- 加密存储备注
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (prescription_id) REFERENCES prescriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (usage_mapping_id) REFERENCES usage_mappings(id)
);

-- =====================================================
-- 创建索引
-- =====================================================

-- 员工表索引
CREATE INDEX IF NOT EXISTS idx_staff_phone_hash ON staff(phone_hash);
CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role_mapping_id);
CREATE INDEX IF NOT EXISTS idx_staff_department ON staff(department_mapping_id);
CREATE INDEX IF NOT EXISTS idx_staff_active ON staff(is_active);

-- 患者表索引
CREATE INDEX IF NOT EXISTS idx_patients_phone_hash ON patients(phone_hash);
CREATE INDEX IF NOT EXISTS idx_patients_gender ON patients(gender_mapping_id);
CREATE INDEX IF NOT EXISTS idx_patients_status ON patients(status_mapping_id);
CREATE INDEX IF NOT EXISTS idx_patients_region ON patients(region_mapping_id);

-- 预约表索引
CREATE INDEX IF NOT EXISTS idx_appointments_patient ON appointments(patient_id);
CREATE INDEX IF NOT EXISTS idx_appointments_doctor ON appointments(doctor_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status_mapping_id);
CREATE INDEX IF NOT EXISTS idx_appointments_source ON appointments(source_mapping_id);

-- 就诊表索引
CREATE INDEX IF NOT EXISTS idx_visits_patient ON visits(patient_id);
CREATE INDEX IF NOT EXISTS idx_visits_doctor ON visits(doctor_id);
CREATE INDEX IF NOT EXISTS idx_visits_date ON visits(visit_date);
CREATE INDEX IF NOT EXISTS idx_visits_status ON visits(status_mapping_id);
CREATE INDEX IF NOT EXISTS idx_visits_type ON visits(visit_type_mapping_id);

-- 病历表索引
CREATE INDEX IF NOT EXISTS idx_medical_records_visit ON medical_records(visit_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_patient ON medical_records(patient_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_doctor ON medical_records(doctor_id);

-- 产品表索引
CREATE INDEX IF NOT EXISTS idx_products_code ON products(product_code);
CREATE INDEX IF NOT EXISTS idx_products_type ON products(product_type_mapping_id);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_mapping_id);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);

-- 处方表索引
CREATE INDEX IF NOT EXISTS idx_prescriptions_visit ON prescriptions(visit_id);
CREATE INDEX IF NOT EXISTS idx_prescriptions_patient ON prescriptions(patient_id);
CREATE INDEX IF NOT EXISTS idx_prescriptions_doctor ON prescriptions(doctor_id);
CREATE INDEX IF NOT EXISTS idx_prescriptions_status ON prescriptions(status_mapping_id);

-- 处方明细表索引
CREATE INDEX IF NOT EXISTS idx_prescription_items_prescription ON prescription_items(prescription_id);
CREATE INDEX IF NOT EXISTS idx_prescription_items_product ON prescription_items(product_id);
CREATE INDEX IF NOT EXISTS idx_prescription_items_usage ON prescription_items(usage_mapping_id);

SELECT 'Main tables created successfully!' AS message;
