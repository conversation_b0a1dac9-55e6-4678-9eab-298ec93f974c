<template>
  <div class="outpatient-workstation">
    <!-- 模块头部导航栏 -->
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">门诊工作台</h1>
    </header>

    <!-- 三栏式布局容器 -->
    <div class="main-content">
      <!-- 左侧栏：流程控制与工具面板 -->
      <div class="left-panel">
        <!-- 门诊单搜索框 -->
        <div class="search-section">
          <el-button class="call-btn" @click="callNext">
            <el-icon><Bell /></el-icon>
            叫号
          </el-button>
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索患者..."
              clearable
              @keyup.enter="searchPatient"
            />
          </div>
          <el-button type="primary" class="admit-btn" @click="admitNewPatient">
            <el-icon><Plus /> </el-icon>
            接诊
          </el-button>
        </div>

        <!-- 患者列表预览区 -->
        <div class="patient-queue-container" :class="{ 'collapsed': isPatientQueueCollapsed }">
          <div class="queue-header" @click="togglePatientQueue">
            <h3>今日预约 ({{ patientQueue.length }})</h3>
            <el-icon class="toggle-icon" :class="{ 'collapsed': isPatientQueueCollapsed }">
              <ArrowDown />
            </el-icon>
          </div>
          <transition name="slide-fade" mode="out-in">
            <div class="queue-list-container" v-if="!isPatientQueueCollapsed" key="queue-open">
              <div class="queue-list">
                <div
                  v-for="patient in patientQueue"
                  :key="patient.id"
                  :class="['queue-item', { active: currentPatient?.id === patient.id }]"
                  @click="selectPatient(patient)"
                >
                  <div class="patient-basic-row">
                    <span class="patient-name">{{ patient.name }}</span>
                    <span class="doctor-name">{{ patient.doctor }}</span>
                    <span class="appointment-time">{{ patient.appointmentTime }}</span>
                  </div>
                  <div class="patient-tags-row">
                    <div class="status-tag" :class="patient.status">
                      {{ getStatusText(patient.status) }}
                    </div>
                    <div class="source-tag" :class="patient.appointmentSource">
                      {{ getAppointmentSourceText(patient.appointmentSource) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else key="queue-closed" class="queue-placeholder"></div>
          </transition>
        </div>

        <!-- AI听诊容器 -->
        <div class="ai-auscultation-container" :class="{ 'adsorbed': isPatientQueueCollapsed, 'collapsed': isAuscultationCollapsed }">
          <div class="auscultation-header" @click="toggleAuscultation">
            <h3>AI听诊</h3>
            <el-icon class="toggle-icon" :class="{ 'collapsed': isAuscultationCollapsed }">
              <ArrowDown />
            </el-icon>
          </div>
          <transition name="slide-fade" mode="out-in">
            <div class="auscultation-content" v-if="!isAuscultationCollapsed" key="auscultation-open">
              <div class="recording-section">
                <el-button
                  class="record-btn"
                  :class="{ 'recording': isRecording }"
                  @click="toggleRecording"
                >
                  <el-icon v-if="!isRecording"><Microphone /></el-icon>
                  <el-icon v-else class="recording-icon"><Loading /></el-icon>
                  {{ isRecording ? '停止录音' : '开始录音' }}
                </el-button>
                <div class="record-status">
                  {{ isRecording ? '正在录音...' : '点击听诊' }}
                </div>
                <el-button
                  class="ai-extract-btn"
                  size="small"
                  @click="extractAIInfo"
                >
                  <el-icon><Star /></el-icon>
                  AI提取
                </el-button>
              </div>
              <div class="transcription-section">
                <div class="transcription-content">
                  <div v-if="transcriptionText" class="transcription-text">
                    {{ transcriptionText }}
                  </div>
                  <div v-else class="transcription-placeholder">
                    语音转文本内容将在这里显示...
                  </div>
                </div>
              </div>
            </div>
            <div v-else key="auscultation-closed" class="auscultation-placeholder"></div>
          </transition>
        </div>

        <!-- 工具区 -->
        <div class="tools-section">
          <div class="tools-grid">
            <el-button class="tool-btn" @click="showMedicalCaseHelper">
              <el-icon><Document /></el-icon>
              医案助手
            </el-button>
            <el-button class="tool-btn" @click="showDashboard">
              <el-icon><DataAnalysis /></el-icon>
              门诊看板
            </el-button>
            <el-button class="tool-btn" @click="showSchedule">
              <el-icon><Calendar /></el-icon>
              预约看板
            </el-button>
            <el-button class="tool-btn" @click="showMedicineHelper">
              <el-icon><Document /></el-icon>
              用药助手
            </el-button>
            <el-button class="tool-btn add-tool-btn" @click="showAddTool">
              <el-icon><Plus /></el-icon>
              新增工具
            </el-button>
          </div>
        </div>
      </div>

      <!-- 中间栏：电子病历工单画布 -->
      <div class="center-panel">
        <div v-if="currentPatient" class="medical-record-container">
          <!-- 固定区域：顶部操作栏和基本信息区 -->
          <div class="fixed-header">
            <!-- 顶部操作栏 -->
            <div class="top-actions">
              <el-button size="small" @click="previewFees">
                <el-icon><Money /></el-icon>
                费用预览
              </el-button>
              <el-button size="small" @click="saveRecord">
                <el-icon><Document /></el-icon>
                保存病历
              </el-button>
              <el-button size="small" @click="continueAdmit">
                <el-icon><ArrowRight /></el-icon>
                继续接诊
              </el-button>
              <el-button size="small" @click="printRecord">
                <el-icon><Printer /></el-icon>
                打印
              </el-button>
            </div>

            <!-- 基本信息与就诊属性区 -->
            <div class="basic-info-section">
              <div class="info-row">
                <div class="basic-info">
                  <span class="info-item">患者姓名：{{ currentPatient.name }}</span>
                  <span class="info-item">性别：{{ currentPatient.gender }}</span>
                  <span class="info-item">年龄：{{ currentPatient.age }}岁</span>
                  <span class="info-item">手机号：{{ currentPatient.phone }}</span>
                </div>
                <div class="visit-attributes">
                  <el-select v-model="visitType" placeholder="初诊/复诊" size="small">
                    <el-option label="初诊" value="first" />
                    <el-option label="复诊" value="follow" />
                  </el-select>
                  <el-select v-model="feeType" placeholder="费用类别" size="small">
                    <el-option label="自费" value="self" />
                    <el-option label="医保" value="insurance" />
                  </el-select>
                </div>
              </div>
            </div>
          </div>

          <!-- 可滚动区域：病历区 -->
          <div class="scrollable-content">
            <div class="medical-record-section">
              <div class="record-block">
                <h3 class="block-title">主诉</h3>
                <el-input
                  v-model="medicalRecord.chiefComplaint"
                  type="textarea"
                  :rows="3"
                  placeholder="请描述患者的主要症状和不适..."
                />
              </div>

              <div class="record-block">
                <h3 class="block-title">现病史</h3>
                <el-input
                  v-model="medicalRecord.presentIllness"
                  type="textarea"
                  :rows="3"
                  placeholder="请描述本次疾病的发生发展过程..."
                />
              </div>

              <div class="record-block">
                <h3 class="block-title">既往史</h3>
                <el-input
                  v-model="medicalRecord.pastHistory"
                  type="textarea"
                  :rows="3"
                  placeholder="请描述患者的既往疾病史..."
                />
              </div>

              <!-- 体格检查 -->
              <div class="record-block">
                <h3 class="block-title">体格检查</h3>
                <div class="four-diagnosis">
                  <div class="diagnosis-item">
                    <div class="diagnosis-header">
                      <h4>望诊</h4>
                      <el-button size="small" @click="getAIInfo('look')">
                        <el-icon><Star /></el-icon>
                        灵机同步
                      </el-button>
                    </div>
                    <el-input
                      v-model="medicalRecord.lookDiagnosis"
                      type="textarea"
                      :rows="2"
                      placeholder="请描述望诊情况..."
                    />
                  </div>

                  <div class="diagnosis-item">
                    <div class="diagnosis-header">
                      <h4>闻诊</h4>
                      <el-button size="small" @click="getAIInfo('listen')">
                        <el-icon><Star /></el-icon>
                        灵机同步
                      </el-button>
                    </div>
                    <el-input
                      v-model="medicalRecord.listenDiagnosis"
                      type="textarea"
                      :rows="2"
                      placeholder="请描述闻诊情况..."
                    />
                  </div>

                  <div class="diagnosis-item">
                    <div class="diagnosis-header">
                      <h4>问诊</h4>
                      <el-button size="small" @click="getAIInfo('ask')">
                        <el-icon><Star /></el-icon>
                        灵机同步
                      </el-button>
                    </div>
                    <el-input
                      v-model="medicalRecord.askDiagnosis"
                      type="textarea"
                      :rows="2"
                      placeholder="请描述问诊情况..."
                    />
                  </div>

                  <div class="diagnosis-item">
                    <div class="diagnosis-header">
                      <h4>切诊</h4>
                      <el-button size="small" @click="getAIInfo('touch')">
                        <el-icon><Star /></el-icon>
                        灵机同步
                      </el-button>
                    </div>
                    <el-input
                      v-model="medicalRecord.touchDiagnosis"
                      type="textarea"
                      :rows="2"
                      placeholder="请描述切诊情况..."
                    />
                  </div>
                </div>
              </div>

              <!-- 诊断 -->
              <div class="record-block">
                <h3 class="block-title">诊断</h3>
                <div class="diagnosis-inputs">
                  <el-input
                    v-model="medicalRecord.tcmDiagnosis"
                    placeholder="中医诊断"
                    class="diagnosis-input"
                  />
                  <el-input
                    v-model="medicalRecord.wmDiagnosis"
                    placeholder="西医诊断"
                    class="diagnosis-input"
                  />
                </div>
              </div>
            </div>

            <!-- 适宜技术区 -->
            <div class="suitable-tech-section">
              <div class="section-header">
                <h3 class="block-title">适宜技术</h3>
                <el-button size="small" @click="addSuitableTech">
                  <el-icon><Plus /></el-icon>
                  添加
                </el-button>
              </div>
              <div class="tech-list">
                <div
                  v-for="(tech, index) in suitableTechs"
                  :key="index"
                  class="tech-card"
                >
                  <el-checkbox v-model="tech.enabled" />
                  <el-input v-model="tech.name" placeholder="技术名称" size="small" />
                  <el-input v-model="tech.location" placeholder="操作部位" size="small" />
                  <el-input v-model="tech.acupoints" placeholder="具体穴位" size="small" />
                  <el-button type="danger" size="small" @click="removeSuitableTech(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 处方医嘱区 -->
            <div class="prescription-section">
              <div class="section-header">
                <h3 class="block-title">处方医嘱</h3>
                <div class="prescription-buttons">
                  <el-button size="small" @click="openPrescriptionDialog('tcm')">
                    <el-icon><Document /></el-icon>
                    中药处方
                  </el-button>
                  <el-button size="small" @click="openPrescriptionDialog('patent')">
                    <el-icon><Box /></el-icon>
                    中成药处方
                  </el-button>
                  <el-button size="small" @click="openPrescriptionDialog('infusion')">
                    <el-icon><Lock /></el-icon>
                    输注处方
                  </el-button>
                  <el-button size="small" @click="openPrescriptionDialog('history')">
                    <el-icon><Document /></el-icon>
                    历史处方
                  </el-button>
                  <el-button size="small" @click="openPrescriptionDialog('template')">
                    <el-icon><Document /></el-icon>
                    处方模板
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 医嘱事项区 -->
            <div class="orders-section">
              <h3 class="block-title">医嘱事项</h3>
              <div class="orders-content">
                <el-input
                  v-model="ordersText"
                  type="textarea"
                  :rows="4"
                  placeholder="医嘱内容..."
                  readonly
                />
              </div>
            </div>

            <!-- 审计校验区 -->
            <div class="audit-section">
              <h3 class="block-title">审计校验</h3>
              <div class="audit-messages">
                <div class="audit-item">
                  <el-icon><Money /></el-icon>
                  <span>方剂审核：逍遥散配伍合理，无禁忌</span>
                </div>
                <div class="audit-item">
                  <el-icon><Lock /></el-icon>
                  <span>医保合规性：药品均在医保目录内</span>
                </div>
              </div>
            </div>

            <!-- 工单信息展示区 -->
            <div class="record-info">
              <span>病历ID: {{ recordId }}</span>
              <span>就诊时间: {{ visitDate }}</span>
            </div>
          </div>
        </div>

        <div v-else class="no-patient">
          <el-empty description="请选择患者开始诊疗" />
        </div>
      </div>

      <!-- 右侧栏：参考信息与智能辅助面板 -->
      <div class="right-panel">
        <div class="right-panel-container">
          <el-tabs v-model="activeTab" class="reference-tabs">
            <el-tab-pane label="历史" name="history">
              <div class="history-timeline">
                <div
                  v-for="visit in patientHistory"
                  :key="visit.id"
                  class="timeline-item"
                  @click="viewHistoryVisit(visit)"
                >
                  <div class="timeline-date">{{ visit.date }}</div>
                  <div class="timeline-content">
                    <div class="visit-diagnosis">{{ visit.diagnosis }}</div>
                    <div class="visit-prescription">{{ visit.prescription }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="报告" name="reports">
              <div class="reports-list">
                <div
                  v-for="report in patientReports"
                  :key="report.id"
                  class="report-item"
                  @click="viewReport(report)"
                >
                  <div class="report-name">{{ report.name }}</div>
                  <div class="report-date">{{ report.date }}</div>
                  <div class="report-status" :class="report.status">
                    {{ report.statusText }}
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="影像" name="images">
              <div class="images-gallery">
                <div
                  v-for="image in patientImages"
                  :key="image.id"
                  class="image-item"
                  @click="viewImage(image)"
                >
                  <img :src="image.thumbnail" :alt="image.name" />
                  <div class="image-name">{{ image.name }}</div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="AI 辅助" name="ai">
              <div class="ai-diagnosis">
                <!-- 加载状态 -->
                <div v-if="isLoadingAI" class="loading-container">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  <p>正在加载AI诊断数据...</p>
                </div>
                
                <!-- AI诊断内容 -->
                <div v-else-if="aiDiagnosisData">
                  <!-- 辩证分析卡片 -->
                  <div class="ai-card">
                    <h4 class="ai-card-title">辩证分析</h4>
                    <div class="ai-card-content">
                      <p class="analysis-text">
                        {{ getDialecticaAnalysis }}
                      </p>
                    </div>
                  </div>

                  <!-- 证素得分卡片 -->
                  <div class="ai-card">
                    <h4 class="ai-card-title">证素得分</h4>
                    <div class="ai-card-content">
                      <div class="scores-list">
                        <div v-for="score in getScoresList" :key="score.syndromeElement" class="score-item">
                          <span class="score-element">{{ score.syndromeElement }}</span>
                          <span class="score-value">{{ score.score }}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 证型容器列表 -->
                  <div 
                    v-for="(syndromeGroup, syndromeName) in getSyndromeGroups" 
                    :key="syndromeName"
                    class="syndrome-group-card"
                  >
                    <div class="syndrome-group-header">
                      <h4 class="syndrome-group-title">{{ syndromeName }}</h4>
                    </div>
                    <div class="syndrome-group-content">
                      <div class="treatment-methods-container">
                        <div
                          v-for="(recommendation, index) in syndromeGroup"
                          :key="index"
                          class="treatment-method-card clickable"
                          @click="showPrescriptionDialog(recommendation)"
                        >
                          <div class="method-info-row">
                            <div class="method-name">{{ recommendation.therapy }}</div>
                            <div class="method-prescription">{{ recommendation.formula }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 无数据状态 -->
                <div v-else class="no-data-container">
                  <el-empty description="暂无AI诊断数据" />
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 子页面遮罩层 -->
    <div v-if="showSubPage" class="sub-page-overlay" @click="closeSubPage">
      <div class="sub-page-container" @click.stop>
        <!-- 方剂详细页面 -->
        <div v-if="subPageType === 'prescription'" class="sub-page prescription-sub-page">
          <div class="sub-page-header">
            <h2>{{ selectedMethod?.therapy || '方剂详情' }} - 方剂详情</h2>
            <el-button type="text" class="close-btn" @click="closeSubPage">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="sub-page-content">
            <div class="prescription-detail">
              <h3>主方：{{ selectedMethod?.prescription }}</h3>
              
              <h4>治法：{{ selectedMethod?.therapy }}</h4>
              <p>对应证型：{{ selectedMethod?.syndrome }}</p>
              
              <h4>方剂描述：</h4>
              <p>{{ selectedMethod?.formulaDetail?.description || '暂无描述' }}</p>
              
              <h4>组成：</h4>
              <div class="composition-table">
                <el-table :data="selectedMethod?.formulaDetail?.dose || []" size="small" border>
                  <el-table-column prop="medicinalMaterials" label="药材" width="100" />
                  <el-table-column prop="dose" label="用量" width="80">
                    <template #default="scope">
                      {{ scope.row.dose }}{{ scope.row.doseType }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="medicinalMaterialsId" label="药材ID" width="80" />
                </el-table>
              </div>
            </div>
          </div>
        </div>

        <!-- 医案助手页面 -->
        <div v-if="subPageType === 'medicalCase'" class="sub-page tool-sub-page">
          <div class="sub-page-header">
            <h2>医案助手</h2>
            <el-button type="text" class="close-btn" @click="closeSubPage">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="sub-page-content">
            <div class="tool-content">
              <h3>功能开发中</h3>
              <p>医案助手功能正在开发中，敬请期待...</p>
            </div>
          </div>
        </div>

        <!-- 门诊看板页面 -->
        <div v-if="subPageType === 'dashboard'" class="sub-page tool-sub-page">
          <div class="sub-page-header">
            <h2>门诊看板</h2>
            <el-button type="text" class="close-btn" @click="closeSubPage">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="sub-page-content">
            <div class="tool-content">
              <h3>功能开发中</h3>
              <p>门诊看板功能正在开发中，敬请期待...</p>
            </div>
          </div>
        </div>

        <!-- 预约看板页面 -->
        <div v-if="subPageType === 'schedule'" class="sub-page tool-sub-page">
          <div class="sub-page-header">
            <h2>预约看板</h2>
            <el-button type="text" class="close-btn" @click="closeSubPage">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="sub-page-content">
            <div class="tool-content">
              <h3>功能开发中</h3>
              <p>预约看板功能正在开发中，敬请期待...</p>
            </div>
          </div>
        </div>

        <!-- 用药助手页面 -->
        <div v-if="subPageType === 'medicine'" class="sub-page tool-sub-page">
          <div class="sub-page-header">
            <h2>用药助手</h2>
            <el-button type="text" class="close-btn" @click="closeSubPage">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="sub-page-content">
            <div class="tool-content">
              <h3>功能开发中</h3>
              <p>用药助手功能正在开发中，敬请期待...</p>
            </div>
          </div>
        </div>

        <!-- 新增工具页面 -->
        <div v-if="subPageType === 'addTool'" class="sub-page tool-sub-page">
          <div class="sub-page-header">
            <h2>新增工具</h2>
            <el-button type="text" class="close-btn" @click="closeSubPage">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="sub-page-content">
            <div class="tool-content">
              <h3>自定义工具配置</h3>
              <div class="add-tool-form">
                <el-form :model="newToolForm" label-width="80px">
                  <el-form-item label="工具名称">
                    <el-input v-model="newToolForm.name" placeholder="请输入工具名称" />
                  </el-form-item>
                  <el-form-item label="工具图标">
                    <el-select v-model="newToolForm.icon" placeholder="请选择图标">
                      <el-option label="文档" value="Document" />
                      <el-option label="数据分析" value="DataAnalysis" />
                      <el-option label="日历" value="Calendar" />
                      <el-option label="设置" value="Setting" />
                      <el-option label="用户" value="User" />
                      <el-option label="图表" value="TrendCharts" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="工具描述">
                    <el-input 
                      v-model="newToolForm.description" 
                      type="textarea" 
                      :rows="3"
                      placeholder="请输入工具功能描述" 
                    />
                  </el-form-item>
                  <el-form-item label="工具链接">
                    <el-input v-model="newToolForm.url" placeholder="请输入工具链接地址（可选）" />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="addNewTool">确认添加</el-button>
                    <el-button @click="closeSubPage">取消</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import {
  Search,
  Plus,
  Bell,
  DataAnalysis,
  Calendar,
  Document,
  Money,
  ArrowRight,
  Printer,
  Star,
  Delete,
  Box,
  Lock,
  ArrowLeft,
  Close,
  ArrowDown,
  Microphone,
  Loading
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAIDiagnosis, type AIDiagnosisResponse, type SyndromeRecommendation, type FormulaDose } from '@/mock/ai-diagnosis'
import { patientsData, type Patient as MockPatient } from '@/mock/database/patients'

// 类型定义
interface Patient {
  id: number
  name: string
  doctor: string
  appointmentTime: string
  status: string
  gender: string
  age: number
  phone: string
  appointmentSource: 'online' | 'offline' // 预约来源：线上、线下
}

interface MedicalRecord {
  chiefComplaint: string
  presentIllness: string
  pastHistory: string
  lookDiagnosis: string
  listenDiagnosis: string
  askDiagnosis: string
  touchDiagnosis: string
  tcmDiagnosis: string
  wmDiagnosis: string
}

interface SuitableTech {
  enabled: boolean
  name: string
  location: string
  acupoints: string
}

interface MedicalOrder {
  type: string
  content: string
  time: string
}

interface AuditMessage {
  id: number
  content: string
}

interface PatientHistory {
  id: number
  date: string
  diagnosis: string
  prescription: string
}

interface PatientReport {
  id: number
  name: string
  date: string
  status: string
  statusText: string
}

interface PatientImage {
  id: number
  name: string
  thumbnail: string
}

interface Syndrome {
  name: string
  confidence: number
  reason: string
}

interface Recommendation {
  id: number
  name: string
  principle: string
}

interface ChatMessage {
  id: number
  type: string
  content: string
}

interface AIChat {
  input: string
  messages: ChatMessage[]
}

interface AIAnalysis {
  syndromes: Syndrome[]
  recommendations: Recommendation[]
}

interface AuditMessages {
  prescription: AuditMessage[]
  insurance: AuditMessage[]
}

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const currentPatient = ref<Patient | null>(null)
const activeTab = ref('history')
const visitType = ref('')
const feeType = ref('')
const ordersText = ref('肝郁脾虚证患者，建议服用逍遥散加减，疏肝解郁，健脾养血。每日一剂，分两次服用。注意调节情志，避免辛辣刺激性食物。')

// 子页面相关数据
const showSubPage = ref(false)
const subPageType = ref<'prescription' | 'medicalCase' | 'dashboard' | 'schedule' | 'medicine' | 'addTool'>('prescription')
const selectedMethod = ref<any>(null)

// 新增工具相关数据
const newToolForm = reactive({
  name: '',
  icon: '',
  description: '',
  url: ''
})

// 自定义工具列表
const customTools = ref<any[]>([])

// 病历相关数据
const recordId = ref('MR20250121001')
const visitDate = ref('2025-01-21')

// 患者队列数据 - 从mock数据中获取今天预约的患者
const patientQueue = ref<Patient[]>([])

// 获取今天预约的患者队列
const loadTodayPatients = (): void => {
  const today = '2025-01-21' // 参考就诊时间
  const todayPatients = patientsData.filter(patient =>
    patient.nextAppointment === today && patient.status === 'active'
  )

  // 转换为门诊工作台需要的格式
  patientQueue.value = todayPatients.map((patient, index) => ({
    id: patient.id,
    name: patient.name,
    doctor: '张医生',
    appointmentTime: getAppointmentTime(index),
    status: getPatientStatus(index),
    gender: patient.gender,
    age: patient.age,
    phone: patient.phone,
    appointmentSource: patient.appointmentSource || 'offline' // 使用mock数据中的预约来源，默认为线下
  }))
}

// 根据索引生成预约时间
const getAppointmentTime = (index: number): string => {
  const baseHour = 8 // 从8点开始
  const interval = 30 // 30分钟间隔
  const hour = baseHour + Math.floor((index * interval) / 60)
  const minute = (index * interval) % 60
  return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
}

// 根据索引生成患者状态
const getPatientStatus = (index: number): string => {
  if (index === 0) return 'in-progress' // 第一个患者正在就诊
  if (index < 5) return 'waiting' // 前5个患者待诊
  return 'waiting' // 其他患者待诊
}

// 根据索引生成预约来源
const getAppointmentSource = (index: number): 'online' | 'offline' => {
  // 模拟线上线下预约分布，约60%线上，40%线下
  return Math.random() > 0.4 ? 'online' : 'offline'
}

// 病历数据
const medicalRecord = reactive<MedicalRecord>({
  chiefComplaint: '',
  presentIllness: '',
  pastHistory: '',
  lookDiagnosis: '',
  listenDiagnosis: '',
  askDiagnosis: '',
  touchDiagnosis: '',
  tcmDiagnosis: '',
  wmDiagnosis: ''
})

// 适宜技术
const suitableTechs = ref<SuitableTech[]>([])

// 医嘱事项
const medicalOrders = ref<MedicalOrder[]>([])

// 审计消息
const auditMessages = reactive<AuditMessages>({
  prescription: [],
  insurance: []
})

// 患者历史
const patientHistory = ref<PatientHistory[]>([
  {
    id: 1,
    date: '2025-01-15',
    diagnosis: '肝郁脾虚证',
    prescription: '逍遥散加减'
  },
  {
    id: 2,
    date: '2025-01-08',
    diagnosis: '气血两虚证',
    prescription: '八珍汤加减'
  }
])

// 患者报告
const patientReports = ref<PatientReport[]>([
  {
    id: 1,
    name: '血常规检查',
    date: '2025-01-20',
    status: 'normal',
    statusText: '正常'
  },
  {
    id: 2,
    name: '肝功能检查',
    date: '2025-01-20',
    status: 'abnormal',
    statusText: '异常'
  }
])

// 患者影像
const patientImages = ref<PatientImage[]>([
  {
    id: 1,
    name: '胸部X光片',
    thumbnail: '/api/images/xray-thumb.jpg'
  },
  {
    id: 2,
    name: '腹部CT',
    thumbnail: '/api/images/ct-thumb.jpg'
  }
])

// AI诊断数据
const aiDiagnosisData = ref<AIDiagnosisResponse | null>(null)
const isLoadingAI = ref(false)

// 监听标签页切换
watch(activeTab, (newTab) => {
  if (newTab === 'ai' && !aiDiagnosisData.value) {
    loadAIDiagnosis()
  }
})

// 计算属性
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    waiting: '待诊',
    'in-progress': '诊中',
    return: '回诊',
    completed: '已诊',
    unpaid: '未收费'
  }
  return statusMap[status] || status
}

// 获取预约来源显示文本
const getAppointmentSourceText = (source: 'online' | 'offline'): string => {
  const sourceMap: Record<string, string> = {
    online: '线上',
    offline: '线下'
  }
  return sourceMap[source] || source
}

// 获取预约来源标签类型
const getAppointmentSourceType = (source: 'online' | 'offline'): string => {
  const typeMap: Record<string, string> = {
    online: 'primary',
    offline: 'success'
  }
  return typeMap[source] || 'info'
}

// 方法
const searchPatient = (): void => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  // 模拟搜索功能
  ElMessage.success(`搜索到 ${Math.floor(Math.random() * 10) + 1} 个相关患者`)
}

const admitNewPatient = (): void => {
  // 实现新患者接诊逻辑
  ElMessage.success('已创建新的门诊单')
}

const selectPatient = (patient: Patient): void => {
  currentPatient.value = patient
  // 加载患者病历数据
}

const callNext = (): void => {
  ElMessage.success('已叫号下一位患者')
}

const showDashboard = (): void => {
  showSubPage.value = true
  subPageType.value = 'dashboard'
}

const showSchedule = (): void => {
  showSubPage.value = true
  subPageType.value = 'schedule'
}

const showMedicineHelper = (): void => {
  showSubPage.value = true
  subPageType.value = 'medicine'
}

const showAddTool = (): void => {
  showSubPage.value = true
  subPageType.value = 'addTool'
  // 重置表单
  newToolForm.name = ''
  newToolForm.icon = ''
  newToolForm.description = ''
  newToolForm.url = ''
}

const addNewTool = (): void => {
  if (!newToolForm.name.trim()) {
    ElMessage.warning('请输入工具名称')
    return
  }
  
  if (!newToolForm.icon) {
    ElMessage.warning('请选择工具图标')
    return
  }
  
  // 添加新工具到自定义工具列表
  const newTool = {
    id: Date.now(),
    name: newToolForm.name,
    icon: newToolForm.icon,
    description: newToolForm.description,
    url: newToolForm.url
  }
  
  customTools.value.push(newTool)
  
  ElMessage.success('工具添加成功')
  closeSubPage()
}

const previewFees = (): void => {
  ElMessage.info('显示费用预览')
}

const saveRecord = async (): Promise<void> => {
  try {
    await ElMessageBox.confirm('确认保存当前病历？', '保存确认', {
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })
    ElMessage.success('病历保存成功')
  } catch {
    // 用户取消
  }
}

const continueAdmit = (): void => {
  ElMessage.success('已自动叫号下一位')
  // 切换到下一位患者
}

const printRecord = (): void => {
  ElMessage.info('打开打印预览')
}

const getAIInfo = (type: string): void => {
  ElMessage.info(`从灵机获取${type}信息`)
}

const addSuitableTech = (): void => {
  suitableTechs.value.push({
    enabled: true,
    name: '',
    location: '',
    acupoints: ''
  })
}

const removeSuitableTech = (index: number): void => {
  suitableTechs.value.splice(index, 1)
}

const openPrescription = (type: string): void => {
  const typeMap: Record<string, string> = {
    chinese: '中成药处方',
    infusion: '输注处方',
    herbal: '中药处方',
    history: '历史处方',
    template: '处方模板'
  }
  
  ElMessageBox.alert(
    `这是${typeMap[type]}的详细内容。\n\n在这里可以编辑和开立${typeMap[type]}。\n\n功能包括：\n- 药品选择\n- 剂量设置\n- 用法用量\n- 注意事项`,
    typeMap[type],
    {
      confirmButtonText: '确定',
      customClass: 'prescription-dialog'
    }
  )
}

const viewHistoryVisit = (visit: PatientHistory): void => {
  ElMessage.info(`查看历史就诊: ${visit.date}`)
}

const viewReport = (report: PatientReport): void => {
  ElMessage.info(`查看报告: ${report.name}`)
}

const viewImage = (image: PatientImage): void => {
  ElMessage.info(`查看影像: ${image.name}`)
}

const sendAIMessage = (): void => {
  if (!aiChat.input.trim()) return
  
  // 添加用户消息
  aiChat.messages.push({
    id: Date.now(),
    type: 'user',
    content: aiChat.input
  })
  
  // 模拟AI回复
  setTimeout(() => {
    aiChat.messages.push({
      id: Date.now() + 1,
      type: 'ai',
      content: '根据您的病历信息，我建议...'
    })
  }, 1000)
  
  aiChat.input = ''
}

const goBack = (): void => {
  router.push('/dashboard')
}

const showPrescriptionDialog = async (recommendation: SyndromeRecommendation): Promise<void> => {
  if (!aiDiagnosisData.value) {
    await loadAIDiagnosis()
  }
  
  // 根据推荐找到对应的方剂详情
  const formulaDetail = aiDiagnosisData.value?.data.dose.find(
    dose => dose.formula === recommendation.formula
  )
  
  selectedMethod.value = {
    name: recommendation.therapy,
    prescription: recommendation.formula,
    syndrome: recommendation.syndrome,
    formulaDetail: formulaDetail
  }
  
  showSubPage.value = true
  subPageType.value = 'prescription'
}

const closeSubPage = (): void => {
  showSubPage.value = false
}

// 加载AI诊断数据
const loadAIDiagnosis = async (): Promise<void> => {
  isLoadingAI.value = true
  try {
    aiDiagnosisData.value = await getAIDiagnosis()
  } catch (error) {
    ElMessage.error('加载AI诊断数据失败')
    console.error('AI诊断数据加载失败:', error)
  } finally {
    isLoadingAI.value = false
  }
}

// 获取推荐列表
const getRecommendList = computed(() => {
  return aiDiagnosisData.value?.data.recommendList || []
})

// 获取辩证分析
const getDialecticaAnalysis = computed(() => {
  return aiDiagnosisData.value?.data.dialecticaAnalysis || '暂无辩证分析数据'
})

// 获取证型评分
const getScoresList = computed(() => {
  return aiDiagnosisData.value?.data.scoresList || []
})

// 按证型分组治法推荐
const getSyndromeGroups = computed(() => {
  const groups: Record<string, SyndromeRecommendation[]> = {}
  
  getRecommendList.value.forEach(recommendation => {
    const syndromeName = recommendation.syndrome
    if (!groups[syndromeName]) {
      groups[syndromeName] = []
    }
    groups[syndromeName].push(recommendation)
  })
  
  return groups
})

// 生命周期
onMounted(() => {
  // 加载今天预约的患者队列
  loadTodayPatients()
  
  // 初始化数据
  if (patientQueue.value.length > 0) {
    currentPatient.value = patientQueue.value[0]
  }
  
  // 如果当前是AI辅助标签页，加载AI诊断数据
  if (activeTab.value === 'ai') {
    loadAIDiagnosis()
  }
})

// 工具方法
const showMedicalCaseHelper = (): void => {
  showSubPage.value = true
  subPageType.value = 'medicalCase'
}

// 新增的逻辑
const isPatientQueueCollapsed = ref(false)
const togglePatientQueue = () => {
  // 如果当前今日预约是展开的，点击收起
  if (!isPatientQueueCollapsed.value) {
    isPatientQueueCollapsed.value = true
    // 自动展开AI听诊，让AI听诊header吸附到今日预约下方
    isAuscultationCollapsed.value = false
  } else {
    // 如果当前今日预约是收起的，点击展开
    isPatientQueueCollapsed.value = false
    // 如果AI听诊也是展开的，则收起AI听诊（确保至少有一个展开）
    if (!isAuscultationCollapsed.value) {
      isAuscultationCollapsed.value = true
    }
  }
}

const isAuscultationCollapsed = ref(false)
const toggleAuscultation = () => {
  // 如果当前AI听诊是展开的，点击收起
  if (!isAuscultationCollapsed.value) {
    isAuscultationCollapsed.value = true
    // 自动展开今日预约，让AI听诊header下移到工具区上方
    isPatientQueueCollapsed.value = false
  } else {
    // 如果当前AI听诊是收起的，点击展开
    isAuscultationCollapsed.value = false
    // 如果今日预约也是展开的，则收起今日预约（确保至少有一个展开）
    if (!isPatientQueueCollapsed.value) {
      isPatientQueueCollapsed.value = true
    }
  }
}

const isRecording = ref(false)
const toggleRecording = () => {
  isRecording.value = !isRecording.value
  
  if (isRecording.value) {
    // 开始录音
    ElMessage.success('开始录音，请说话...')
    startRecording()
  } else {
    // 停止录音
    ElMessage.success('录音结束，正在转文本...')
    stopRecording()
  }
}

const transcriptionText = ref('')

// 模拟录音功能
const startRecording = () => {
  // 模拟录音过程
  setTimeout(() => {
    if (isRecording.value) {
      // 模拟语音转文本结果
      const mockTranscription = `患者主诉：胸闷气短，伴有咳嗽，痰多色白。
      
体格检查：
- 呼吸音：双肺呼吸音粗，可闻及少量湿啰音
- 心率：80次/分，心律齐
- 血压：120/80mmHg
- 体温：36.8℃

初步诊断：支气管炎

建议：
1. 继续观察症状变化
2. 必要时进行胸部X线检查
3. 注意休息，避免受凉`
      
      transcriptionText.value = mockTranscription
      isRecording.value = false
      ElMessage.success('语音转文本完成')
    }
  }, 3000) // 模拟3秒录音时间
}

const stopRecording = () => {
  isRecording.value = false
  ElMessage.success('录音已停止')
}

// AI提取功能
const extractAIInfo = () => {
  if (!transcriptionText.value.trim()) {
    ElMessage.warning('请先录音获取语音内容')
    return
  }

  ElMessage.info('正在使用AI提取关键信息...')

  // 模拟AI提取过程
  setTimeout(() => {
    // 模拟AI提取的结果
    const extractedInfo = {
      chiefComplaint: '胸闷气短，伴有咳嗽',
      presentIllness: '患者近期出现胸闷气短症状，伴有咳嗽，痰多色白',
      lookDiagnosis: '面色萎黄，精神疲倦',
      listenDiagnosis: '语声低微，咳嗽声重浊',
      askDiagnosis: '胸闷气短，咳嗽痰多，色白质稠',
      touchDiagnosis: '脉象细弱，舌质淡红，苔薄白'
    }

    // 自动填充到对应的字段
    medicalRecord.value.chiefComplaint = extractedInfo.chiefComplaint
    medicalRecord.value.presentIllness = extractedInfo.presentIllness
    medicalRecord.value.lookDiagnosis = extractedInfo.lookDiagnosis
    medicalRecord.value.listenDiagnosis = extractedInfo.listenDiagnosis
    medicalRecord.value.askDiagnosis = extractedInfo.askDiagnosis
    medicalRecord.value.touchDiagnosis = extractedInfo.touchDiagnosis

    ElMessage.success('AI提取完成，已自动填充到病历中')
  }, 2000)
}
</script>

<style scoped>
.outpatient-workstation {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 固定高度，防止页面滚动 */
  overflow: hidden; /* 防止整体页面滚动 */
  /* 背景图片现在由全局样式提供 */
}

/* 模块头部导航栏样式 */
.module-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.3s;
}

.back-button:hover {
  color: #40a9ff;
}

.module-title {
  flex: 1;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  min-height: 0;
  overflow: hidden; /* 防止内容溢出 */
}

/* 左侧栏样式 */
.left-panel {
  width: 20%;
  background-color: #fff;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位 */
  height: 100%; /* 使用100%而不是100vh，适应父容器高度 */
  overflow: hidden; /* 防止内容溢出 */
}

.search-section {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0; /* 不压缩 */
}

.search-box {
  flex: 1;
  display: flex;
  justify-content: center;
}

.search-box .el-input {
  max-width: 200px;
  width: 100%;
}

.call-btn {
  border-radius: 6px;
  width: 60px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  background-color: #f0f0f0;
  border: 1px solid #d0d0d0;
  color: #333;
}

.call-btn:hover {
  background-color: #e0e0e0;
  border-color: #c0c0c0;
}

.admit-btn {
  border-radius: 6px;
  width: 60px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.patient-queue-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素能够正确收缩 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 使用更平滑的缓动函数 */
  position: relative; /* 添加相对定位 */
  flex: 1; /* 占据剩余空间 */
  min-height: 48px; /* 设置最小高度为header高度 */
}

/* 当患者队列收起时，减少占用空间 */
.patient-queue-container.collapsed {
  flex: none; /* 收起时不占据flex空间 */
  min-height: 48px; /* 只保留header高度 */
  max-height: 48px; /* 限制最大高度 */
}

.queue-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
  flex-shrink: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.queue-header:hover {
  background-color: #f8f9fa;
}

.queue-header h3 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.toggle-icon {
  transition: transform 0.3s ease;
  color: #666;
}

.toggle-icon.collapsed {
  transform: rotate(-90deg);
}

.queue-list-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.queue-list-container[v-show="false"] {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

/* 当患者队列收起时，隐藏列表容器 */
.patient-queue-container.collapsed .queue-list-container {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

/* 滚动条样式 */
.queue-list-container::-webkit-scrollbar {
  width: 6px;
}

.queue-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.queue-list-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.queue-list-container:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

.queue-list {
  padding: 0;
}

.queue-item {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.queue-item:hover {
  background-color: #f0f9ff;
}

.queue-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.patient-basic-row {
  display: flex;
  gap: 8px;
  flex: 1;
  justify-content: space-between;
  align-items: center;
}

.patient-tags-row {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: flex-end;
}

.patient-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.doctor-name,
.appointment-time {
  font-size: 11px;
  color: #666;
}

.status-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  color: #fff;
  white-space: nowrap;
}

.status-tag.waiting {
  background-color: #1890ff;
}

.status-tag.in-progress {
  background-color: #52c41a;
  animation: pulse 2s infinite;
}

.status-tag.return {
  background-color: #fa8c16;
}

.status-tag.completed {
  background-color: #8c8c8c;
}

.status-tag.unpaid {
  background-color: #722ed1;
}

/* 预约来源标签样式 */
.source-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  color: #fff;
  white-space: nowrap;
  font-weight: 500;
}

.source-tag.online {
  background-color: #1890ff;
  border: 1px solid #1890ff;
}

.source-tag.offline {
  background-color: #52c41a;
  border: 1px solid #52c41a;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* AI听诊容器样式 */
.ai-auscultation-container {
  border-top: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素能够正确收缩 */
  flex: 1; /* 占据剩余空间 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 使用更平滑的缓动函数 */
  transform: translateY(0); /* 初始位置 */
  height: auto; /* 让高度自动调整 */
  position: relative; /* 添加相对定位 */
  min-height: 48px; /* 设置最小高度为header高度 */
}

/* AI听诊容器收起状态 */
.ai-auscultation-container.collapsed {
  min-height: 48px; /* 只显示header的高度 */
  max-height: 48px; /* 限制最大高度 */
  flex: none; /* 收起时不占据flex空间 */
}

/* AI听诊吸附效果 - 当患者队列收起时 */
.ai-auscultation-container.adsorbed {
  border-top: none; /* 收起今日预约时移除上边框 */
  margin-top: 0; /* 确保紧贴今日预约header */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
  transform: translateY(0); /* 移除上移，直接贴紧 */
  position: relative; /* 改回相对定位 */
  z-index: 5; /* 确保层级正确 */
  flex: 1; /* 吸附时占据剩余空间 */
  min-height: 200px; /* 吸附时的最小高度 */
}

/* 当今日预约收起时，AI听诊容器完全调整到下方 */
.patient-queue-container.collapsed + .ai-auscultation-container.adsorbed {
  margin-top: 0;
  transform: translateY(0);
  border-top: 1px solid #1890ff;
}

/* 确保AI听诊在患者队列收起时能够占据更多空间 */
.patient-queue-container.collapsed + .ai-auscultation-container {
  flex: 1;
  min-height: 200px;
}

.auscultation-header {
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.auscultation-header:hover {
  background-color: #f8f9fa;
}

.auscultation-header h3 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

/* AI听诊内容展开/收起动画 */
.auscultation-content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 内容高度变化动画 */
  min-height: 0; /* 允许收缩 */
}

.auscultation-content[v-show="false"] {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.recording-section {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.record-btn {
  width: 100px;
  height: 36px;
  border-radius: 18px;
  background: #f0f9ff;
  border-color: #1890ff;
  color: #1890ff;
  transition: all 0.3s;
  padding: 0 12px;
  flex-shrink: 0;
  font-size: 12px;
}

.record-btn:hover {
  background: #e6f7ff;
  border-color: #40a9ff;
  color: #40a9ff;
}

.record-btn.recording {
  background: #fff2f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
  animation: pulse 1.5s infinite;
}

.recording-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.record-status {
  font-size: 12px;
  color: #666;
  text-align: center;
  white-space: nowrap;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-extract-btn {
  width: 100px;
  height: 36px;
  border-radius: 18px;
  background: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
  transition: all 0.3s;
  padding: 0 12px;
  flex-shrink: 0;
  font-size: 12px;
}

.ai-extract-btn:hover {
  background: #f0f9ff;
  border-color: #73d13d;
  color: #73d13d;
}

.transcription-section {
  flex: 1; /* 占据剩余空间 */
  padding: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许收缩 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 高度变化动画 */
  height: auto; /* 让高度自动调整 */
}

.transcription-content {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
  min-height: 150px; /* 增加最小高度 */
  max-height: 800px; /* 增加最大高度，给更多内容空间 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 高度变化动画 */
  height: auto; /* 让高度自动调整 */
  display: flex;
  flex-direction: column;
}

/* 当内容较少时，让容器自适应内容高度 */
.transcription-content:not(:has(.transcription-text:not(:empty))) {
  max-height: 250px; /* 内容较少时限制最大高度 */
}

/* 当内容较多时，允许更大的最大高度 */
.transcription-content:has(.transcription-text:not(:empty)) {
  max-height: 1200px; /* 内容较多时允许更大的高度 */
}

/* 当内容非常多时，进一步增加最大高度 */
.transcription-content:has(.transcription-text:not(:empty)):has(.transcription-text:not(:empty):not(:empty)) {
  max-height: 1000px; /* 内容非常多时允许更大的高度 */
}

.transcription-text {
  font-size: 13px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
  word-wrap: break-word;
  overflow-wrap: break-word;
  margin: 0;
  padding: 0;
  flex: 1; /* 让文本内容占据可用空间 */
  min-height: 0; /* 允许收缩 */
}

/* 转录内容滚动条样式 */
.transcription-content::-webkit-scrollbar {
  width: 6px;
}

.transcription-content::-webkit-scrollbar-track {
  background: transparent;
}

.transcription-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.transcription-content:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

.transcription-placeholder {
  font-size: 13px;
  color: #999;
  text-align: center;
  padding: 20px;
  font-style: italic;
}

.tools-section {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0; /* 不压缩，保持固定高度 */
  transition: all 0.3s ease; /* 添加过渡动画 */
  position: relative; /* 添加相对定位 */
  z-index: 1; /* 确保在AI听诊下方 */
  height: auto; /* 让高度自动调整 */
  min-height: 140px; /* 增加最小高度，确保5个按钮都能显示 */
  margin-top: auto; /* 确保工具区始终在底部 */
  background-color: #fff; /* 确保背景色 */
}

.tools-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  height: 100%; /* 确保网格占满容器高度 */
  align-content: start; /* 从顶部开始排列 */
}

.tool-btn {
  width: 100%;
  justify-content: flex-start;
  padding: 8px 12px;
  height: 36px;
  font-size: 12px;
  margin: 0;
  flex-shrink: 0;
}

.add-tool-btn {
  background-color: #f0f9ff;
  border-color: #1890ff;
  color: #1890ff;
}

.add-tool-btn:hover {
  background-color: #e6f7ff;
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 中间栏样式 */
.center-panel {
  width: 60%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.medical-record-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.fixed-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  justify-content: flex-end;
}

.basic-info-section {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.basic-info {
  display: flex;
  gap: 24px;
}

.info-item {
  color: #333;
  font-size: 14px;
}

.visit-attributes {
  display: flex;
  gap: 12px;
}

.visit-attributes .el-select {
  width: 120px;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.record-block {
  margin-bottom: 24px;
}

.block-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #1890ff;
}

.four-diagnosis {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.diagnosis-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
}

.diagnosis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.diagnosis-header h4 {
  margin: 0;
  color: #333;
  font-size: 14px;
}

.diagnosis-inputs {
  display: flex;
  gap: 16px;
}

.diagnosis-input {
  flex: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tech-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tech-card {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.prescription-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.orders-section {
  margin-top: 24px;
}

.orders-content {
  margin-top: 12px;
}

.audit-section {
  margin-top: 24px;
}

.audit-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.audit-item .el-icon {
  margin-top: 2px;
  flex-shrink: 0;
}

.record-info {
  display: flex;
  justify-content: flex-end;
  gap: 24px;
  font-size: 12px;
  color: #666;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.no-patient {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 右侧栏样式 */
.right-panel {
  width: 20%;
  background-color: #fff;
  border-left: 1px solid #e0e0e0;
}

.right-panel-container {
  height: 100%;
}

.reference-tabs {
  height: 100%;
}

.reference-tabs :deep(.el-tabs__header) {
  text-align: center;
  margin-bottom: 16px;
}

.reference-tabs :deep(.el-tabs__nav-wrap) {
  justify-content: center;
  display: flex;
}

.reference-tabs :deep(.el-tabs__nav) {
  display: flex;
  justify-content: center;
  width: 100%;
}

.reference-tabs :deep(.el-tabs__item) {
  text-align: center;
  flex: 1;
  max-width: 80px;
  min-width: 60px;
  padding: 0 8px;
  font-size: 13px;
}

.reference-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.history-timeline {
  padding: 20px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.timeline-item {
  padding: 12px;
  border-left: 2px solid #1890ff;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.timeline-item:hover {
  background-color: #f0f9ff;
}

.timeline-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.timeline-content {
  font-size: 14px;
}

.visit-diagnosis {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.visit-prescription {
  color: #666;
  font-size: 12px;
}

.reports-list {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.report-item {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.report-item:hover {
  border-color: #1890ff;
  background-color: #f0f9ff;
}

.report-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.report-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.report-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.report-status.normal {
  background-color: #f6ffed;
  color: #52c41a;
}

.report-status.abnormal {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.images-gallery {
  padding: 16px 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  justify-content: space-between;
  height: 100%;
}

.image-item {
  cursor: pointer;
  transition: all 0.3s;
}

.image-item:hover {
  transform: scale(1.05);
}

.image-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 4px;
}

.image-name {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.ai-diagnosis {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
}

.ai-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s;
}

.ai-card.clickable {
  cursor: pointer;
  border-color: #d0d0d0;
}

.ai-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ai-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.ai-card-content {
  font-size: 12px;
  color: #666;
}

.analysis-text {
  margin-bottom: 12px;
}

.scores-list {
  margin: 12px 0 16px 0;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
}

.score-element {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.score-value {
  font-weight: 500;
  color: #52c41a;
  font-size: 14px;
}

.syndrome-group-card {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.syndrome-group-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.syndrome-group-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.syndrome-group-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  padding: 0;
  border: none;
}

.syndrome-group-content {
  padding: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading-container p {
  margin-top: 16px;
  font-size: 14px;
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.tool-sub-page {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  width: 50%;
  height: 80%;
  max-width: 100%;
  max-height: 100%;
  position: relative;
  overflow: hidden;
}

.tool-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 200px;
}

.tool-content > * {
  width: 80%;
  max-width: 500px;
}

.tool-content h3 {
  margin-bottom: 16px;
  color: #333;
}

.tool-content p {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
}

.add-tool-form {
  max-width: 400px;
}

.add-tool-form .el-form-item {
  margin-bottom: 20px;
}

.add-tool-form .el-button {
  margin-right: 12px;
}

/* 治法卡片容器样式 */
.treatment-methods-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: visible;
}

.treatment-method-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.treatment-method-card:hover {
  background: #e3f2fd;
  border-color: #1890ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.method-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.method-name {
  font-size: 16px;
  font-weight: 600;
  color: #34495e;
  flex: 1;
}

.method-prescription {
  font-size: 14px;
  font-weight: 500;
  color: #7f8c8d;
  text-align: right;
  flex: 1;
}

/* 子页面遮罩层样式 */
.sub-page-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.sub-page-container {
  width: 70%;
  height: 80%;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sub-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sub-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.sub-page-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #e6e6e6;
  transform: scale(1.1);
}

.sub-page-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.prescription-detail h3,
.prescription-detail h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.prescription-detail h4 {
  margin: 20px 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #666;
}

.prescription-detail p {
  margin: 8px 0 16px 0;
  line-height: 1.6;
  color: #666;
}

.composition-table {
  margin: 12px 0 16px 0;
}

.composition-table .el-table {
  font-size: 12px;
}

.composition-table .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  text-align: center;
}

.composition-table .el-table td {
  padding: 8px 4px;
  text-align: center;
}

.composition-table .el-table .el-table__cell:first-child {
  font-weight: 500;
  color: #1890ff;
}

.composition-table .el-table .el-table__cell:last-child {
  text-align: left;
  padding-left: 8px;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 滚动条显示逻辑 */
*:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

/* 右侧栏滚动条样式 */
.reference-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.reference-tabs :deep(.el-tabs__content)::-webkit-scrollbar {
  width: 6px;
}

.reference-tabs :deep(.el-tabs__content)::-webkit-scrollbar-track {
  background: transparent;
}

.reference-tabs :deep(.el-tabs__content)::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.reference-tabs :deep(.el-tabs__content):hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

/* 中间栏滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 6px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.scrollable-content:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

/* AI诊断区域滚动条样式 */
.ai-diagnosis::-webkit-scrollbar {
  width: 6px;
}

.ai-diagnosis::-webkit-scrollbar-track {
  background: transparent;
}

.ai-diagnosis::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.ai-diagnosis:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

/* 子页面内容滚动条样式 */
.sub-page-content::-webkit-scrollbar {
  width: 6px;
}

.sub-page-content::-webkit-scrollbar-track {
  background: transparent;
}

.sub-page-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.sub-page-content:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

/* 当AI听诊收起时，工具区上边框样式调整 */

/* AI听诊收起时，工具区上边框变为AI听诊的边框 */
.ai-auscultation-container:has(.auscultation-content:not([style*="display: none"])) + .tools-section {
  border-top-color: #e0e0e0;
}

/* Vue Transition 动画样式 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(-30px);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-30px);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.slide-fade-enter-to,
.slide-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
  max-height: 500px; /* 增加最大高度以适应动态内容 */
  padding-top: 16px;
  padding-bottom: 16px;
}

/* 占位符样式 */
.queue-placeholder,
.auscultation-placeholder {
  height: 0;
  overflow: hidden;
}
</style> 