import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiService } from '@/services/api'
import type { ConsultationRequest, ChatRequest, ChatResponse } from '@/services/api'

export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  isComplete?: boolean
  extractedData?: any
}

export interface ConsultationSession {
  sessionId: string
  patientInfo: {
    name: string
    age: number
    gender: string
  }
  messages: Message[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export const useConsultationStore = defineStore('consultation', () => {
  // 状态
  const currentSession = ref<ConsultationSession | null>(null)
  const sessions = ref<ConsultationSession[]>([])
  const isLoading = ref(false)
  const isConnected = ref(false)

  // 计算属性
  const hasActiveSession = computed(() => {
    return currentSession.value !== null && currentSession.value.isActive
  })

  const currentMessages = computed(() => {
    return currentSession.value?.messages || []
  })

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 开始新的问诊会话
  const startConsultation = async (patientInfo: ConsultationRequest) => {
    try {
      isLoading.value = true
      
      const response = await apiService.startConsultation(patientInfo)
      
      const newSession: ConsultationSession = {
        sessionId: response.session_id || generateId(),
        patientInfo: {
          name: patientInfo.patient_name,
          age: patientInfo.patient_age,
          gender: patientInfo.patient_gender
        },
        messages: [],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // 添加系统欢迎消息
      if (response.message) {
        newSession.messages.push({
          id: generateId(),
          role: 'assistant',
          content: response.message,
          timestamp: new Date()
        })
      }

      currentSession.value = newSession
      sessions.value.unshift(newSession)
      
      return newSession
    } catch (error) {
      console.error('开始问诊失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 发送消息
  const sendMessage = async (content: string) => {
    if (!currentSession.value) {
      throw new Error('没有活跃的问诊会话')
    }

    try {
      isLoading.value = true

      // 添加用户消息
      const userMessage: Message = {
        id: generateId(),
        role: 'user',
        content,
        timestamp: new Date()
      }
      currentSession.value.messages.push(userMessage)

      // 发送到后端
      const chatRequest: ChatRequest = {
        session_id: currentSession.value.sessionId,
        message: content
      }

      const response: ChatResponse = await apiService.sendMessage(chatRequest)

      // 添加助手回复
      const assistantMessage: Message = {
        id: generateId(),
        role: 'assistant',
        content: response.response,
        timestamp: new Date(),
        isComplete: response.is_complete,
        extractedData: response.extracted_data
      }
      currentSession.value.messages.push(assistantMessage)

      // 更新会话状态
      currentSession.value.updatedAt = new Date()
      if (response.is_complete) {
        currentSession.value.isActive = false
      }

      return response
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 结束当前会话
  const endCurrentSession = () => {
    if (currentSession.value) {
      currentSession.value.isActive = false
      currentSession.value.updatedAt = new Date()
    }
  }

  // 切换到指定会话
  const switchToSession = (sessionId: string) => {
    const session = sessions.value.find(s => s.sessionId === sessionId)
    if (session) {
      currentSession.value = session
    }
  }

  // 删除会话
  const deleteSession = async (sessionId: string) => {
    try {
      await apiService.deleteRecord(sessionId)
      
      // 从本地状态中移除
      sessions.value = sessions.value.filter(s => s.sessionId !== sessionId)
      
      // 如果删除的是当前会话，清空当前会话
      if (currentSession.value?.sessionId === sessionId) {
        currentSession.value = null
      }
    } catch (error) {
      console.error('删除会话失败:', error)
      throw error
    }
  }

  // 加载历史会话
  const loadSessions = async () => {
    try {
      const records = await apiService.getRecords()
      // 这里需要根据后端返回的数据格式进行转换
      sessions.value = records.map(record => ({
        sessionId: record.session_id,
        patientInfo: record.patient_info,
        messages: record.messages || [],
        isActive: record.is_active || false,
        createdAt: new Date(record.created_at),
        updatedAt: new Date(record.updated_at)
      }))
    } catch (error) {
      console.error('加载会话历史失败:', error)
    }
  }

  // 清空所有会话
  const clearAllSessions = () => {
    sessions.value = []
    currentSession.value = null
  }

  return {
    // 状态
    currentSession,
    sessions,
    isLoading,
    isConnected,
    
    // 计算属性
    hasActiveSession,
    currentMessages,
    
    // 方法
    startConsultation,
    sendMessage,
    endCurrentSession,
    switchToSession,
    deleteSession,
    loadSessions,
    clearAllSessions
  }
})
