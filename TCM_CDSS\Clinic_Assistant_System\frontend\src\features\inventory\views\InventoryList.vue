<template>
  <div class="inventory-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">库存管理</h1>
      <div class="add-button" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
      </div>
    </header>
    <div class="search-section">
      <div class="search-left">
        <div class="search-container">
          <el-icon class="search-icon"><Search /></el-icon>
          <input
            v-model="searchQuery"
            type="text"
            class="search-input"
            placeholder="搜索药品名称或编码"
            @input="handleSearch"
          />
        </div>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
      <div class="filter-right">
        <div class="filter-tabs">
          <div
            v-for="tab in filterTabs"
            :key="tab.value"
            :class="['filter-tab', { active: currentFilter === tab.value }]"
            @click="currentFilter = tab.value"
          >
            {{ tab.label }}
          </div>
        </div>
      </div>
    </div>
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-icon warning">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ lowStockCount }}</div>
          <div class="stat-label">库存不足</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon success">
          <el-icon><Box /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalItems }}</div>
          <div class="stat-label">总品种数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon info">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">¥{{ totalValue.toFixed(2) }}</div>
          <div class="stat-label">库存总值</div>
        </div>
      </div>
    </div>
    <div class="inventory-list">
      <div
        v-for="item in filteredInventoryItems"
        :key="item.id"
        class="inventory-item"
        @click="viewDetail(item)"
      >
        <div class="item-header">
          <div class="medicine-info">
            <div class="medicine-name">{{ item.name }}</div>
            <div class="medicine-code">{{ item.code }}</div>
          </div>
          <div class="stock-status" :class="getStockStatusClass(item.stock)">
            {{ getStockStatusText(item.stock) }}
          </div>
        </div>
        
        <div class="item-content">
          <div class="stock-info">
            <div class="stock-amount">
              <span class="label">库存：</span>
              <span class="amount">{{ item.stock }}</span>
              <span class="unit">{{ item.unit }}</span>
            </div>
            <div class="stock-limit">
              <span class="label">最低库存：</span>
              <span class="limit">{{ item.minStock }}{{ item.unit }}</span>
            </div>
          </div>
          <div class="price-info">
            <div class="price">¥{{ item.price.toFixed(2) }}/{{ item.unit }}</div>
            <div class="total-value">总值：¥{{ (item.stock * item.price).toFixed(2) }}</div>
          </div>
        </div>

        <div class="item-actions">
          <el-button
            type="primary"
            size="small"
            @click.stop="openInStockDialog(item)"
          >
            入库
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click.stop="openOutStockDialog(item)"
          >
            出库
          </el-button>
          <el-button
            type="info"
            size="small"
            @click.stop="viewDetail(item)"
          >
            详情
          </el-button>
        </div>
      </div>
    </div>
    <div v-if="filteredInventoryItems.length === 0" class="empty-state">
      <el-icon size="64" color="#C7C7CC"><Box /></el-icon>
      <p>暂无库存数据</p>
    </div>
    <el-dialog
      v-model="showAddDialog"
      title="新增药品"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="inventoryFormRef"
        :model="inventoryForm"
        :rules="inventoryRules"
        label-width="100px"
      >
        <el-form-item label="药品名称" prop="name">
          <el-input v-model="inventoryForm.name" placeholder="请输入药品名称" />
        </el-form-item>
        <el-form-item label="药品编码" prop="code">
          <el-input v-model="inventoryForm.code" placeholder="请输入药品编码" />
        </el-form-item>
        <el-form-item label="规格" prop="specification">
          <el-input v-model="inventoryForm.specification" placeholder="请输入规格" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-select v-model="inventoryForm.unit" placeholder="请选择单位" style="width: 100%">
            <el-option label="克" value="克" />
            <el-option label="千克" value="千克" />
            <el-option label="包" value="包" />
            <el-option label="瓶" value="瓶" />
            <el-option label="盒" value="盒" />
          </el-select>
        </el-form-item>
        <el-form-item label="单价" prop="price">
          <el-input-number
            v-model="inventoryForm.price"
            :min="0"
            :precision="2"
            :step="0.01"
            style="width: 100%"
            placeholder="请输入单价"
          />
        </el-form-item>
        <el-form-item label="初始库存" prop="stock">
          <el-input-number
            v-model="inventoryForm.stock"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入初始库存"
          />
        </el-form-item>
        <el-form-item label="最低库存" prop="minStock">
          <el-input-number
            v-model="inventoryForm.minStock"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入最低库存"
          />
        </el-form-item>
        <el-form-item label="供应商" prop="supplier">
          <el-input v-model="inventoryForm.supplier" placeholder="请输入供应商" />
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            v-model="inventoryForm.note"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息..."
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveInventoryItem">保存</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showInStockDialog"
      title="药品入库"
      width="400px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedItem" class="in-stock-form">
        <div class="item-info">
          <div class="item-name">{{ selectedItem.name }}</div>
          <div class="item-code">{{ selectedItem.code }}</div>
          <div class="current-stock">当前库存：{{ selectedItem.stock }}{{ selectedItem.unit }}</div>
        </div>
        <el-form
          ref="inStockFormRef"
          :model="inStockForm"
          :rules="inStockRules"
          label-width="80px"
        >
          <el-form-item label="入库数量" prop="amount">
            <el-input-number
              v-model="inStockForm.amount"
              :min="0.01"
              :precision="2"
              style="width: 100%"
              placeholder="请输入入库数量"
            />
          </el-form-item>
          <el-form-item label="入库单价" prop="price">
            <el-input-number
              v-model="inStockForm.price"
              :min="0"
              :precision="2"
              :step="0.01"
              style="width: 100%"
              placeholder="请输入入库单价"
            />
          </el-form-item>
          <el-form-item label="供应商" prop="supplier">
            <el-input v-model="inStockForm.supplier" placeholder="请输入供应商" />
          </el-form-item>
          <el-form-item label="备注" prop="note">
            <el-input
              v-model="inStockForm.note"
              type="textarea"
              :rows="2"
              placeholder="请输入备注信息..."
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showInStockDialog = false">取消</el-button>
        <el-button type="primary" @click="processInStock">确认入库</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showOutStockDialog"
      title="药品出库"
      width="400px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedItem" class="out-stock-form">
        <div class="item-info">
          <div class="item-name">{{ selectedItem.name }}</div>
          <div class="item-code">{{ selectedItem.code }}</div>
          <div class="current-stock">当前库存：{{ selectedItem.stock }}{{ selectedItem.unit }}</div>
        </div>
        <el-form
          ref="outStockFormRef"
          :model="outStockForm"
          :rules="outStockRules"
          label-width="80px"
        >
          <el-form-item label="出库数量" prop="amount">
            <el-input-number
              v-model="outStockForm.amount"
              :min="0.01"
              :max="selectedItem.stock"
              :precision="2"
              style="width: 100%"
              placeholder="请输入出库数量"
            />
          </el-form-item>
          <el-form-item label="出库原因" prop="reason">
            <el-select v-model="outStockForm.reason" placeholder="请选择出库原因" style="width: 100%">
              <el-option label="处方发药" value="prescription" />
              <el-option label="损耗" value="loss" />
              <el-option label="过期" value="expired" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="note">
            <el-input
              v-model="outStockForm.note"
              type="textarea"
              :rows="2"
              placeholder="请输入备注信息..."
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showOutStockDialog = false">取消</el-button>
        <el-button type="primary" @click="processOutStock">确认出库</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDetailDialog"
      title="库存详情"
      width="600px"
    >
      <div v-if="selectedItem" class="detail-content">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-row">
            <span class="label">药品名称：</span>
            <span>{{ selectedItem.name }}</span>
          </div>
          <div class="detail-row">
            <span class="label">药品编码：</span>
            <span>{{ selectedItem.code }}</span>
          </div>
          <div class="detail-row">
            <span class="label">规格：</span>
            <span>{{ selectedItem.specification }}</span>
          </div>
          <div class="detail-row">
            <span class="label">单位：</span>
            <span>{{ selectedItem.unit }}</span>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>库存信息</h4>
          <div class="detail-row">
            <span class="label">当前库存：</span>
            <span class="stock-amount">{{ selectedItem.stock }}{{ selectedItem.unit }}</span>
          </div>
          <div class="detail-row">
            <span class="label">最低库存：</span>
            <span>{{ selectedItem.minStock }}{{ selectedItem.unit }}</span>
          </div>
          <div class="detail-row">
            <span class="label">单价：</span>
            <span class="price">¥{{ selectedItem.price.toFixed(2) }}/{{ selectedItem.unit }}</span>
          </div>
          <div class="detail-row">
            <span class="label">库存总值：</span>
            <span class="total-value">¥{{ (selectedItem.stock * selectedItem.price).toFixed(2) }}</span>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>其他信息</h4>
          <div class="detail-row">
            <span class="label">供应商：</span>
            <span>{{ selectedItem.supplier }}</span>
          </div>
          <div class="detail-row">
            <span class="label">创建时间：</span>
            <span>{{ formatDate(selectedItem.createTime) }}</span>
          </div>
          <div class="detail-row">
            <span class="label">备注：</span>
            <span>{{ selectedItem.note || '-' }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Plus,
  Search,
  Box,
  Warning,
  Money
} from '@element-plus/icons-vue'

const router = useRouter()

// 搜索相关
const searchQuery = ref('')
const currentFilter = ref('all')

// 筛选标签
const filterTabs = [
  { label: '全部', value: 'all' },
  { label: '库存不足', value: 'low' },
  { label: '库存充足', value: 'normal' },
  { label: '库存过多', value: 'high' }
]

// 模拟库存数据
const inventoryItems = ref([
  {
    id: 1,
    name: '柴胡',
    code: 'CP001',
    specification: '统货',
    unit: '克',
    stock: 500,
    minStock: 100,
    price: 0.5,
    supplier: '同仁堂',
    createTime: new Date('2024-01-01'),
    note: ''
  },
  {
    id: 2,
    name: '当归',
    code: 'CP002',
    specification: '统货',
    unit: '克',
    stock: 80,
    minStock: 100,
    price: 0.8,
    supplier: '同仁堂',
    createTime: new Date('2024-01-01'),
    note: ''
  },
  {
    id: 3,
    name: '白芍',
    code: 'CP003',
    specification: '统货',
    unit: '克',
    stock: 1200,
    minStock: 100,
    price: 0.6,
    supplier: '同仁堂',
    createTime: new Date('2024-01-01'),
    note: ''
  },
  {
    id: 4,
    name: '黄芪',
    code: 'CP004',
    specification: '统货',
    unit: '克',
    stock: 200,
    minStock: 100,
    price: 1.2,
    supplier: '同仁堂',
    createTime: new Date('2024-01-01'),
    note: ''
  }
])

// 过滤后的库存列表
const filteredInventoryItems = computed(() => {
  let filtered = inventoryItems.value

  // 按状态筛选
  if (currentFilter.value !== 'all') {
    filtered = filtered.filter(item => {
      const status = getStockStatus(item.stock, item.minStock)
      return status === currentFilter.value
    })
  }

  // 按搜索关键词筛选
  if (searchQuery.value) {
    filtered = filtered.filter(item =>
      item.name.includes(searchQuery.value) ||
      item.code.includes(searchQuery.value)
    )
  }

  return filtered
})

// 统计数据
const lowStockCount = computed(() => {
  return inventoryItems.value.filter(item => item.stock < item.minStock).length
})

const totalItems = computed(() => {
  return inventoryItems.value.length
})

const totalValue = computed(() => {
  return inventoryItems.value.reduce((sum, item) => {
    return sum + (item.stock * item.price)
  }, 0)
})

// 弹窗控制
const showAddDialog = ref(false)
const showInStockDialog = ref(false)
const showOutStockDialog = ref(false)
const showDetailDialog = ref(false)
const selectedItem = ref(null)

// 表单数据
const inventoryFormRef = ref()
const inventoryForm = ref({
  name: '',
  code: '',
  specification: '',
  unit: '',
  price: 0,
  stock: 0,
  minStock: 0,
  supplier: '',
  note: ''
})

const inventoryRules = {
  name: [
    { required: true, message: '请输入药品名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入药品编码', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请选择单位', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '单价不能小于0', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入初始库存', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存不能小于0', trigger: 'blur' }
  ],
  minStock: [
    { required: true, message: '请输入最低库存', trigger: 'blur' },
    { type: 'number', min: 0, message: '最低库存不能小于0', trigger: 'blur' }
  ]
}

// 入库表单
const inStockFormRef = ref()
const inStockForm = ref({
  amount: 0,
  price: 0,
  supplier: '',
  note: ''
})

const inStockRules = {
  amount: [
    { required: true, message: '请输入入库数量', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '入库数量必须大于0', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入入库单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '单价不能小于0', trigger: 'blur' }
  ]
}

// 出库表单
const outStockFormRef = ref()
const outStockForm = ref({
  amount: 0,
  reason: '',
  note: ''
})

const outStockRules = {
  amount: [
    { required: true, message: '请输入出库数量', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '出库数量必须大于0', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请选择出库原因', trigger: 'change' }
  ]
}

// 返回功能中心
const goBack = () => router.push('/dashboard')

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

// 获取库存状态
const getStockStatus = (stock: number, minStock: number) => {
  if (stock < minStock) return 'low'
  if (stock > minStock * 3) return 'high'
  return 'normal'
}

// 获取库存状态文本
const getStockStatusText = (stock: number) => {
  const item = inventoryItems.value.find(item => item.stock === stock)
  if (!item) return '正常'
  
  const status = getStockStatus(stock, item.minStock)
  const statusMap = {
    low: '库存不足',
    normal: '库存正常',
    high: '库存充足'
  }
  return statusMap[status] || '正常'
}

// 获取库存状态样式类
const getStockStatusClass = (stock: number) => {
  const item = inventoryItems.value.find(item => item.stock === stock)
  if (!item) return 'normal'
  
  const status = getStockStatus(stock, item.minStock)
  return status
}

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN')
}

// 查看库存详情
const viewDetail = (item: any) => {
  selectedItem.value = item
  showDetailDialog.value = true
}

// 显示入库弹窗
const openInStockDialog = (item: any) => {
  selectedItem.value = item
  inStockForm.value = {
    amount: 0,
    price: item.price,
    supplier: item.supplier,
    note: ''
  }
  showInStockDialog.value = true
}

// 显示出库弹窗
const openOutStockDialog = (item: any) => {
  selectedItem.value = item
  outStockForm.value = {
    amount: 0,
    reason: '',
    note: ''
  }
  showOutStockDialog.value = true
}

// 处理入库
const processInStock = async () => {
  if (!inStockFormRef.value || !selectedItem.value) return

  try {
    await inStockFormRef.value.validate()
    
    selectedItem.value.stock += inStockForm.value.amount
    selectedItem.value.price = inStockForm.value.price // 更新最新价格
    
    showInStockDialog.value = false
    ElMessage.success('入库成功')
  } catch (error) {
    ElMessage.error('请检查表单信息')
  }
}

// 处理出库
const processOutStock = async () => {
  if (!outStockFormRef.value || !selectedItem.value) return

  try {
    await outStockFormRef.value.validate()
    
    if (outStockForm.value.amount > selectedItem.value.stock) {
      ElMessage.error('出库数量不能超过当前库存')
      return
    }
    
    selectedItem.value.stock -= outStockForm.value.amount
    
    showOutStockDialog.value = false
    ElMessage.success('出库成功')
  } catch (error) {
    ElMessage.error('请检查表单信息')
  }
}

// 保存库存项目
const saveInventoryItem = async () => {
  if (!inventoryFormRef.value) return

  try {
    await inventoryFormRef.value.validate()
    
    const newItem = {
      id: Date.now(),
      ...inventoryForm.value,
      createTime: new Date()
    }
    
    inventoryItems.value.unshift(newItem)
    showAddDialog.value = false
    
    // 重置表单
    inventoryForm.value = {
      name: '',
      code: '',
      specification: '',
      unit: '',
      price: 0,
      stock: 0,
      minStock: 0,
      supplier: '',
      note: ''
    }
    
    ElMessage.success('药品添加成功')
  } catch (error) {
    ElMessage.error('请检查表单信息')
  }
}
</script>

<style scoped>
.inventory-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}
.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}
.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}
.back-button:hover {
  color: var(--primary-dark);
}
.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}
.add-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: white;
}
.add-button:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}
.search-section {
  padding: var(--spacing-sm) var(--spacing-xl);
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}
.search-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.search-container {
  position: relative;
  width: 300px;
}
.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}
.search-input {
  width: 100%;
  height: 36px;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 44px;
  border: 1px solid var(--border-color);
  border-radius: 18px;
  background: var(--background-color);
  font-size: var(--font-size-md);
  outline: none;
  transition: all var(--transition-fast);
}
.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}
.search-input::placeholder {
  color: var(--text-secondary);
}
.filter-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.filter-tabs {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}
.filter-tab {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.filter-tab:hover {
  color: var(--primary-color);
}
.filter-tab.active {
  background: var(--primary-color);
  color: white;
}
.stats-section {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
}
.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-light);
}
.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: white;
}
.stat-icon.warning {
  background: #ff9500;
}
.stat-icon.success {
  background: #34c759;
}
.stat-icon.info {
  background: #007aff;
}
.stat-content {
  flex: 1;
}
.stat-number {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}
.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.inventory-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
}
.inventory-item {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-fast);
}
.inventory-item:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}
.medicine-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.medicine-name {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}
.medicine-code {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.stock-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
}
.stock-status.low {
  background: #f8d7da;
  color: #721c24;
}
.stock-status.normal {
  background: #d4edda;
  color: #155724;
}
.stock-status.high {
  background: #fff3cd;
  color: #856404;
}
.item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}
.stock-info {
  flex: 1;
}
.stock-amount {
  margin-bottom: var(--spacing-xs);
}
.stock-amount .label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.stock-amount .amount {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}
.stock-amount .unit {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.stock-limit {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.stock-limit .label {
  margin-right: var(--spacing-xs);
}
.price-info {
  text-align: right;
}
.price {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}
.total-value {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.item-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  gap: var(--spacing-md);
}
.empty-state p {
  font-size: var(--font-size-md);
  margin: 0;
}
.item-info {
  background: var(--background-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-medium);
  margin-bottom: var(--spacing-lg);
}
.item-info .item-name {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}
.item-info .item-code {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}
.item-info .current-stock {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: 500;
}
.detail-content {
  padding: var(--spacing-md);
}
.detail-section {
  margin-bottom: var(--spacing-lg);
}
.detail-section h4 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}
.detail-row {
  display: flex;
  margin-bottom: var(--spacing-sm);
}
.detail-row .label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 100px;
}
.detail-row .stock-amount {
  color: var(--primary-color);
  font-weight: 600;
}
.detail-row .price {
  color: var(--primary-color);
  font-weight: 600;
}
.detail-row .total-value {
  color: var(--primary-color);
  font-weight: 600;
}
</style> 