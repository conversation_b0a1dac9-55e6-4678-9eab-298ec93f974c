# api接口
服务器地址：http://192.168.0.92:82/v1/chat/completions

请求方式：POST
响应格式：JSON

请求头
名称 类型 描述  Content-Type string  固定值application/json  


请求体
名称	类型	描述
request_id	string	 请求id
phone_number	string	手机号
query	string	聊天内容
api_key	string	认证key

{
    "request_id": "12345",
    "phone_number": "13900000000",
    "query": "",
    "api_key": "XXXXXX"
}


响应流
名称	类型	描述
request_id	string	 请求id
phone_number	string	手机号
response	string	响应内容

{"request_id": "12345", "phone_number": "13913977533", "response": ""}
{"request_id": "12345", "phone_number": "13913977533", "response": "\u563f"}
{"request_id": "12345", "phone_number": "13913977533", "response": "\uff0c"}
{"request_id": "12345", "phone_number": "13913977533", "response": "\u4e01"}
{"request_id":"12345","phone_number":"13913977533","response":"\u540c\u5b66\uff01\u6211\u662fAlan"}
{"request_id":"12345","phone_number":"13913977533","response":"\uff0c\u4f60\u7684\u8bfe\u7a0b\u54a8\u8be2"}
{"request_id":"12345","phone_number":"13913977533","response":"\u987e\u95ee\u548cAI\u804c\u4e1a"}
{"request_id":"12345","phone_number":"13913977533","response":"\u89c4\u5212\u5e08\u3002\u6709\u4ec0\u4e48"}
{"request_id":"12345","phone_number":"13913977533","response":"\u6211\u80fd\u5e2e\u5230\u4f60\u7684"}
{"request_id": "12345", "phone_number": "13913977533", "response": "\u5417\uff1f"}
{"request_id": "12345", "phone_number": "13913977533", "response": ""}



# 后端服务器代码
```python
import json
from flask import Flask, request, Response, jsonify
from langchain.chat_models import ChatOpenAI
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
chat_model = ChatOpenAI(
    model="GuanBao_BianCang_qwen2.5_7b_v0.0.1",  # 本地模型路径 vllm部署后model调用的名字
    api_key="EMPTY",  # 本地调用不需要 API key
    openai_api_base="http://192.168.0.92:81/v1"  # 本地 API 基础地址22222为你vllm不熟的时候自定义的端口号
)
app = Flask(__name__)
@app.route('/chat', methods=['POST'])
def chat_api():
    # 从请求中获取数据
    data = request.json
    request_id = data.get('request_id')
    phone_number = data.get('phone_number')
    query = data.get('query')
    logger.info("正在处理请求: %s", request_id)
    logger.info("正在处理用户: %s", phone_number)
    logger.info("正在处理问题: %s", query)
    if not request_id or not phone_number or not query:
        return jsonify({"error": "Missing required fields"}), 400
    # 创建一个生成器来逐块返回数据
    def generate_response():
        for chunk in chat_model.stream(query):
            json_chunk = json.dumps({
                "request_id": request_id,
                "phone_number": phone_number,
                "response": chunk.content
            })
            yield json_chunk + '\n'  # 每个 JSON 块后面加上换行符
    # 使用 Flask 的 Response 对象来返回流式响应
    return Response(generate_response(), content_type='application/json')
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=82, threaded=True)

```