#!/usr/bin/env python3
"""
测试智能体短期记忆功能
"""

import requests
import json
import time

def test_chat_memory():
    """测试聊天服务的记忆功能"""
    print("🧠 测试聊天服务短期记忆功能")
    print("=" * 50)
    
    base_url = "http://localhost:8002/api"
    
    # 测试对话序列
    conversation = [
        "我头痛",
        "主要在太阳穴附近",
        "已经持续三天了",
        "还有发热症状",
        "体温大概38度",
        "晚上会盗汗",
        "睡眠质量不好"
    ]
    
    session_id = None
    
    print("开始对话测试...")
    for i, message in enumerate(conversation, 1):
        try:
            # 发送消息
            response = requests.post(f"{base_url}/chat", json={
                "message": message,
                "session_id": session_id
            }, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get("session_id")  # 获取会话ID
                ai_response = data.get("response", "无回复")
                
                print(f"\n第{i}轮对话:")
                print(f"👤 用户: {message}")
                print(f"🤖 AI: {ai_response}")
                print(f"📝 会话ID: {session_id}")
                
                # 检查AI是否记住了之前的信息
                if i > 1:
                    # 检查是否避免重复询问
                    if "头痛" in message and "头痛" in ai_response and "哪个部位" in ai_response:
                        print("⚠️ AI可能重复询问了已知信息")
                    elif i > 2 and any(keyword in ai_response.lower() for keyword in ["头痛", "太阳穴", "三天"]):
                        print("✅ AI记住了之前的症状信息")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                break
                
            time.sleep(2)  # 避免请求过快
            
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            break
    
    # 测试获取对话历史
    if session_id:
        print(f"\n📋 获取会话历史...")
        try:
            response = requests.get(f"{base_url}/chat/history/{session_id}")
            if response.status_code == 200:
                data = response.json()
                history = data.get("history", [])
                print(f"✅ 历史记录条数: {len(history)}")
                print(f"📊 会话摘要: {data.get('summary', '无')}")
            else:
                print(f"❌ 获取历史失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 获取历史异常: {e}")

def test_inquiry_memory():
    """测试问诊服务的记忆功能"""
    print("\n🏥 测试问诊服务智能体记忆功能")
    print("=" * 50)
    
    base_url = "http://localhost:8002/api"
    
    # 1. 开始问诊
    print("1. 开始问诊...")
    try:
        response = requests.post(f"{base_url}/inquiry/start", json={
            "patient_name": "张三",
            "patient_age": 35,
            "patient_gender": "男"
        })
        
        if response.status_code == 200:
            data = response.json()
            session_id = data.get("session_id")
            welcome_msg = data.get("message")
            
            print(f"✅ 问诊启动成功")
            print(f"📝 会话ID: {session_id}")
            print(f"💬 欢迎消息: {welcome_msg}")
            
            # 2. 进行对话测试
            print(f"\n2. 进行智能体对话...")
            
            inquiry_conversation = [
                "我最近头痛很严重",
                "主要是偏头痛，在左侧太阳穴",
                "已经持续一周了，每天都痛",
                "还伴有恶心想吐的感觉",
                "工作压力比较大，经常熬夜",
                "以前也有过类似症状，但没这么严重"
            ]
            
            for i, message in enumerate(inquiry_conversation, 1):
                try:
                    # 使用问诊专用接口
                    response = requests.post(f"{base_url}/inquiry/chat", json={
                        "message": message,
                        "session_id": session_id
                    }, timeout=30)
                    
                    if response.status_code == 200:
                        data = response.json()
                        ai_response = data.get("response", "无回复")
                        
                        print(f"\n第{i}轮问诊:")
                        print(f"👤 患者: {message}")
                        print(f"🩺 医生助手: {ai_response}")
                        
                        # 检查智能体是否基于历史信息提问
                        if i > 1:
                            if "头痛" in message and "头痛" not in ai_response:
                                print("✅ AI记住了头痛症状，没有重复询问")
                            if "太阳穴" in message and "部位" not in ai_response:
                                print("✅ AI记住了疼痛部位")
                            if "一周" in message and "多长时间" not in ai_response:
                                print("✅ AI记住了持续时间")
                    
                    else:
                        print(f"❌ 问诊对话失败: {response.status_code}")
                        break
                        
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"❌ 问诊对话异常: {e}")
                    break
            
            # 3. 获取问诊摘要
            print(f"\n3. 获取问诊摘要...")
            try:
                response = requests.get(f"{base_url}/inquiry/session/{session_id}/summary")
                if response.status_code == 200:
                    data = response.json()
                    summary = data.get("summary", {})
                    
                    print(f"✅ 问诊摘要:")
                    print(f"   患者: {summary.get('patient_info', {}).get('name')} "
                          f"({summary.get('patient_info', {}).get('age')}岁, "
                          f"{summary.get('patient_info', {}).get('gender')})")
                    print(f"   对话轮数: {summary.get('message_count', 0)}")
                    print(f"   当前阶段: {summary.get('current_stage', '未知')}")
                    print(f"   已收集信息: {summary.get('collected_info', {})}")
                else:
                    print(f"❌ 获取摘要失败: {response.status_code}")
            except Exception as e:
                print(f"❌ 获取摘要异常: {e}")
                
        else:
            print(f"❌ 问诊启动失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 问诊启动异常: {e}")

def test_memory_persistence():
    """测试记忆持久性"""
    print("\n💾 测试记忆持久性")
    print("=" * 50)
    
    base_url = "http://localhost:8002/api"
    
    # 创建一个会话并发送几条消息
    print("1. 创建会话并建立记忆...")
    
    messages = ["我头痛", "在前额部位", "持续两天了"]
    session_id = None
    
    for msg in messages:
        try:
            response = requests.post(f"{base_url}/chat", json={
                "message": msg,
                "session_id": session_id
            })
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get("session_id")
                print(f"✅ 发送: {msg}")
            else:
                print(f"❌ 发送失败: {msg}")
                return
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            return
    
    print(f"\n2. 测试记忆延续...")
    
    # 发送一个需要基于历史信息回答的问题
    test_message = "这个症状严重吗？"
    
    try:
        response = requests.post(f"{base_url}/chat", json={
            "message": test_message,
            "session_id": session_id
        })
        
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get("response", "")
            
            print(f"👤 用户: {test_message}")
            print(f"🤖 AI: {ai_response}")
            
            # 检查AI是否基于之前的头痛信息回答
            if any(keyword in ai_response for keyword in ["头痛", "前额", "两天"]):
                print("✅ AI成功基于历史信息回答")
            else:
                print("⚠️ AI可能没有充分利用历史信息")
                
        else:
            print(f"❌ 测试失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def main():
    """主测试函数"""
    print("🧠 智能体短期记忆功能测试")
    print("=" * 60)
    
    try:
        # 测试服务器连接
        response = requests.get("http://localhost:8002/api/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器连接失败，请确保服务器正在运行")
            return
        
        print("✅ 服务器连接正常")
        
        # 运行测试
        test_chat_memory()
        test_inquiry_memory()
        test_memory_persistence()
        
        print("\n🎉 记忆功能测试完成！")
        print("\n💡 测试要点:")
        print("1. AI应该记住之前提到的症状信息")
        print("2. 避免重复询问已知信息")
        print("3. 基于对话历史提供连贯的回复")
        print("4. 会话ID应该保持一致")
        print("5. 可以获取完整的对话历史")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
