<template>
  <div class="pharmacy-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">药房管理</h1>
      <div class="add-button" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
      </div>
    </header>
    <div class="search-section">
      <div class="search-left">
        <div class="search-container">
          <el-icon class="search-icon"><Search /></el-icon>
          <input
            v-model="searchQuery"
            type="text"
            class="search-input"
            placeholder="搜索患者姓名或处方编号"
            @input="handleSearch"
          />
        </div>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
      <div class="filter-right">
        <div class="filter-tabs">
          <div
            v-for="tab in filterTabs"
            :key="tab.value"
            :class="['filter-tab', { active: currentFilter === tab.value }]"
            @click="currentFilter = tab.value"
          >
            {{ tab.label }}
          </div>
        </div>
      </div>
    </div>
    <div class="prescription-list">
      <div
        v-for="prescription in filteredPrescriptions"
        :key="prescription.id"
        class="prescription-item"
        @click="viewPrescriptionDetail(prescription)"
      >
        <div class="item-header">
          <div class="prescription-info">
            <div class="prescription-id">处方号：{{ prescription.id }}</div>
            <div class="patient-name">{{ prescription.patientName }}</div>
          </div>
          <div class="status-badge" :class="prescription.status">
            {{ getStatusText(prescription.status) }}
          </div>
        </div>
        
        <div class="item-content">
          <div class="medicine-info">
            <div class="medicine-count">{{ prescription.medicines.length }} 味药</div>
            <div class="medicine-list">
              {{ prescription.medicines.map(m => m.name).join('、') }}
            </div>
          </div>
          <div class="time-info">
            <div class="create-time">{{ formatDate(prescription.createTime) }}</div>
            <div v-if="prescription.dispenseTime" class="dispense-time">
              发药时间：{{ formatDate(prescription.dispenseTime) }}
            </div>
          </div>
        </div>

        <div class="item-actions">
          <el-button
            v-if="prescription.status === 'pending'"
            type="primary"
            size="small"
            @click.stop="dispenseMedicine(prescription)"
          >
            发药
          </el-button>
          <el-button
            v-if="prescription.status === 'dispensed'"
            type="success"
            size="small"
            @click.stop="printPrescription(prescription)"
          >
            打印
          </el-button>
          <el-button
            type="info"
            size="small"
            @click.stop="viewDetail(prescription)"
          >
            详情
          </el-button>
        </div>
      </div>
    </div>
    <div v-if="filteredPrescriptions.length === 0" class="empty-state">
      <el-icon size="64" color="#C7C7CC"><Box /></el-icon>
      <p>暂无处方数据</p>
    </div>
    <el-dialog
      v-model="showAddDialog"
      title="新增处方"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="prescriptionFormRef"
        :model="prescriptionForm"
        :rules="prescriptionRules"
        label-width="100px"
      >
        <el-form-item label="患者姓名" prop="patientName">
          <el-input v-model="prescriptionForm.patientName" placeholder="请输入患者姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="patientPhone">
          <el-input v-model="prescriptionForm.patientPhone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="医师" prop="doctor">
          <el-select v-model="prescriptionForm.doctor" placeholder="请选择医师" style="width: 100%">
            <el-option
              v-for="doctor in doctors"
              :key="doctor"
              :label="doctor"
              :value="doctor"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="药品" prop="medicines">
          <div class="medicine-list-form">
            <div
              v-for="(medicine, index) in prescriptionForm.medicines"
              :key="index"
              class="medicine-item"
            >
              <el-input
                v-model="medicine.name"
                placeholder="药品名称"
                style="flex: 2"
              />
              <el-input-number
                v-model="medicine.amount"
                :min="1"
                :max="100"
                placeholder="克数"
                style="flex: 1"
              />
              <el-input
                v-model="medicine.note"
                placeholder="备注"
                style="flex: 1"
              />
              <el-button
                type="danger"
                size="small"
                @click="removeMedicine(index)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <el-button type="primary" size="small" @click="addMedicine">
              <el-icon><Plus /></el-icon>
              添加药品
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="用法用量" prop="usage">
          <el-input
            v-model="prescriptionForm.usage"
            type="textarea"
            :rows="3"
            placeholder="请描述用法用量..."
          />
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            v-model="prescriptionForm.note"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息..."
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="savePrescription">保存</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDetailDialog"
      title="处方详情"
      width="700px"
    >
      <div v-if="selectedPrescription" class="detail-content">
        <div class="detail-section">
          <h4>患者信息</h4>
          <div class="detail-row">
            <span class="label">姓名：</span>
            <span>{{ selectedPrescription.patientName }}</span>
          </div>
          <div class="detail-row">
            <span class="label">手机：</span>
            <span>{{ selectedPrescription.patientPhone }}</span>
          </div>
          <div class="detail-row">
            <span class="label">医师：</span>
            <span>{{ selectedPrescription.doctor }}</span>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>药品信息</h4>
          <div class="medicine-detail-list">
            <div
              v-for="(medicine, index) in selectedPrescription.medicines"
              :key="index"
              class="medicine-detail-item"
            >
              <div class="medicine-name">{{ medicine.name }}</div>
              <div class="medicine-amount">{{ medicine.amount }}g</div>
              <div class="medicine-note">{{ medicine.note || '-' }}</div>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>用法用量</h4>
          <div class="usage-text">{{ selectedPrescription.usage }}</div>
        </div>
        
        <div class="detail-section">
          <h4>时间信息</h4>
          <div class="detail-row">
            <span class="label">开具时间：</span>
            <span>{{ formatDate(selectedPrescription.createTime) }}</span>
          </div>
          <div v-if="selectedPrescription.dispenseTime" class="detail-row">
            <span class="label">发药时间：</span>
            <span>{{ formatDate(selectedPrescription.dispenseTime) }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Plus,
  Search,
  Box,
  Delete
} from '@element-plus/icons-vue'

const router = useRouter()

// 搜索相关
const searchQuery = ref('')
const currentFilter = ref('all')

// 筛选标签
const filterTabs = [
  { label: '全部', value: 'all' },
  { label: '待发药', value: 'pending' },
  { label: '已发药', value: 'dispensed' },
  { label: '已取消', value: 'cancelled' }
]

// 医师列表
const doctors = ['张医生', '王医生', '李医生', '赵医生']

// 模拟处方数据
const prescriptions = ref([
  {
    id: 'P20240115001',
    patientName: '李小明',
    patientPhone: '138****1234',
    doctor: '张医生',
    medicines: [
      { name: '柴胡', amount: 10, note: '' },
      { name: '当归', amount: 10, note: '' },
      { name: '白芍', amount: 10, note: '' },
      { name: '白术', amount: 10, note: '' }
    ],
    usage: '水煎服，每日1剂，分2次服用',
    status: 'pending',
    createTime: new Date('2024-01-15 09:30:00'),
    dispenseTime: null,
    note: ''
  },
  {
    id: 'P20240115002',
    patientName: '王小红',
    patientPhone: '139****5678',
    doctor: '王医生',
    medicines: [
      { name: '黄芪', amount: 15, note: '' },
      { name: '党参', amount: 12, note: '' },
      { name: '茯苓', amount: 10, note: '' },
      { name: '炙甘草', amount: 6, note: '' }
    ],
    usage: '水煎服，每日1剂，分2次服用',
    status: 'dispensed',
    createTime: new Date('2024-01-15 10:15:00'),
    dispenseTime: new Date('2024-01-15 10:30:00'),
    note: ''
  },
  {
    id: 'P20240115003',
    patientName: '张三',
    patientPhone: '137****9012',
    doctor: '李医生',
    medicines: [
      { name: '川芎', amount: 10, note: '' },
      { name: '红花', amount: 6, note: '' },
      { name: '桃仁', amount: 10, note: '' },
      { name: '赤芍', amount: 10, note: '' }
    ],
    usage: '水煎服，每日1剂，分2次服用',
    status: 'pending',
    createTime: new Date('2024-01-15 11:00:00'),
    dispenseTime: null,
    note: ''
  }
])

// 过滤后的处方列表
const filteredPrescriptions = computed(() => {
  let filtered = prescriptions.value

  // 按状态筛选
  if (currentFilter.value !== 'all') {
    filtered = filtered.filter(item => item.status === currentFilter.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value) {
    filtered = filtered.filter(item =>
      item.patientName.includes(searchQuery.value) ||
      item.id.includes(searchQuery.value)
    )
  }

  return filtered
})

// 弹窗控制
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const selectedPrescription = ref(null)

// 表单数据
const prescriptionFormRef = ref()
const prescriptionForm = ref({
  patientName: '',
  patientPhone: '',
  doctor: '',
  medicines: [{ name: '', amount: 10, note: '' }],
  usage: '',
  note: ''
})

const prescriptionRules = {
  patientName: [
    { required: true, message: '请输入患者姓名', trigger: 'blur' }
  ],
  patientPhone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  doctor: [
    { required: true, message: '请选择医师', trigger: 'change' }
  ],
  medicines: [
    { required: true, message: '请添加至少一种药品', trigger: 'change' }
  ],
  usage: [
    { required: true, message: '请输入用法用量', trigger: 'blur' }
  ]
}

// 返回功能中心
const goBack = () => router.push('/dashboard')

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待发药',
    dispensed: '已发药',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (date: Date) => {
  if (!date) return ''
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查看处方详情
const viewPrescriptionDetail = (prescription: any) => {
  selectedPrescription.value = prescription
  showDetailDialog.value = true
}

// 发药
const dispenseMedicine = (prescription: any) => {
  prescription.status = 'dispensed'
  prescription.dispenseTime = new Date()
  ElMessage.success('发药成功')
}

// 打印处方
const printPrescription = (prescription: any) => {
  ElMessage.info('打印功能开发中...')
}

// 查看详情
const viewDetail = (prescription: any) => {
  selectedPrescription.value = prescription
  showDetailDialog.value = true
}

// 添加药品
const addMedicine = () => {
  prescriptionForm.value.medicines.push({ name: '', amount: 10, note: '' })
}

// 删除药品
const removeMedicine = (index: number) => {
  if (prescriptionForm.value.medicines.length > 1) {
    prescriptionForm.value.medicines.splice(index, 1)
  }
}

// 保存处方
const savePrescription = async () => {
  if (!prescriptionFormRef.value) return

  try {
    await prescriptionFormRef.value.validate()
    
    const newPrescription = {
      id: `P${Date.now()}`,
      ...prescriptionForm.value,
      status: 'pending',
      createTime: new Date(),
      dispenseTime: null
    }
    
    prescriptions.value.unshift(newPrescription)
    showAddDialog.value = false
    
    // 重置表单
    prescriptionForm.value = {
      patientName: '',
      patientPhone: '',
      doctor: '',
      medicines: [{ name: '', amount: 10, note: '' }],
      usage: '',
      note: ''
    }
    
    ElMessage.success('处方创建成功')
  } catch (error) {
    ElMessage.error('请检查表单信息')
  }
}
</script>

<style scoped>
.pharmacy-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}
.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}
.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}
.back-button:hover {
  color: var(--primary-dark);
}
.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}
.add-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: white;
}
.add-button:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}
.search-section {
  padding: var(--spacing-sm) var(--spacing-xl);
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}
.search-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.search-container {
  position: relative;
  width: 300px;
}
.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}
.search-input {
  width: 100%;
  height: 36px;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 44px;
  border: 1px solid var(--border-color);
  border-radius: 18px;
  background: var(--background-color);
  font-size: var(--font-size-md);
  outline: none;
  transition: all var(--transition-fast);
}
.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}
.search-input::placeholder {
  color: var(--text-secondary);
}
.filter-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.filter-tabs {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}
.filter-tab {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.filter-tab:hover {
  color: var(--primary-color);
}
.filter-tab.active {
  background: var(--primary-color);
  color: white;
}
.prescription-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
}
.prescription-item {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-fast);
}
.prescription-item:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}
.prescription-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.prescription-id {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--primary-color);
}
.patient-name {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}
.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
}
.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}
.status-badge.dispensed {
  background: #d4edda;
  color: #155724;
}
.status-badge.cancelled {
  background: #f8d7da;
  color: #721c24;
}
.item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}
.medicine-info {
  flex: 1;
}
.medicine-count {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}
.medicine-list {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}
.time-info {
  text-align: right;
}
.create-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}
.dispense-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}
.item-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  gap: var(--spacing-md);
}
.empty-state p {
  font-size: var(--font-size-md);
  margin: 0;
}
.medicine-list-form {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-md);
}
.medicine-item {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  margin-bottom: var(--spacing-sm);
}
.detail-content {
  padding: var(--spacing-md);
}
.detail-section {
  margin-bottom: var(--spacing-lg);
}
.detail-section h4 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}
.detail-row {
  display: flex;
  margin-bottom: var(--spacing-sm);
}
.detail-row .label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 80px;
}
.medicine-detail-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.medicine-detail-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--background-color);
  border-radius: var(--border-radius-small);
}
.medicine-detail-item .medicine-name {
  flex: 2;
  font-weight: 500;
}
.medicine-detail-item .medicine-amount {
  flex: 1;
  text-align: center;
}
.medicine-detail-item .medicine-note {
  flex: 1;
  color: var(--text-secondary);
}
.usage-text {
  padding: var(--spacing-sm);
  background: var(--background-color);
  border-radius: var(--border-radius-small);
  line-height: 1.5;
}
</style> 