// 模拟数据库 - 患者表 (patients)
// 模拟从数据库中读取的患者数据

export interface Patient {
  id: number
  name: string
  phone: string
  gender: '男' | '女'
  age: number
  diagnosis: string
  lastVisit: string | null
  nextAppointment: string | null
  visitCount: number
  status: 'active' | 'inactive'
  appointmentSource?: 'online' | 'offline' // 预约来源：线上、线下
  createdAt: string
  updatedAt: string
}

// 模拟患者数据
export const patientsData: Patient[] = [
  {
    id: 1,
    name: '李小明',
    phone: '138****1234',
    gender: '男',
    age: 34,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2025-01-15',
    nextAppointment: '2025-01-21',
    visitCount: 5,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-06-15',
    updatedAt: '2025-01-15'
  },
  {
    id: 2,
    name: '王小红',
    phone: '139****5678',
    gender: '女',
    age: 28,
    diagnosis: '气血两虚证',
    lastVisit: '2025-01-14',
    nextAppointment: '2025-01-21',
    visitCount: 3,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2023-08-22',
    updatedAt: '2025-01-14'
  },
  {
    id: 3,
    name: '张建国',
    phone: '137****9012',
    gender: '男',
    age: 45,
    diagnosis: '肾阳虚证',
    lastVisit: '2025-01-13',
    nextAppointment: '2025-01-21',
    visitCount: 8,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-03-10',
    updatedAt: '2025-01-13'
  },
  {
    id: 4,
    name: '李秀英',
    phone: '136****3456',
    gender: '女',
    age: 52,
    diagnosis: '痰湿阻肺证',
    lastVisit: '2025-01-12',
    nextAppointment: '2025-01-21',
    visitCount: 12,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2022-11-05',
    updatedAt: '2025-01-12'
  },
  {
    id: 5,
    name: '王德明',
    phone: '135****7890',
    gender: '男',
    age: 38,
    diagnosis: '心脾两虚证',
    lastVisit: '2025-01-11',
    nextAppointment: '2025-01-21',
    visitCount: 6,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-05-18',
    updatedAt: '2025-01-11'
  },
  {
    id: 6,
    name: '赵丽华',
    phone: '134****1234',
    gender: '女',
    age: 29,
    diagnosis: '肝阳上亢证',
    lastVisit: '2025-01-10',
    nextAppointment: '2025-01-21',
    visitCount: 4,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2023-09-30',
    updatedAt: '2025-01-10'
  },
  {
    id: 7,
    name: '钱志强',
    phone: '133****5678',
    gender: '男',
    age: 41,
    diagnosis: '脾胃虚弱证',
    lastVisit: '2025-01-09',
    nextAppointment: '2025-01-21',
    visitCount: 7,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-04-12',
    updatedAt: '2025-01-09'
  },
  {
    id: 8,
    name: '孙美玲',
    phone: '132****9012',
    gender: '女',
    age: 35,
    diagnosis: '阴虚火旺证',
    lastVisit: '2025-01-08',
    nextAppointment: '2025-01-21',
    visitCount: 9,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-07-25',
    updatedAt: '2025-01-08'
  },
  {
    id: 9,
    name: '陈志伟',
    phone: '131****2345',
    gender: '男',
    age: 47,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2025-01-07',
    nextAppointment: '2025-01-21',
    visitCount: 15,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2022-08-15',
    updatedAt: '2025-01-07'
  },
  {
    id: 10,
    name: '刘雅琴',
    phone: '130****6789',
    gender: '女',
    age: 31,
    diagnosis: '气血两虚证',
    lastVisit: '2025-01-06',
    nextAppointment: '2025-01-21',
    visitCount: 2,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-10-08',
    updatedAt: '2025-01-06'
  },
  {
    id: 11,
    name: '杨建华',
    phone: '129****0123',
    gender: '男',
    age: 56,
    diagnosis: '肾阳虚证',
    lastVisit: '2025-01-05',
    nextAppointment: '2025-01-21',
    visitCount: 18,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2022-05-20',
    updatedAt: '2025-01-05'
  },
  {
    id: 12,
    name: '周淑芬',
    phone: '128****4567',
    gender: '女',
    age: 42,
    diagnosis: '痰湿阻肺证',
    lastVisit: '2025-01-04',
    nextAppointment: '2025-01-21',
    visitCount: 11,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-01-14',
    updatedAt: '2025-01-04'
  },
  {
    id: 13,
    name: '吴志强',
    phone: '127****8901',
    gender: '男',
    age: 33,
    diagnosis: '心脾两虚证',
    lastVisit: '2025-01-03',
    nextAppointment: '2025-01-21',
    visitCount: 3,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-11-20',
    updatedAt: '2025-01-03'
  },
  {
    id: 14,
    name: '郑丽娜',
    phone: '126****2345',
    gender: '女',
    age: 39,
    diagnosis: '肝阳上亢证',
    lastVisit: '2025-01-02',
    nextAppointment: '2025-01-21',
    visitCount: 6,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2023-02-18',
    updatedAt: '2025-01-02'
  },
  {
    id: 15,
    name: '黄志明',
    phone: '125****6789',
    gender: '男',
    age: 44,
    diagnosis: '脾胃虚弱证',
    lastVisit: '2025-01-01',
    nextAppointment: '2025-01-21',
    visitCount: 10,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2022-12-10',
    updatedAt: '2025-01-01'
  },
  {
    id: 16,
    name: '林秀珍',
    phone: '124****0123',
    gender: '女',
    age: 27,
    diagnosis: '阴虚火旺证',
    lastVisit: '2024-12-31',
    nextAppointment: '2025-01-21',
    visitCount: 4,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2023-08-05',
    updatedAt: '2024-12-31'
  },
  {
    id: 17,
    name: '马国强',
    phone: '123****4567',
    gender: '男',
    age: 51,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2024-12-30',
    nextAppointment: '2025-01-21',
    visitCount: 13,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2022-09-22',
    updatedAt: '2024-12-30'
  },
  {
    id: 18,
    name: '朱美华',
    phone: '122****8901',
    gender: '女',
    age: 36,
    diagnosis: '气血两虚证',
    lastVisit: '2024-12-29',
    nextAppointment: '2025-01-21',
    visitCount: 7,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-03-15',
    updatedAt: '2024-12-29'
  },
  {
    id: 19,
    name: '胡志强',
    phone: '121****2345',
    gender: '男',
    age: 48,
    diagnosis: '肾阳虚证',
    lastVisit: '2024-12-28',
    nextAppointment: '2025-01-21',
    visitCount: 16,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2022-07-08',
    updatedAt: '2024-12-28'
  },
  {
    id: 20,
    name: '何丽萍',
    phone: '120****6789',
    gender: '女',
    age: 32,
    diagnosis: '痰湿阻肺证',
    lastVisit: '2024-12-27',
    nextAppointment: '2025-01-21',
    visitCount: 5,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-06-30',
    updatedAt: '2024-12-27'
  },
  {
    id: 21,
    name: '罗志伟',
    phone: '119****0123',
    gender: '男',
    age: 43,
    diagnosis: '心脾两虚证',
    lastVisit: '2024-12-26',
    nextAppointment: '2025-01-21',
    visitCount: 9,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2023-01-25',
    updatedAt: '2024-12-26'
  },
  {
    id: 22,
    name: '梁秀英',
    phone: '118****4567',
    gender: '女',
    age: 38,
    diagnosis: '肝阳上亢证',
    lastVisit: '2024-12-25',
    nextAppointment: '2025-01-21',
    visitCount: 8,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2023-04-18',
    updatedAt: '2024-12-25'
  },
  {
    id: 23,
    name: '宋志明',
    phone: '117****8901',
    gender: '男',
    age: 55,
    diagnosis: '脾胃虚弱证',
    lastVisit: '2024-12-24',
    nextAppointment: '2025-01-21',
    visitCount: 14,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2022-10-12',
    updatedAt: '2024-12-24'
  },
  {
    id: 24,
    name: '唐丽娜',
    phone: '116****2345',
    gender: '女',
    age: 30,
    diagnosis: '阴虚火旺证',
    lastVisit: '2024-12-23',
    nextAppointment: '2025-01-21',
    visitCount: 3,
    status: 'active',
    appointmentSource: 'offline',
    createdAt: '2023-09-08',
    updatedAt: '2024-12-23'
  },
  {
    id: 25,
    name: '韩国强',
    phone: '115****6789',
    gender: '男',
    age: 46,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2024-12-22',
    nextAppointment: '2025-01-21',
    visitCount: 11,
    status: 'active',
    appointmentSource: 'online',
    createdAt: '2022-11-30',
    updatedAt: '2024-12-22'
  },
  {
    id: 26,
    name: '赵丽华',
    phone: '134****1234',
    gender: '女',
    age: 29,
    diagnosis: '肝阳上亢证',
    lastVisit: '2025-06-20',
    nextAppointment: '2025-06-22',
    visitCount: 4,
    status: 'active',
    createdAt: '2023-09-30',
    updatedAt: '2025-06-20'
  },
  {
    id: 27,
    name: '钱志强',
    phone: '133****5678',
    gender: '男',
    age: 41,
    diagnosis: '脾胃虚弱证',
    lastVisit: '2025-06-19',
    nextAppointment: '2025-06-24',
    visitCount: 7,
    status: 'active',
    createdAt: '2023-04-12',
    updatedAt: '2025-06-19'
  },
  {
    id: 28,
    name: '孙美玲',
    phone: '132****9012',
    gender: '女',
    age: 35,
    diagnosis: '阴虚火旺证',
    lastVisit: '2025-06-18',
    nextAppointment: null,
    visitCount: 9,
    status: 'inactive',
    createdAt: '2023-07-25',
    updatedAt: '2025-06-18'
  },
  { 
    id: 29, 
    name: '陈志伟', 
    phone: '131****2345',
    gender: '男',
    age: 47,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2024-01-14',
    nextAppointment: '2024-01-24',
    visitCount: 15,
    status: 'active',
    createdAt: '2022-08-15',
    updatedAt: '2024-01-14'
  },
  { 
    id: 30, 
    name: '刘雅琴', 
    phone: '130****6789',
    gender: '女',
    age: 31,
    diagnosis: '气血两虚证',
    lastVisit: '2024-01-11',
    nextAppointment: null,
    visitCount: 2,
    status: 'inactive',
    createdAt: '2023-10-08',
    updatedAt: '2024-01-11'
  },
  { 
    id: 31, 
    name: '杨建华', 
    phone: '129****0123',
    gender: '男',
    age: 56,
    diagnosis: '肾阳虚证',
    lastVisit: '2024-01-06',
    nextAppointment: '2024-01-26',
    visitCount: 18,
    status: 'active',
    createdAt: '2022-05-20',
    updatedAt: '2024-01-06'
  },
  { 
    id: 32, 
    name: '周淑芬', 
    phone: '128****4567',
    gender: '女',
    age: 42,
    diagnosis: '痰湿阻肺证',
    lastVisit: '2024-01-13',
    nextAppointment: '2024-01-23',
    visitCount: 11,
    status: 'active',
    createdAt: '2023-01-14',
    updatedAt: '2024-01-13'
  },
  { 
    id: 33, 
    name: '吴志强', 
    phone: '127****8901',
    gender: '男',
    age: 33,
    diagnosis: '心脾两虚证',
    lastVisit: '2024-01-04',
    nextAppointment: null,
    visitCount: 3,
    status: 'inactive',
    createdAt: '2023-11-20',
    updatedAt: '2024-01-04'
  },
  { 
    id: 34, 
    name: '郑秀兰', 
    phone: '126****2345',
    gender: '女',
    age: 48,
    diagnosis: '肝阳上亢证',
    lastVisit: '2024-01-16',
    nextAppointment: '2024-01-27',
    visitCount: 14,
    status: 'active',
    createdAt: '2022-12-03',
    updatedAt: '2024-01-16'
  },
  { 
    id: 35, 
    name: '黄建国', 
    phone: '125****6789',
    gender: '男',
    age: 39,
    diagnosis: '脾胃虚弱证',
    lastVisit: '2024-01-02',
    nextAppointment: null,
    visitCount: 5,
    status: 'inactive',
    createdAt: '2023-06-28',
    updatedAt: '2024-01-02'
  },
  { 
    id: 36, 
    name: '林美华', 
    phone: '124****0123',
    gender: '女',
    age: 26,
    diagnosis: '阴虚火旺证',
    lastVisit: '2024-01-17',
    nextAppointment: '2024-01-28',
    visitCount: 6,
    status: 'active',
    createdAt: '2023-12-05',
    updatedAt: '2024-01-17'
  },
  { 
    id: 37, 
    name: '何志明', 
    phone: '123****4567',
    gender: '男',
    age: 44,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2024-01-01',
    nextAppointment: '2024-01-29',
    visitCount: 10,
    status: 'active',
    createdAt: '2023-02-18',
    updatedAt: '2024-01-01'
  },
  { 
    id: 38, 
    name: '罗雅芳', 
    phone: '122****8901',
    gender: '女',
    age: 37,
    diagnosis: '气血两虚证',
    lastVisit: '2024-01-18',
    nextAppointment: null,
    visitCount: 7,
    status: 'inactive',
    createdAt: '2023-08-09',
    updatedAt: '2024-01-18'
  },
  { 
    id: 39, 
    name: '马志强', 
    phone: '121****2345',
    gender: '男',
    age: 43,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2024-01-19',
    nextAppointment: '2024-01-30',
    visitCount: 13,
    status: 'active',
    createdAt: '2022-09-12',
    updatedAt: '2024-01-19'
  },
  { 
    id: 40, 
    name: '朱丽娜', 
    phone: '120****6789',
    gender: '女',
    age: 25,
    diagnosis: '阴虚火旺证',
    lastVisit: '2024-01-20',
    nextAppointment: '2024-01-31',
    visitCount: 4,
    status: 'active',
    createdAt: '2023-10-15',
    updatedAt: '2024-01-20'
  },
  { 
    id: 41, 
    name: '胡建国', 
    phone: '119****0123',
    gender: '男',
    age: 58,
    diagnosis: '肾阳虚证',
    lastVisit: '2024-01-21',
    nextAppointment: null,
    visitCount: 20,
    status: 'inactive',
    createdAt: '2022-03-08',
    updatedAt: '2024-01-21'
  },
  { 
    id: 42, 
    name: '郭秀英', 
    phone: '118****4567',
    gender: '女',
    age: 49,
    diagnosis: '痰湿阻肺证',
    lastVisit: '2024-01-22',
    nextAppointment: '2024-02-01',
    visitCount: 16,
    status: 'active',
    createdAt: '2022-07-22',
    updatedAt: '2024-01-22'
  },
  { 
    id: 43, 
    name: '何德明', 
    phone: '117****8901',
    gender: '男',
    age: 36,
    diagnosis: '心脾两虚证',
    lastVisit: '2024-01-23',
    nextAppointment: null,
    visitCount: 8,
    status: 'inactive',
    createdAt: '2023-04-30',
    updatedAt: '2024-01-23'
  },
  { 
    id: 44, 
    name: '高美玲', 
    phone: '116****2345',
    gender: '女',
    age: 32,
    diagnosis: '肝阳上亢证',
    lastVisit: '2024-01-24',
    nextAppointment: '2024-02-02',
    visitCount: 6,
    status: 'active',
    createdAt: '2023-11-05',
    updatedAt: '2024-01-24'
  },
  { 
    id: 45, 
    name: '林志伟', 
    phone: '115****6789',
    gender: '男',
    age: 51,
    diagnosis: '脾胃虚弱证',
    lastVisit: '2024-01-25',
    nextAppointment: '2024-02-03',
    visitCount: 19,
    status: 'active',
    createdAt: '2022-01-18',
    updatedAt: '2024-01-25'
  },
  { 
    id: 46, 
    name: '罗雅琴', 
    phone: '114****0123',
    gender: '女',
    age: 27,
    diagnosis: '气血两虚证',
    lastVisit: '2024-01-26',
    nextAppointment: null,
    visitCount: 3,
    status: 'inactive',
    createdAt: '2023-12-10',
    updatedAt: '2024-01-26'
  },
  { 
    id: 47, 
    name: '梁建华', 
    phone: '113****4567',
    gender: '男',
    age: 46,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2024-01-27',
    nextAppointment: '2024-02-04',
    visitCount: 12,
    status: 'active',
    createdAt: '2023-02-25',
    updatedAt: '2024-01-27'
  },
  { 
    id: 48, 
    name: '宋淑芬', 
    phone: '112****8901',
    gender: '女',
    age: 53,
    diagnosis: '肾阳虚证',
    lastVisit: '2024-01-28',
    nextAppointment: '2024-02-05',
    visitCount: 22,
    status: 'active',
    createdAt: '2021-12-03',
    updatedAt: '2024-01-28'
  },
  { 
    id: 49, 
    name: '谢志强', 
    phone: '111****2345',
    gender: '男',
    age: 30,
    diagnosis: '阴虚火旺证',
    lastVisit: '2024-01-29',
    nextAppointment: null,
    visitCount: 5,
    status: 'inactive',
    createdAt: '2023-09-18',
    updatedAt: '2024-01-29'
  },
  { 
    id: 50, 
    name: '韩秀兰', 
    phone: '110****6789',
    gender: '女',
    age: 40,
    diagnosis: '痰湿阻肺证',
    lastVisit: '2024-01-30',
    nextAppointment: '2024-02-06',
    visitCount: 14,
    status: 'active',
    createdAt: '2022-10-14',
    updatedAt: '2024-01-30'
  },
  { 
    id: 51, 
    name: '唐建国', 
    phone: '109****0123',
    gender: '男',
    age: 55,
    diagnosis: '心脾两虚证',
    lastVisit: '2024-01-31',
    nextAppointment: null,
    visitCount: 17,
    status: 'inactive',
    createdAt: '2022-04-20',
    updatedAt: '2024-01-31'
  },
  { 
    id: 52, 
    name: '冯美华', 
    phone: '108****4567',
    gender: '女',
    age: 29,
    diagnosis: '肝阳上亢证',
    lastVisit: '2024-02-01',
    nextAppointment: '2024-02-07',
    visitCount: 7,
    status: 'active',
    createdAt: '2023-07-08',
    updatedAt: '2024-02-01'
  },
  { 
    id: 53, 
    name: '董志明', 
    phone: '107****8901',
    gender: '男',
    age: 38,
    diagnosis: '脾胃虚弱证',
    lastVisit: '2024-02-02',
    nextAppointment: '2024-02-08',
    visitCount: 9,
    status: 'active',
    createdAt: '2023-01-30',
    updatedAt: '2024-02-02'
  },
  { 
    id: 54, 
    name: '萧雅芳', 
    phone: '106****2345',
    gender: '女',
    age: 45,
    diagnosis: '气血两虚证',
    lastVisit: '2024-02-03',
    nextAppointment: null,
    visitCount: 11,
    status: 'inactive',
    createdAt: '2023-05-12',
    updatedAt: '2024-02-03'
  },
  { 
    id: 55, 
    name: '程建华', 
    phone: '105****6789',
    gender: '男',
    age: 33,
    diagnosis: '肝郁脾虚证',
    lastVisit: '2024-02-04',
    nextAppointment: '2024-02-09',
    visitCount: 6,
    status: 'active',
    createdAt: '2023-11-28',
    updatedAt: '2024-02-04'
  },
  { 
    id: 56, 
    name: '曹淑芬', 
    phone: '104****0123',
    gender: '女',
    age: 50,
    diagnosis: '肾阳虚证',
    lastVisit: '2024-02-05',
    nextAppointment: '2024-02-10',
    visitCount: 18,
    status: 'active',
    createdAt: '2022-06-15',
    updatedAt: '2024-02-05'
  },
  { 
    id: 57, 
    name: '袁志强', 
    phone: '103****4567',
    gender: '男',
    age: 42,
    diagnosis: '阴虚火旺证',
    lastVisit: '2024-02-06',
    nextAppointment: null,
    visitCount: 8,
    status: 'inactive',
    createdAt: '2023-03-22',
    updatedAt: '2024-02-06'
  },
  { 
    id: 58, 
    name: '潘秀兰', 
    phone: '102****8901',
    gender: '女',
    age: 35,
    diagnosis: '痰湿阻肺证',
    lastVisit: '2024-02-07',
    nextAppointment: '2024-02-11',
    visitCount: 10,
    status: 'active',
    createdAt: '2023-08-05',
    updatedAt: '2024-02-07'
  }
]

// 模拟数据库查询函数
export const patientService = {
  // 获取所有患者
  getAllPatients(): Promise<Patient[]> {
    return new Promise((resolve) => {
      // 模拟网络延迟
      setTimeout(() => {
        resolve([...patientsData])
      }, 100)
    })
  },

  // 根据ID获取患者
  getPatientById(id: number): Promise<Patient | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const patient = patientsData.find(p => p.id === id)
        resolve(patient || null)
      }, 50)
    })
  },

  // 搜索患者
  searchPatients(query: string): Promise<Patient[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const filtered = patientsData.filter(patient => 
          patient.name.includes(query) || 
          patient.phone.includes(query) ||
          patient.diagnosis.includes(query)
        )
        resolve(filtered)
      }, 150)
    })
  },

  // 根据条件筛选患者
  filterPatients(filters: {
    status?: string
    gender?: string
    diagnosis?: string
    hasAppointment?: boolean
  }): Promise<Patient[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filtered = [...patientsData]
        
        if (filters.status) {
          filtered = filtered.filter(p => p.status === filters.status)
        }
        if (filters.gender) {
          filtered = filtered.filter(p => p.gender === filters.gender)
        }
        if (filters.diagnosis) {
          filtered = filtered.filter(p => p.diagnosis === filters.diagnosis)
        }
        if (filters.hasAppointment !== undefined) {
          if (filters.hasAppointment) {
            filtered = filtered.filter(p => !!p.nextAppointment)
          } else {
            filtered = filtered.filter(p => !p.nextAppointment)
          }
        }
        
        resolve(filtered)
      }, 100)
    })
  },

  // 添加新患者
  addPatient(patient: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<Patient> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newPatient: Patient = {
          ...patient,
          id: Math.max(...patientsData.map(p => p.id)) + 1,
          createdAt: new Date().toISOString().split('T')[0],
          updatedAt: new Date().toISOString().split('T')[0]
        }
        patientsData.push(newPatient)
        resolve(newPatient)
      }, 200)
    })
  },

  // 更新患者信息
  updatePatient(id: number, updates: Partial<Patient>): Promise<Patient | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = patientsData.findIndex(p => p.id === id)
        if (index !== -1) {
          patientsData[index] = {
            ...patientsData[index],
            ...updates,
            updatedAt: new Date().toISOString().split('T')[0]
          }
          resolve(patientsData[index])
        } else {
          resolve(null)
        }
      }, 150)
    })
  },

  // 删除患者
  deletePatient(id: number): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = patientsData.findIndex(p => p.id === id)
        if (index !== -1) {
          patientsData.splice(index, 1)
          resolve(true)
        } else {
          resolve(false)
        }
      }, 100)
    })
  }
} 