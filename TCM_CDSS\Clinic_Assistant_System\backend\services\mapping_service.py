"""
映射服务
处理所有映射表的数据转换和查询
"""

from typing import Optional, List, Dict, Any
from database.connection import execute_query

class MappingService:
    """映射服务类"""

    @staticmethod
    async def get_id_by_code(table_name: str, code: str) -> Optional[int]:
        """
        根据代码获取映射ID

        Args:
            table_name: 映射表名
            code: 代码

        Returns:
            映射ID或None
        """
        try:
            result = await execute_query(
                f"SELECT id FROM {table_name} WHERE code = ? AND is_active = 1",
                (code,)
            )
            return result[0]['id'] if result else None
        except Exception as error:
            print(f"Error getting ID for {table_name}.{code}: {error}")
            return None

    @staticmethod
    async def get_by_id(table_name: str, id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取映射信息

        Args:
            table_name: 映射表名
            id: 映射ID

        Returns:
            映射信息或None
        """
        try:
            result = await execute_query(
                f"SELECT * FROM {table_name} WHERE id = ? AND is_active = 1",
                (id,)
            )
            return result[0] if result else None
        except Exception as error:
            print(f"Error getting {table_name} by ID {id}: {error}")
            return None

    @staticmethod
    async def get_all(table_name: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取所有映射数据

        Args:
            table_name: 映射表名
            category: 分类（可选）

        Returns:
            映射数据列表
        """
        try:
            sql = f"SELECT * FROM {table_name} WHERE is_active = 1"
            params = []

            if category:
                sql += " AND category = ?"
                params.append(category)

            sql += " ORDER BY sort_order ASC, display_name ASC"

            results = await execute_query(sql, tuple(params))
            return results
        except Exception as error:
            print(f"Error getting all {table_name}: {error}")
            return []

    # 性别映射相关方法
    @staticmethod
    async def get_gender_id_by_code(code: str) -> Optional[int]:
        """根据代码获取性别映射ID"""
        return await MappingService.get_id_by_code('gender_mappings', code)

    @staticmethod
    async def get_gender_by_id(id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取性别映射信息"""
        return await MappingService.get_by_id('gender_mappings', id)

    @staticmethod
    async def get_all_genders() -> List[Dict[str, Any]]:
        """获取所有性别映射"""
        return await MappingService.get_all('gender_mappings')

    # 角色映射相关方法
    @staticmethod
    async def get_role_id_by_code(code: str) -> Optional[int]:
        """根据代码获取角色映射ID"""
        return await MappingService.get_id_by_code('role_mappings', code)

    @staticmethod
    async def get_role_by_id(id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取角色映射信息"""
        return await MappingService.get_by_id('role_mappings', id)

    @staticmethod
    async def get_all_roles() -> List[Dict[str, Any]]:
        """获取所有角色映射"""
        return await MappingService.get_all('role_mappings')

    # 科室映射相关方法
    @staticmethod
    async def get_department_id_by_code(code: str) -> Optional[int]:
        """根据代码获取科室映射ID"""
        return await MappingService.get_id_by_code('department_mappings', code)

    @staticmethod
    async def get_department_by_id(id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取科室映射信息"""
        return await MappingService.get_by_id('department_mappings', id)

    @staticmethod
    async def get_all_departments() -> List[Dict[str, Any]]:
        """获取所有科室映射"""
        return await MappingService.get_all('department_mappings')

    # 状态映射相关方法
    @staticmethod
    async def get_status_id_by_code(category: str, code: str) -> Optional[int]:
        """根据分类和代码获取状态映射ID"""
        try:
            result = await execute_query(
                "SELECT id FROM status_mappings WHERE category = ? AND code = ? AND is_active = 1",
                (category, code)
            )
            return result[0]['id'] if result else None
        except Exception as error:
            print(f"Error getting status ID for {category}.{code}: {error}")
            return None

    @staticmethod
    async def get_status_by_id(id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取状态映射信息"""
        return await MappingService.get_by_id('status_mappings', id)

    @staticmethod
    async def get_statuses_by_category(category: str) -> List[Dict[str, Any]]:
        """根据分类获取状态映射列表"""
        return await MappingService.get_all('status_mappings', category)

    @staticmethod
    async def create_mapping_cache() -> Dict[str, Any]:
        """创建映射缓存"""
        try:
            cache = {
                'genders': await MappingService.get_all_genders(),
                'roles': await MappingService.get_all_roles(),
                'departments': await MappingService.get_all_departments(),
                'patient_statuses': await MappingService.get_statuses_by_category('patient'),
                'appointment_statuses': await MappingService.get_statuses_by_category('appointment'),
                'visit_statuses': await MappingService.get_statuses_by_category('visit'),
                'prescription_statuses': await MappingService.get_statuses_by_category('prescription')
            }
            return cache
        except Exception as error:
            print(f"Error creating mapping cache: {error}")
            return {}
