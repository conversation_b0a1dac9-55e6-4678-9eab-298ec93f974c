from typing import Dict, Any, List, Optional
from datetime import datetime
from langgraph.graph import StateGraph, END
from pydantic import BaseModel

from src.agents import (
    ChiefComplaintAgent, PresentIllnessAgent, PastHistoryAgent,
    FamilyHistoryAgent, AllergyHistoryAgent
)


class ConsultationState(BaseModel):
    """问诊状态"""
    patient_id: str
    session_id: str
    current_step: str = "chief_complaint"
    collected_data: Dict[str, Any] = {}
    conversation_history: List[Dict[str, str]] = []
    next_agent: Optional[str] = None
    is_complete: bool = False
    medical_record: Optional[Dict[str, Any]] = None
    current_agent_response: Optional[str] = None
    patient_response: Optional[str] = None


class ConsultationWorkflow:
    """问诊工作流管理器"""
    
    def __init__(self):
        self.agents = {
            "chief_complaint": ChiefComplaintAgent(),
            "present_illness": PresentIllnessAgent(),
            "past_history": PastHistoryAgent(),
            "family_history": FamilyHistoryAgent(),
            "allergy_history": AllergyHistoryAgent()
        }
        
        # 智能体执行顺序
        self.agent_sequence = [
            "chief_complaint",
            "present_illness", 
            "past_history",
            "family_history",
            "allergy_history"
        ]
        
        self.workflow = self._create_workflow()
    
    def _create_workflow(self) -> StateGraph:
        """创建问诊工作流"""
        workflow = StateGraph(ConsultationState)
        
        # 添加节点
        workflow.add_node("controller", self._consultation_controller)
        workflow.add_node("chief_complaint_agent", lambda s: self._execute_agent(s, "chief_complaint"))
        workflow.add_node("present_illness_agent", lambda s: self._execute_agent(s, "present_illness"))
        workflow.add_node("past_history_agent", lambda s: self._execute_agent(s, "past_history"))
        workflow.add_node("family_history_agent", lambda s: self._execute_agent(s, "family_history"))
        workflow.add_node("allergy_history_agent", lambda s: self._execute_agent(s, "allergy_history"))
        workflow.add_node("generate_record", self._generate_medical_record)
        
        # 设置入口点
        workflow.set_entry_point("controller")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "controller",
            self._route_to_agent,
            {
                "chief_complaint_agent": "chief_complaint_agent",
                "present_illness_agent": "present_illness_agent",
                "past_history_agent": "past_history_agent",
                "family_history_agent": "family_history_agent",
                "allergy_history_agent": "allergy_history_agent",
                "generate_record": "generate_record",
                "end": END
            }
        )
        
        # 智能体执行后结束当前轮次，将控制权交还给外部循环
        workflow.add_edge("chief_complaint_agent", END)
        workflow.add_edge("present_illness_agent", END)
        workflow.add_edge("past_history_agent", END)
        workflow.add_edge("family_history_agent", END)
        workflow.add_edge("allergy_history_agent", END)
        
        # 生成病历后结束
        workflow.add_edge("generate_record", END)
        
        # 编译工作流
        return workflow.compile()
    
    def _consultation_controller(self, state: ConsultationState) -> Dict[str, Any]:
        """主控节点 - 在每次调用开始时运行，决定执行哪个智能体"""
        current_step = state.current_step
        next_step = current_step

        agent_data_key = f"{current_step}_data"

        if agent_data_key in state.collected_data:
            is_complete = state.collected_data[agent_data_key].get("is_complete", False)

            if is_complete:
                current_index = self.agent_sequence.index(current_step)
                if current_index < len(self.agent_sequence) - 1:
                    next_step = self.agent_sequence[current_index + 1]
                else:
                    next_step = "generate_record"

        return {"current_step": next_step}
    
    def _route_to_agent(self, state: ConsultationState) -> str:
        """路由到相应的智能体"""
        current_step = state.current_step
        
        if current_step in self.agents:
            return f"{current_step}_agent"
        elif current_step == "generate_record":
            return "generate_record"
        else:
            return "end"
    
    def _execute_agent(self, state: ConsultationState, agent_name: str) -> Dict[str, Any]:
        """执行指定智能体"""
        agent = self.agents[agent_name]
        
        # 准备上下文
        context = {
            "patient_id": state.patient_id,
            "session_id": state.session_id,
            "collected_data": state.collected_data,
            "conversation_history": state.conversation_history,
            "current_step": state.current_step
        }
        
        # 如果有患者回答，添加到上下文
        if state.patient_response:
            context["patient_response"] = state.patient_response
        
        # 执行智能体
        response = agent.agent_workflow(context)
        
        # 创建新的状态字典
        new_state = state.dict()
        
        # 更新状态
        new_state["current_agent_response"] = response.response
        new_state["collected_data"][f"{agent_name}_data"] = {
            "extracted_data": response.extracted_data,
            "entities": response.entities,
            "confidence": response.confidence,
            "is_complete": response.is_complete
        }
        
        # 添加对话历史
        if response.response:
            new_state["conversation_history"].append({
                "role": "agent",
                "content": response.response,
                "timestamp": datetime.now().isoformat(),
                "agent": agent_name
            })
        
        if state.patient_response:
            new_state["conversation_history"].append({
                "role": "patient",
                "content": state.patient_response,
                "timestamp": datetime.now().isoformat()
            })
        
        # 清空患者回答，准备下一轮
        new_state["patient_response"] = None
        
        return new_state
    
    def _generate_medical_record(self, state: ConsultationState) -> Dict[str, Any]:
        """生成中医病历"""
        # 整合所有智能体收集的数据
        medical_record = {
            "patient_id": state.patient_id,
            "session_id": state.session_id,
            "chief_complaint": state.collected_data.get("chief_complaint_data", {}),
            "present_illness": state.collected_data.get("present_illness_data", {}),
            "past_history": state.collected_data.get("past_history_data", {}),
            "family_history": state.collected_data.get("family_history_data", {}),
            "allergy_history": state.collected_data.get("allergy_history_data", {}),
            "conversation_history": state.conversation_history,
            "created_at": datetime.now().isoformat()
        }
        
        state.medical_record = medical_record
        state.is_complete = True
        
        return state.dict()
    
    def start_consultation(self, patient_id: str, session_id: str) -> ConsultationState:
        """开始问诊会话"""
        initial_state = ConsultationState(
            patient_id=patient_id,
            session_id=session_id,
            current_step="chief_complaint"
        )
        
        return initial_state
    
    def process_patient_response(self, state: ConsultationState, patient_response: str) -> ConsultationState:
        """处理患者回答"""
        state.patient_response = patient_response
        return state
    
    def run_workflow(self, state: ConsultationState) -> ConsultationState:
        """运行工作流"""
        # 使用编译后的工作流，传入状态字典，返回 ConsultationState
        result = self.workflow.invoke(state.dict(), {"recursion_limit": 50})
        return ConsultationState.parse_obj(result)


def create_consultation_workflow() -> ConsultationWorkflow:
    """创建问诊工作流实例"""
    return ConsultationWorkflow() 