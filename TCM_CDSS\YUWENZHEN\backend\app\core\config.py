"""
配置管理模块

整合四个项目的配置管理功能，支持OpenRouter和Ollama双模式，
提供完整的环境变量配置和灵活的部署选项。
"""

import os
from pathlib import Path
from typing import Optional, List, Dict, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类 - 整合版本"""
    
    # ==================== 应用基础配置 ====================
    app_name: str = Field(default="YUWENZHEN - 中医预问诊智能体系统", description="应用名称")
    app_version: str = Field(default="2.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    environment: str = Field(default="development", description="运行环境")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务监听地址")
    port: int = Field(default=8000, description="服务监听端口")
    
    # ==================== 数据库配置 ====================
    # 支持PostgreSQL和SQLite
    database_url: str = Field(
        default="sqlite:///./data/yuwenzhen.db", 
        description="数据库连接URL"
    )
    database_echo: bool = Field(default=False, description="数据库SQL日志")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379", description="Redis连接URL")
    redis_db: int = Field(default=0, description="Redis数据库编号")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    
    # ==================== LLM配置 ====================
    # LLM服务选择：openrouter 或 ollama
    llm_service: str = Field(default="openrouter", description="LLM服务类型")
    
    # OpenRouter配置（主要）
    openrouter_api_key: str = Field(default="", description="OpenRouter API密钥")
    openrouter_base_url: str = Field(
        default="https://openrouter.ai/api/v1", 
        description="OpenRouter API地址"
    )
    default_model: str = Field(
        default="anthropic/claude-3.5-sonnet", 
        description="默认模型"
    )
    
    # Ollama配置（备选）
    ollama_base_url: str = Field(
        default="http://localhost:11434", 
        description="Ollama服务地址"
    )
    ollama_model: str = Field(
        default="qwen2.5:32b", 
        description="Ollama模型名称"
    )
    
    # LLM通用配置
    llm_temperature: float = Field(default=0.7, description="LLM温度参数")
    llm_max_tokens: int = Field(default=2048, description="LLM最大令牌数")
    llm_timeout: int = Field(default=30, description="LLM请求超时时间")
    
    # ==================== 安全配置 ====================
    secret_key: str = Field(
        default="yuwenzhen-secret-key-change-in-production", 
        description="JWT密钥"
    )
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间")
    password_min_length: int = Field(default=8, description="密码最小长度")
    
    # ==================== CORS配置 ====================
    cors_origins: List[str] = Field(
        default=["*"], 
        description="CORS允许的源"
    )
    cors_credentials: bool = Field(default=True, description="CORS允许凭证")
    cors_methods: List[str] = Field(
        default=["*"], 
        description="CORS允许的方法"
    )
    cors_headers: List[str] = Field(
        default=["*"], 
        description="CORS允许的头部"
    )
    
    # ==================== 缓存配置 ====================
    cache_ttl: int = Field(default=3600, description="缓存TTL（秒）")
    session_ttl: int = Field(default=7200, description="会话TTL（秒）")
    
    # ==================== 限流配置 ====================
    rate_limit_per_minute: int = Field(default=60, description="每分钟请求限制")
    rate_limit_per_hour: int = Field(default=1000, description="每小时请求限制")
    
    # ==================== 文件配置 ====================
    max_file_size: int = Field(default=10485760, description="最大文件大小（字节）")
    upload_dir: str = Field(default="./data/uploads", description="上传目录")
    
    # ==================== 日志配置 ====================
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")
    
    # ==================== 前端配置 ====================
    frontend_url: str = Field(default="http://localhost:3000", description="前端URL")
    static_files_dir: str = Field(default="./frontend/dist", description="静态文件目录")
    
    # ==================== 监控配置 ====================
    enable_metrics: bool = Field(default=True, description="启用指标监控")
    metrics_port: int = Field(default=9090, description="指标端口")
    
    # ==================== 邮件配置（可选） ====================
    smtp_host: Optional[str] = Field(default=None, description="SMTP服务器地址")
    smtp_port: Optional[int] = Field(default=None, description="SMTP服务器端口")
    smtp_user: Optional[str] = Field(default=None, description="SMTP用户名")
    smtp_password: Optional[str] = Field(default=None, description="SMTP密码")
    smtp_tls: bool = Field(default=True, description="SMTP使用TLS")
    
    # ==================== 第三方服务配置（可选） ====================
    speech_to_text_api_key: Optional[str] = Field(default=None, description="语音识别API密钥")
    image_recognition_api_key: Optional[str] = Field(default=None, description="图像识别API密钥")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    # ==================== 验证器 ====================
    @validator('llm_service')
    def validate_llm_service(cls, v):
        if v not in ['openrouter', 'ollama']:
            raise ValueError('llm_service must be either "openrouter" or "ollama"')
        return v
    
    @validator('environment')
    def validate_environment(cls, v):
        if v not in ['development', 'testing', 'production']:
            raise ValueError('environment must be one of: development, testing, production')
        return v
    
    @validator('cors_origins', pre=True)
    def validate_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    # ==================== 属性方法 ====================
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == "production"
    
    @property
    def is_ollama_enabled(self) -> bool:
        """是否启用Ollama"""
        return self.llm_service == "ollama"
    
    @property
    def is_openrouter_enabled(self) -> bool:
        """是否启用OpenRouter"""
        return self.llm_service == "openrouter"
    
    @property
    def project_root(self) -> Path:
        """项目根目录"""
        return Path(__file__).parent.parent.parent.parent
    
    @property
    def data_dir(self) -> Path:
        """数据目录"""
        data_path = self.project_root / "data"
        data_path.mkdir(exist_ok=True)
        return data_path
    
    @property
    def logs_dir(self) -> Path:
        """日志目录"""
        logs_path = self.project_root / "logs"
        logs_path.mkdir(exist_ok=True)
        return logs_path


# 全局配置实例
settings = Settings()


# ==================== 便捷函数 ====================
def get_settings() -> Settings:
    """获取配置实例"""
    return settings


def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.database_url


def get_redis_url() -> str:
    """获取Redis连接URL"""
    return settings.redis_url


def get_llm_config() -> Dict[str, Any]:
    """获取LLM配置"""
    if settings.is_openrouter_enabled:
        return {
            "service": "openrouter",
            "api_key": settings.openrouter_api_key,
            "base_url": settings.openrouter_base_url,
            "model": settings.default_model,
            "temperature": settings.llm_temperature,
            "max_tokens": settings.llm_max_tokens,
            "timeout": settings.llm_timeout,
        }
    else:
        return {
            "service": "ollama",
            "base_url": settings.ollama_base_url,
            "model": settings.ollama_model,
            "temperature": settings.llm_temperature,
            "max_tokens": settings.llm_max_tokens,
            "timeout": settings.llm_timeout,
        }


def get_cors_config() -> Dict[str, Any]:
    """获取CORS配置"""
    return {
        "allow_origins": settings.cors_origins,
        "allow_credentials": settings.cors_credentials,
        "allow_methods": settings.cors_methods,
        "allow_headers": settings.cors_headers,
    }


def is_debug_mode() -> bool:
    """检查是否为调试模式"""
    return settings.debug


def get_log_config() -> Dict[str, Any]:
    """获取日志配置"""
    return {
        "level": settings.log_level,
        "format": settings.log_format,
        "file": settings.log_file,
    }
