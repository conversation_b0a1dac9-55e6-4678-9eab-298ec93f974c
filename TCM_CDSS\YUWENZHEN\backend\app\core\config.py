"""
配置管理模块

简化的配置管理，专注于开发测试环境，使用SQLite数据库。
"""

import os
from pathlib import Path
from typing import Optional, List, Dict, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类 - 简化版本"""

    # ==================== 应用基础配置 ====================
    app_name: str = Field(default="YUWENZHEN - 中医预问诊智能体系统", description="应用名称")
    app_version: str = Field(default="2.0.0", description="应用版本")
    debug: bool = Field(default=True, description="调试模式")
    environment: str = Field(default="development", description="运行环境")

    # 服务器配置
    host: str = Field(default="127.0.0.1", description="服务监听地址")
    port: int = Field(default=8000, description="服务监听端口")

    # ==================== 数据库配置 ====================
    # 统一使用SQLite
    database_url: str = Field(
        default="sqlite:///./data/yuwenzhen.db",
        description="SQLite数据库文件路径"
    )
    database_echo: bool = Field(default=False, description="数据库SQL日志")

    # 简化缓存配置 - 使用本地缓存
    enable_redis: bool = Field(default=False, description="是否启用Redis")
    redis_url: str = Field(default="redis://localhost:6379", description="Redis连接URL")
    
    # ==================== LLM配置 ====================
    # 简化LLM配置，优先使用Ollama本地部署
    llm_service: str = Field(default="ollama", description="LLM服务类型")

    # Ollama配置（主要）
    ollama_base_url: str = Field(
        default="http://localhost:11434",
        description="Ollama服务地址"
    )
    ollama_model: str = Field(
        default="qwen2.5:7b",
        description="Ollama模型名称"
    )

    # OpenRouter配置（备选）
    openrouter_api_key: str = Field(default="", description="OpenRouter API密钥")
    openrouter_base_url: str = Field(
        default="https://openrouter.ai/api/v1",
        description="OpenRouter API地址"
    )
    default_model: str = Field(
        default="anthropic/claude-3.5-sonnet",
        description="默认模型"
    )

    # LLM通用配置
    llm_temperature: float = Field(default=0.7, description="LLM温度参数")
    llm_max_tokens: int = Field(default=1024, description="LLM最大令牌数")
    llm_timeout: int = Field(default=30, description="LLM请求超时时间")
    
    # ==================== 安全配置 ====================
    secret_key: str = Field(
        default="yuwenzhen-dev-secret-key",
        description="JWT密钥"
    )
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=60, description="访问令牌过期时间")

    # ==================== CORS配置 ====================
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="CORS允许的源"
    )
    cors_credentials: bool = Field(default=True, description="CORS允许凭证")

    # ==================== 缓存配置 ====================
    cache_ttl: int = Field(default=1800, description="缓存TTL（秒）")
    session_ttl: int = Field(default=3600, description="会话TTL（秒）")

    # ==================== 文件配置 ====================
    max_file_size: int = Field(default=5242880, description="最大文件大小（5MB）")
    upload_dir: str = Field(default="./data/uploads", description="上传目录")

    # ==================== 日志配置 ====================
    log_level: str = Field(default="DEBUG", description="日志级别")
    log_format: str = Field(default="simple", description="日志格式")

    # ==================== 前端配置 ====================
    frontend_url: str = Field(default="http://localhost:3000", description="前端URL")
    static_files_dir: str = Field(default="./frontend/dist", description="静态文件目录")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    # ==================== 验证器 ====================
    @validator('llm_service')
    def validate_llm_service(cls, v):
        if v not in ['openrouter', 'ollama']:
            raise ValueError('llm_service must be either "openrouter" or "ollama"')
        return v

    @validator('cors_origins', pre=True)
    def validate_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v

    # ==================== 属性方法 ====================
    @property
    def is_ollama_enabled(self) -> bool:
        """是否启用Ollama"""
        return self.llm_service == "ollama"

    @property
    def project_root(self) -> Path:
        """项目根目录"""
        return Path(__file__).parent.parent.parent.parent

    @property
    def data_dir(self) -> Path:
        """数据目录"""
        data_path = self.project_root / "data"
        data_path.mkdir(exist_ok=True)
        return data_path


# 全局配置实例
settings = Settings()


# ==================== 便捷函数 ====================
def get_settings() -> Settings:
    """获取配置实例"""
    return settings


def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.database_url


def get_llm_config() -> Dict[str, Any]:
    """获取LLM配置"""
    if settings.is_ollama_enabled:
        return {
            "service": "ollama",
            "base_url": settings.ollama_base_url,
            "model": settings.ollama_model,
            "temperature": settings.llm_temperature,
            "max_tokens": settings.llm_max_tokens,
            "timeout": settings.llm_timeout,
        }
    else:
        return {
            "service": "openrouter",
            "api_key": settings.openrouter_api_key,
            "base_url": settings.openrouter_base_url,
            "model": settings.default_model,
            "temperature": settings.llm_temperature,
            "max_tokens": settings.llm_max_tokens,
            "timeout": settings.llm_timeout,
        }


def get_cors_config() -> Dict[str, Any]:
    """获取CORS配置"""
    return {
        "allow_origins": settings.cors_origins,
        "allow_credentials": settings.cors_credentials,
        "allow_methods": ["GET", "POST", "PUT", "DELETE"],
        "allow_headers": ["*"],
    }
