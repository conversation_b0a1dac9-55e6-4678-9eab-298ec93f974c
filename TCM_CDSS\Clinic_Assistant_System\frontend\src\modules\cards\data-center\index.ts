// 数据中心卡片模块
import type { CardModule } from '../index'
import { ref, computed } from 'vue'

// 数据中心统计数据
export const dataCenterStats = ref({
  // 今日数据统计
  todayStats: {
    totalPatients: 156,        // 今日就诊患者数
    totalVisits: 189,          // 今日就诊次数
    totalPrescriptions: 142,   // 今日处方数
    totalRevenue: 28650,       // 今日收入
    aiDiagnosis: 98,          // AI辅助诊断次数
    avgDiagnosisTime: 12.5     // 平均诊疗时间(分钟)
  },
  
  // 本周数据统计
  weekStats: {
    totalPatients: 892,        // 本周就诊患者数
    totalVisits: 1156,         // 本周就诊次数
    totalPrescriptions: 987,   // 本周处方数
    totalRevenue: 186420,      // 本周收入
    aiDiagnosis: 654,         // AI辅助诊断次数
    avgDiagnosisTime: 13.2     // 平均诊疗时间(分钟)
  },
  
  // 本月数据统计
  monthStats: {
    totalPatients: 3456,       // 本月就诊患者数
    totalVisits: 4523,         // 本月就诊次数
    totalPrescriptions: 3892,  // 本月处方数
    totalRevenue: 756890,      // 本月收入
    aiDiagnosis: 2567,        // AI辅助诊断次数
    avgDiagnosisTime: 12.8     // 平均诊疗时间(分钟)
  },
  
  // 各模块数据统计
  moduleStats: {
    patients: {
      newPatients: 45,         // 新增患者
      activePatients: 234,     // 活跃患者
      totalRecords: 1567       // 总病历数
    },
    appointments: {
      totalAppointments: 189,  // 总预约数
      completedAppointments: 156, // 已完成预约
      cancelledAppointments: 12,  // 取消预约
      onlineBookings: 98       // 线上预约数
    },
    prescriptions: {
      herbalPrescriptions: 89,    // 中药处方
      patentPrescriptions: 45,    // 中成药处方
      injectionPrescriptions: 8,  // 输注处方
      avgMedicineCount: 8.5       // 平均用药数量
    },
    billing: {
      totalBills: 156,         // 总账单数
      paidBills: 142,          // 已付费账单
      unpaidBills: 14,         // 未付费账单
      avgBillAmount: 183.7     // 平均账单金额
    },
    inventory: {
      totalMedicines: 456,     // 总药品数
      lowStockMedicines: 23,   // 库存不足药品
      expiringSoon: 8,         // 即将过期药品
      stockValue: 125600       // 库存价值
    }
  },
  
  // AI功能使用统计
  aiStats: {
    syndromeIdentification: 98,  // 证型辨识次数
    formulaRecommendation: 87,   // 方剂推荐次数
    safetyCheck: 142,           // 配伍检查次数
    qaInteraction: 34,          // 智能问答次数
    avgAccuracy: 0.876          // 平均准确率
  }
})

// 计算今日数据摘要
export const todaySummary = computed(() => {
  const stats = dataCenterStats.value.todayStats
  return `今日${stats.totalPatients}人次就诊，收入¥${stats.totalRevenue.toLocaleString()}`
})

// 数据中心卡片配置
export const dataCenterCard: CardModule = {
  key: 'data-center',
  name: '数据中心',
  route: '/data-center',
  icon: 'DataAnalysis',
  color: '#722ED1',
  info: () => todaySummary.value,
  hasDetailStats: true,
  statsData: dataCenterStats,
  permission: 'data_center'
}
