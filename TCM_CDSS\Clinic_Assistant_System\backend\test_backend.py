#!/usr/bin/env python3
"""
测试后端功能
Test Backend Functionality
"""

import asyncio
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

async def test_database_connection():
    """测试数据库连接"""
    try:
        from database.connection import test_connection, get_database_stats
        
        print("🔌 Testing database connection...")
        connected = await test_connection()
        
        if connected:
            print("✅ Database connection successful")
            
            print("\n📊 Getting database statistics...")
            stats = await get_database_stats()
            
            print("Database Statistics:")
            for table, count in stats.items():
                if isinstance(count, int):
                    print(f"  📋 {table}: {count} records")
                else:
                    print(f"  📋 {table}: {count}")
        else:
            print("❌ Database connection failed")
            return False
            
        return True
    except Exception as error:
        print(f"❌ Database test error: {error}")
        return False

async def test_mapping_service():
    """测试映射服务"""
    try:
        from services.mapping_service import MappingService
        
        print("\n🗂️ Testing mapping service...")
        
        # 测试获取所有性别映射
        genders = await MappingService.get_all_genders()
        print(f"  📋 Genders: {len(genders)} items")
        
        # 测试获取所有角色映射
        roles = await MappingService.get_all_roles()
        print(f"  📋 Roles: {len(roles)} items")
        
        # 测试获取所有科室映射
        departments = await MappingService.get_all_departments()
        print(f"  📋 Departments: {len(departments)} items")
        
        # 测试创建映射缓存
        cache = await MappingService.create_mapping_cache()
        print(f"  📋 Cache created with {len(cache)} categories")
        
        print("✅ Mapping service test successful")
        return True
    except Exception as error:
        print(f"❌ Mapping service test error: {error}")
        return False

def test_encryption_utils():
    """测试加密工具"""
    try:
        from utils.encryption import EncryptionUtils
        
        print("\n🔐 Testing encryption utils...")
        
        # 测试文本加密解密
        test_text = "测试加密文本"
        encrypted = EncryptionUtils.encrypt(test_text)
        decrypted = EncryptionUtils.decrypt(encrypted)
        
        if decrypted == test_text:
            print("  ✅ Text encryption/decryption successful")
        else:
            print("  ❌ Text encryption/decryption failed")
            return False
        
        # 测试密码哈希
        password = "test_password"
        hashed = EncryptionUtils.hash_password(password)
        verified = EncryptionUtils.verify_password(password, hashed)
        
        if verified:
            print("  ✅ Password hashing/verification successful")
        else:
            print("  ❌ Password hashing/verification failed")
            return False
        
        # 测试手机号哈希
        phone = "13800138000"
        phone_hash = EncryptionUtils.hash_phone(phone)
        if phone_hash:
            print("  ✅ Phone hashing successful")
        else:
            print("  ❌ Phone hashing failed")
            return False
        
        # 测试数据脱敏
        masked_phone = EncryptionUtils.mask_sensitive_data(phone, "phone")
        if masked_phone == "138****8000":
            print("  ✅ Data masking successful")
        else:
            print("  ❌ Data masking failed")
            return False
        
        print("✅ Encryption utils test successful")
        return True
    except Exception as error:
        print(f"❌ Encryption utils test error: {error}")
        return False

async def main():
    """主测试函数"""
    print("🚀 Starting backend functionality tests...\n")
    
    tests = [
        ("Database Connection", test_database_connection()),
        ("Mapping Service", test_mapping_service()),
        ("Encryption Utils", test_encryption_utils())
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Testing: {test_name}")
        print('='*50)
        
        if asyncio.iscoroutine(test_func):
            result = await test_func
        else:
            result = test_func
        
        if result:
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All tests passed! Backend is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
