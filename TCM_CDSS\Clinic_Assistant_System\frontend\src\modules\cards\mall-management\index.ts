// 商城管理卡片模块
import type { CardModule } from '../index'
import { ref } from 'vue'

// 商城管理统计数据
export const mallManagementStats = ref({
  totalProducts: 156,
  todayOrders: 23,
  monthlySales: 89650,
  activeUsers: 1234,
  categories: {
    herbs: 89,
    medicine: 45,
    equipment: 22
  },
  orderStatus: {
    pending: 8,
    processing: 12,
    shipped: 15,
    delivered: 18
  },
  recentOrders: [
    { id: '#20250121001', amount: 268, status: 'delivered', time: '1小时前' },
    { id: '#20250121002', amount: 156, status: 'shipped', time: '2小时前' },
    { id: '#20250121003', amount: 89, status: 'processing', time: '3小时前' }
  ]
})

// 商城管理卡片配置
export const mallManagementCard: CardModule = {
  key: 'mall-management',
  name: '商城管理',
  route: '/mall-management',
  icon: 'ShoppingCart',
  color: '#FF6B35',
  info: '管理自有商城',
  hasDetailStats: true,
  statsData: mallManagementStats,
  permission: 'mall_management'
}

// 商城管理详细统计数据获取函数
export const getMallManagementDetailStats = () => {
  return {
    totalProducts: mallManagementStats.value.totalProducts,
    todayOrders: mallManagementStats.value.todayOrders,
    monthlySales: mallManagementStats.value.monthlySales,
    activeUsers: mallManagementStats.value.activeUsers,
    categories: mallManagementStats.value.categories,
    orderStatus: mallManagementStats.value.orderStatus,
    recentOrders: mallManagementStats.value.recentOrders
  }
}

// 更新商城管理统计数据
export const updateMallManagementStats = (newStats: any) => {
  mallManagementStats.value = { ...mallManagementStats.value, ...newStats }
}

// 模拟新增订单
export const addNewOrder = (orderData: any) => {
  mallManagementStats.value.todayOrders += 1
  mallManagementStats.value.monthlySales += orderData.amount
  mallManagementStats.value.orderStatus.pending += 1
  mallManagementStats.value.recentOrders.unshift({
    id: orderData.id,
    amount: orderData.amount,
    status: 'pending',
    time: '刚刚'
  })
  // 保持最近订单记录不超过10条
  if (mallManagementStats.value.recentOrders.length > 10) {
    mallManagementStats.value.recentOrders.pop()
  }
}

// 模拟处理订单
export const processOrder = (orderId: string, newStatus: string) => {
  const order = mallManagementStats.value.recentOrders.find(o => o.id === orderId)
  if (order) {
    const oldStatus = order.status
    order.status = newStatus
    
    // 更新订单状态统计
    if (mallManagementStats.value.orderStatus[oldStatus] > 0) {
      mallManagementStats.value.orderStatus[oldStatus] -= 1
    }
    mallManagementStats.value.orderStatus[newStatus] += 1
  }
}
