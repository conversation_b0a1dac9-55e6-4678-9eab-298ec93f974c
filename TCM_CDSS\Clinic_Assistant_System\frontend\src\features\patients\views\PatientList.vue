<template>
  <div class="patient-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">患者管理</h1>
      <div class="add-button" @click="addPatient">
        <el-icon><Plus /></el-icon>
      </div>
    </header>
    <div class="search-section">
      <div class="search-left">
        <div class="search-container">
          <el-icon class="search-icon"><Search /></el-icon>
          <input
            v-model="searchQuery"
            type="text"
            class="search-input"
            placeholder="搜索患者姓名或手机号"
            @input="handleSearch"
          />
        </div>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
      <div class="filter-right">
        <!-- 二级筛选下拉菜单 -->
        <el-cascader
          v-model="selectedFilter"
          :options="filterOptions"
          :props="cascaderProps"
          placeholder="筛选条件"
          clearable
          style="width: 200px;"
          @change="handleFilterChange"
        />

        <!-- 预约时间筛选 -->
        <el-select v-model="appointmentTimeFilter" placeholder="预约筛选" clearable style="width: 120px;">
          <el-option label="全部" value="" />
          <el-option label="今日预约" value="today" />
          <el-option label="明日预约" value="tomorrow" />
          <el-option label="本周预约" value="thisWeek" />
        </el-select>
        
        <el-dropdown @command="handleSort" trigger="click">
          <el-button type="primary" plain>
            <el-icon><Sort /></el-icon>
            排序
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="name-asc">姓名 A-Z</el-dropdown-item>
              <el-dropdown-item command="name-desc">姓名 Z-A</el-dropdown-item>
              <el-dropdown-item command="age-asc">年龄 小-大</el-dropdown-item>
              <el-dropdown-item command="age-desc">年龄 大-小</el-dropdown-item>
              <el-dropdown-item command="visits-asc">就诊次数 少-多</el-dropdown-item>
              <el-dropdown-item command="visits-desc">就诊次数 多-少</el-dropdown-item>
              <el-dropdown-item command="lastVisit-asc">最后就诊 早-晚</el-dropdown-item>
              <el-dropdown-item command="lastVisit-desc">最后就诊 晚-早</el-dropdown-item>
              <el-dropdown-item command="nextAppointment-asc">下次预约 早-晚</el-dropdown-item>
              <el-dropdown-item command="nextAppointment-desc">下次预约 晚-早</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="patient-grid">
      <div
        v-for="patient in filteredPatients"
        :key="patient.id"
        class="patient-card"
        @click="viewPatient(patient)"
      >
        <div class="card-header">
          <div class="patient-avatar">
            {{ patient.name.charAt(0) }}
          </div>
          <div class="patient-basic-info">
            <div class="patient-name">{{ patient.name }}</div>
            <div class="patient-details">
              <span class="patient-gender">{{ patient.gender }}</span>
              <span class="patient-age">{{ patient.age }}岁</span>
            </div>
          </div>
          <div class="patient-status" :class="patient.status">
            {{ getStatusText(patient.status) }}
          </div>
        </div>
        
        <div class="card-content">
          <div class="info-row">
            <el-icon class="info-icon"><Phone /></el-icon>
            <span class="info-text">{{ patient.phone }}</span>
          </div>
          
          <div class="info-row" v-if="patient.diagnosis">
            <el-icon class="info-icon"><Document /></el-icon>
            <span class="info-text diagnosis">{{ patient.diagnosis }}</span>
          </div>
          
          <div class="info-row" v-if="patient.lastVisit">
            <el-icon class="info-icon"><Calendar /></el-icon>
            <span class="info-text">最后就诊：{{ formatDate(patient.lastVisit) }}</span>
          </div>
          
          <div class="info-row">
            <el-icon class="info-icon"><Clock /></el-icon>
            <span class="info-text next-appointment">
              下次预约：{{ patient.nextAppointment ? formatDate(patient.nextAppointment) : '—' }}
            </span>
          </div>
        </div>
        
        <div class="card-footer">
          <div class="visit-count">
            就诊次数：{{ patient.visitCount }}次
          </div>
          <div class="card-actions">
            <el-button size="small" type="primary" @click.stop="viewPatient(patient)">
              查看详情
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="filteredPatients.length === 0" class="empty-state">
      <el-icon size="64" color="#C7C7CC"><User /></el-icon>
      <p>暂无患者数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, Plus, Search, Phone, Document, Calendar, Clock, User, Sort, ArrowDown } from '@element-plus/icons-vue'
import { patientService, type Patient } from '@/mock/database/patients'

const router = useRouter()

// 搜索相关
const searchQuery = ref('')
const sortBy = ref('')
const sortOrder = ref('asc')

// 新的筛选相关
const selectedFilter = ref<string>('')
const appointmentTimeFilter = ref('')

// 二级筛选配置
const cascaderProps = {
  expandTrigger: 'hover' as const,
  emitPath: false,
  checkStrictly: true
}

// 筛选选项配置
const filterOptions = [
  {
    value: 'patient-attr',
    label: '患者属性',
    children: [
      { value: 'gender-male', label: '男性患者' },
      { value: 'gender-female', label: '女性患者' },
      { value: 'age-young', label: '青年(25-35岁)' },
      { value: 'age-middle', label: '中年(36-45岁)' },
      { value: 'age-senior', label: '中老年(46岁以上)' }
    ]
  },
  {
    value: 'status',
    label: '就诊状态',
    children: [
      { value: 'status-active', label: '活跃患者' },
      { value: 'status-inactive', label: '非活跃患者' },
      { value: 'appointment-has', label: '有预约' },
      { value: 'appointment-none', label: '无预约' }
    ]
  },
  {
    value: 'diagnosis',
    label: '中医证型',
    children: [
      { value: 'diagnosis-肝郁脾虚证', label: '肝郁脾虚证' },
      { value: 'diagnosis-气血两虚证', label: '气血两虚证' },
      { value: 'diagnosis-肾阳虚证', label: '肾阳虚证' },
      { value: 'diagnosis-痰湿阻肺证', label: '痰湿阻肺证' },
      { value: 'diagnosis-心脾两虚证', label: '心脾两虚证' },
      { value: 'diagnosis-肝阳上亢证', label: '肝阳上亢证' },
      { value: 'diagnosis-脾胃虚弱证', label: '脾胃虚弱证' },
      { value: 'diagnosis-阴虚火旺证', label: '阴虚火旺证' }
    ]
  }
]

// 患者数据
const patients = ref<Patient[]>([])
const loading = ref(false)

// 获取患者数据
const fetchPatients = async () => {
  loading.value = true
  try {
    const data = await patientService.getAllPatients()
    patients.value = data
  } catch (error) {
    console.error('获取患者数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 过滤和排序后的患者列表
const filteredPatients = computed(() => {
  let filtered = patients.value

  // 搜索过滤
  if (searchQuery.value) {
    filtered = filtered.filter(patient =>
      patient.name.includes(searchQuery.value) ||
      patient.phone.includes(searchQuery.value) ||
      patient.diagnosis?.includes(searchQuery.value)
    )
  }

  // 二级筛选过滤
  if (selectedFilter.value) {
    const filterValue = selectedFilter.value

    // 性别筛选
    if (filterValue === 'gender-male') {
      filtered = filtered.filter(patient => patient.gender === '男')
    } else if (filterValue === 'gender-female') {
      filtered = filtered.filter(patient => patient.gender === '女')
    }

    // 年龄段筛选
    else if (filterValue === 'age-young') {
      filtered = filtered.filter(patient => patient.age >= 25 && patient.age <= 35)
    } else if (filterValue === 'age-middle') {
      filtered = filtered.filter(patient => patient.age >= 36 && patient.age <= 45)
    } else if (filterValue === 'age-senior') {
      filtered = filtered.filter(patient => patient.age >= 46)
    }

    // 状态筛选
    else if (filterValue === 'status-active') {
      filtered = filtered.filter(patient => patient.status === 'active')
    } else if (filterValue === 'status-inactive') {
      filtered = filtered.filter(patient => patient.status === 'inactive')
    }

    // 预约筛选
    else if (filterValue === 'appointment-has') {
      filtered = filtered.filter(patient => !!patient.nextAppointment)
    } else if (filterValue === 'appointment-none') {
      filtered = filtered.filter(patient => !patient.nextAppointment)
    }

    // 证型筛选
    else if (filterValue.startsWith('diagnosis-')) {
      const diagnosisType = filterValue.replace('diagnosis-', '')
      filtered = filtered.filter(patient => patient.diagnosis === diagnosisType)
    }
  }

  // 预约时间筛选
  if (appointmentTimeFilter.value) {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)

    if (appointmentTimeFilter.value === 'today') {
      const todayStr = today.toISOString().split('T')[0]
      filtered = filtered.filter(patient =>
        patient.nextAppointment && patient.nextAppointment.startsWith(todayStr)
      )
    } else if (appointmentTimeFilter.value === 'tomorrow') {
      const tomorrowStr = tomorrow.toISOString().split('T')[0]
      filtered = filtered.filter(patient =>
        patient.nextAppointment && patient.nextAppointment.startsWith(tomorrowStr)
      )
    } else if (appointmentTimeFilter.value === 'thisWeek') {
      const weekStart = new Date(today)
      weekStart.setDate(today.getDate() - today.getDay())
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekStart.getDate() + 6)

      filtered = filtered.filter(patient => {
        if (!patient.nextAppointment) return false
        const appointmentDate = new Date(patient.nextAppointment)
        return appointmentDate >= weekStart && appointmentDate <= weekEnd
      })
    }
  }


  
  // 排序
  if (sortBy.value) {
    filtered = [...filtered].sort((a, b) => {
      let aValue, bValue
      
      switch (sortBy.value) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'age':
          aValue = a.age
          bValue = b.age
          break
        case 'visits':
          aValue = a.visitCount
          bValue = b.visitCount
          break
        case 'lastVisit':
          aValue = a.lastVisit ? new Date(a.lastVisit).getTime() : 0
          bValue = b.lastVisit ? new Date(b.lastVisit).getTime() : 0
          break
        case 'nextAppointment':
          aValue = a.nextAppointment ? new Date(a.nextAppointment).getTime() : 0
          bValue = b.nextAppointment ? new Date(b.nextAppointment).getTime() : 0
          break
        default:
          return 0
      }
      
      if (sortOrder.value === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }
  
  return filtered
})

// 获取状态文本
const getStatusText = (status: string) => {
  return status === 'active' ? '活跃' : '非活跃'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

// 返回功能中心
const goBack = () => router.push('/dashboard')

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

// 新增患者
const addPatient = () => {
  // 跳转到新增患者页面
  router.push('/patients/add')
}

// 查看患者详情
const viewPatient = (patient: Patient) => {
  router.push(`/patients/${patient.id}`)
}

// 排序处理
const handleSort = (command: string) => {
  const [field, order] = command.split('-')
  sortBy.value = field
  sortOrder.value = order
}

// 筛选变化处理
const handleFilterChange = (value: string) => {
  selectedFilter.value = value
}

// 页面加载时获取数据
onMounted(() => {
  fetchPatients()
  // 默认显示今日预约
  appointmentTimeFilter.value = 'today'
})
</script>

<style scoped>
.patient-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.back-button:hover {
  color: var(--primary-dark);
}

.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.add-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: white;
}

.add-button:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}

.search-section {
  padding: var(--spacing-sm) var(--spacing-xl);
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.search-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.search-container {
  position: relative;
  width: 250px;
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}

.search-input {
  width: 100%;
  height: 36px;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 44px;
  border: 1px solid var(--border-color);
  border-radius: 18px;
  background: var(--background-color);
  font-size: var(--font-size-md);
  outline: none;
  transition: all var(--transition-fast);
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.filter-right {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: nowrap;
  justify-content: flex-end;
}

.filter-right .el-select {
  width: 120px;
}

.filter-right .el-button {
  white-space: nowrap;
  width: 120px;
}

.filter-right .el-cascader {
  min-width: 200px;
}

.patient-grid {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg) var(--spacing-xl);
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(3, auto);
  gap: var(--spacing-md);
  grid-auto-flow: row;
  grid-auto-rows: auto;
}

.patient-card {
  background: var(--surface-color);
  border-radius: 8px;
  padding: var(--spacing-sm);
  box-shadow: var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.patient-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.patient-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: 600;
  flex-shrink: 0;
}

.patient-basic-info {
  flex: 1;
  min-width: 0;
}

.patient-name {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.patient-details {
  display: flex;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.patient-gender {
  background: var(--primary-light);
  color: var(--primary-color);
  padding: 1px 4px;
  border-radius: 8px;
  font-size: var(--font-size-xs);
}

.patient-age {
  color: var(--text-secondary);
}

.patient-status {
  padding: 1px 6px;
  border-radius: 10px;
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.patient-status.active {
  background: #e8f5e8;
  color: #52c41a;
}

.patient-status.inactive {
  background: #fff2e8;
  color: #fa8c16;
}

.card-content {
  margin-bottom: var(--spacing-xs);
}

.info-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: 2px;
  font-size: var(--font-size-xs);
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-icon {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  flex-shrink: 0;
}

.info-text {
  color: var(--text-primary);
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.diagnosis {
  color: var(--primary-color);
  font-weight: 500;
}

.next-appointment {
  color: #fa8c16;
  font-weight: 500;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--spacing-xs);
  border-top: 1px solid var(--border-color);
}

.visit-count {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.card-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  gap: var(--spacing-md);
}

.empty-state p {
  font-size: var(--font-size-md);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .patient-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .search-container {
    width: 200px;
  }
  
  .filter-right .el-select,
  .filter-right .el-button {
    width: 100px;
  }
}

@media (max-width: 900px) {
  .patient-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .search-section {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .search-left {
    width: 100%;
  }
  
  .filter-right {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .patient-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: var(--spacing-md);
  }
  
  .search-container {
    width: 200px;
  }
  
  .filter-right .el-select,
  .filter-right .el-button {
    width: 90px;
  }
}

@media (max-width: 480px) {
  .patient-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-right {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }
  
  .filter-right .el-select,
  .filter-right .el-button {
    width: calc(50% - var(--spacing-xs));
  }
}
</style> 