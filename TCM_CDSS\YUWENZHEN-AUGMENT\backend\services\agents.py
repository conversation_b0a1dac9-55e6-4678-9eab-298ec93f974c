"""
智能体服务 - 多智能体协同预问诊流程
"""

import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
from abc import ABC, abstractmethod
from services.llm_service import llm_service

logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """基础智能体类"""
    
    def __init__(self, name: str):
        self.name = name
        self.required_elements = []  # 必需信息元素
        self.optional_elements = []  # 可选信息元素
        self.collected_info = {}     # 已收集信息
        self.conversation_history = []  # 对话历史
        self.is_completed = False    # 工作是否完成
        
    @abstractmethod
    def get_system_prompt(self) -> str:
        """获取智能体的系统提示词"""
        pass
    
    @abstractmethod
    def get_start_message(self) -> str:
        """获取智能体开始工作的消息"""
        pass
    
    @abstractmethod
    def extract_information(self, user_message: str) -> Dict[str, Any]:
        """从用户消息中提取信息"""
        pass
    
    @abstractmethod
    def generate_report(self) -> str:
        """生成工作结果报告"""
        pass
    
    def check_completeness(self) -> Tuple[bool, List[str]]:
        """检查信息完整性"""
        missing_required = []
        for element in self.required_elements:
            if element not in self.collected_info or not self.collected_info[element]:
                missing_required.append(element)
        return len(missing_required) == 0, missing_required
    
    def generate_follow_up_question(self, missing_elements: List[str]) -> str:
        """基于缺失信息生成追问"""
        if not missing_elements:
            return ""
        
        # 构建追问提示词
        follow_up_prompt = f"""基于以下信息，生成一个简洁的追问：

已收集信息: {self.collected_info}
缺失的必需信息: {missing_elements}
对话历史: {self.conversation_history[-3:] if self.conversation_history else []}

请生成一个针对缺失信息的简洁问题（不超过30字）。"""
        
        try:
            question = llm_service.call_llm("请生成追问", follow_up_prompt)
            return question
        except Exception as e:
            logger.error(f"生成追问失败: {e}")
            return f"请补充{missing_elements[0]}的相关信息。"
    
    def process_message(self, user_message: str) -> Tuple[str, bool]:
        """处理用户消息"""
        try:
            # 记录对话历史
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })
            
            # 提取信息
            extracted_info = self.extract_information(user_message)
            self.collected_info.update(extracted_info)
            
            # 检查完整性
            is_complete, missing_elements = self.check_completeness()
            
            if is_complete:
                # 工作完成，生成报告
                self.is_completed = True
                report = self.generate_report()
                response = f"✅ {self.name}工作完成！\n\n{report}"
                
                self.conversation_history.append({
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat()
                })
                
                return response, True
            else:
                # 需要继续采集信息
                follow_up = self.generate_follow_up_question(missing_elements)
                
                self.conversation_history.append({
                    "role": "assistant", 
                    "content": follow_up,
                    "timestamp": datetime.now().isoformat()
                })
                
                return follow_up, False
                
        except Exception as e:
            logger.error(f"{self.name}处理消息失败: {e}")
            return "抱歉，处理您的消息时出现错误，请重新描述。", False
    
    def start_work(self) -> str:
        """开始工作"""
        start_message = self.get_start_message()
        self.conversation_history.append({
            "role": "assistant",
            "content": start_message,
            "timestamp": datetime.now().isoformat()
        })
        return start_message

class ChiefComplaintAgent(BaseAgent):
    """主诉采集智能体"""
    
    def __init__(self):
        super().__init__("主诉采集智能体")
        self.required_elements = ["main_symptom", "duration"]
        self.optional_elements = ["accompanying_symptoms", "severity"]
    
    def get_system_prompt(self) -> str:
        return """你是主诉采集智能体，负责采集患者的主要症状信息。

采集目标：
- 主要症状（必需）：患者最主要的不适症状
- 持续时间（必需）：症状持续了多长时间
- 伴随症状（可选）：除主症状外的其他症状
- 严重程度（可选）：症状的严重程度

工作原则：
1. 每次只问一个重点问题
2. 语言简洁专业，不超过30字
3. 确保获取必需信息后才能结束工作
4. 不进行诊断，只采集信息"""
    
    def get_start_message(self) -> str:
        return "🔍 现在开始采集您的主要症状信息。请详细描述您最主要的不适症状。"
    
    def extract_information(self, user_message: str) -> Dict[str, Any]:
        """从用户消息中提取主诉信息"""
        extracted = {}

        # 提取主要症状
        symptom_keywords = ["头痛", "发热", "咳嗽", "胸痛", "腹痛", "恶心", "呕吐", "腹泻", "便秘", "失眠", "眩晕", "乏力"]
        for keyword in symptom_keywords:
            if keyword in user_message:
                extracted["main_symptom"] = keyword
                break

        # 如果没有找到关键词，但包含症状描述，直接使用用户描述
        if "main_symptom" not in extracted and any(word in user_message for word in ["痛", "疼", "不舒服", "难受"]):
            extracted["main_symptom"] = user_message[:20]  # 取前20个字符作为症状描述

        # 提取持续时间 - 改进的时间提取逻辑
        import re
        time_patterns = [
            r'(\d+)\s*天',
            r'(\d+)\s*周',
            r'(\d+)\s*月',
            r'(\d+)\s*年',
            r'(\d+)\s*小时',
            r'(\d+)\s*分钟',
            r'三天', r'两天', r'一天',
            r'一周', r'两周', r'三周',
            r'一个月', r'两个月', r'三个月'
        ]

        for pattern in time_patterns:
            match = re.search(pattern, user_message)
            if match:
                if match.groups():
                    extracted["duration"] = match.group(0)
                else:
                    extracted["duration"] = pattern.replace('\\', '')
                break

        # 提取伴随症状
        accompanying_keywords = ["伴有", "同时", "还有", "另外", "太阳穴", "附近", "周围"]
        for keyword in accompanying_keywords:
            if keyword in user_message:
                extracted["accompanying_symptoms"] = user_message
                break

        # 提取严重程度
        severity_keywords = {"轻微": "轻度", "严重": "重度", "剧烈": "重度", "轻度": "轻度", "中度": "中度", "重度": "重度"}
        for keyword, level in severity_keywords.items():
            if keyword in user_message:
                extracted["severity"] = level
                break

        return extracted
    
    def generate_report(self) -> str:
        """生成主诉工作报告"""
        main_symptom = self.collected_info.get("main_symptom", "未明确")
        duration = self.collected_info.get("duration", "未明确")
        accompanying = self.collected_info.get("accompanying_symptoms", "无")
        severity = self.collected_info.get("severity", "未评估")
        
        return f"""📋 主诉采集报告：
患者主诉：{main_symptom} + {duration} + {accompanying}
严重程度：{severity}"""

class PresentIllnessAgent(BaseAgent):
    """现病史采集智能体"""
    
    def __init__(self):
        super().__init__("现病史采集智能体")
        self.required_elements = ["onset_cause", "symptom_progression", "influencing_factors"]
        self.optional_elements = ["previous_treatment"]
    
    def get_system_prompt(self) -> str:
        return """你是现病史采集智能体，负责了解当前疾病的发展过程。

采集目标：
- 发病诱因（必需）：什么原因导致的发病
- 症状变化（必需）：症状如何发展变化
- 影响因素（必需）：什么因素会加重或缓解症状
- 治疗情况（可选）：之前是否接受过治疗

工作原则：
1. 按逻辑顺序询问：诱因→变化→影响因素→治疗
2. 语言简洁专业，不超过30字
3. 确保获取必需信息后才能结束工作"""
    
    def get_start_message(self) -> str:
        return "🔍 现在了解您疾病的发展过程。请问您觉得是什么原因导致了这个症状？"
    
    def extract_information(self, user_message: str) -> Dict[str, Any]:
        """从用户消息中提取现病史信息"""
        extracted = {}
        
        # 提取发病诱因
        cause_keywords = ["因为", "由于", "导致", "引起", "诱发", "压力", "熬夜", "感冒", "外伤"]
        for keyword in cause_keywords:
            if keyword in user_message:
                extracted["onset_cause"] = user_message
                break
        
        # 提取症状变化
        progression_keywords = ["加重", "减轻", "好转", "恶化", "变化", "发展", "逐渐", "突然"]
        for keyword in progression_keywords:
            if keyword in user_message:
                extracted["symptom_progression"] = user_message
                break
        
        # 提取影响因素
        factor_keywords = ["休息", "活动", "吃饭", "睡觉", "工作", "缓解", "加重"]
        for keyword in factor_keywords:
            if keyword in user_message:
                extracted["influencing_factors"] = user_message
                break
        
        # 提取治疗情况
        treatment_keywords = ["治疗", "吃药", "看医生", "检查", "手术", "针灸", "按摩"]
        for keyword in treatment_keywords:
            if keyword in user_message:
                extracted["previous_treatment"] = user_message
                break
        
        return extracted
    
    def generate_report(self) -> str:
        """生成现病史工作报告"""
        onset_cause = self.collected_info.get("onset_cause", "不明")
        progression = self.collected_info.get("symptom_progression", "未描述")
        factors = self.collected_info.get("influencing_factors", "未明确")
        treatment = self.collected_info.get("previous_treatment", "未治疗")
        
        return f"""📋 现病史采集报告：
发病诱因：{onset_cause}
症状变化：{progression}
影响因素：{factors}
治疗情况：{treatment}"""

class PastHistoryAgent(BaseAgent):
    """既往史采集智能体"""

    def __init__(self):
        super().__init__("既往史采集智能体")
        self.required_elements = ["previous_diseases", "allergies"]
        self.optional_elements = ["surgeries", "medications"]

    def get_system_prompt(self) -> str:
        return """你是既往史采集智能体，负责了解患者的健康背景。

采集目标：
- 既往疾病（必需）：以前得过什么病
- 过敏史（必需）：对什么过敏
- 手术史（可选）：做过什么手术
- 用药史（可选）：长期服用什么药物

工作原则：
1. 逐项询问既往健康情况
2. 语言简洁专业，不超过30字
3. 确保获取必需信息后才能结束工作"""

    def get_start_message(self) -> str:
        return "🔍 现在了解您的既往健康情况。请问您以前得过什么疾病吗？"

    def extract_information(self, user_message: str) -> Dict[str, Any]:
        extracted = {}

        # 提取既往疾病
        disease_keywords = ["高血压", "糖尿病", "心脏病", "肝病", "肾病", "无", "没有", "健康"]
        for keyword in disease_keywords:
            if keyword in user_message:
                extracted["previous_diseases"] = user_message
                break

        # 提取过敏史
        allergy_keywords = ["过敏", "青霉素", "海鲜", "花粉", "无过敏", "不过敏"]
        for keyword in allergy_keywords:
            if keyword in user_message:
                extracted["allergies"] = user_message
                break

        return extracted

    def generate_report(self) -> str:
        diseases = self.collected_info.get("previous_diseases", "未明确")
        allergies = self.collected_info.get("allergies", "未明确")
        surgeries = self.collected_info.get("surgeries", "无")
        medications = self.collected_info.get("medications", "无")

        return f"""📋 既往史采集报告：
既往疾病：{diseases}
过敏史：{allergies}
手术史：{surgeries}
用药史：{medications}"""

class InquiryWorkflow:
    """预问诊工作流程控制器"""

    def __init__(self, patient_info: Dict[str, Any]):
        self.patient_info = patient_info
        self.agents = [
            ChiefComplaintAgent(),
            PresentIllnessAgent(),
            PastHistoryAgent()
        ]
        self.current_agent_index = 0
        self.agent_reports = []
        self.start_time = datetime.now()
        self.is_completed = False

    def get_current_agent(self) -> Optional[BaseAgent]:
        """获取当前工作的智能体"""
        if self.current_agent_index < len(self.agents):
            return self.agents[self.current_agent_index]
        return None

    def start_inquiry(self) -> str:
        """开始预问诊流程"""
        if self.agents:
            current_agent = self.agents[0]
            welcome_msg = f"您好{self.patient_info.get('name', '')}，我是您的中医预问诊助手。\n\n"
            welcome_msg += "我将通过几个步骤来了解您的健康状况：\n"
            welcome_msg += "1️⃣ 主要症状采集\n"
            welcome_msg += "2️⃣ 疾病发展过程\n"
            welcome_msg += "3️⃣ 既往健康情况\n\n"
            welcome_msg += current_agent.start_work()
            return welcome_msg
        return "预问诊系统初始化失败"

    def process_message(self, user_message: str) -> str:
        """处理用户消息"""
        try:
            current_agent = self.get_current_agent()
            if not current_agent:
                return "预问诊已完成，感谢您的配合！"

            # 当前智能体处理消息
            response, is_agent_completed = current_agent.process_message(user_message)

            if is_agent_completed:
                # 当前智能体工作完成，保存报告
                self.agent_reports.append({
                    "agent_name": current_agent.name,
                    "report": current_agent.generate_report(),
                    "collected_info": current_agent.collected_info.copy(),
                    "completion_time": datetime.now().isoformat()
                })

                # 切换到下一个智能体
                self.current_agent_index += 1

                if self.current_agent_index >= len(self.agents):
                    # 所有智能体工作完成
                    self.is_completed = True
                    final_record = self.generate_final_record()
                    return response + "\n\n" + final_record
                else:
                    # 开始下一个智能体的工作
                    next_agent = self.get_current_agent()
                    next_start = next_agent.start_work()
                    return response + "\n\n" + next_start

            return response

        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            return "抱歉，处理您的消息时出现错误，请重新描述。"

    def generate_final_record(self) -> str:
        """生成最终的结构化预问诊病历"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds() / 60

        # 构建结构化病历
        record = {
            "patient_info": self.patient_info,
            "inquiry_reports": self.agent_reports,
            "inquiry_summary": {
                "start_time": self.start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_minutes": round(duration, 1),
                "total_agents": len(self.agents),
                "completeness": "100%"
            }
        }

        # 格式化输出
        final_output = "🎉 预问诊完成！\n\n"
        final_output += "📋 结构化预问诊病历\n"
        final_output += "=" * 40 + "\n\n"

        final_output += f"👤 患者信息：\n"
        final_output += f"姓名：{self.patient_info.get('name', '未提供')}\n"
        final_output += f"年龄：{self.patient_info.get('age', '未提供')}\n"
        final_output += f"性别：{self.patient_info.get('gender', '未提供')}\n\n"

        for report in self.agent_reports:
            final_output += report["report"] + "\n\n"

        final_output += f"📊 问诊总结：\n"
        final_output += f"问诊时长：{round(duration, 1)}分钟\n"
        final_output += f"完成度：100%\n"
        final_output += f"建议：请携带此预问诊记录就诊\n\n"

        final_output += "✅ 预问诊流程已完成，会话结束。"

        return final_output

    def get_progress(self) -> Dict[str, Any]:
        """获取当前进度"""
        return {
            "current_agent": self.current_agent_index,
            "total_agents": len(self.agents),
            "progress_percentage": (self.current_agent_index / len(self.agents)) * 100,
            "current_agent_name": self.get_current_agent().name if self.get_current_agent() else "已完成",
            "is_completed": self.is_completed
        }
