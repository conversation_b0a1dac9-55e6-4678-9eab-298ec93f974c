# 中医诊前预问诊智能体系统 技术文档

## 目录
- [项目简介](#项目简介)
- [项目结构](#项目结构)
- [依赖与环境](#依赖与环境)
- [核心模块说明](#核心模块说明)
- [运行与部署](#运行与部署)
- [API接口文档](#api接口文档)
- [二次开发与扩展指导](#二次开发与扩展指导)
- [典型流程与时序图](#典型流程与时序图)
- [常见问题与排查](#常见问题与排查)

---

## 项目简介
本系统基于 LangChain 0.2.x、LangGraph、OpenRouter/Ollama 等技术栈，采用多智能体协同采集患者完整病史，自动生成结构化中医病历，支持智能追问与信息补全。支持云端大模型（OpenRouter）和本地大模型（Ollama），可灵活切换。

---

## 项目结构
```plaintext
ywz2/
├── demo_agents.py                # 智能体单元演示脚本
├── demo_simple.py                # 简化版演示脚本
├── demo_workflow.py              # 完整工作流演示脚本
├── docker-compose.yml            # Docker Compose 配置
├── Dockerfile                    # Docker 镜像构建文件
├── docs/
│   ├── 系统设计文档.md           # 详细系统设计文档
│   └── 技术文档.md               # 本文档
├── env.example                   # 环境变量示例文件
├── PROJECT_STATUS.md             # 项目进度与状态说明
├── README.md                     # 项目简介与快速上手
├── requirements.txt              # Python 依赖包列表
├── run_demo.py                   # 启动脚本
├── src/
│   ├── agents/                   # 智能体相关代码
│   ├── api/                      # API 路由与 WebSocket
│   ├── config.py                 # 配置与环境变量管理
│   ├── main.py                   # 主入口
│   ├── models/                   # 数据模型
│   ├── utils/                    # 工具类
│   └── workflows/                # 工作流与LangGraph集成
├── test_openrouter.py            # OpenRouter集成测试
├── tests/                        # 测试用例目录
└── examples/                     # 所有演示与测试脚本
    ├── demo_agents.py
    ├── demo_simple.py
    ├── demo_workflow.py
    ├── run_demo.py
    └── test_openrouter.py
```

---

## 依赖与环境
- Python 3.10+
- 主要依赖见 `requirements.txt`：
  - langchain>=0.2
  - langgraph
  - pydantic>=2.0
  - fastapi
  - httpx
  - uvicorn
  - python-dotenv
  - 及其他
- 推荐使用 conda/venv 虚拟环境
- 支持 Docker 一键部署

---

## 核心模块说明
### 1. 智能体模块（src/agents/）
- `base_agent.py`：智能体基类，定义统一接口（如 agent_workflow、ask_has_or_not、ask_details_if_has 等）。
- `chief_complaint_agent.py` 等：各类病史采集智能体，继承基类，实现具体问询与信息提取。

### 2. 工作流模块（src/workflows/）
- `consultation_workflow.py`：核心问诊流程，基于 LangGraph 实现多智能体协同、状态管理、条件路由。
  - `ConsultationState`：Pydantic 状态模型，记录会话全局状态。
  - `ConsultationWorkflow`：工作流管理器，负责智能体调度、状态推进。

### 3. 工具模块（src/utils/）
- `openrouter_client.py`：OpenRouter API 封装，支持多模型选择。
- `ollama_client.py`：Ollama 本地模型客户端，兼容 LangChain 接口。

### 4. 数据模型（src/models/）
- `consultation.py`、`medical_record.py`、`patient.py`、`symptom.py`：结构化数据定义，Pydantic 2.x 兼容。

### 5. API模块（src/api/）
- `routes.py`、`websocket.py`：API 路由与实时通信支持。

---

## 运行与部署
### 1. 本地运行
```bash
pip install -r requirements.txt
cp env.example .env
# 编辑 .env，填写 OPENROUTER_API_KEY 或 OLLAMA 相关参数
python examples/demo_workflow.py
```

### 2. Docker 部署
```bash
docker build -t ywz2 .
docker-compose up
```

### 3. 测试
```bash
python examples/test_openrouter.py
# 也可在 tests/ 目录下添加/运行更多测试用例
```

---

## API接口文档
### 1. RESTful API（示例）
#### 1.1 问诊会话启动
```
POST /api/consultation/start
{
  "patient_id": "string",
  "session_id": "string"
}
返回：
{
  "state": { ... },
  "question": "智能体首轮提问"
}
```

#### 1.2 提交患者回答
```
POST /api/consultation/answer
{
  "session_id": "string",
  "answer": "string"
}
返回：
{
  "state": { ... },
  "question": "智能体追问/下一个智能体提问/问诊结束"
}
```

#### 1.3 获取结构化病历
```
GET /api/consultation/record?session_id=xxx
返回：
{
  "medical_record": { ... }
}
```

### 2. WebSocket（实时对话）
- 支持事件：`start`、`answer`、`update`、`end` 等
- 详见 `src/api/websocket.py`

### 3. 主要数据结构
- `ConsultationState`：会话状态，见 `consultation_workflow.py`
- `MedicalRecord`：结构化病历，见 `models/medical_record.py`

---

## 二次开发与扩展指导
### 1. 新增/自定义智能体
- 继承 `BaseAgent`，实现 `agent_workflow` 方法。
- 在 `consultation_workflow.py` 的 `self.agents` 注册。
- 可自定义问询模板、信息抽取逻辑。

### 2. 扩展数据模型
- 在 `src/models/` 下新增/修改 Pydantic 模型。
- 在工作流和智能体中引用。

### 3. 接入新大模型
- 实现 LangChain 兼容的 LLM Client（参考 `ollama_client.py`）。
- 在模型选择器注册。
- 配置 .env 切换。

### 4. API/前端对接
- 基于 `src/api/` 进行二次开发，支持 RESTful、WebSocket 等多端集成。
- 可对接 Web、H5、小程序等。

### 5. 工作流自定义
- 可在 `consultation_workflow.py` 中自定义节点、条件路由、状态推进逻辑。
- 支持复杂多智能体协同、动态节点切换。

---

## 典型流程与时序图
```mermaid
sequenceDiagram
    participant User as 患者
    participant API as API/前端
    participant Controller as 主控节点
    participant Agent as 智能体
    participant LLM as 大模型

    User->>API: 提交初始信息
    API->>Controller: 初始化会话
    loop 智能体轮询
        Controller->>Agent: 分配采集任务
        Agent->>LLM: 生成提问/解析回答
        LLM-->>Agent: 返回结果
        Agent->>Controller: 返回结构化信息
        Controller->>API: 返回提问
        API->>User: 展示提问
        User->>API: 提交回答
    end
    Controller->>API: 返回完整结构化病历
    API->>User: 展示病历
```
[![](https://mermaid.ink/img/pako:eNp1k8tu00AUhl_FmrWbxo7jOLOohMoGqZXYsEHeWPGQWortMLElIIoEgaSl10hNqdrSoNBIQUIkQmIBCVFfxre8BTMeUlu4eGHP6HznP_85nmmCiq0jAEEDPXeRVUEPDa2KNVO1OPLUNewYFaOuWQ73pIEwpzW4oH0Tve5k4w8eP6Jh8ln33x-FX6dZZNO2HGzXakzI-zkPjsfRfjts_7pHrorIm9a7mEVvF97iNMtsbW1Twh-Ngy9Df3CgWoyhVtc2NogVyAUnPW828veu_fGBdzsM3vz1RYIESRxBjjH-4Qfv92U0vWZYzbbriYVoMYmmn1mEPkk6LUcdU5nusnO03N1dXnW9-dzfHyZ8jBCUGIdc2P8U7PWIv-X5ZD0a3wSDnn81CL_1E55wa4lydNunwPw0GHzMaqZbSZHvaEOpvrO26ZRYAvOSko5nRKdJ2vp-Fo5m_xL3TDrVArJ0tvhfQX9yGJz9uPMZnnf84276_6RrsyjgQRUbOoAOdhEPTIRNjW5Bk-apwNlBJlIBJEsdPdPcmqMC1WqRNHJkntq2ucrEtlvdWW3cuq45q7N_RxD_CG_aruUAKBSFWALAJngBYDGfk_OlkqgUZFGSRVHhwUsApUJOEQqiUi5LRVnOK3KLB6_imvlcqSiJgqKU5IIsKJJc5gHSDcfG2-z-xdew9QfgTXG-?type=png)](https://mermaid-live.nodejs.cn/edit#pako:eNp1k8tu00AUhl_FmrWbxo7jOLOohMoGqZXYsEHeWPGQWortMLElIIoEgaSl10hNqdrSoNBIQUIkQmIBCVFfxre8BTMeUlu4eGHP6HznP_85nmmCiq0jAEEDPXeRVUEPDa2KNVO1OPLUNewYFaOuWQ73pIEwpzW4oH0Tve5k4w8eP6Jh8ln33x-FX6dZZNO2HGzXakzI-zkPjsfRfjts_7pHrorIm9a7mEVvF97iNMtsbW1Twh-Ngy9Df3CgWoyhVtc2NogVyAUnPW828veu_fGBdzsM3vz1RYIESRxBjjH-4Qfv92U0vWZYzbbriYVoMYmmn1mEPkk6LUcdU5nusnO03N1dXnW9-dzfHyZ8jBCUGIdc2P8U7PWIv-X5ZD0a3wSDnn81CL_1E55wa4lydNunwPw0GHzMaqZbSZHvaEOpvrO26ZRYAvOSko5nRKdJ2vp-Fo5m_xL3TDrVArJ0tvhfQX9yGJz9uPMZnnf84276_6RrsyjgQRUbOoAOdhEPTIRNjW5Bk-apwNlBJlIBJEsdPdPcmqMC1WqRNHJkntq2ucrEtlvdWW3cuq45q7N_RxD_CG_aruUAKBSFWALAJngBYDGfk_OlkqgUZFGSRVHhwUsApUJOEQqiUi5LRVnOK3KLB6_imvlcqSiJgqKU5IIsKJJc5gHSDcfG2-z-xdew9QfgTXG-)
---

## 常见问题与排查
- **依赖冲突**：优先使用 requirements.txt 推荐版本，pydantic 2.x 需注意字段校验变化。
- **API Key 问题**：确保 .env 配置正确，OpenRouter 额度充足。
- **递归/死循环**：外部 while 循环推进对话，内部每步只执行一次。
- **本地模型**：Ollama 需提前启动并下载好所需模型。
- **LangGraph 版本兼容**：如遇API变更，参考最新官方文档及时调整。

---

如需更详细的开发细节、接口示例或遇到具体问题，欢迎随时补充！ 