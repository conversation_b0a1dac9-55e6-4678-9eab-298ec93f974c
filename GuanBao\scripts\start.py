#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GuanBao AI Chat 启动脚本
使用统一配置文件启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# 添加config目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config.config import get_config

class GuanBaoLauncher:
    def __init__(self):
        self.config = get_config()
        self.processes = []
        self.running = True
        
    def start_backend(self):
        """启动后端服务"""
        print(f"启动后端服务 - {self.config.backend_host}:{self.config.backend_port}")

        backend_script = project_root / "backend" / "app.py"
        # 使用llms环境的Python
        llms_python = r"d:\ProgramData\Miniconda3\envs\llms\python.exe"
        cmd = [llms_python, str(backend_script)]

        try:
            process = subprocess.Popen(
                cmd,
                cwd=str(project_root),  # 使用项目根目录作为工作目录
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8',
                bufsize=1,
                shell=False  # 明确指定不使用shell
            )
            self.processes.append(('backend', process))

            # 在后台线程中输出日志
            def log_output():
                try:
                    for line in process.stdout:
                        if self.running:
                            print(f"[后端] {line.strip()}")
                except UnicodeDecodeError:
                    print("[后端] 日志输出解码失败，请检查编码设置")
                except Exception as e:
                    print(f"[后端] 日志输出异常: {e}")

            threading.Thread(target=log_output, daemon=True).start()
            return True

        except Exception as e:
            print(f"启动后端服务失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print(f"启动前端服务 - {self.config.frontend_host}:{self.config.frontend_port}")

        frontend_dir = project_root / "vue-project"

        # 检查是否已安装依赖
        if not (frontend_dir / "node_modules").exists():
            print("正在安装前端依赖...")
            try:
                npm_install = subprocess.run(
                    ["npm", "install"],
                    cwd=str(frontend_dir),
                    capture_output=True,
                    text=True,
                    shell=True  # Windows下需要shell=True来找到npm
                )
                if npm_install.returncode != 0:
                    print(f"安装前端依赖失败: {npm_install.stderr}")
                    return False
            except Exception as e:
                print(f"安装前端依赖异常: {e}")
                return False

        # 启动开发服务器
        try:
            # Windows下需要使用shell=True来执行npm命令
            process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=str(frontend_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                shell=True  # Windows下必须使用shell=True
            )
            self.processes.append(('frontend', process))

            # 在后台线程中输出日志
            def log_output():
                try:
                    for line in process.stdout:
                        if self.running:
                            # 处理编码问题
                            try:
                                print(f"[前端] {line.strip()}")
                            except UnicodeDecodeError:
                                print(f"[前端] {line.strip().encode('utf-8', errors='ignore').decode('utf-8')}")
                except Exception as e:
                    print(f"[前端] 日志输出异常: {e}")

            threading.Thread(target=log_output, daemon=True).start()
            return True

        except Exception as e:
            print(f"启动前端服务失败: {e}")
            return False
    
    def wait_for_services(self):
        """等待服务启动"""
        print("等待服务启动...")
        time.sleep(3)
        
        # 检查后端健康状态
        try:
            import requests
            health_url = f"http://localhost:{self.config.backend_port}/api/health"
            response = requests.get(health_url, timeout=5)
            if response.status_code == 200:
                print("✅ 后端服务启动成功")
            else:
                print("❌ 后端服务启动失败")
        except Exception as e:
            print(f"❌ 后端服务检查失败: {e}")
        
        # 检查前端服务
        try:
            import requests
            frontend_url = f"http://localhost:{self.config.frontend_port}"
            response = requests.get(frontend_url, timeout=5)
            if response.status_code == 200:
                print("✅ 前端服务启动成功")
            else:
                print("❌ 前端服务启动失败")
        except Exception as e:
            print(f"❌ 前端服务检查失败: {e}")
    
    def stop_services(self):
        """停止所有服务"""
        print("\n正在停止服务...")
        self.running = False
        
        for name, process in self.processes:
            try:
                print(f"停止{name}服务...")
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"强制停止{name}服务...")
                process.kill()
            except Exception as e:
                print(f"停止{name}服务失败: {e}")
        
        print("所有服务已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止服务...")
        self.stop_services()
        sys.exit(0)
    
    def run(self):
        """运行启动器"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("=" * 50)
        print("GuanBao AI Chat 启动器")
        print("=" * 50)
        print(f"项目: {self.config.get('project.name')}")
        print(f"版本: {self.config.get('project.version')}")
        print(f"前端: http://{self.config.frontend_host}:{self.config.frontend_port}")
        print(f"后端: http://{self.config.backend_host}:{self.config.backend_port}")
        print("=" * 50)
        
        # 启动后端服务
        if not self.start_backend():
            print("后端服务启动失败，退出")
            return
        
        # 等待后端启动
        time.sleep(2)
        
        # 启动前端服务
        if not self.start_frontend():
            print("前端服务启动失败，停止后端服务")
            self.stop_services()
            return
        
        # 等待服务启动完成
        self.wait_for_services()
        
        print("\n" + "=" * 50)
        print("🚀 所有服务已启动!")
        print(f"📱 前端访问地址: http://localhost:{self.config.frontend_port}")
        print(f"🔧 后端API地址: http://localhost:{self.config.backend_port}")
        print("按 Ctrl+C 停止所有服务")
        print("=" * 50)
        
        # 等待用户中断
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_services()

def main():
    """主函数"""
    launcher = GuanBaoLauncher()
    launcher.run()

if __name__ == '__main__':
    main()
