# YUWENZHEN 项目架构设计方案

## 📋 项目概述

**项目名称**: YUWENZHEN  
**项目位置**: TCM_CDSS/YUWENZHEN  
**架构模式**: 前后端分离 + 微服务化智能体系统  
**技术理念**: 整合四个项目的优点，构建现代化的中医预问诊智能体系统

## 🎯 设计目标

### 核心目标
1. **功能完整性**: 整合ywz2的完整智能体功能
2. **用户友好性**: 保留YUWENZHEN-AUGMENT的优秀界面
3. **技术先进性**: 采用最新的技术栈和架构模式
4. **部署灵活性**: 支持多种部署方式
5. **可扩展性**: 模块化设计，易于扩展和维护

### 整合策略
- **智能体系统**: 采用ywz2的完整多智能体协同架构
- **前端界面**: 基于YUWENZHEN-AUGMENT的Web界面进行优化
- **技术栈**: 使用ywz2的先进技术栈
- **部署方案**: 结合两者优点，支持Docker和本地部署

## 🏗️ 技术架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
├─────────────────────────────────────────────────────────────┤
│  React/Vue.js + TypeScript + Vite                          │
│  ├── 主页导航 (Home)                                        │
│  ├── 问诊界面 (Consultation)                                │
│  ├── 病历查看 (Records)                                     │
│  ├── 系统管理 (Admin)                                       │
│  └── 实时通信 (WebSocket)                                   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (API Gateway)                  │
├─────────────────────────────────────────────────────────────┤
│  FastAPI + CORS + 认证中间件                                │
│  ├── RESTful API 路由                                       │
│  ├── WebSocket 路由                                         │
│  ├── 静态文件服务                                           │
│  └── API文档 (Swagger)                                      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   业务服务层 (Services)                     │
├─────────────────────────────────────────────────────────────┤
│  ├── 问诊服务 (ConsultationService)                         │
│  ├── 智能体管理服务 (AgentService)                          │
│  ├── 会话管理服务 (SessionService)                          │
│  ├── 病历服务 (RecordService)                               │
│  └── 通知服务 (NotificationService)                         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 智能体协同层 (Agent Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  LangGraph 工作流引擎                                       │
│  ├── 主诉采集智能体 (ChiefComplaintAgent)                   │
│  ├── 现病史采集智能体 (PresentIllnessAgent)                 │
│  ├── 既往史采集智能体 (PastHistoryAgent)                    │
│  ├── 家族史采集智能体 (FamilyHistoryAgent)                  │
│  ├── 过敏史采集智能体 (AllergyHistoryAgent)                 │
│  └── 工作流协调器 (WorkflowCoordinator)                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   LLM调用层 (LLM Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  ├── OpenRouter客户端 (主要)                                │
│  ├── Ollama客户端 (备选)                                    │
│  ├── 模型选择器 (ModelSelector)                             │
│  └── 响应缓存 (ResponseCache)                               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   数据存储层 (Data Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  ├── PostgreSQL (主数据库)                                  │
│  ├── Redis (缓存 + 会话)                                    │
│  ├── SQLite (本地开发)                                      │
│  └── 文件存储 (上传文件)                                    │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构设计

```
TCM_CDSS/YUWENZHEN/
├── 📁 frontend/                     # 前端应用
│   ├── 📁 src/
│   │   ├── 📁 components/           # 可复用组件
│   │   ├── 📁 pages/                # 页面组件
│   │   ├── 📁 services/             # API服务
│   │   ├── 📁 stores/               # 状态管理
│   │   ├── 📁 utils/                # 工具函数
│   │   └── 📁 assets/               # 静态资源
│   ├── package.json
│   ├── vite.config.ts
│   └── tsconfig.json
├── 📁 backend/                      # 后端应用
│   ├── 📁 app/
│   │   ├── 📁 api/                  # API路由
│   │   │   ├── __init__.py
│   │   │   ├── routes.py            # 主路由
│   │   │   ├── health.py            # 健康检查
│   │   │   ├── consultation.py      # 问诊API
│   │   │   ├── records.py           # 病历API
│   │   │   └── websocket.py         # WebSocket
│   │   ├── 📁 services/             # 业务服务
│   │   │   ├── __init__.py
│   │   │   ├── consultation_service.py
│   │   │   ├── agent_service.py
│   │   │   ├── session_service.py
│   │   │   └── record_service.py
│   │   ├── 📁 agents/               # 智能体模块
│   │   │   ├── __init__.py
│   │   │   ├── base_agent.py        # 基类
│   │   │   ├── chief_complaint_agent.py
│   │   │   ├── present_illness_agent.py
│   │   │   ├── past_history_agent.py
│   │   │   ├── family_history_agent.py
│   │   │   └── allergy_history_agent.py
│   │   ├── 📁 workflows/            # 工作流
│   │   │   ├── __init__.py
│   │   │   ├── consultation_workflow.py
│   │   │   └── workflow_coordinator.py
│   │   ├── 📁 models/               # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── patient.py
│   │   │   ├── consultation.py
│   │   │   ├── medical_record.py
│   │   │   └── session.py
│   │   ├── 📁 core/                 # 核心模块
│   │   │   ├── __init__.py
│   │   │   ├── config.py            # 配置管理
│   │   │   ├── database.py          # 数据库连接
│   │   │   ├── cache.py             # 缓存管理
│   │   │   └── security.py          # 安全认证
│   │   ├── 📁 utils/                # 工具模块
│   │   │   ├── __init__.py
│   │   │   ├── openrouter_client.py
│   │   │   ├── ollama_client.py
│   │   │   ├── entity_extractor.py
│   │   │   └── medical_parser.py
│   │   └── main.py                  # 应用入口
│   ├── requirements.txt
│   ├── pyproject.toml
│   └── alembic.ini
├── 📁 tests/                        # 测试代码
│   ├── 📁 unit/                     # 单元测试
│   ├── 📁 integration/              # 集成测试
│   └── 📁 e2e/                      # 端到端测试
├── 📁 docs/                         # 项目文档
│   ├── 📁 api/                      # API文档
│   ├── 📁 architecture/             # 架构文档
│   ├── 📁 deployment/               # 部署文档
│   └── 📁 user/                     # 用户文档
├── 📁 scripts/                      # 脚本文件
│   ├── start.py                     # 启动脚本
│   ├── setup.py                     # 环境设置
│   └── deploy.py                    # 部署脚本
├── 📁 config/                       # 配置文件
│   ├── development.env
│   ├── production.env
│   └── docker.env
├── 📁 docker/                       # Docker配置
│   ├── Dockerfile.backend
│   ├── Dockerfile.frontend
│   └── docker-compose.yml
├── README.md
├── .env.example
└── .gitignore
```

## 🔧 技术栈选型

### 前端技术栈
```yaml
核心框架: React 18 + TypeScript
构建工具: Vite 5.x
状态管理: Zustand / Redux Toolkit
UI组件库: Ant Design / Material-UI
样式方案: Tailwind CSS + CSS Modules
HTTP客户端: Axios
WebSocket: Socket.io-client
开发工具: ESLint + Prettier + Husky
```

### 后端技术栈
```yaml
核心框架: FastAPI 0.104+
异步支持: asyncio + uvicorn
智能体框架: LangChain 0.2+ + LangGraph 0.2+
数据验证: Pydantic 2.5+
数据库ORM: SQLAlchemy 2.0+ + Alembic
缓存系统: Redis 5.0+
任务队列: Celery + Redis
监控日志: structlog + prometheus
测试框架: pytest + pytest-asyncio
代码质量: black + isort + mypy
```

### LLM集成技术栈
```yaml
主要LLM: OpenRouter (多模型支持)
备选LLM: Ollama (本地部署)
模型管理: 动态模型选择 + 负载均衡
缓存策略: Redis + 本地缓存
错误处理: 重试机制 + 降级策略
```

### 数据库技术栈
```yaml
主数据库: PostgreSQL 15+ (生产环境)
开发数据库: SQLite (本地开发)
缓存数据库: Redis 7.0+
搜索引擎: Elasticsearch (可选)
文件存储: 本地文件系统 / MinIO
```

## 🚀 部署架构设计

### 开发环境
```yaml
前端: Vite Dev Server (端口: 3000)
后端: uvicorn --reload (端口: 8000)
数据库: SQLite (本地文件)
缓存: Redis (Docker容器)
LLM: Ollama (本地服务)
```

### 生产环境
```yaml
前端: Nginx + 静态文件
后端: Gunicorn + uvicorn workers
数据库: PostgreSQL (主从复制)
缓存: Redis Cluster
LLM: OpenRouter (云服务) + Ollama (备选)
负载均衡: Nginx
容器化: Docker + Docker Compose
```

---

*设计文档版本: v1.0*  
*设计时间: 2025-08-15*  
*设计人员: Augment Agent*
