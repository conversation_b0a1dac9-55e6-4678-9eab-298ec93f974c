#!/usr/bin/env python3
"""
智能体工作流程演示脚本
展示"先问有无，若有则追问，无则结束"的逻辑
"""

import os
import sys
from dotenv import load_dotenv
import argparse

# 加载环境变量
load_dotenv()

# 动态将项目根目录加入 sys.path
ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)

from src.agents.chief_complaint_agent import ChiefComplaintAgent
from src.agents.present_illness_agent import PresentIllnessAgent
from src.agents.past_history_agent import PastHistoryAgent
from src.agents.family_history_agent import FamilyHistoryAgent
from src.agents.allergy_history_agent import AllergyHistoryAgent


def demo_chief_complaint_agent(auto_patient: bool = False):
    """演示主诉采集智能体工作流程"""
    print("=" * 60)
    print("🏥 主诉采集智能体演示")
    print("=" * 60)
    
    agent = ChiefComplaintAgent()
    history = []
    
    # 场景1：患者有症状
    print("\n📋 场景1：患者有症状")
    print("-" * 40)
    
    # 第一步：询问有无
    context = {"conversation_history": history}
    has_question = agent.ask_has_or_not(context)
    print(f"智能体询问: {has_question}")
    
    if auto_patient:
        from src.utils.ollama_client import OllamaLLMClient
        ollama = OllamaLLMClient()
        prompt = f"[对话历史]\n\n[医生提问]\n{has_question}\n请模拟患者真实、简洁地回答医生的问题。"
        patient_response = ollama.invoke(prompt)
        if hasattr(patient_response, 'content'):
            patient_response = patient_response.content
        print(f"患者回答: {patient_response}")
    else:
        patient_response = input("患者回答: ")
    
    history.append({"role": "agent", "content": has_question})
    history.append({"role": "patient", "content": patient_response})
    
    # 解析患者回答
    has_result = agent.parse_has_or_not_response(patient_response)
    print(f"解析结果: {has_result}")
    
    if has_result.get("has", False):
        # 第二步：追问详情
        details_question = agent.ask_details_if_has(context, has_result)
        print(f"智能体追问: {details_question}")
        
        if auto_patient:
            prompt = f"[对话历史]\n" + "\n".join([f"{msg['role']}: {msg['content']}" for msg in history[-5:]]) + f"\n[医生追问]\n{details_question}\n请模拟患者真实、简洁地回答医生的问题。"
            detailed_response = ollama.invoke(prompt)
            if hasattr(detailed_response, 'content'):
                detailed_response = detailed_response.content
            print(f"患者详细回答: {detailed_response}")
        else:
            detailed_response = input("患者详细回答: ")
        
        history.append({"role": "agent", "content": details_question})
        history.append({"role": "patient", "content": detailed_response})
        
        context = {"patient_response": patient_response, "detailed_response": detailed_response, "conversation_history": history}
        
        response = agent.agent_workflow(context)
        print(f"\n📊 提取的数据: {response.extracted_data}")
        print(f"🔍 提取的实体: {response.entities}")
        print(f"✅ 完成状态: {response.is_complete}")
    
    # 场景2：患者无症状
    print("\n📋 场景2：患者无症状")
    print("-" * 40)
    
    context = {"conversation_history": history}
    has_question = agent.ask_has_or_not(context)
    print(f"智能体询问: {has_question}")
    
    if auto_patient:
        prompt = f"[对话历史]\n\n[医生提问]\n{has_question}\n请模拟患者真实、简洁地回答医生的问题。"
        patient_response = ollama.invoke(prompt)
        if hasattr(patient_response, 'content'):
            patient_response = patient_response.content
        print(f"患者回答: {patient_response}")
    else:
        patient_response = input("患者回答: ")
    
    history.append({"role": "agent", "content": has_question})
    history.append({"role": "patient", "content": patient_response})
    
    # 解析患者回答
    has_result = agent.parse_has_or_not_response(patient_response)
    print(f"解析结果: {has_result}")
    
    if not has_result.get("has", True):
        context = {"patient_response": patient_response, "conversation_history": history}
        response = agent.agent_workflow(context)
        print(f"智能体响应: {response.response}")
        print(f"✅ 完成状态: {response.is_complete}")


def demo_present_illness_agent():
    """演示现病史采集智能体工作流程"""
    print("\n" + "=" * 60)
    print("🏥 现病史采集智能体演示")
    print("=" * 60)
    
    agent = PresentIllnessAgent()
    
    # 演示不同症状类别
    symptom_categories = ["寒热症状", "汗症", "疼痛症状"]
    
    for category in symptom_categories:
        print(f"\n📋 症状类别: {category}")
        print("-" * 40)
        
        # 重置到指定类别
        agent.reset_categories()
        while agent.symptom_categories[agent.current_category_index] != category:
            agent.next_category()
        
        # 第一步：询问有无
        context = {}
        has_question = agent.ask_has_or_not(context)
        print(f"智能体询问: {has_question}")
        
        # 模拟患者回答
        if category == "寒热症状":
            patient_response = "是的，我最近总是怕冷"
        elif category == "汗症":
            patient_response = "没有，出汗正常"
        else:
            patient_response = "有一点，但不是特别明显"
        
        print(f"患者回答: {patient_response}")
        
        # 解析患者回答
        has_result = agent.parse_has_or_not_response(patient_response)
        print(f"解析结果: {has_result}")
        
        if has_result.get("has", False):
            # 第二步：追问详情
            details_question = agent.ask_details_if_has(context, has_result)
            print(f"智能体追问: {details_question}")
            
            # 模拟患者详细回答
            detailed_response = "怕冷比较明显，特别是晚上，需要盖厚被子"
            print(f"患者详细回答: {detailed_response}")
            
            # 第三步：提取信息
            context = {
                "patient_response": patient_response,
                "detailed_response": detailed_response
            }
            
            response = agent.agent_workflow(context)
            print(f"📊 提取的数据: {response.extracted_data}")
            print(f"✅ 完成状态: {response.is_complete}")
        else:
            context = {"patient_response": patient_response}
            response = agent.agent_workflow(context)
            print(f"智能体响应: {response.response}")
            print(f"✅ 完成状态: {response.is_complete}")


def demo_workflow_summary():
    """演示工作流程总结"""
    print("\n" + "=" * 60)
    print("🔄 智能体工作流程总结")
    print("=" * 60)
    
    print("""
    📋 统一工作流程："先问有无，若有则追问，无则结束"
    
    1️⃣ 询问有无
       - 智能体首先询问患者是否有相关症状或情况
       - 使用标准化的询问模板
    
    2️⃣ 解析回答
       - 使用LLM解析患者的回答
       - 判断是否有相关情况（has: true/false）
       - 计算置信度（confidence: 0.0-1.0）
    
    3️⃣ 追问详情（如果有）
       - 生成详细的追问问题
       - 针对不同症状类别使用不同的追问模板
    
    4️⃣ 提取信息（如果有详细回答）
       - 提取结构化数据
       - 识别医疗实体
       - 计算置信度
    
    5️⃣ 完成判断
       - 判断当前智能体是否完成
       - 决定下一步行动（继续追问或结束）
    
    🎯 优势：
    - 标准化流程，易于理解和维护
    - 减少无效追问，提高效率
    - 确保信息收集的完整性
    - 支持灵活的状态管理
    """)


def main():
    """主函数"""
    print("🤖 中医诊前预问诊智能体系统演示")
    print("基于OpenRouter + LangChain + LangGraph")
    print("统一逻辑：先问有无，若有则追问，无则结束")
    
    # 检查环境变量
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key or api_key == "your-openrouter-api-key-here":
        print("\n❌ 请设置OPENROUTER_API_KEY环境变量")
        print("在.env文件中设置：OPENROUTER_API_KEY=your-actual-api-key")
        return
    
    try:
        parser = argparse.ArgumentParser()
        parser.add_argument('--auto', action='store_true', help='自动模拟患者输入')
        parser.add_argument('--manual', action='store_true', help='手动输入患者回答')
        args = parser.parse_args()
        auto_patient = args.auto and not args.manual
        
        # 运行演示
        demo_chief_complaint_agent(auto_patient=auto_patient)
        demo_present_illness_agent()
        demo_workflow_summary()
        
        print("\n🎉 演示完成！")
        print("\n💡 提示：")
        print("- 这是基于OpenRouter的演示，需要有效的API密钥")
        print("- 实际使用中，智能体会根据患者回答动态调整")
        print("- 所有智能体都遵循相同的标准化流程")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查OpenRouter API密钥是否正确设置")


if __name__ == "__main__":
    main() 