# 🗄️ SQLite数据库说明

## 📊 数据库信息

- **数据库文件**: `tcm_clinic_system.db`
- **数据库类型**: SQLite 3
- **字符编码**: UTF-8
- **架构**: 优化的映射表设计，支持数据加密和安全存储

## 🏗️ 数据库架构特点

### 安全设计
- **映射表**: 敏感信息通过映射ID存储
- **数据加密**: 姓名、地址等敏感字段加密存储
- **哈希处理**: 手机号、身份证号使用SHA256哈希
- **外键约束**: 保证数据完整性

### 表结构
| 表类型 | 表名 | 说明 |
|--------|------|------|
| 映射表 | gender_mappings | 性别映射 |
| 映射表 | role_mappings | 角色映射 |
| 映射表 | department_mappings | 科室映射 |
| 映射表 | status_mappings | 状态映射 |
| 主表 | staff | 员工信息（加密存储） |
| 主表 | patients | 患者信息（加密存储） |
| 主表 | appointments | 预约记录 |
| 主表 | visits | 就诊记录 |

## 🔑 默认账号

| 角色 | 员工号 | 手机号 | 密码 |
|------|--------|--------|------|
| 管理员 | ADM001 | 13000000000 | password |
| 医生 | DOC001 | 13800138000 | password |
| 医生 | DOC002 | 13900139000 | password |
| 医生 | DOC003 | 13700137000 | password |
| 前台 | REC001 | 13600136000 | password |
| 药师 | PHA001 | 13500135000 | password |
| 护士 | NUR001 | 13400134000 | password |

## 🔧 连接方式

### 后端连接（推荐）
```python
# 使用FastAPI后端服务
import httpx

async with httpx.AsyncClient() as client:
    response = await client.get('http://localhost:8000/api/stats')
    data = response.json()
```

### 直接连接
```python
# Python + SQLite3
import sqlite3

conn = sqlite3.connect('./datasets/tcm_clinic_system.db')
cursor = conn.cursor()

# 查询示例
cursor.execute("SELECT * FROM staff WHERE is_active = 1")
rows = cursor.fetchall()
```

```python
# 使用SQLAlchemy (推荐)
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

engine = create_engine('sqlite:///./datasets/tcm_clinic_system.db')
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
```

## 🛠️ 技术栈

### 后端技术栈
- **框架**: FastAPI
- **语言**: Python 3.8+
- **数据库**: SQLite3
- **ORM**: SQLAlchemy
- **认证**: JWT + bcrypt
- **API文档**: Swagger/OpenAPI
- **异步支持**: asyncio

### 依赖包
```bash
pip install fastapi uvicorn sqlalchemy sqlite3 bcrypt python-jose[cryptography] python-multipart
```

## ⚠️ 重要提醒

1. **生产环境**: 请立即修改默认密码
2. **数据备份**: 定期备份数据库文件
3. **安全访问**: 建议通过FastAPI后端访问，不要直接操作数据库
4. **权限控制**: 严格按照角色权限进行数据访问

## 🚀 使用方式

1. **启动后端服务**:
   ```bash
   cd backend
   pip install -r requirements.txt
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **访问API**: http://localhost:8000
3. **API文档**: http://localhost:8000/docs (Swagger UI)
4. **健康检查**: http://localhost:8000/health
5. **数据统计**: http://localhost:8000/api/stats
