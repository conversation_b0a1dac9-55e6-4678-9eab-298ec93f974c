import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from datetime import datetime
class Logger:
    """日志管理器"""
    
    def __init__(self, log_dir: str = "logs"):
        """
        初始化日志管理器
        :param log_dir: 日志目录
        """
        name = datetime.now().strftime("%Y-%m-%d_%H")
        self.logger = logging.getLogger()
        self.logger.setLevel(logging.DEBUG)
        
        # 创建日志目录
        log_path = Path(log_dir)
        log_path.mkdir(exist_ok=True)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_format = logging.Formatter(
            '%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        console_handler.setFormatter(console_format)
        
        # 普通日志文件处理器
        file_handler = RotatingFileHandler(
            log_path / f"{name}.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_format = logging.Formatter(
            '%(asctime)s  - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        file_handler.setFormatter(file_format)
        
        # 错误日志文件处理器
        error_handler = TimedRotatingFileHandler(
            log_path / f"{name}_error.log",
            when='midnight',
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_format = logging.Formatter(
            '%(asctime)s - - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s\n'
            'Exception: %(exc_info)s\n'
            'Stack Trace: %(stack_info)s'
        )
        error_handler.setFormatter(error_format)
        
        # 添加处理器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        
    def get_logger(self):
        return self.logger 
    
    def logd(self, msg):
        """DEBUG级别日志快捷方法"""
        self.logger.debug(msg)
        
    def logi(self, msg):
        """INFO级别日志快捷方法"""
        self.logger.info(msg)
        
    def logw(self, msg):
        """WARNING级别日志快捷方法"""
        self.logger.warning(msg)
        
    def loge(self, msg):
        """ERROR级别日志快捷方法"""
        self.logger.error(msg)
    def logex(self, msg):
        """EXCEPTION级别日志快捷方法,自动捕获并记录异常信息"""
        self.logger.exception(msg)

    def log_api_request(self, method: str, path: str, request_data: str = None):
        """记录 API 请求信息
        
        :param method: 请求方法，如 GET, POST 等
        :param path: 请求路径
        :param request_data: 请求数据
        """
        if request_data:
            self.logger.info(f"API Request - Method: {method}, Path: {path}, Data: {request_data}")
        else:
            self.logger.info(f"API Request - Method: {method}, Path: {path}")

    def log_api_response(self, method: str, path: str, status_code: int, response_data: str = None):
        """记录 API 响应信息
        :param method: 请求方法，如 GET, POST 等
        :param path: 请求路径
        :param status_code: 响应状态码
        :param response_data: 响应数据
        """
        if response_data:
            self.logger.info(f"API Response - Method: {method}, Path: {path}, Status: {status_code}, Data: {response_data}")
        else:
            self.logger.info(f"API Response - Method: {method}, Path: {path}, Status: {status_code}")


if __name__ == "__main__":
    # 初始化日志管理器，使用默认日志目录
    logger = Logger()
    def made_error():
        # 记录不同级别的日志
        logger.logd("调试信息")
        logger.logi("普通信息")
        logger.logw("警告信息")
        logger.loge("错误信息")
        try:
            # 模拟一个会抛出异常的操作
            result = 1 / 0
        except Exception:
            logger.logex("这是一条使用exception方法记录的日志，会自动捕获并记录异常信息")

    made_error()