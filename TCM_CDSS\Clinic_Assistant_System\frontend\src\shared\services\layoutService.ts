// 卡片布局管理服务
export interface LayoutData {
  moduleOrder: string[]
  timestamp: number
  userId: string
  version: string
}

export interface LayoutPreset {
  id: string
  name: string
  description: string
  moduleOrder: string[]
  isDefault?: boolean
}

class LayoutService {
  private readonly STORAGE_KEY = 'dashboard-layout'
  private readonly PRESETS_KEY = 'dashboard-layout-presets'
  private readonly CURRENT_VERSION = '1.0.0'

  // 默认布局预设
  private readonly defaultPresets: LayoutPreset[] = [
    {
      id: 'default',
      name: '默认布局',
      description: '系统推荐的功能卡片布局',
      moduleOrder: [
        'workstation',
        'patients', 
        'appointments',
        'billing',
        'pharmacy',
        'inventory',
        'family-doctor',
        'research-center',
        'data-center',
        'app-center',
        'mall-management',
        'scheduling',
        'staff',
        'reports'
      ],
      isDefault: true
    },
    {
      id: 'doctor-focused',
      name: '医生专用',
      description: '针对医生工作流程优化的布局',
      moduleOrder: [
        'workstation',
        'patients',
        'family-doctor',
        'research-center',
        'appointments',
        'data-center',
        'pharmacy',
        'billing',
        'inventory',
        'app-center',
        'mall-management',
        'scheduling',
        'staff',
        'reports'
      ]
    },
    {
      id: 'admin-focused',
      name: '管理员专用',
      description: '针对管理员工作需求的布局',
      moduleOrder: [
        'data-center',
        'reports',
        'staff',
        'scheduling',
        'inventory',
        'billing',
        'patients',
        'workstation',
        'appointments',
        'pharmacy',
        'family-doctor',
        'research-center',
        'app-center',
        'mall-management'
      ]
    }
  ]

  // 保存布局
  saveLayout(moduleOrder: string[], userId: string): boolean {
    try {
      const layoutData: LayoutData = {
        moduleOrder,
        timestamp: Date.now(),
        userId,
        version: this.CURRENT_VERSION
      }
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(layoutData))
      console.log('布局已保存:', layoutData)
      return true
    } catch (error) {
      console.error('保存布局失败:', error)
      return false
    }
  }

  // 加载布局
  loadLayout(userId: string): string[] {
    try {
      const savedLayout = localStorage.getItem(this.STORAGE_KEY)
      if (!savedLayout) {
        return this.getDefaultLayout()
      }

      const layoutData: LayoutData = JSON.parse(savedLayout)
      
      // 检查用户ID和版本
      if (layoutData.userId !== userId) {
        console.log('用户ID不匹配，使用默认布局')
        return this.getDefaultLayout()
      }

      if (layoutData.version !== this.CURRENT_VERSION) {
        console.log('布局版本不匹配，使用默认布局')
        return this.getDefaultLayout()
      }

      return layoutData.moduleOrder || this.getDefaultLayout()
    } catch (error) {
      console.error('加载布局失败:', error)
      return this.getDefaultLayout()
    }
  }

  // 获取默认布局
  getDefaultLayout(): string[] {
    const defaultPreset = this.defaultPresets.find(p => p.isDefault)
    return defaultPreset ? defaultPreset.moduleOrder : []
  }

  // 重置为默认布局
  resetToDefault(userId: string): boolean {
    const defaultOrder = this.getDefaultLayout()
    return this.saveLayout(defaultOrder, userId)
  }

  // 获取所有预设
  getPresets(): LayoutPreset[] {
    try {
      const savedPresets = localStorage.getItem(this.PRESETS_KEY)
      if (savedPresets) {
        const customPresets: LayoutPreset[] = JSON.parse(savedPresets)
        return [...this.defaultPresets, ...customPresets]
      }
      return this.defaultPresets
    } catch (error) {
      console.error('获取预设失败:', error)
      return this.defaultPresets
    }
  }

  // 应用预设
  applyPreset(presetId: string, userId: string): boolean {
    const presets = this.getPresets()
    const preset = presets.find(p => p.id === presetId)
    
    if (!preset) {
      console.error('预设不存在:', presetId)
      return false
    }

    return this.saveLayout(preset.moduleOrder, userId)
  }

  // 保存自定义预设
  saveCustomPreset(preset: Omit<LayoutPreset, 'id'>): boolean {
    try {
      const customPresets = this.getCustomPresets()
      const newPreset: LayoutPreset = {
        ...preset,
        id: `custom-${Date.now()}`
      }
      
      customPresets.push(newPreset)
      localStorage.setItem(this.PRESETS_KEY, JSON.stringify(customPresets))
      
      console.log('自定义预设已保存:', newPreset)
      return true
    } catch (error) {
      console.error('保存自定义预设失败:', error)
      return false
    }
  }

  // 获取自定义预设
  private getCustomPresets(): LayoutPreset[] {
    try {
      const savedPresets = localStorage.getItem(this.PRESETS_KEY)
      return savedPresets ? JSON.parse(savedPresets) : []
    } catch (error) {
      console.error('获取自定义预设失败:', error)
      return []
    }
  }

  // 删除自定义预设
  deleteCustomPreset(presetId: string): boolean {
    try {
      const customPresets = this.getCustomPresets()
      const filteredPresets = customPresets.filter(p => p.id !== presetId)
      
      localStorage.setItem(this.PRESETS_KEY, JSON.stringify(filteredPresets))
      console.log('自定义预设已删除:', presetId)
      return true
    } catch (error) {
      console.error('删除自定义预设失败:', error)
      return false
    }
  }

  // 导出布局配置
  exportLayout(userId: string): string {
    const layoutData = {
      layout: this.loadLayout(userId),
      presets: this.getCustomPresets(),
      exportTime: new Date().toISOString(),
      version: this.CURRENT_VERSION
    }
    
    return JSON.stringify(layoutData, null, 2)
  }

  // 导入布局配置
  importLayout(configJson: string, userId: string): boolean {
    try {
      const config = JSON.parse(configJson)
      
      if (config.layout) {
        this.saveLayout(config.layout, userId)
      }
      
      if (config.presets && Array.isArray(config.presets)) {
        localStorage.setItem(this.PRESETS_KEY, JSON.stringify(config.presets))
      }
      
      console.log('布局配置导入成功')
      return true
    } catch (error) {
      console.error('导入布局配置失败:', error)
      return false
    }
  }

  // 清除所有布局数据
  clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.PRESETS_KEY)
      console.log('所有布局数据已清除')
      return true
    } catch (error) {
      console.error('清除布局数据失败:', error)
      return false
    }
  }
}

// 导出单例实例
export const layoutService = new LayoutService()
