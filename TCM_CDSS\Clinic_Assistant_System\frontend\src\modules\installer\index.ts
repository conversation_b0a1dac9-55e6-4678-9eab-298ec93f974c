// 模块安装器 - 用于动态安装和管理功能卡片模块
import type { CardModule } from '../cards/index'

export interface ModulePackage {
  id: string
  name: string
  version: string
  description: string
  author: string
  dependencies?: string[]
  cardModule: CardModule
  installScript?: () => Promise<void>
  uninstallScript?: () => Promise<void>
  updateScript?: (oldVersion: string, newVersion: string) => Promise<void>
}

export interface InstalledModule {
  id: string
  version: string
  installedAt: string
  enabled: boolean
}

class ModuleInstaller {
  private installedModules: Map<string, InstalledModule> = new Map()
  private availablePackages: Map<string, ModulePackage> = new Map()

  constructor() {
    this.loadInstalledModules()
  }

  // 加载已安装的模块列表
  private loadInstalledModules() {
    const stored = localStorage.getItem('installed_modules')
    if (stored) {
      try {
        const modules = JSON.parse(stored)
        Object.entries(modules).forEach(([id, module]) => {
          this.installedModules.set(id, module as InstalledModule)
        })
      } catch (error) {
        console.error('Failed to load installed modules:', error)
      }
    }
  }

  // 保存已安装的模块列表
  private saveInstalledModules() {
    const modules = Object.fromEntries(this.installedModules)
    localStorage.setItem('installed_modules', JSON.stringify(modules))
  }

  // 注册可用的模块包
  registerPackage(pkg: ModulePackage) {
    this.availablePackages.set(pkg.id, pkg)
  }

  // 获取所有可用的模块包
  getAvailablePackages(): ModulePackage[] {
    return Array.from(this.availablePackages.values())
  }

  // 获取已安装的模块
  getInstalledModules(): InstalledModule[] {
    return Array.from(this.installedModules.values())
  }

  // 检查模块是否已安装
  isInstalled(moduleId: string): boolean {
    return this.installedModules.has(moduleId)
  }

  // 检查模块是否启用
  isEnabled(moduleId: string): boolean {
    const module = this.installedModules.get(moduleId)
    return module ? module.enabled : false
  }

  // 安装模块
  async installModule(moduleId: string): Promise<boolean> {
    const pkg = this.availablePackages.get(moduleId)
    if (!pkg) {
      throw new Error(`Module package ${moduleId} not found`)
    }

    if (this.isInstalled(moduleId)) {
      throw new Error(`Module ${moduleId} is already installed`)
    }

    try {
      // 检查依赖
      if (pkg.dependencies) {
        for (const dep of pkg.dependencies) {
          if (!this.isInstalled(dep)) {
            throw new Error(`Dependency ${dep} is not installed`)
          }
        }
      }

      // 执行安装脚本
      if (pkg.installScript) {
        await pkg.installScript()
      }

      // 记录安装信息
      const installedModule: InstalledModule = {
        id: moduleId,
        version: pkg.version,
        installedAt: new Date().toISOString(),
        enabled: true
      }

      this.installedModules.set(moduleId, installedModule)
      this.saveInstalledModules()

      return true
    } catch (error) {
      console.error(`Failed to install module ${moduleId}:`, error)
      throw error
    }
  }

  // 卸载模块
  async uninstallModule(moduleId: string): Promise<boolean> {
    const pkg = this.availablePackages.get(moduleId)
    const installedModule = this.installedModules.get(moduleId)

    if (!installedModule) {
      throw new Error(`Module ${moduleId} is not installed`)
    }

    try {
      // 执行卸载脚本
      if (pkg?.uninstallScript) {
        await pkg.uninstallScript()
      }

      // 移除安装记录
      this.installedModules.delete(moduleId)
      this.saveInstalledModules()

      return true
    } catch (error) {
      console.error(`Failed to uninstall module ${moduleId}:`, error)
      throw error
    }
  }

  // 启用/禁用模块
  toggleModule(moduleId: string, enabled: boolean): boolean {
    const installedModule = this.installedModules.get(moduleId)
    if (!installedModule) {
      throw new Error(`Module ${moduleId} is not installed`)
    }

    installedModule.enabled = enabled
    this.saveInstalledModules()
    return true
  }

  // 更新模块
  async updateModule(moduleId: string): Promise<boolean> {
    const pkg = this.availablePackages.get(moduleId)
    const installedModule = this.installedModules.get(moduleId)

    if (!pkg || !installedModule) {
      throw new Error(`Module ${moduleId} not found or not installed`)
    }

    if (installedModule.version === pkg.version) {
      throw new Error(`Module ${moduleId} is already up to date`)
    }

    try {
      // 执行更新脚本
      if (pkg.updateScript) {
        await pkg.updateScript(installedModule.version, pkg.version)
      }

      // 更新版本信息
      installedModule.version = pkg.version
      this.saveInstalledModules()

      return true
    } catch (error) {
      console.error(`Failed to update module ${moduleId}:`, error)
      throw error
    }
  }

  // 获取启用的卡片模块
  getEnabledCardModules(): CardModule[] {
    const enabledModules: CardModule[] = []
    
    for (const [moduleId, installedModule] of this.installedModules) {
      if (installedModule.enabled) {
        const pkg = this.availablePackages.get(moduleId)
        if (pkg) {
          enabledModules.push(pkg.cardModule)
        }
      }
    }

    return enabledModules
  }

  // 清理无效的安装记录
  cleanup() {
    const validModules = new Map<string, InstalledModule>()
    
    for (const [moduleId, installedModule] of this.installedModules) {
      if (this.availablePackages.has(moduleId)) {
        validModules.set(moduleId, installedModule)
      }
    }

    this.installedModules = validModules
    this.saveInstalledModules()
  }
}

// 创建全局模块安装器实例
export const moduleInstaller = new ModuleInstaller()

// 导出类型和实例
export { ModuleInstaller }
export default moduleInstaller
