# 中医诊所助手系统 - 后端

## 目录结构

```
backend/
├── README.md                    # 本文件
├── requirements.txt             # Python依赖配置
├── main.py                      # FastAPI应用入口
├── config/                      # 配置文件
│   ├── database.py             # 数据库配置
│   ├── auth.py                 # 认证配置
│   └── settings.py             # 应用配置
├── models/                      # SQLAlchemy数据模型
│   ├── __init__.py             # 模型入口
│   ├── user.py                 # 用户模型
│   ├── patient.py              # 患者模型
│   ├── appointment.py          # 预约模型
│   ├── visit.py                # 就诊模型
│   ├── medical_record.py       # 病历模型
│   ├── prescription.py         # 处方模型
│   └── mappings/               # 映射表模型
│       ├── __init__.py         # 映射模型入口
│       ├── gender_mapping.py   # 性别映射
│       ├── status_mapping.py   # 状态映射
│       ├── diagnosis_mapping.py # 诊断映射
│       └── symptom_mapping.py  # 症状映射
├── schemas/                     # Pydantic数据模式
│   ├── __init__.py             # 模式入口
│   ├── user.py                 # 用户模式
│   ├── patient.py              # 患者模式
│   ├── appointment.py          # 预约模式
│   ├── visit.py                # 就诊模式
│   └── response.py             # 响应模式
├── routers/                     # API路由
│   ├── __init__.py             # 路由入口
│   ├── auth.py                 # 认证路由
│   ├── users.py                # 用户路由
│   ├── patients.py             # 患者路由
│   ├── appointments.py         # 预约路由
│   ├── visits.py               # 就诊路由
│   ├── prescriptions.py        # 处方路由
│   └── reports.py              # 报表路由
├── middleware/                  # 中间件
│   ├── __init__.py             # 中间件入口
│   ├── auth.py                 # 认证中间件
│   ├── validation.py           # 数据验证中间件
│   ├── logging.py              # 日志中间件
│   └── error_handler.py        # 错误处理中间件
├── services/                    # 业务服务层
│   ├── __init__.py             # 服务入口
│   ├── auth_service.py         # 认证服务
│   ├── patient_service.py      # 患者服务
│   ├── appointment_service.py  # 预约服务
│   ├── prescription_service.py # 处方服务
│   ├── report_service.py       # 报表服务
│   └── mapping_service.py      # 映射服务
├── utils/                       # 工具函数
│   ├── __init__.py             # 工具入口
│   ├── encryption.py           # 加密工具
│   ├── validation.py           # 验证工具
│   ├── date_utils.py           # 日期工具
│   └── response_utils.py       # 响应工具
├── database/                    # 数据库相关
│   ├── __init__.py             # 数据库入口
│   ├── connection.py           # 数据库连接
│   ├── migrations/             # 数据库迁移
│   ├── seeders/                # 数据填充
│   └── schema/                 # 数据库结构
│       ├── create_tables.sql   # 创建表结构
│       ├── create_mappings.sql # 创建映射表
│       └── insert_mappings.sql # 插入映射数据
├── tests/                       # 测试文件
│   ├── __init__.py             # 测试入口
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   └── fixtures/               # 测试数据
└── logs/                        # 日志文件
    ├── app.log                 # 应用日志
    ├── error.log               # 错误日志
    └── access.log              # 访问日志
```

## 技术栈

- **语言**: Python 3.8+
- **框架**: FastAPI
- **数据库**: SQLite 3
- **ORM**: SQLAlchemy
- **认证**: JWT + bcrypt
- **数据验证**: Pydantic
- **API文档**: Swagger/OpenAPI
- **异步支持**: asyncio
- **ASGI服务器**: Uvicorn
- **测试**: pytest

## 安装和运行

```bash
# 进入后端目录
cd backend

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python scripts/init_database.py

# 启动开发服务器
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 启动生产服务器
uvicorn main:app --host 0.0.0.0 --port 8000

# 运行测试
pytest
```

## API文档

API文档将在服务启动后可通过以下地址访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- OpenAPI JSON: http://localhost:8000/openapi.json

## 数据安全

- 所有敏感数据通过映射表存储
- 密码使用bcrypt加密
- JWT token用于身份验证
- 数据传输使用HTTPS
- 输入数据严格验证

## 开发规范

- 使用Black进行代码格式化
- 使用flake8进行代码检查
- 使用mypy进行类型检查
- 遵循RESTful API设计原则
- 所有API返回统一格式的JSON响应
- 完善的错误处理和日志记录
- 使用Pydantic进行数据验证
- 遵循PEP 8编码规范
