# 个人资料模块

## 功能描述
个人资料模块负责管理用户的个人信息，包括基本信息、密码修改、偏好设置等功能。

## 目录结构
```
profile/
├── views/                    # 页面视图
│   └── Profile.vue           # 个人资料页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 个人信息管理
- 密码修改
- 偏好设置
- 头像上传

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 