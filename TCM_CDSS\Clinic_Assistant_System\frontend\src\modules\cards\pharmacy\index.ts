// 药房管理卡片模块
import type { CardModule } from '../index'

// 药房管理统计数据
export const pharmacyStats = {
  pendingPrescriptions: 8,
  dispensedToday: 15,
  lowStockItems: 12,
  totalMedicines: 256
}

// 药房管理卡片配置
export const pharmacyCard: CardModule = {
  key: 'pharmacy',
  name: '药房管理',
  route: '/pharmacy',
  icon: 'Box',
  color: '#FF3B30',
  info: `${pharmacyStats.pendingPrescriptions} 个处方待发药`,
  hasDetailStats: false,
  statsData: pharmacyStats,
  permission: 'pharmacy_manage'
}

// 药房管理详细统计数据获取函数
export const getPharmacyDetailStats = () => {
  return {
    pending: pharmacyStats.pendingPrescriptions,
    dispensed: pharmacyStats.dispensedToday,
    lowStock: pharmacyStats.lowStockItems,
    total: pharmacyStats.totalMedicines
  }
}

// 药房管理卡片信息更新函数
export const updatePharmacyInfo = () => {
  return `${pharmacyStats.pendingPrescriptions} 个处方待发药`
}
