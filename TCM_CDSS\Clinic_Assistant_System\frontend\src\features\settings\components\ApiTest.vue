<!--
  API测试页面
  
  主要功能：
  1. 测试前后端连接
  2. 验证API接口
  3. 显示响应数据
  4. 错误处理演示
  
  <AUTHOR>
  @version 1.0.0
  @since 2024-01-01
-->

<template>
  <div class="api-test-container">
    <div class="test-header">
      <h3>🔗 前后端连接测试</h3>
      <p>测试前端与Python FastAPI后端的连接状态</p>
    </div>

    <div class="test-sections">
      <!-- 基础连接测试 -->
      <el-card class="test-card" header="基础连接测试">
        <div class="test-item">
          <el-button 
            type="primary" 
            @click="testHealth" 
            :loading="healthLoading"
            icon="Connection"
          >
            健康检查
          </el-button>
          <div v-if="healthResult" class="result-display">
            <pre>{{ JSON.stringify(healthResult, null, 2) }}</pre>
          </div>
        </div>

        <div class="test-item">
          <el-button 
            type="success" 
            @click="testRoot" 
            :loading="rootLoading"
            icon="House"
          >
            根路径测试
          </el-button>
          <div v-if="rootResult" class="result-display">
            <pre>{{ JSON.stringify(rootResult, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 数据接口测试 -->
      <el-card class="test-card" header="数据接口测试">
        <div class="test-item">
          <el-button 
            type="warning" 
            @click="testStats" 
            :loading="statsLoading"
            icon="DataAnalysis"
          >
            数据库统计
          </el-button>
          <div v-if="statsResult" class="result-display">
            <pre>{{ JSON.stringify(statsResult, null, 2) }}</pre>
          </div>
        </div>

        <div class="test-item">
          <el-button 
            type="info" 
            @click="testMappings" 
            :loading="mappingsLoading"
            icon="Collection"
          >
            映射数据
          </el-button>
          <div v-if="mappingsResult" class="result-display">
            <pre>{{ JSON.stringify(mappingsResult, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 认证测试 -->
      <el-card class="test-card" header="认证测试">
        <div class="test-item">
          <el-form inline>
            <el-form-item label="手机号">
              <el-input 
                v-model="loginForm.phone" 
                placeholder="13000000000"
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item label="密码">
              <el-input 
                v-model="loginForm.password" 
                type="password" 
                placeholder="password"
                style="width: 120px"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testLogin" 
                :loading="loginLoading"
                icon="User"
              >
                测试登录
              </el-button>
            </el-form-item>
          </el-form>
          <div v-if="loginResult" class="result-display">
            <pre>{{ JSON.stringify(loginResult, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 加密测试 -->
      <el-card class="test-card" header="加密功能测试">
        <div class="test-item">
          <el-form inline>
            <el-form-item label="测试文本">
              <el-input 
                v-model="encryptText" 
                placeholder="输入要加密的文本"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="danger" 
                @click="testEncryption" 
                :loading="encryptLoading"
                icon="Lock"
              >
                测试加密
              </el-button>
            </el-form-item>
          </el-form>
          <div v-if="encryptResult" class="result-display">
            <pre>{{ JSON.stringify(encryptResult, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 连接状态总览 -->
      <el-card class="test-card" header="连接状态总览">
        <div class="status-grid">
          <div class="status-item">
            <el-tag :type="connectionStatus.health ? 'success' : 'danger'">
              健康检查: {{ connectionStatus.health ? '✅ 正常' : '❌ 异常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <el-tag :type="connectionStatus.api ? 'success' : 'danger'">
              API接口: {{ connectionStatus.api ? '✅ 正常' : '❌ 异常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <el-tag :type="connectionStatus.database ? 'success' : 'danger'">
              数据库: {{ connectionStatus.database ? '✅ 正常' : '❌ 异常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <el-tag :type="connectionStatus.auth ? 'success' : 'danger'">
              认证系统: {{ connectionStatus.auth ? '✅ 正常' : '❌ 异常' }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { healthApi, mappingApi, statsApi, testApi } from '@/shared/services/api'
import AuthService from '@/shared/services/authService'

// 测试结果状态
const healthResult = ref(null)
const rootResult = ref(null)
const statsResult = ref(null)
const mappingsResult = ref(null)
const loginResult = ref(null)
const encryptResult = ref(null)

// 加载状态
const healthLoading = ref(false)
const rootLoading = ref(false)
const statsLoading = ref(false)
const mappingsLoading = ref(false)
const loginLoading = ref(false)
const encryptLoading = ref(false)

// 表单数据
const loginForm = reactive({
  phone: '13000000000',
  password: 'password'
})
const encryptText = ref('测试加密文本')

// 连接状态
const connectionStatus = reactive({
  health: false,
  api: false,
  database: false,
  auth: false
})

/**
 * 测试健康检查
 */
async function testHealth() {
  healthLoading.value = true
  try {
    const result = await healthApi.check()
    healthResult.value = result
    connectionStatus.health = result.success
    connectionStatus.database = result.data?.connection || false
    ElMessage.success('健康检查完成')
  } catch (error) {
    console.error('Health check failed:', error)
    connectionStatus.health = false
  } finally {
    healthLoading.value = false
  }
}

/**
 * 测试根路径
 */
async function testRoot() {
  rootLoading.value = true
  try {
    const result = await fetch('/api').then(res => res.json())
    rootResult.value = result
    connectionStatus.api = true
    ElMessage.success('根路径测试完成')
  } catch (error) {
    console.error('Root test failed:', error)
    connectionStatus.api = false
  } finally {
    rootLoading.value = false
  }
}

/**
 * 测试数据库统计
 */
async function testStats() {
  statsLoading.value = true
  try {
    const result = await statsApi.getDatabaseStats()
    statsResult.value = result
    ElMessage.success('数据库统计获取完成')
  } catch (error) {
    console.error('Stats test failed:', error)
  } finally {
    statsLoading.value = false
  }
}

/**
 * 测试映射数据
 */
async function testMappings() {
  mappingsLoading.value = true
  try {
    const result = await mappingApi.getAll()
    mappingsResult.value = result
    ElMessage.success('映射数据获取完成')
  } catch (error) {
    console.error('Mappings test failed:', error)
  } finally {
    mappingsLoading.value = false
  }
}

/**
 * 测试登录
 */
async function testLogin() {
  loginLoading.value = true
  try {
    const success = await AuthService.tempLogin(loginForm)
    const user = AuthService.getCurrentUser()
    loginResult.value = { success, user }
    connectionStatus.auth = success
    if (success) {
      ElMessage.success('登录测试成功')
    }
  } catch (error) {
    console.error('Login test failed:', error)
    connectionStatus.auth = false
  } finally {
    loginLoading.value = false
  }
}

/**
 * 测试加密
 */
async function testEncryption() {
  encryptLoading.value = true
  try {
    const result = await testApi.testEncryption(encryptText.value)
    encryptResult.value = result
    ElMessage.success('加密测试完成')
  } catch (error) {
    console.error('Encryption test failed:', error)
  } finally {
    encryptLoading.value = false
  }
}
</script>

<style scoped>
.api-test-container {
  width: 100%;
}

.test-header {
  margin-bottom: 30px;
}

.test-header h3 {
  color: #409eff;
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 600;
}

.test-header p {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.test-sections {
  display: grid;
  gap: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.test-item {
  margin-bottom: 20px;
}

.result-display {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.result-display pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  text-align: center;
}
</style>
