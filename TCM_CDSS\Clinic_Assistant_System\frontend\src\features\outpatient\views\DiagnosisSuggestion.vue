<template>
  <div class="diagnosis-suggestion-page">
    <div class="page-header">
      <h2>诊断建议</h2>
      <el-button type="text" class="close-btn" @click="$emit('close')">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <div class="page-content">
      <div class="suggestion-container">
        <div class="input-section">
          <h3>患者信息输入</h3>
          <el-form :model="patientInfo" label-width="100px" class="patient-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="主诉">
                  <el-input v-model="patientInfo.chiefComplaint" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="现病史">
                  <el-input v-model="patientInfo.presentIllness" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="既往史">
                  <el-input v-model="patientInfo.pastHistory" type="textarea" :rows="2" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="体格检查">
                  <el-input v-model="patientInfo.physicalExam" type="textarea" :rows="2" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item>
              <el-button type="primary" @click="generateSuggestion" :loading="generating">
                <el-icon><MagicStick /></el-icon>
                生成诊断建议
              </el-button>
              <el-button @click="clearForm">
                <el-icon><Delete /></el-icon>
                清空表单
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div v-if="diagnosisSuggestion" class="suggestion-result">
          <h3>诊断建议</h3>
          
          <div class="result-section">
            <h4>西医诊断建议</h4>
            <div class="diagnosis-list">
              <div
                v-for="diagnosis in diagnosisSuggestion.westernDiagnosis"
                :key="diagnosis.name"
                class="diagnosis-item"
              >
                <div class="diagnosis-header">
                  <span class="diagnosis-name">{{ diagnosis.name }}</span>
                  <el-tag :type="getConfidenceType(diagnosis.confidence)" size="small">
                    置信度: {{ diagnosis.confidence }}%
                  </el-tag>
                </div>
                <p class="diagnosis-reason">{{ diagnosis.reason }}</p>
                <div class="supporting-evidence">
                  <span class="evidence-label">支持证据：</span>
                  <span class="evidence-text">{{ diagnosis.evidence }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="result-section">
            <h4>中医诊断建议</h4>
            <div class="tcm-diagnosis">
              <div class="tcm-item">
                <label>病名：</label>
                <span>{{ diagnosisSuggestion.tcmDiagnosis.disease }}</span>
              </div>
              <div class="tcm-item">
                <label>证型：</label>
                <span>{{ diagnosisSuggestion.tcmDiagnosis.syndrome }}</span>
              </div>
              <div class="tcm-item">
                <label>病机：</label>
                <span>{{ diagnosisSuggestion.tcmDiagnosis.pathogenesis }}</span>
              </div>
              <div class="tcm-item">
                <label>治法：</label>
                <span>{{ diagnosisSuggestion.tcmDiagnosis.treatment }}</span>
              </div>
            </div>
          </div>
          
          <div class="result-section">
            <h4>鉴别诊断</h4>
            <div class="differential-diagnosis">
              <div
                v-for="diff in diagnosisSuggestion.differentialDiagnosis"
                :key="diff.name"
                class="diff-item"
              >
                <h5>{{ diff.name }}</h5>
                <p>{{ diff.description }}</p>
                <div class="diff-points">
                  <span class="points-label">鉴别要点：</span>
                  <span>{{ diff.differentiationPoints }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="result-section">
            <h4>进一步检查建议</h4>
            <div class="examination-suggestions">
              <div
                v-for="exam in diagnosisSuggestion.furtherExaminations"
                :key="exam.name"
                class="exam-suggestion"
              >
                <div class="exam-header">
                  <span class="exam-name">{{ exam.name }}</span>
                  <el-tag :type="getPriorityType(exam.priority)" size="small">
                    {{ exam.priority }}
                  </el-tag>
                </div>
                <p class="exam-purpose">目的：{{ exam.purpose }}</p>
              </div>
            </div>
          </div>
          
          <div class="result-section">
            <h4>治疗建议</h4>
            <div class="treatment-suggestions">
              <div class="treatment-item">
                <h5>西医治疗</h5>
                <p>{{ diagnosisSuggestion.treatmentSuggestions.western }}</p>
              </div>
              <div class="treatment-item">
                <h5>中医治疗</h5>
                <p>{{ diagnosisSuggestion.treatmentSuggestions.tcm }}</p>
              </div>
              <div class="treatment-item">
                <h5>生活指导</h5>
                <p>{{ diagnosisSuggestion.treatmentSuggestions.lifestyle }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="!diagnosisSuggestion && !generating" class="empty-state">
          <el-empty description="请输入患者信息生成诊断建议" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Close, MagicStick, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

defineEmits<{
  close: []
}>()

const generating = ref(false)
const patientInfo = ref({
  chiefComplaint: '',
  presentIllness: '',
  pastHistory: '',
  physicalExam: ''
})
const diagnosisSuggestion = ref<any>(null)

const generateSuggestion = async () => {
  if (!patientInfo.value.chiefComplaint.trim()) {
    ElMessage.warning('请输入主诉信息')
    return
  }
  
  generating.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 模拟诊断建议结果
    diagnosisSuggestion.value = {
      westernDiagnosis: [
        {
          name: '急性上呼吸道感染',
          confidence: 85,
          reason: '基于患者发热、咳嗽、鼻塞等症状',
          evidence: '发热38.5°C，咽部充血，扁桃体肿大'
        },
        {
          name: '急性支气管炎',
          confidence: 65,
          reason: '患者有持续性咳嗽，伴有痰液',
          evidence: '咳嗽3天，白痰，胸部听诊有湿啰音'
        }
      ],
      tcmDiagnosis: {
        disease: '感冒',
        syndrome: '风寒感冒',
        pathogenesis: '风寒外袭，肺气失宣，卫阳被遏',
        treatment: '疏风散寒，宣肺解表'
      },
      differentialDiagnosis: [
        {
          name: '流行性感冒',
          description: '由流感病毒引起的急性呼吸道传染病',
          differentiationPoints: '起病急骤，全身症状重，有流行病学史'
        },
        {
          name: '过敏性鼻炎',
          description: '由过敏原引起的鼻黏膜炎症',
          differentiationPoints: '鼻痒、打喷嚏为主，无发热，有过敏史'
        }
      ],
      furtherExaminations: [
        {
          name: '血常规',
          priority: '必要',
          purpose: '了解白细胞计数，判断感染类型'
        },
        {
          name: '胸部X光',
          priority: '建议',
          purpose: '排除肺炎等下呼吸道感染'
        },
        {
          name: 'C反应蛋白',
          priority: '可选',
          purpose: '评估炎症程度'
        }
      ],
      treatmentSuggestions: {
        western: '对症治疗，退热药物，止咳化痰药，抗病毒药物（如需要）',
        tcm: '荆防败毒散加减，疏风散寒，宣肺解表',
        lifestyle: '多休息，多饮水，清淡饮食，避免受凉，保持室内通风'
      }
    }
    
    ElMessage.success('诊断建议生成完成')
  } catch (error) {
    ElMessage.error('生成失败，请重试')
  } finally {
    generating.value = false
  }
}

const clearForm = () => {
  patientInfo.value = {
    chiefComplaint: '',
    presentIllness: '',
    pastHistory: '',
    physicalExam: ''
  }
  diagnosisSuggestion.value = null
}

const getConfidenceType = (confidence: number) => {
  if (confidence >= 80) return 'success'
  if (confidence >= 60) return 'warning'
  return 'danger'
}

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    '必要': 'danger',
    '建议': 'warning',
    '可选': 'info'
  }
  return typeMap[priority] || 'info'
}
</script>

<style scoped>
.diagnosis-suggestion-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--background-color);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

.page-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.close-btn:hover {
  color: var(--text-primary);
}

.page-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.suggestion-container {
  max-width: 1000px;
  margin: 0 auto;
}

.input-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.input-section h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.patient-form {
  margin-top: var(--spacing-md);
}

.suggestion-result {
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
}

.suggestion-result h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.result-section {
  margin-bottom: var(--spacing-xl);
}

.result-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.diagnosis-list,
.differential-diagnosis,
.examination-suggestions,
.treatment-suggestions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.diagnosis-item,
.diff-item,
.exam-suggestion,
.treatment-item {
  padding: var(--spacing-md);
  background: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.diagnosis-header,
.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.diagnosis-name,
.exam-name {
  font-weight: 600;
  color: var(--text-primary);
}

.diagnosis-reason,
.exam-purpose {
  margin: var(--spacing-xs) 0;
  color: var(--text-secondary);
  line-height: 1.4;
}

.supporting-evidence,
.diff-points {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

.evidence-label,
.points-label {
  font-weight: 600;
  color: var(--text-primary);
}

.evidence-text {
  color: var(--text-secondary);
}

.tcm-diagnosis {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.tcm-item {
  display: flex;
  align-items: center;
}

.tcm-item label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: var(--spacing-xs);
  min-width: 60px;
}

.tcm-item span {
  color: var(--text-secondary);
}

.diff-item h5,
.treatment-item h5 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--primary-color);
}

.diff-item p,
.treatment-item p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.4;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

@media (max-width: 768px) {
  .page-content {
    padding: var(--spacing-md);
  }
  
  .diagnosis-header,
  .exam-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}
</style>
