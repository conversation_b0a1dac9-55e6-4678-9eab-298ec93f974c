// 门诊工作站统计相关方法

import type { Patient } from '@/shared/types'

export function calculateWorkstationStats(_patients: Patient[]) {
  // 模拟门诊工作站统计数据
  const waiting = Math.floor(Math.random() * 10) + 5 // 5-15个待接诊
  const completed = Math.floor(Math.random() * 8) + 3 // 3-11个已接诊
  const notAdmitted = Math.floor(Math.random() * 5) + 2 // 2-7个未接诊
  const unpaid = Math.floor(Math.random() * 6) + 1 // 1-7个未收费
  const inProgress = Math.floor(Math.random() * 3) + 1 // 1-4个诊中
  const returnVisit = Math.floor(Math.random() * 4) + 1 // 1-5个回诊

  return {
    waiting,
    completed,
    notAdmitted,
    unpaid,
    inProgress,
    returnVisit
  }
} 