#!/usr/bin/env python3
"""
完整工作流程演示脚本
展示LangGraph多智能体协同和"先问有无，若有则追问，无则结束"的逻辑
"""

import os
import sys
import json
from dotenv import load_dotenv
import argparse

# 加载环境变量
load_dotenv()

# 动态将项目根目录加入 sys.path
ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)

from src.workflows import create_consultation_workflow, ConsultationState


def demo_complete_workflow(auto_patient: bool = False):
    """演示完整的工作流程"""
    print("=" * 80)
    print("🤖 中医诊前预问诊智能体系统 - 完整工作流程演示")
    print("基于OpenRouter + LangChain + LangGraph")
    print("=" * 80)
    
    # 创建工作流实例
    workflow = create_consultation_workflow()
    
    # 模拟患者信息
    patient_id = "demo-patient-001"
    session_id = "demo-session-001"
    
    print(f"\n📋 患者信息:")
    print(f"   - 患者ID: {patient_id}")
    print(f"   - 会话ID: {session_id}")
    print(f"   - 智能体数量: {len(workflow.agents)}")
    print(f"   - 执行顺序: {' → '.join(workflow.agent_sequence)}")
    
    # 开始问诊
    print(f"\n🚀 开始问诊会话...")
    state = workflow.start_consultation(patient_id, session_id)
    
    # 首次运行以获取初始问题
    state = workflow.run_workflow(state)

    response_index = 0
    
    # 执行工作流
    agent_summaries = []
    while not state.is_complete:
        print(f"\n{'='*60}")
        print(f"📊 当前步骤: {state.current_step}")
        print(f"📈 进度: {response_index}")
        print(f"{'='*60}")
        # 显示智能体响应
        if state.current_agent_response:
            print(f"🤖 模型提问: {state.current_agent_response}")
        else:
            print("🤖 模型正在生成最终报告...")
        # 获取患者回答
        if auto_patient:
            from src.utils.ollama_client import OllamaLLMClient
            model_name = os.getenv("LLM_MODEL", "qwen2.5:32b")
            ollama = OllamaLLMClient(model_name=model_name)
            history = state.conversation_history
            history_text = "\n".join([f"{msg['role']}: {msg['content']}" for msg in history[-5:]])
            prompt = f"[对话历史]\n{history_text}\n[医生提问]\n{state.current_agent_response}\n请模拟患者真实、简洁地回答医生的问题。"
            patient_response = ollama.invoke(prompt)
            if hasattr(patient_response, 'content'):
                patient_response = patient_response.content
            print(f"👤 模拟患者回答: {patient_response}")
        else:
            patient_response = input("👤 请输入患者回答: ")
        # 处理患者回答
        state = workflow.process_patient_response(state, patient_response)
        response_index += 1
        # 运行工作流
        state = workflow.run_workflow(state)
        # 显示收集的数据
        current_agent_data = state.collected_data.get(f"{state.current_step}_data", {})
        if current_agent_data:
            print(f"📋 收集的数据:")
            print(f"   - 提取数据: {current_agent_data.get('extracted_data', {})}")
            print(f"   - 实体: {current_agent_data.get('entities', [])}")
            print(f"   - 置信度: {current_agent_data.get('confidence', 0)}")
            print(f"   - 是否完成: {current_agent_data.get('is_complete', False)}")
            if current_agent_data.get('is_complete') and current_agent_data.get('summary'):
                print(f"📝 智能体结论: {current_agent_data['summary']}")
                agent_summaries.append(current_agent_data['summary'])
    
    # 显示最终结果
    print(f"\n{'='*80}")
    print("🎉 问诊完成！")
    print(f"{'='*80}")
    print("\n📝 各智能体结论报告：")
    for summary in agent_summaries:
        print(f"- {summary}")
    
    if state.medical_record:
        print(f"\n📋 生成的中医病历:")
        print(json.dumps(state.medical_record, indent=2, ensure_ascii=False))
    
    print(f"\n📊 会话统计:")
    print(f"   - 总对话轮次: {len(state.conversation_history)}")
    print(f"   - 智能体响应: {len([msg for msg in state.conversation_history if msg['role'] == 'agent'])}")
    print(f"   - 患者回答: {len([msg for msg in state.conversation_history if msg['role'] == 'patient'])}")
    print(f"   - 收集数据项: {len(state.collected_data)}")
    
    return state


def demo_agent_details():
    """演示智能体详细信息"""
    print(f"\n{'='*80}")
    print("🔍 智能体详细信息")
    print(f"{'='*80}")
    
    workflow = create_consultation_workflow()
    
    for name, agent in workflow.agents.items():
        print(f"\n🤖 {agent.name}")
        print(f"   - 类型: {agent.agent_type}")
        print(f"   - 描述: {agent.description}")
        
        # 显示询问模板
        context = {}
        has_question = agent.ask_has_or_not(context)
        print(f"   - 询问模板: {has_question}")
        
        # 显示追问模板
        has_result = {"has": True, "confidence": 0.9}
        details_question = agent.ask_details_if_has(context, has_result)
        print(f"   - 追问模板: {details_question[:100]}...")


def demo_workflow_logic():
    """演示工作流逻辑"""
    print(f"\n{'='*80}")
    print("🔄 工作流逻辑说明")
    print(f"{'='*80}")
    
    print("""
    📋 LangGraph工作流架构:
    
    1️⃣ 主控节点 (Controller)
       - 决定下一步执行哪个智能体
       - 检查当前智能体是否完成
       - 管理智能体执行顺序
    
    2️⃣ 智能体节点 (Agent Nodes)
       - 执行具体的问诊逻辑
       - 遵循"先问有无，若有则追问，无则结束"
       - 提取结构化数据和医疗实体
    
    3️⃣ 状态管理 (State Management)
       - 维护会话状态
       - 存储收集的数据
       - 管理对话历史
    
    4️⃣ 条件路由 (Conditional Routing)
       - 根据状态决定下一步
       - 支持动态智能体切换
       - 处理异常情况
    
    🎯 核心优势:
    - 标准化流程，易于理解和维护
    - 灵活的状态管理
    - 支持复杂的多智能体协同
    - 可扩展的架构设计
    """)


def main():
    """主函数"""
    print("🤖 中医诊前预问诊智能体系统 - 工作流程演示")
    print("基于OpenRouter + LangChain + LangGraph")
    print("统一逻辑：先问有无，若有则追问，无则结束")
    
    # 检查环境变量
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key or api_key == "your-openrouter-api-key-here":
        print("\n❌ 请设置OPENROUTER_API_KEY环境变量")
        print("在.env文件中设置：OPENROUTER_API_KEY=your-actual-api-key")
        return
    
    try:
        parser = argparse.ArgumentParser()
        parser.add_argument('--auto', action='store_true', help='自动模拟患者输入')
        parser.add_argument('--manual', action='store_true', default=False, help='手动输入患者回答')
        
        parser.add_argument('--backend', choices=['ollama', 'openrouter'], default='openrouter', help='选择大模型后端，默认为openrouter')
        parser.add_argument('--model', type=str, default='qwen/qwen3-32b:free', help='指定大模型名称，默认为qwen/qwen3-32b:free')
        args = parser.parse_args()
        auto_patient = args.auto and not args.manual

        if args.backend:
            os.environ['LLM_BACKEND'] = args.backend
        if args.model:
            os.environ['LLM_MODEL'] = args.model
        
        # 运行演示
        demo_agent_details()
        demo_workflow_logic()
        demo_complete_workflow(auto_patient=auto_patient)
        
        print("\n🎉 演示完成！")
        print("\n💡 提示:")
        print("- 这是基于OpenRouter的完整工作流程演示")
        print("- 展示了LangGraph多智能体协同的能力")
        print("- 所有智能体都遵循统一的标准化流程")
        print("- 支持灵活的状态管理和条件路由")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        print("请检查OpenRouter API密钥是否正确设置")


if __name__ == "__main__":
    main() 