# 中医诊所助手系统后端 - Python依赖
# TCM Clinic Assistant System Backend - Python Dependencies

# FastAPI框架和相关依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库相关
sqlalchemy==2.0.23
aiosqlite==0.19.0
# sqlite3 是Python内置模块，无需安装

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP客户端
httpx==0.25.2
requests==2.31.0

# 日期时间处理
python-dateutil==2.8.2

# 环境变量管理
python-dotenv==1.0.0

# 日志记录
loguru==0.7.2

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# 代码质量工具
black==23.11.0
flake8==6.1.0
mypy==1.7.1
isort==5.12.0

# 开发工具
pre-commit==3.6.0

# 文件处理
aiofiles==23.2.1

# 任务调度
apscheduler==3.10.4

# 数据处理
pandas==2.1.4
numpy==1.25.2

# 加密工具
cryptography==41.0.8

# CORS支持
fastapi-cors==0.0.6

# 限流
slowapi==0.1.9

# 健康检查
fastapi-health==0.4.0
