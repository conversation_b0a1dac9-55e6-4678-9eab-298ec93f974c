<template>
  <div class="billing-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">划价收费</h1>
      <div class="add-button" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
      </div>
    </header>
    <div class="search-section">
      <div class="search-left">
        <div class="search-container">
          <el-icon class="search-icon"><Search /></el-icon>
          <input
            v-model="searchQuery"
            type="text"
            class="search-input"
            placeholder="搜索患者姓名或手机号"
            @input="handleSearch"
          />
        </div>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
      <div class="filter-right">
        <div class="filter-tabs">
          <div
            v-for="tab in filterTabs"
            :key="tab.value"
            :class="['filter-tab', { active: currentFilter === tab.value }]"
            @click="currentFilter = tab.value"
          >
            {{ tab.label }}
          </div>
        </div>
      </div>
    </div>
    <div class="billing-list">
      <div
        v-for="item in filteredBillingItems"
        :key="item.id"
        class="billing-item"
        @click="viewBillingDetail(item)"
      >
        <div class="item-header">
          <div class="patient-info">
            <div class="patient-name">{{ item.patientName }}</div>
            <div class="patient-phone">{{ item.patientPhone }}</div>
          </div>
          <div class="status-badge" :class="item.status">
            {{ getStatusText(item.status) }}
          </div>
        </div>
        
        <div class="item-content">
          <div class="service-info">
            <div class="service-name">{{ item.serviceName }}</div>
            <div class="service-detail">{{ item.serviceDetail }}</div>
          </div>
          <div class="amount-info">
            <div class="amount">¥{{ item.amount.toFixed(2) }}</div>
            <div class="date">{{ formatDate(item.createTime) }}</div>
          </div>
        </div>

        <div class="item-actions">
          <el-button
            v-if="item.status === 'pending'"
            type="primary"
            size="small"
            @click.stop="processPayment(item)"
          >
            收费
          </el-button>
          <el-button
            v-if="item.status === 'paid'"
            type="success"
            size="small"
            @click.stop="printReceipt(item)"
          >
            打印
          </el-button>
          <el-button
            type="info"
            size="small"
            @click.stop="viewDetail(item)"
          >
            详情
          </el-button>
        </div>
      </div>
    </div>
    <div v-if="filteredBillingItems.length === 0" class="empty-state">
      <el-icon size="64" color="#C7C7CC"><Money /></el-icon>
      <p>暂无收费项目</p>
    </div>
    <el-dialog
      v-model="showAddDialog"
      title="新增收费项目"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="billingFormRef"
        :model="billingForm"
        :rules="billingRules"
        label-width="100px"
      >
        <el-form-item label="患者姓名" prop="patientName">
          <el-input v-model="billingForm.patientName" placeholder="请输入患者姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="patientPhone">
          <el-input v-model="billingForm.patientPhone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="服务项目" prop="serviceName">
          <el-select v-model="billingForm.serviceName" placeholder="请选择服务项目" style="width: 100%">
            <el-option
              v-for="service in serviceOptions"
              :key="service.value"
              :label="service.label"
              :value="service.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务详情" prop="serviceDetail">
          <el-input
            v-model="billingForm.serviceDetail"
            type="textarea"
            :rows="3"
            placeholder="请描述服务详情..."
          />
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input-number
            v-model="billingForm.amount"
            :min="0"
            :precision="2"
            :step="0.01"
            style="width: 100%"
            placeholder="请输入金额"
          />
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            v-model="billingForm.note"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息..."
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveBillingItem">保存</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDetailDialog"
      title="收费详情"
      width="600px"
    >
      <div v-if="selectedItem" class="detail-content">
        <div class="detail-section">
          <h4>患者信息</h4>
          <div class="detail-row">
            <span class="label">姓名：</span>
            <span>{{ selectedItem.patientName }}</span>
          </div>
          <div class="detail-row">
            <span class="label">手机：</span>
            <span>{{ selectedItem.patientPhone }}</span>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>服务信息</h4>
          <div class="detail-row">
            <span class="label">服务项目：</span>
            <span>{{ selectedItem.serviceName }}</span>
          </div>
          <div class="detail-row">
            <span class="label">服务详情：</span>
            <span>{{ selectedItem.serviceDetail }}</span>
          </div>
          <div class="detail-row">
            <span class="label">金额：</span>
            <span class="amount">¥{{ selectedItem.amount.toFixed(2) }}</span>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>时间信息</h4>
          <div class="detail-row">
            <span class="label">创建时间：</span>
            <span>{{ formatDate(selectedItem.createTime) }}</span>
          </div>
          <div v-if="selectedItem.payTime" class="detail-row">
            <span class="label">支付时间：</span>
            <span>{{ formatDate(selectedItem.payTime) }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Plus,
  Search,
  Money
} from '@element-plus/icons-vue'

const router = useRouter()

// 搜索相关
const searchQuery = ref('')
const currentFilter = ref('all')

// 筛选标签
const filterTabs = [
  { label: '全部', value: 'all' },
  { label: '待收费', value: 'pending' },
  { label: '已收费', value: 'paid' },
  { label: '已取消', value: 'cancelled' }
]

// 服务项目选项
const serviceOptions = [
  { label: '中医诊疗', value: '中医诊疗' },
  { label: '中药处方', value: '中药处方' },
  { label: '针灸治疗', value: '针灸治疗' },
  { label: '推拿按摩', value: '推拿按摩' },
  { label: '拔罐治疗', value: '拔罐治疗' },
  { label: '艾灸治疗', value: '艾灸治疗' }
]

// 模拟收费数据
const billingItems = ref([
  {
    id: 1,
    patientName: '李小明',
    patientPhone: '138****1234',
    serviceName: '中医诊疗',
    serviceDetail: '内科诊疗，开具中药处方',
    amount: 120.00,
    status: 'pending',
    createTime: new Date('2024-01-15 09:30:00'),
    payTime: null,
    note: ''
  },
  {
    id: 2,
    patientName: '王小红',
    patientPhone: '139****5678',
    serviceName: '中药处方',
    serviceDetail: '逍遥散加减，7剂',
    amount: 85.50,
    status: 'paid',
    createTime: new Date('2024-01-15 10:15:00'),
    payTime: new Date('2024-01-15 10:20:00'),
    note: ''
  },
  {
    id: 3,
    patientName: '张三',
    patientPhone: '137****9012',
    serviceName: '针灸治疗',
    serviceDetail: '颈椎病针灸治疗，30分钟',
    amount: 150.00,
    status: 'pending',
    createTime: new Date('2024-01-15 11:00:00'),
    payTime: null,
    note: ''
  }
])

// 过滤后的收费项目
const filteredBillingItems = computed(() => {
  let filtered = billingItems.value

  // 按状态筛选
  if (currentFilter.value !== 'all') {
    filtered = filtered.filter(item => item.status === currentFilter.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value) {
    filtered = filtered.filter(item =>
      item.patientName.includes(searchQuery.value) ||
      item.patientPhone.includes(searchQuery.value)
    )
  }

  return filtered
})

// 弹窗控制
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const selectedItem = ref(null)

// 表单数据
const billingFormRef = ref()
const billingForm = ref({
  patientName: '',
  patientPhone: '',
  serviceName: '',
  serviceDetail: '',
  amount: 0,
  note: ''
})

const billingRules = {
  patientName: [
    { required: true, message: '请输入患者姓名', trigger: 'blur' }
  ],
  patientPhone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  serviceName: [
    { required: true, message: '请选择服务项目', trigger: 'change' }
  ],
  serviceDetail: [
    { required: true, message: '请输入服务详情', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额不能小于0', trigger: 'blur' }
  ]
}

// 返回功能中心
const goBack = () => router.push('/dashboard')

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待收费',
    paid: '已收费',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (date: Date) => {
  if (!date) return ''
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查看收费详情
const viewBillingDetail = (item: any) => {
  selectedItem.value = item
  showDetailDialog.value = true
}

// 处理收费
const processPayment = (item: any) => {
  item.status = 'paid'
  item.payTime = new Date()
  ElMessage.success('收费成功')
}

// 打印收据
const printReceipt = (item: any) => {
  ElMessage.info('打印功能开发中...')
}

// 查看详情
const viewDetail = (item: any) => {
  selectedItem.value = item
  showDetailDialog.value = true
}

// 保存收费项目
const saveBillingItem = async () => {
  if (!billingFormRef.value) return

  try {
    await billingFormRef.value.validate()
    
    const newItem = {
      id: Date.now(),
      ...billingForm.value,
      status: 'pending',
      createTime: new Date(),
      payTime: null
    }
    
    billingItems.value.unshift(newItem)
    showAddDialog.value = false
    
    // 重置表单
    billingForm.value = {
      patientName: '',
      patientPhone: '',
      serviceName: '',
      serviceDetail: '',
      amount: 0,
      note: ''
    }
    
    ElMessage.success('收费项目创建成功')
  } catch (error) {
    ElMessage.error('请检查表单信息')
  }
}
</script>

<style scoped>
.billing-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}
.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}
.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}
.back-button:hover {
  color: var(--primary-dark);
}
.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}
.add-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: white;
}
.add-button:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}
.search-section {
  padding: var(--spacing-sm) var(--spacing-xl);
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}
.search-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.search-container {
  position: relative;
  width: 300px;
}
.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}
.search-input {
  width: 100%;
  height: 36px;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 44px;
  border: 1px solid var(--border-color);
  border-radius: 18px;
  background: var(--background-color);
  font-size: var(--font-size-md);
  outline: none;
  transition: all var(--transition-fast);
}
.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}
.search-input::placeholder {
  color: var(--text-secondary);
}
.filter-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
.filter-tabs {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}
.filter-tab {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.filter-tab:hover {
  color: var(--primary-color);
}
.filter-tab.active {
  background: var(--primary-color);
  color: white;
}
.billing-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
}
.billing-item {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-fast);
}
.billing-item:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}
.patient-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.patient-name {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}
.patient-phone {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}
.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
}
.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}
.status-badge.paid {
  background: #d4edda;
  color: #155724;
}
.status-badge.cancelled {
  background: #f8d7da;
  color: #721c24;
}
.item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}
.service-info {
  flex: 1;
}
.service-name {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}
.service-detail {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}
.amount-info {
  text-align: right;
}
.amount {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}
.date {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}
.item-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  gap: var(--spacing-md);
}
.empty-state p {
  font-size: var(--font-size-md);
  margin: 0;
}
.detail-content {
  padding: var(--spacing-md);
}
.detail-section {
  margin-bottom: var(--spacing-lg);
}
.detail-section h4 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}
.detail-row {
  display: flex;
  margin-bottom: var(--spacing-sm);
}
.detail-row .label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 80px;
}
.detail-row .amount {
  color: var(--primary-color);
  font-weight: 600;
}
</style> 