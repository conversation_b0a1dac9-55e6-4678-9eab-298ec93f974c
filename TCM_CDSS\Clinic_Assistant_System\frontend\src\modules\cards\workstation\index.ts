// 门诊工作站卡片模块
import type { CardModule } from '../index'
import { ref, computed } from 'vue'

// 门诊工作站统计数据
export const workstationStats = ref({
  waiting: 8,
  inProgress: 1,
  completed: 12,
  notAdmitted: 3,
  unpaid: 2,
  returnVisit: 1,
  todayIncome: 5680,
  avgDuration: 25
})

// 门诊工作站卡片信息计算
export const workstationCardInfo = computed(() => {
  return `${workstationStats.value.waiting} 个待接诊`
})

// 门诊工作站卡片配置
export const workstationCard: CardModule = {
  key: 'workstation',
  name: '门诊工作站',
  route: '/outpatient-workstation',
  icon: 'Document',
  color: '#5856D6',
  info: () => workstationCardInfo.value,
  hasDetailStats: true,
  statsData: workstationStats,
  permission: 'workstation_access'
}

// 门诊工作站详细统计数据获取函数
export const getWorkstationDetailStats = () => {
  return {
    waiting: workstationStats.value.waiting,
    inProgress: workstationStats.value.inProgress,
    completed: workstationStats.value.completed,
    notAdmitted: workstationStats.value.notAdmitted,
    unpaid: workstationStats.value.unpaid,
    returnVisit: workstationStats.value.returnVisit,
    todayIncome: workstationStats.value.todayIncome,
    avgDuration: workstationStats.value.avgDuration
  }
}

// 更新门诊工作站统计数据
export const updateWorkstationStats = (newStats: any) => {
  workstationStats.value = { ...workstationStats.value, ...newStats }
}

// 从患者数据计算门诊工作站统计
export const calculateWorkstationStats = (patients: any[]) => {
  const today = new Date().toISOString().split('T')[0]
  const todayPatients = patients.filter(p => p.nextAppointment === today)
  
  const waiting = Math.max(0, todayPatients.length - 5)
  const inProgress = Math.min(1, todayPatients.length)
  const completed = Math.floor(todayPatients.length * 0.6)
  const notAdmitted = Math.floor(todayPatients.length * 0.1)
  const unpaid = Math.floor(todayPatients.length * 0.08)
  const returnVisit = Math.floor(todayPatients.length * 0.05)
  const todayIncome = completed * 150 + Math.floor(Math.random() * 1000)
  const avgDuration = 20 + Math.floor(Math.random() * 15)

  updateWorkstationStats({
    waiting,
    inProgress,
    completed,
    notAdmitted,
    unpaid,
    returnVisit,
    todayIncome,
    avgDuration
  })
}
