<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医智能体预问诊演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .back-button {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.05);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            padding: 30px;
        }
        
        .sidebar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .main-area {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }
        
        .agent-status {
            margin-bottom: 20px;
        }
        
        .agent-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .agent-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .agent-card.active {
            border-color: #4CAF50;
            background: #f1f8e9;
        }
        
        .agent-card.completed {
            border-color: #2196F3;
            background: #e3f2fd;
        }
        
        .agent-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .agent-description {
            font-size: 0.9em;
            color: #666;
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-waiting {
            background: #f5f5f5;
            color: #666;
        }
        
        .status-active {
            background: #4CAF50;
            color: white;
        }
        
        .status-completed {
            background: #2196F3;
            color: white;
        }
        
        .chat-area {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: #fafafa;
            margin-bottom: 20px;
        }
        
        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .message.user {
            background: #e3f2fd;
            margin-left: auto;
            text-align: right;
        }
        
        .message.assistant {
            background: #f1f8e9;
            margin-right: auto;
        }
        
        .message.system {
            background: #fff3e0;
            margin: 0 auto;
            text-align: center;
            max-width: 90%;
        }
        
        .message-header {
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .message-content {
            line-height: 1.6;
        }
        
        .input-area {
            display: flex;
            gap: 10px;
        }
        
        .input-area input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            transition: width 0.3s ease;
        }
        
        .demo-controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .demo-controls button {
            margin: 0 10px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-button">← 返回主页</a>
            <h1>🤖 中医智能体预问诊演示</h1>
            <p>体验多智能体协同的中医问诊流程</p>
        </div>
        
        <div class="content">
            <!-- 侧边栏 - 智能体状态 -->
            <div class="sidebar">
                <h3>🔧 智能体状态</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <div id="progressText">准备开始...</div>
                
                <div class="agent-status" id="agentStatus">
                    <div class="agent-card" data-agent="supervisor">
                        <div class="agent-name">
                            🎯 监督智能体
                            <span class="status-badge status-waiting">等待</span>
                        </div>
                        <div class="agent-description">协调整个问诊流程</div>
                    </div>
                    
                    <div class="agent-card" data-agent="chief_complaint">
                        <div class="agent-name">
                            🗣️ 主诉采集智能体
                            <span class="status-badge status-waiting">等待</span>
                        </div>
                        <div class="agent-description">采集患者主要症状</div>
                    </div>
                    
                    <div class="agent-card" data-agent="present_illness">
                        <div class="agent-name">
                            📝 现病史采集智能体
                            <span class="status-badge status-waiting">等待</span>
                        </div>
                        <div class="agent-description">详细了解现病史</div>
                    </div>
                    
                    <div class="agent-card" data-agent="medical_history">
                        <div class="agent-name">
                            📋 既往史采集智能体
                            <span class="status-badge status-waiting">等待</span>
                        </div>
                        <div class="agent-description">了解既往病史</div>
                    </div>
                    
                    <div class="agent-card" data-agent="family_history">
                        <div class="agent-name">
                            👨‍👩‍👧‍👦 家族史采集智能体
                            <span class="status-badge status-waiting">等待</span>
                        </div>
                        <div class="agent-description">了解家族病史</div>
                    </div>
                </div>
                
                <div class="demo-controls">
                    <button class="btn" onclick="startDemo()">开始演示</button>
                    <button class="btn" onclick="resetDemo()">重置</button>
                </div>
            </div>
            
            <!-- 主区域 - 对话界面 -->
            <div class="main-area">
                <h3>💬 问诊对话</h3>
                
                <div class="chat-area" id="chatArea">
                    <div class="message system">
                        <div class="message-header">系统提示</div>
                        <div class="message-content">
                            欢迎使用中医智能体预问诊系统！<br>
                            点击"开始演示"体验完整的问诊流程。
                        </div>
                    </div>
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <div>AI正在思考中...</div>
                </div>
                
                <div class="input-area">
                    <input type="text" id="messageInput" placeholder="输入您的症状或回答..." 
                           onkeypress="handleKeyPress(event)" disabled>
                    <button class="btn" onclick="sendMessage()" id="sendBtn" disabled>发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let currentSession = null;
        let demoStep = 0;
        let isDemo = false;
        
        // 演示对话流程
        const demoFlow = [
            "医生，我最近身体不太舒服",
            "主要是头痛，已经持续三天了，还有发热",
            "头痛是持续性的，主要在太阳穴附近，发热大概38度左右",
            "有时候感觉冷，有时候感觉热，寒热交替",
            "出汗比较多，特别是晚上睡觉的时候会盗汗",
            "除了头痛，身体其他地方没有明显疼痛",
            "睡眠质量不好，经常做梦，醒来还是很累",
            "食欲一般，不太想吃油腻的东西，口渴但不想喝太多水",
            "大便基本正常，小便有点黄，次数正常",
            "以前身体还可以，没有什么大病，就是偶尔感冒",
            "家里人身体都还好，父母有高血压，其他没什么特殊的"
        ];
        
        // 智能体映射
        const agentMapping = {
            0: 'supervisor',
            1: 'chief_complaint',
            2: 'chief_complaint', 
            3: 'chief_complaint',
            4: 'present_illness',
            5: 'present_illness',
            6: 'present_illness',
            7: 'present_illness',
            8: 'present_illness',
            9: 'medical_history',
            10: 'family_history'
        };
        
        // 开始演示
        async function startDemo() {
            isDemo = true;
            demoStep = 0;
            
            // 启用输入
            document.getElementById('messageInput').disabled = false;
            document.getElementById('sendBtn').disabled = false;
            
            // 开始问诊
            try {
                const response = await fetch(`${API_BASE}/inquiry/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        patient_name: "张三",
                        patient_age: 35,
                        patient_gender: "男"
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentSession = data.session_id;
                    
                    addMessage('system', `问诊开始！会话ID: ${currentSession}`);
                    addMessage('assistant', data.message);
                    
                    updateAgentStatus('supervisor', 'active');
                    updateProgress(5);
                    
                    // 自动开始第一轮对话
                    setTimeout(() => {
                        autoSendMessage();
                    }, 2000);
                }
            } catch (error) {
                addMessage('system', `启动失败: ${error.message}`);
            }
        }
        
        // 自动发送消息（演示模式）
        async function autoSendMessage() {
            if (!isDemo || demoStep >= demoFlow.length) return;
            
            const message = demoFlow[demoStep];
            const currentAgent = agentMapping[demoStep];
            
            // 更新智能体状态
            updateAgentStatus(currentAgent, 'active');
            
            // 发送消息
            addMessage('user', message);
            await sendChatMessage(message);
            
            demoStep++;
            updateProgress((demoStep / demoFlow.length) * 100);
            
            // 继续下一轮
            if (demoStep < demoFlow.length) {
                setTimeout(() => {
                    autoSendMessage();
                }, 3000);
            } else {
                addMessage('system', '🎉 演示完成！所有智能体协同工作完毕。');
                updateProgress(100);
            }
        }
        
        // 发送聊天消息
        async function sendChatMessage(message) {
            showLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addMessage('assistant', data.response);
                } else {
                    throw new Error('发送失败');
                }
            } catch (error) {
                addMessage('system', `错误: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }
        
        // 手动发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            input.value = '';
            addMessage('user', message);
            await sendChatMessage(message);
        }
        
        // 添加消息到聊天区域
        function addMessage(type, content) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let header = '';
            switch(type) {
                case 'user':
                    header = '👤 患者';
                    break;
                case 'assistant':
                    header = '🤖 AI助手';
                    break;
                case 'system':
                    header = '📢 系统';
                    break;
            }
            
            messageDiv.innerHTML = `
                <div class="message-header">${header}</div>
                <div class="message-content">${content}</div>
            `;
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        // 更新智能体状态
        function updateAgentStatus(agentName, status) {
            const agentCard = document.querySelector(`[data-agent="${agentName}"]`);
            if (!agentCard) return;
            
            // 清除所有状态类
            agentCard.classList.remove('active', 'completed');
            
            // 更新状态徽章
            const badge = agentCard.querySelector('.status-badge');
            badge.classList.remove('status-waiting', 'status-active', 'status-completed');
            
            switch(status) {
                case 'active':
                    agentCard.classList.add('active');
                    badge.classList.add('status-active');
                    badge.textContent = '工作中';
                    break;
                case 'completed':
                    agentCard.classList.add('completed');
                    badge.classList.add('status-completed');
                    badge.textContent = '已完成';
                    break;
                default:
                    badge.classList.add('status-waiting');
                    badge.textContent = '等待';
            }
        }
        
        // 更新进度条
        function updateProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `进度: ${Math.round(percentage)}%`;
        }
        
        // 显示/隐藏加载状态
        function showLoading(show) {
            const loading = document.getElementById('loading');
            loading.style.display = show ? 'block' : 'none';
        }
        
        // 重置演示
        function resetDemo() {
            isDemo = false;
            demoStep = 0;
            currentSession = null;
            
            // 清空聊天区域
            const chatArea = document.getElementById('chatArea');
            chatArea.innerHTML = `
                <div class="message system">
                    <div class="message-header">系统提示</div>
                    <div class="message-content">
                        欢迎使用中医智能体预问诊系统！<br>
                        点击"开始演示"体验完整的问诊流程。
                    </div>
                </div>
            `;
            
            // 重置智能体状态
            const agentCards = document.querySelectorAll('.agent-card');
            agentCards.forEach(card => {
                updateAgentStatus(card.dataset.agent, 'waiting');
            });
            
            // 重置进度
            updateProgress(0);
            
            // 禁用输入
            document.getElementById('messageInput').disabled = true;
            document.getElementById('sendBtn').disabled = true;
        }
        
        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // 页面加载时初始化
        window.onload = function() {
            console.log('智能体演示页面加载完成');
        };
    </script>
</body>
</html>
