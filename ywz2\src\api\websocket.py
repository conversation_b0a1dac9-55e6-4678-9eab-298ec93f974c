from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import HTMLResponse
import json
from datetime import datetime

from src.workflows import create_consultation_workflow, ConsultationState

websocket_router = APIRouter()

# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        self.active_connections[session_id] = websocket
    
    def disconnect(self, session_id: str):
        if session_id in self.active_connections:
            del self.active_connections[session_id]
    
    async def send_personal_message(self, message: str, session_id: str):
        if session_id in self.active_connections:
            await self.active_connections[session_id].send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections.values():
            await connection.send_text(message)

manager = ConnectionManager()

# 全局工作流实例
workflow = create_consultation_workflow()

# 内存存储会话状态（生产环境应使用Redis）
session_storage: Dict[str, ConsultationState] = {}


@websocket_router.websocket("/ws/consultations/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket问诊接口"""
    await manager.connect(websocket, session_id)
    
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理消息
            response = await process_websocket_message(session_id, message_data)
            
            # 发送响应
            await manager.send_personal_message(json.dumps(response), session_id)
            
    except WebSocketDisconnect:
        manager.disconnect(session_id)
    except Exception as e:
        error_response = {
            "type": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(json.dumps(error_response), session_id)
        manager.disconnect(session_id)


async def process_websocket_message(session_id: str, message_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理WebSocket消息"""
    message_type = message_data.get("type", "message")
    
    if message_type == "start_consultation":
        return await start_consultation(session_id, message_data)
    elif message_type == "send_message":
        return await send_message(session_id, message_data)
    elif message_type == "get_status":
        return await get_status(session_id)
    elif message_type == "get_medical_record":
        return await get_medical_record(session_id)
    else:
        return {
            "type": "error",
            "message": "未知的消息类型",
            "timestamp": datetime.now().isoformat()
        }


async def start_consultation(session_id: str, message_data: Dict[str, Any]) -> Dict[str, Any]:
    """开始问诊会话"""
    try:
        patient_id = message_data.get("patient_id", session_id)
        
        # 创建初始状态
        initial_state = workflow.start_consultation(
            patient_id=patient_id,
            session_id=session_id
        )
        
        # 运行工作流获取第一个智能体的响应
        state = workflow.run_workflow(initial_state)
        
        # 存储会话状态
        session_storage[session_id] = state
        
        return {
            "type": "consultation_started",
            "session_id": session_id,
            "patient_id": patient_id,
            "current_step": state.current_step,
            "agent_response": state.current_agent_response,
            "is_complete": state.is_complete,
            "progress": 0.0,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "type": "error",
            "message": f"开始问诊失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def send_message(session_id: str, message_data: Dict[str, Any]) -> Dict[str, Any]:
    """发送消息"""
    try:
        if session_id not in session_storage:
            return {
                "type": "error",
                "message": "会话不存在",
                "timestamp": datetime.now().isoformat()
            }
        
        message = message_data.get("message", "")
        if not message:
            return {
                "type": "error",
                "message": "消息内容不能为空",
                "timestamp": datetime.now().isoformat()
            }
        
        state = session_storage[session_id]
        
        # 处理患者回答
        state = workflow.process_patient_response(state, message)
        
        # 运行工作流
        state = workflow.run_workflow(state)
        
        # 更新存储
        session_storage[session_id] = state
        
        # 计算进度
        total_steps = len(workflow.agent_sequence)
        current_index = workflow.agent_sequence.index(state.current_step) if state.current_step in workflow.agent_sequence else 0
        progress = (current_index / total_steps) * 100 if not state.is_complete else 100.0
        
        return {
            "type": "message_response",
            "session_id": session_id,
            "current_step": state.current_step,
            "agent_response": state.current_agent_response,
            "is_complete": state.is_complete,
            "progress": progress,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "type": "error",
            "message": f"处理消息失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def get_status(session_id: str) -> Dict[str, Any]:
    """获取会话状态"""
    try:
        if session_id not in session_storage:
            return {
                "type": "error",
                "message": "会话不存在",
                "timestamp": datetime.now().isoformat()
            }
        
        state = session_storage[session_id]
        
        # 计算进度
        total_steps = len(workflow.agent_sequence)
        current_index = workflow.agent_sequence.index(state.current_step) if state.current_step in workflow.agent_sequence else 0
        progress = (current_index / total_steps) * 100 if not state.is_complete else 100.0
        
        return {
            "type": "status",
            "session_id": session_id,
            "current_step": state.current_step,
            "agent_response": state.current_agent_response,
            "is_complete": state.is_complete,
            "progress": progress,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "type": "error",
            "message": f"获取状态失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def get_medical_record(session_id: str) -> Dict[str, Any]:
    """获取病历"""
    try:
        if session_id not in session_storage:
            return {
                "type": "error",
                "message": "会话不存在",
                "timestamp": datetime.now().isoformat()
            }
        
        state = session_storage[session_id]
        
        if not state.is_complete or not state.medical_record:
            return {
                "type": "error",
                "message": "问诊尚未完成，无法生成病历",
                "timestamp": datetime.now().isoformat()
            }
        
        return {
            "type": "medical_record",
            "session_id": session_id,
            "record": state.medical_record,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "type": "error",
            "message": f"获取病历失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


# 简单的WebSocket测试页面
@websocket_router.get("/ws/test")
async def get_websocket_test_page():
    """获取WebSocket测试页面"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebSocket问诊测试</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 800px; margin: 0 auto; }
            .chat-box { border: 1px solid #ccc; height: 400px; overflow-y: scroll; padding: 10px; margin: 10px 0; }
            .input-box { display: flex; margin: 10px 0; }
            .input-box input { flex: 1; padding: 10px; margin-right: 10px; }
            .input-box button { padding: 10px 20px; }
            .message { margin: 5px 0; padding: 5px; border-radius: 5px; }
            .agent { background-color: #e3f2fd; }
            .patient { background-color: #f3e5f5; }
            .system { background-color: #fff3e0; }
            .error { background-color: #ffebee; color: #c62828; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>WebSocket问诊测试</h1>
            <div>
                <button onclick="startConsultation()">开始问诊</button>
                <button onclick="getStatus()">获取状态</button>
                <button onclick="getMedicalRecord()">获取病历</button>
            </div>
            <div class="chat-box" id="chatBox"></div>
            <div class="input-box">
                <input type="text" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()">发送</button>
            </div>
        </div>

        <script>
            let ws = null;
            let sessionId = null;

            function connect() {
                ws = new WebSocket('ws://localhost:8000/api/v1/ws/consultations/test-session');
                
                ws.onopen = function(event) {
                    addMessage('系统', 'WebSocket连接已建立', 'system');
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleResponse(data);
                };
                
                ws.onclose = function(event) {
                    addMessage('系统', 'WebSocket连接已关闭', 'system');
                };
                
                ws.onerror = function(error) {
                    addMessage('系统', 'WebSocket错误: ' + error, 'error');
                };
            }

            function startConsultation() {
                if (!ws) {
                    connect();
                    setTimeout(startConsultation, 1000);
                    return;
                }
                
                const message = {
                    type: 'start_consultation',
                    patient_id: 'test-patient',
                    patient_name: '测试患者',
                    gender: '男',
                    age: 30
                };
                
                ws.send(JSON.stringify(message));
            }

            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                
                if (!message) return;
                
                addMessage('患者', message, 'patient');
                input.value = '';
                
                if (ws && ws.readyState === WebSocket.OPEN) {
                    const data = {
                        type: 'send_message',
                        message: message
                    };
                    ws.send(JSON.stringify(data));
                }
            }

            function getStatus() {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    const data = { type: 'get_status' };
                    ws.send(JSON.stringify(data));
                }
            }

            function getMedicalRecord() {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    const data = { type: 'get_medical_record' };
                    ws.send(JSON.stringify(data));
                }
            }

            function handleResponse(data) {
                switch(data.type) {
                    case 'consultation_started':
                        sessionId = data.session_id;
                        addMessage('系统', '问诊会话已开始', 'system');
                        if (data.agent_response) {
                            addMessage('智能体', data.agent_response, 'agent');
                        }
                        break;
                    case 'message_response':
                        if (data.agent_response) {
                            addMessage('智能体', data.agent_response, 'agent');
                        }
                        if (data.is_complete) {
                            addMessage('系统', '问诊已完成！', 'system');
                        }
                        break;
                    case 'status':
                        addMessage('系统', `当前步骤: ${data.current_step}, 进度: ${data.progress.toFixed(1)}%`, 'system');
                        break;
                    case 'medical_record':
                        addMessage('系统', '病历已生成', 'system');
                        console.log('病历数据:', data.record);
                        break;
                    case 'error':
                        addMessage('系统', '错误: ' + data.message, 'error');
                        break;
                    default:
                        addMessage('系统', '未知响应类型: ' + data.type, 'error');
                }
            }

            function addMessage(sender, message, type) {
                const chatBox = document.getElementById('chatBox');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.innerHTML = `<strong>${sender}:</strong> ${message}`;
                chatBox.appendChild(messageDiv);
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }

            // 页面加载时连接WebSocket
            window.onload = function() {
                connect();
            };
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content) 