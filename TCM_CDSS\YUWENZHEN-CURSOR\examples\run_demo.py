#!/usr/bin/env python3
"""
启动脚本 - 支持多种演示模式
"""

import os
import sys
import argparse
from dotenv import load_dotenv
from src.agents.chief_complaint_agent import ChiefComplaintAgent
from src.agents.present_illness_agent import PresentIllnessAgent
from src.agents.past_history_agent import PastHistoryAgent
from src.agents.family_history_agent import FamilyHistoryAgent
from src.agents.allergy_history_agent import AllergyHistoryAgent

# 加载环境变量
load_dotenv()

# 动态将项目根目录加入 sys.path
ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)


def run_openrouter_test():
    """运行OpenRouter集成测试"""
    print("🧪 运行OpenRouter集成测试...")
    os.system("python test_openrouter.py")


def run_agent_demo():
    """运行智能体演示"""
    print("🤖 运行智能体演示...")
    os.system("python demo_agents.py")


def run_workflow_demo():
    """运行工作流演示"""
    print("🔄 运行LangGraph工作流演示...")
    os.system("python demo_workflow.py")


def run_fastapi_server():
    """运行FastAPI服务器"""
    print("🚀 启动FastAPI服务器...")
    print("📊 访问地址:")
    print("   - API文档: http://localhost:8000/docs")
    print("   - WebSocket测试: http://localhost:8000/api/v1/ws/test")
    print("   - 健康检查: http://localhost:8000/health")
    print("\n按 Ctrl+C 停止服务器")
    
    os.system("python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload")


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查OpenRouter API密钥
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key or api_key == "your-openrouter-api-key-here":
        print("❌ 请设置OPENROUTER_API_KEY环境变量")
        print("在.env文件中设置：OPENROUTER_API_KEY=your-actual-api-key")
        return False
    
    print("✅ OpenRouter API密钥已配置")
    
    # 检查依赖
    try:
        import langchain
        import langgraph
        import fastapi
        import uvicorn
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("🤖 中医诊前预问诊智能体系统")
    print("基于OpenRouter + LangChain + LangGraph")
    print("="*60)
    print("请选择运行模式:")
    print("1. 🔍 环境检查")
    print("2. 🧪 OpenRouter集成测试")
    print("3. 🤖 智能体演示")
    print("4. 🔄 LangGraph工作流演示")
    print("5. 🚀 启动FastAPI服务器")
    print("6. 📋 显示项目信息")
    print("0. 🚪 退出")
    print("="*60)


def show_project_info():
    """显示项目信息"""
    print("\n" + "="*60)
    print("📋 项目信息")
    print("="*60)
    print("项目名称: 中医诊前预问诊智能体系统")
    print("技术栈: OpenRouter + LangChain + LangGraph + FastAPI")
    print("核心特性:")
    print("  ✅ 基于OpenRouter的统一大模型调用")
    print("  ✅ 标准化智能体架构")
    print("  ✅ 统一逻辑：先问有无，若有则追问，无则结束")
    print("  ✅ LangGraph多智能体协同工作流")
    print("  ✅ FastAPI RESTful API接口")
    print("  ✅ WebSocket实时对话支持")
    print("  ✅ Docker容器化部署")
    
    print("\n📁 项目结构:")
    print("  src/")
    print("  ├── agents/          # 智能体模块")
    print("  ├── workflows/       # LangGraph工作流")
    print("  ├── api/            # FastAPI接口")
    print("  ├── models/         # 数据模型")
    print("  ├── utils/          # 工具类")
    print("  └── config.py       # 配置管理")
    
    print("\n🚀 快速开始:")
    print("  1. 设置OpenRouter API密钥")
    print("  2. 安装依赖: pip install -r requirements.txt")
    print("  3. 运行演示: python run_demo.py")
    print("  4. 启动服务: python run_demo.py --server")
    
    print("\n📚 文档:")
    print("  - 系统设计: docs/系统设计文档.md")
    print("  - API文档: http://localhost:8000/docs")
    print("  - 项目状态: PROJECT_STATUS.md")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="中医诊前预问诊智能体系统启动脚本")
    parser.add_argument("--test", action="store_true", help="运行OpenRouter集成测试")
    parser.add_argument("--demo", action="store_true", help="运行智能体演示")
    parser.add_argument("--workflow", action="store_true", help="运行工作流演示")
    parser.add_argument("--server", action="store_true", help="启动FastAPI服务器")
    parser.add_argument("--check", action="store_true", help="检查环境配置")
    parser.add_argument("--info", action="store_true", help="显示项目信息")
    
    args = parser.parse_args()
    
    # 如果指定了参数，直接执行
    if args.test:
        run_openrouter_test()
        return
    elif args.demo:
        run_agent_demo()
        return
    elif args.workflow:
        run_workflow_demo()
        return
    elif args.server:
        run_fastapi_server()
        return
    elif args.check:
        check_environment()
        return
    elif args.info:
        show_project_info()
        return
    
    # 交互式菜单
    while True:
        show_menu()
        
        try:
            choice = input("\n请输入选择 (0-6): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                check_environment()
            elif choice == "2":
                if check_environment():
                    run_openrouter_test()
            elif choice == "3":
                if check_environment():
                    run_agent_demo()
            elif choice == "4":
                if check_environment():
                    run_workflow_demo()
            elif choice == "5":
                if check_environment():
                    run_fastapi_server()
            elif choice == "6":
                show_project_info()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


if __name__ == "__main__":
    main() 