<template>
  <div class="data-center-stats">
    <!-- 今日核心指标 -->
    <div class="stats-section">
      <h4 class="section-title">今日核心指标</h4>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">就诊人次</span>
          <span class="stat-value primary">{{ stats.todayStats.totalPatients }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">处方数量</span>
          <span class="stat-value success">{{ stats.todayStats.totalPrescriptions }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">营业收入</span>
          <span class="stat-value warning">¥{{ stats.todayStats.totalRevenue.toLocaleString() }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">AI辅助</span>
          <span class="stat-value info">{{ stats.todayStats.aiDiagnosis }}次</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">平均诊疗</span>
          <span class="stat-value secondary">{{ stats.todayStats.avgDiagnosisTime }}分钟</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">就诊次数</span>
          <span class="stat-value primary">{{ stats.todayStats.totalVisits }}</span>
        </div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="divider"></div>

    <!-- 模块数据概览 - 只显示前4个 -->
    <div class="stats-section">
      <h4 class="section-title">模块数据概览</h4>
      <div class="module-stats">
        <div class="module-item">
          <span class="module-label">患者管理</span>
          <span class="module-value">新增{{ stats.moduleStats.patients.newPatients }}人</span>
        </div>
        <div class="module-item">
          <span class="module-label">预约挂号</span>
          <span class="module-value">完成{{ stats.moduleStats.appointments.completedAppointments }}个</span>
        </div>
        <div class="module-item">
          <span class="module-label">处方开具</span>
          <span class="module-value">中药{{ stats.moduleStats.prescriptions.herbalPrescriptions }}张</span>
        </div>
        <div class="module-item">
          <span class="module-label">AI功能</span>
          <span class="module-value">准确率{{ (stats.aiStats.avgAccuracy * 100).toFixed(1) }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { dataCenterStats } from './index'

const stats = computed(() => dataCenterStats.value)
</script>

<style scoped>
.data-center-stats {
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: 160px;
  overflow-y: auto;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.stats-section {
  margin-bottom: 10px;
}

.stats-section:last-child {
  margin-bottom: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.stat-label {
  font-size: 10px;
  color: #8c8c8c;
  margin-bottom: 2px;
  text-align: center;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

.stat-value.primary { color: #1890ff; }
.stat-value.success { color: #52c41a; }
.stat-value.warning { color: #fa8c16; }
.stat-value.info { color: #722ed1; }
.stat-value.secondary { color: #8c8c8c; }

.module-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.module-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 6px;
  background: #f8f9fa;
  border-radius: 3px;
  border-left: 2px solid #722ed1;
}

.module-label {
  font-size: 10px;
  color: #595959;
  font-weight: 500;
}

.module-value {
  font-size: 10px;
  color: #722ed1;
  font-weight: 600;
}

.trend-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 4px;
  color: white;
}

.trend-label {
  font-size: 10px;
  opacity: 0.9;
  margin-bottom: 2px;
}

.trend-value {
  font-size: 11px;
  font-weight: 600;
}

.divider {
  height: 1px;
  background: #f0f0f0;
  margin: 8px 0;
}
</style>
