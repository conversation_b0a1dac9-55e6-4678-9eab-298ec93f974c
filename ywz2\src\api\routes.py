from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import uuid
from datetime import datetime

from src.workflows import create_consultation_workflow, ConsultationState

router = APIRouter(prefix="/api/v1", tags=["consultation"])

# 内存存储会话状态（生产环境应使用Redis或数据库）
session_storage: Dict[str, ConsultationState] = {}

# 全局工作流实例
workflow = create_consultation_workflow()


class CreateSessionRequest(BaseModel):
    """创建会话请求"""
    patient_id: str
    patient_name: str
    gender: str
    age: int


class SendMessageRequest(BaseModel):
    """发送消息请求"""
    message: str
    message_type: str = "text"


class SessionResponse(BaseModel):
    """会话响应"""
    session_id: str
    patient_id: str
    current_step: str
    agent_response: Optional[str] = None
    is_complete: bool
    progress: float
    created_at: str
    updated_at: str


class MedicalRecordResponse(BaseModel):
    """病历响应"""
    record_id: str
    patient_id: str
    session_id: str
    chief_complaint: Dict[str, Any]
    present_illness: Dict[str, Any]
    past_history: Dict[str, Any]
    family_history: Dict[str, Any]
    allergy_history: Dict[str, Any]
    conversation_history: list
    created_at: str


@router.post("/consultations", response_model=SessionResponse)
async def create_consultation(request: CreateSessionRequest):
    """创建问诊会话"""
    try:
        session_id = str(uuid.uuid4())
        
        # 创建初始状态
        initial_state = workflow.start_consultation(
            patient_id=request.patient_id,
            session_id=session_id
        )
        
        # 运行工作流获取第一个智能体的响应
        state = workflow.run_workflow(initial_state)
        
        # 存储会话状态
        session_storage[session_id] = state
        
        return SessionResponse(
            session_id=session_id,
            patient_id=request.patient_id,
            current_step=state.current_step,
            agent_response=state.current_agent_response,
            is_complete=state.is_complete,
            progress=0.0,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")


@router.get("/consultations/{session_id}", response_model=SessionResponse)
async def get_session(session_id: str):
    """获取会话状态"""
    if session_id not in session_storage:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    state = session_storage[session_id]
    
    # 计算进度
    total_steps = len(workflow.agent_sequence)
    current_index = workflow.agent_sequence.index(state.current_step) if state.current_step in workflow.agent_sequence else 0
    progress = (current_index / total_steps) * 100 if not state.is_complete else 100.0
    
    return SessionResponse(
        session_id=session_id,
        patient_id=state.patient_id,
        current_step=state.current_step,
        agent_response=state.current_agent_response,
        is_complete=state.is_complete,
        progress=progress,
        created_at=state.conversation_history[0]["timestamp"] if state.conversation_history else datetime.now().isoformat(),
        updated_at=state.conversation_history[-1]["timestamp"] if state.conversation_history else datetime.now().isoformat()
    )


@router.post("/consultations/{session_id}/messages", response_model=SessionResponse)
async def send_message(session_id: str, request: SendMessageRequest):
    """发送消息"""
    if session_id not in session_storage:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    try:
        state = session_storage[session_id]
        
        # 处理患者回答
        state = workflow.process_patient_response(state, request.message)
        
        # 运行工作流
        state = workflow.run_workflow(state)
        
        # 更新存储
        session_storage[session_id] = state
        
        # 计算进度
        total_steps = len(workflow.agent_sequence)
        current_index = workflow.agent_sequence.index(state.current_step) if state.current_step in workflow.agent_sequence else 0
        progress = (current_index / total_steps) * 100 if not state.is_complete else 100.0
        
        return SessionResponse(
            session_id=session_id,
            patient_id=state.patient_id,
            current_step=state.current_step,
            agent_response=state.current_agent_response,
            is_complete=state.is_complete,
            progress=progress,
            created_at=state.conversation_history[0]["timestamp"] if state.conversation_history else datetime.now().isoformat(),
            updated_at=state.conversation_history[-1]["timestamp"] if state.conversation_history else datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理消息失败: {str(e)}")


@router.get("/consultations/{session_id}/medical-record", response_model=MedicalRecordResponse)
async def get_medical_record(session_id: str):
    """获取病历"""
    if session_id not in session_storage:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    state = session_storage[session_id]
    
    if not state.is_complete or not state.medical_record:
        raise HTTPException(status_code=400, detail="问诊尚未完成，无法生成病历")
    
    record = state.medical_record
    
    return MedicalRecordResponse(
        record_id=str(uuid.uuid4()),
        patient_id=record["patient_id"],
        session_id=record["session_id"],
        chief_complaint=record["chief_complaint"],
        present_illness=record["present_illness"],
        past_history=record["past_history"],
        family_history=record["family_history"],
        allergy_history=record["allergy_history"],
        conversation_history=record["conversation_history"],
        created_at=record["created_at"]
    )


@router.get("/agents")
async def get_agents():
    """获取可用智能体列表"""
    agents_info = []
    for name, agent in workflow.agents.items():
        agents_info.append({
            "name": name,
            "display_name": agent.name,
            "description": agent.description,
            "agent_type": agent.agent_type
        })
    
    return {"agents": agents_info}


@router.post("/consultations/{session_id}/switch-agent")
async def switch_agent(session_id: str, agent_name: str):
    """手动切换智能体"""
    if session_id not in session_storage:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    if agent_name not in workflow.agents:
        raise HTTPException(status_code=400, detail="智能体不存在")
    
    state = session_storage[session_id]
    state.current_step = agent_name
    
    # 运行工作流
    state = workflow.run_workflow(state)
    session_storage[session_id] = state
    
    return {"message": f"已切换到{agent_name}智能体", "current_step": agent_name}


@router.delete("/consultations/{session_id}")
async def delete_session(session_id: str):
    """删除会话"""
    if session_id not in session_storage:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    del session_storage[session_id]
    return {"message": "会话已删除"} 