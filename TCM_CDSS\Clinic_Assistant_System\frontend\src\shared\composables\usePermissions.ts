import { computed } from 'vue'
import { useAuthStore } from '@/core/auth/stores/auth'

// 权限映射表 - 将功能模块映射到权限键
const modulePermissionMap: Record<string, string> = {
  'appointments': 'appointment_manage',
  'patients': 'patient_manage',
  'workstation': 'workstation_access',
  'billing': 'billing_manage',
  'pharmacy': 'pharmacy_manage',
  'inventory': 'inventory_manage',
  'scheduling': 'scheduling_manage',
  'staff': 'staff_manage',
  'reports': 'report_view',
  'family-doctor': 'family_doctor',
  'research-center': 'research_center',
  'app-center': 'app_center',
  'mall-management': 'mall_management',
  'data-center': 'data_center',
  'settings': 'system_settings'
}

// 默认权限配置
const defaultRolePermissions = [
  {
    key: 'admin',
    name: '系统管理员',
    permissions: {
      patient_manage: true,
      appointment_manage: true,
      workstation_access: true,
      billing_manage: true,
      pharmacy_manage: true,
      inventory_manage: true,
      scheduling_manage: true,
      staff_manage: true,
      report_view: true,
      family_doctor: true,
      research_center: true,
      app_center: true,
      mall_management: true,
      data_center: true,
      lingji_manage: true,
      system_settings: true
    }
  },
  {
    key: 'doctor',
    name: '医师',
    permissions: {
      patient_manage: true,
      appointment_manage: true,
      workstation_access: true,
      billing_manage: false,
      pharmacy_manage: false,
      inventory_manage: false,
      scheduling_manage: false,
      staff_manage: false,
      report_view: true,
      family_doctor: true,
      research_center: true,
      app_center: true,
      mall_management: false,
      data_center: true,
      lingji_manage: false,
      system_settings: false
    }
  },
  {
    key: 'nurse',
    name: '护士',
    permissions: {
      patient_manage: true,
      appointment_manage: true,
      workstation_access: false,
      billing_manage: false,
      pharmacy_manage: false,
      inventory_manage: false,
      scheduling_manage: false,
      staff_manage: false,
      report_view: false,
      family_doctor: false,
      research_center: false,
      app_center: false,
      mall_management: false,
      data_center: false,
      lingji_manage: false,
      system_settings: false
    }
  },
  {
    key: 'pharmacist',
    name: '药师',
    permissions: {
      patient_manage: false,
      appointment_manage: false,
      workstation_access: false,
      billing_manage: false,
      pharmacy_manage: true,
      inventory_manage: true,
      scheduling_manage: false,
      staff_manage: false,
      report_view: false,
      family_doctor: false,
      research_center: false,
      app_center: false,
      mall_management: false,
      data_center: false,
      lingji_manage: false,
      system_settings: false
    }
  },
  {
    key: 'receptionist',
    name: '前台',
    permissions: {
      patient_manage: true,
      appointment_manage: true,
      workstation_access: false,
      billing_manage: true,
      pharmacy_manage: false,
      inventory_manage: false,
      scheduling_manage: false,
      staff_manage: false,
      report_view: false,
      family_doctor: false,
      research_center: false,
      app_center: false,
      mall_management: false,
      data_center: false,
      lingji_manage: false,
      system_settings: false
    }
  }
]

export function usePermissions() {
  const authStore = useAuthStore()
  
  // 获取当前用户角色权限配置
  const getRolePermissions = () => {
    const savedPermissions = localStorage.getItem('rolePermissions')
    let rolePermissions = defaultRolePermissions
    
    if (savedPermissions) {
      try {
        rolePermissions = JSON.parse(savedPermissions)
      } catch (error) {
        console.error('解析权限配置失败:', error)
      }
    }
    
    return rolePermissions
  }
  
  // 获取当前用户的权限
  const getCurrentUserPermissions = () => {
    const userRole = authStore.user?.role_name || authStore.user?.role || 'nurse' // 优先使用role_name
    const rolePermissions = getRolePermissions()
    const currentRole = rolePermissions.find(role => role.key === userRole)

    return currentRole?.permissions || {}
  }
  
  // 检查是否有特定权限
  const hasPermission = (permissionKey: string) => {
    const permissions = getCurrentUserPermissions()
    return (permissions as any)[permissionKey] === true
  }
  
  // 检查是否有模块访问权限
  const hasModuleAccess = (moduleKey: string) => {
    const permissionKey = modulePermissionMap[moduleKey]
    if (!permissionKey) {
      console.warn(`未找到模块 ${moduleKey} 对应的权限配置`)
      return false
    }
    return hasPermission(permissionKey)
  }
  
  // 获取用户可访问的模块列表
  const getAccessibleModules = (allModules: any[]) => {
    return allModules.filter(module => hasModuleAccess(module.key))
  }
  
  // 当前用户权限
  const currentPermissions = computed(() => getCurrentUserPermissions())
  
  // 当前用户角色
  const currentRole = computed(() => authStore.user?.role_name || authStore.user?.role || 'nurse')
  
  return {
    hasPermission,
    hasModuleAccess,
    getAccessibleModules,
    getCurrentUserPermissions,
    getRolePermissions,
    currentPermissions,
    currentRole,
    modulePermissionMap
  }
} 