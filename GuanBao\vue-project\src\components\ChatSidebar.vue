<template>
  <el-aside width="320px" class="chat-sidebar">
    <div class="sidebar-header">
      <el-button type="primary" class="new-chat-btn" @click="newChat">
        <el-icon><Plus /></el-icon>
        新建对话
      </el-button>
    </div>

    <div class="chat-history">
      <!-- 会话历史列表（预留） -->
    </div>

    <!-- 用户信息和登出 -->
    <div class="user-section">
      <div class="user-info">
        <el-icon><User /></el-icon>
        <span class="phone-number">{{ userPhone }}</span>
      </div>
      <el-button
        type="text"
        class="logout-btn"
        @click="handleLogout"
        title="退出登录"
      >
        <el-icon><SwitchButton /></el-icon>
      </el-button>
    </div>
  </el-aside>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Plus, User, SwitchButton } from '@element-plus/icons-vue';
import { getCurrentUser } from '../utils/authApi.js';

const emit = defineEmits(['new-chat', 'logout']);

const userPhone = ref('');

// 获取用户信息
onMounted(() => {
  const userInfo = getCurrentUser();
  if (userInfo) {
    userPhone.value = userInfo.phoneNumber;
  }
});

const newChat = () => {
  emit('new-chat');
};

const handleLogout = () => {
  emit('logout');
};
</script>

<style scoped>
.chat-sidebar {
  background-color: var(--sidebar-bg, #f7f7f8);
  height: 100vh;
  padding: 1rem;
  border-right: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  margin-bottom: 1.5rem;
}

.new-chat-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 1rem;
}

.chat-history {
  flex-grow: 1;
  overflow-y: auto;
}

/* 用户信息区域 */
.user-section {
  padding: 1rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.5);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary, #6b7280);
}

.phone-number {
  font-size: 0.9rem;
  font-weight: 500;
}

.logout-btn {
  color: var(--text-secondary, #6b7280);
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s;
}

.logout-btn:hover {
  background-color: var(--border-color, #e5e7eb);
  color: var(--text-primary, #1f2937);
}
</style>