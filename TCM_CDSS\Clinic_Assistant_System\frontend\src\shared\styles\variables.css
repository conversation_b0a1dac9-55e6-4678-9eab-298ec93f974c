/**
 * CSS变量定义文件
 * 
 * 主要功能：
 * 1. 定义全局CSS变量，实现设计系统统一
 * 2. 提供iOS风格的设计规范
 * 3. 支持主题切换和样式复用
 * 
 * 变量分类：
 * - 颜色系统：主色调、辅助色、状态色、背景色、文字色
 * - 圆角：不同尺寸的圆角值
 * - 阴影：不同层级的阴影效果
 * - 间距：统一的间距规范
 * - 字体：字体大小规范
 * - 动画：过渡动画时间
 * 
 * 使用说明：
 * - 所有组件应使用这些变量而非硬编码值
 * - 修改变量可实现全局主题调整
 * - 变量命名遵循BEM命名规范
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

/* iOS风格设计变量 */
:root {
  /* 颜色系统 */
  --primary-color: #007AFF;
  --primary-light: #5AC8FA;
  --primary-dark: #0056CC;
  
  --secondary-color: #5856D6;
  --success-color: #34C759;
  --warning-color: #FF9500;
  --danger-color: #FF3B30;
  
  --background-color: #F2F2F7;
  --surface-color: #FFFFFF;
  --border-color: #C7C7CC;
  --text-primary: #000000;
  --text-secondary: #8E8E93;
  --text-tertiary: #C7C7CC;
  
  /* 圆角 */
  --border-radius-small: 6px;
  --border-radius-medium: 12px;
  --border-radius-large: 16px;
  --border-radius-xl: 20px;
  
  /* 阴影 */
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.2);
  
  /* 间距 - 调整导航栏相关间距 */
  --spacing-xs: 4px;
  --spacing-sm: 6px;  /* 从8px减少到6px */
  --spacing-md: 12px; /* 从16px减少到12px */
  --spacing-lg: 20px; /* 从24px减少到20px */
  --spacing-xl: 24px; /* 从32px减少到24px */
  --spacing-xxl: 36px; /* 从48px减少到36px */
  
  /* 字体 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 动画 */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
} 