import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  const sidebarCollapsed = ref(false)
  const currentModule = ref('')
  const notifications = ref<any[]>([])

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setCurrentModule = (module: string) => {
    currentModule.value = module
  }

  const addNotification = (notification: any) => {
    notifications.value.push(notification)
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  return {
    sidebarCollapsed,
    currentModule,
    notifications,
    toggleSidebar,
    setCurrentModule,
    addNotification,
    removeNotification
  }
}) 