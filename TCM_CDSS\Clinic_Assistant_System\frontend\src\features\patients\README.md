# 患者管理模块

## 功能描述
患者管理模块负责管理患者的基本信息、病历记录、就诊历史等功能，是诊所核心业务模块。

## 目录结构
```
patients/
├── views/                    # 页面视图
│   ├── PatientList.vue      # 患者列表页面
│   └── PatientDetail.vue    # 患者详情页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 患者信息管理
- 患者列表展示
- 患者详情查看
- 患者搜索筛选

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 