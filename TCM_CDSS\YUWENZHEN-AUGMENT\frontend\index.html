<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医预问诊智能体系统 - MVP架构</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ffc107;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-dot.healthy {
            background: #28a745;
        }

        .status-dot.unhealthy {
            background: #dc3545;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .content {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .demos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .card-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .demo-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .demo-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .button-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .info-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
        }

        .info-card h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .api-list {
            margin: 15px 0;
        }

        .api-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .api-item code {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 5px 0;
            color: #555;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }

            .demos-grid {
                grid-template-columns: 1fr;
            }

            .info-section {
                grid-template-columns: 1fr;
            }

            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 中医预问诊智能体系统</h1>
            <p class="subtitle">MVP架构 - 前后端分离的专业中医问诊助手</p>
            <div id="systemStatus" class="status-indicator">
                <span class="status-dot"></span>
                <span class="status-text">检查系统状态中...</span>
            </div>
        </div>
        
        <div class="content">
            <div class="demos-grid">
                <div class="demo-card">
                    <div class="card-icon">🍎</div>
                    <h3>iOS风格演示</h3>
                    <p>精美的苹果风格界面设计，提供最佳的用户体验。推荐使用此页面进行体验。</p>
                    <div class="button-group">
                        <a href="/pages/ios" class="btn btn-primary">立即体验</a>
                        <a href="/ios" class="btn btn-secondary">快捷访问</a>
                    </div>
                </div>
                
                <div class="demo-card">
                    <div class="card-icon">🎯</div>
                    <h3>简化演示页面</h3>
                    <p>简洁明了的界面设计，专注于核心功能，适合快速测试和演示。</p>
                    <div class="button-group">
                        <a href="/pages/simple" class="btn btn-success">快速体验</a>
                        <a href="/simple" class="btn btn-secondary">快捷访问</a>
                    </div>
                </div>
                
                <div class="demo-card">
                    <div class="card-icon">🤖</div>
                    <h3>智能体演示</h3>
                    <p>展示智能体工作状态和处理流程，适合开发者和技术人员使用。</p>
                    <div class="button-group">
                        <a href="/pages/agent" class="btn btn-warning">技术演示</a>
                        <a href="/agent" class="btn btn-secondary">快捷访问</a>
                    </div>
                </div>
                
                <div class="demo-card">
                    <div class="card-icon">🔄</div>
                    <h3>工作流程演示</h3>
                    <p>多智能体协同预问诊工作流程，结构化信息采集，生成完整病历。</p>
                    <div class="button-group">
                        <a href="/pages/workflow" class="btn btn-warning">工作流程</a>
                        <a href="/workflow" class="btn btn-secondary">快捷访问</a>
                    </div>
                </div>

                <div class="demo-card">
                    <div class="card-icon">📋</div>
                    <h3>完整功能演示</h3>
                    <p>包含所有功能的完整演示页面，展示系统的全部能力。</p>
                    <div class="button-group">
                        <a href="/pages/full" class="btn btn-info">完整体验</a>
                        <a href="/demo" class="btn btn-secondary">快捷访问</a>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <div class="info-card">
                    <h3>🔗 API接口</h3>
                    <p>开发者可以使用以下API接口：</p>
                    <div class="api-list">
                        <div class="api-item">
                            <code>GET /api/health</code>
                            <span>健康检查</span>
                        </div>
                        <div class="api-item">
                            <code>POST /api/chat</code>
                            <span>聊天接口</span>
                        </div>
                        <div class="api-item">
                            <code>POST /api/inquiry/start</code>
                            <span>开始问诊</span>
                        </div>
                        <div class="api-item">
                            <code>POST /api/workflow/start</code>
                            <span>工作流程问诊</span>
                        </div>
                        <div class="api-item">
                            <code>GET /api/docs</code>
                            <span>API文档</span>
                        </div>
                    </div>
                    <a href="/api/docs" class="btn btn-outline" target="_blank">查看API文档</a>
                </div>
                
                <div class="info-card">
                    <h3>🎯 系统特色</h3>
                    <ul class="feature-list">
                        <li>✅ MVP架构设计，前后端分离</li>
                        <li>✅ 多智能体协同工作流程</li>
                        <li>✅ 结构化信息采集</li>
                        <li>✅ 智能体工作报告</li>
                        <li>✅ 专业的中医问诊流程</li>
                        <li>✅ RESTful API接口</li>
                        <li>✅ 响应式设计，适配多设备</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 中医预问诊智能体系统 - MVP架构版本</p>
            <p>基于FastAPI + 前端静态页面的现代化医疗AI应用</p>
        </div>
    </div>
    
    <script>
        // API基础地址
        const API_BASE = '/api';

        // 检查系统健康状态
        async function checkSystemHealth() {
            const statusIndicator = document.getElementById('systemStatus');
            const statusDot = statusIndicator.querySelector('.status-dot');
            const statusText = statusIndicator.querySelector('.status-text');

            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                if (data.status === 'healthy') {
                    statusDot.className = 'status-dot healthy';
                    statusText.textContent = '系统运行正常';
                } else {
                    statusDot.className = 'status-dot unhealthy';
                    statusText.textContent = '系统状态异常';
                }
            } catch (error) {
                statusDot.className = 'status-dot unhealthy';
                statusText.textContent = '无法连接到服务器';
            }
        }

        // 页面加载时检查系统状态
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemHealth();

            // 每30秒检查一次状态
            setInterval(checkSystemHealth, 30000);
        });
    </script>
</body>
</html>
