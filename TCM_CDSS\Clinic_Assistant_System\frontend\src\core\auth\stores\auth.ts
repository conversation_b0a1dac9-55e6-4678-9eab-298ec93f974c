/**
 * 认证状态管理
 * 
 * 主要功能：
 * 1. 管理用户登录状态
 * 2. 提供登录、登出功能
 * 3. 存储用户信息到localStorage
 * 4. 提供用户认证状态的计算属性
 * 
 * 状态说明：
 * - user: 当前登录用户信息（从localStorage读取）
 * - isAuthenticated: 计算属性，判断用户是否已登录
 * 
 * 方法说明：
 * - login(credentials): 用户登录，验证账号密码
 * - logout(): 用户登出，清除用户信息
 * 
 * 数据存储：
 * - 用户信息存储在localStorage的'user'键中
 * - 不存储明文密码，只存储用户基本信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { users } from '@/core/auth/stores/users'

export interface User {
  id: string
  name: string
  role: string
  avatar?: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<any>(JSON.parse(localStorage.getItem('user') || 'null'))

  const isAuthenticated = computed(() => !!user.value)

  async function login(credentials: { username?: string, password?: string }) {
    const foundUser = users.find(
      u => u.username === credentials.username && u.password === credentials.password
    )

    if (foundUser) {
      const { password, ...userToStore } = foundUser; // 不在本地存储明文密码
      user.value = userToStore
      localStorage.setItem('user', JSON.stringify(userToStore))
      return true
    }
    
    return false
  }

  function logout() {
    user.value = null
    localStorage.removeItem('user')
  }

  return { user, isAuthenticated, login, logout }
}) 