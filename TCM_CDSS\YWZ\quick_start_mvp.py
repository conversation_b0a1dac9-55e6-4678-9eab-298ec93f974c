#!/usr/bin/env python3
"""
中医预问诊智能体系统 - MVP架构快速启动
"""

import subprocess
import webbrowser
import time
import sys

def main():
    """快速启动MVP系统"""
    print("🏥 中医预问诊智能体系统 - MVP架构")
    print("=" * 50)
    print("🚀 正在启动统一服务器...")
    
    try:
        # 启动服务器
        process = subprocess.Popen([
            sys.executable, "backend/app.py"
        ])
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(8)
        
        # 打开浏览器
        print("🌐 自动打开浏览器...")
        webbrowser.open("http://localhost:8002")
        
        print("✅ MVP系统启动完成！")
        print("\n🔗 可用地址:")
        print("   🏠 主页导航: http://localhost:8002")
        print("   🍎 iOS演示: http://localhost:8002/pages/ios")
        print("   🎯 简化演示: http://localhost:8002/pages/simple")
        print("   🤖 智能体演示: http://localhost:8002/pages/agent")
        print("   📋 完整演示: http://localhost:8002/pages/full")
        print("   📚 API文档: http://localhost:8002/api/docs")
        print("\n🎯 快捷访问:")
        print("   http://localhost:8002/ios")
        print("   http://localhost:8002/simple")
        print("   http://localhost:8002/agent")
        print("\n⏹️ 按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请确保已安装依赖: pip install -r requirements.txt")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
