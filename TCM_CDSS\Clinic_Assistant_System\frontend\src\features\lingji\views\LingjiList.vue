<template>
  <div class="lingji-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">灵机管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showComingSoon">
          <el-icon><Plus /></el-icon>
          新建灵机
        </el-button>
      </div>
    </header>

    <div class="content-area">
      <div class="coming-soon-container">
        <div class="coming-soon-content">
          <div class="icon-container">
            <el-icon :size="80" color="#FF6B6B">
              <MagicStick />
            </el-icon>
          </div>
          <h2 class="coming-soon-title">灵机管理功能</h2>
          <p class="coming-soon-subtitle">智能诊疗辅助系统</p>
          <div class="feature-preview">
            <div class="feature-item">
              <el-icon color="#007AFF"><Document /></el-icon>
              <span>智能病历分析</span>
            </div>
            <div class="feature-item">
              <el-icon color="#34C759"><ChatDotRound /></el-icon>
              <span>AI诊疗建议</span>
            </div>
            <div class="feature-item">
              <el-icon color="#FF9500"><DataAnalysis /></el-icon>
              <span>数据智能分析</span>
            </div>
            <div class="feature-item">
              <el-icon color="#5856D6"><Star /></el-icon>
              <span>个性化推荐</span>
            </div>
          </div>
          <div class="coming-soon-badge">
            <el-tag type="warning" size="large">敬请期待</el-tag>
          </div>
          <p class="coming-soon-description">
            灵机管理功能正在紧锣密鼓的开发中，将为您提供智能化的诊疗辅助服务，
            包括病历智能分析、AI诊疗建议、数据分析等功能，敬请期待！
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Plus, MagicStick, Document, ChatDotRound, DataAnalysis, Star } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/dashboard')
}

const showComingSoon = () => {
  ElMessage.info('该功能正在开发中，敬请期待！')
}
</script>

<style scoped>
.lingji-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.content-area {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--spacing-xl);
  overflow-y: auto;
  min-height: 0;
}

.coming-soon-container {
  max-width: 600px;
  width: 100%;
  text-align: center;
  margin-top: var(--spacing-lg);
}

.coming-soon-content {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xxl);
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.icon-container {
  margin-bottom: var(--spacing-lg);
}

.coming-soon-title {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.coming-soon-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.feature-preview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
}

.coming-soon-badge {
  margin-bottom: var(--spacing-lg);
}

.coming-soon-description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 480px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .feature-preview {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .coming-soon-content {
    padding: var(--spacing-lg);
  }
  
  .coming-soon-title {
    font-size: var(--font-size-xl);
  }
}
</style>
