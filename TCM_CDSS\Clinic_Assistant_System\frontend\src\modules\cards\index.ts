// 功能中心卡片模块统一导出
// 每个卡片模块包含：配置信息、统计数据、详细统计组件

export interface CardModule {
  key: string
  name: string
  route: string
  icon: string
  color: string
  info: string | (() => string)
  hasDetailStats?: boolean
  statsData?: any
  permission?: string
}

// 导入所有卡片模块
import { appointmentsCard } from './appointments'
import { patientsCard } from './patients'
import { workstationCard } from './workstation'
import { billingCard } from './billing'
import { pharmacyCard } from './pharmacy'
import { inventoryCard } from './inventory'
import { schedulingCard } from './scheduling'
import { staffCard } from './staff'
import { reportsCard } from './reports'
import { familyDoctorCard } from './family-doctor'
import { researchCenterCard } from './research-center'
import { appCenterCard } from './app-center'
import { mallManagementCard } from './mall-management'
import { dataCenterCard } from './data-center'

// 所有可用的卡片模块
export const allCardModules: CardModule[] = [
  appointmentsCard,
  patientsCard,
  workstationCard,
  billingCard,
  pharmacyCard,
  inventoryCard,
  schedulingCard,
  staffCard,
  reportsCard,
  familyDoctorCard,
  researchCenterCard,
  appCenterCard,
  mallManagementCard,
  dataCenterCard
]

// 根据key获取卡片模块
export const getCardModule = (key: string): CardModule | undefined => {
  return allCardModules.find(module => module.key === key)
}

// 获取有详细统计的卡片
export const getCardsWithDetailStats = (): CardModule[] => {
  return allCardModules.filter(module => module.hasDetailStats)
}
