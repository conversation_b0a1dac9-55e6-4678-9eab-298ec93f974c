// 模拟数据库 - 预约表 (appointments)
// 模拟从数据库中读取的预约数据

import type { Patient } from './patients'

export interface Appointment {
  id: string
  patientId: number // 关联患者ID
  patient: string // 患者姓名（冗余字段，便于显示）
  phone: string // 患者手机号（冗余字段，便于显示）
  gender: string // 患者性别（冗余字段，便于显示）
  age: number // 患者年龄（冗余字段，便于显示）
  doctor: string // 医师姓名
  date: string // 预约日期 YYYY-MM-DD
  time: string // 预约时间 HH:MM
  status: 'pending' | 'completed' | 'cancelled' | 'absent' // 预约状态
  source: 'online' | 'offline' | 'phone' // 预约来源
  note?: string // 备注信息
  diagnosis?: string // 诊断信息（来自患者表）
  visitCount?: number // 就诊次数（来自患者表）
  createTime: string // 创建时间
  updatedAt: string // 更新时间
}

// 模拟预约数据
export const appointmentsData: Appointment[] = [
  {
    id: 'a1',
    patientId: 1,
    patient: '李小明',
    phone: '138****1234',
    gender: '男',
    age: 34,
    doctor: '张医生',
    date: '2025-06-24',
    time: '09:00',
    status: 'pending',
    source: 'online',
    note: '头痛症状，需要详细检查',
    diagnosis: '肝郁脾虚证',
    visitCount: 5,
    createTime: '2025-06-23 15:30:00',
    updatedAt: '2025-06-23 15:30:00'
  },
  {
    id: 'a2',
    patientId: 2,
    patient: '王小红',
    phone: '139****5678',
    gender: '女',
    age: 28,
    doctor: '王医生',
    date: '2025-06-24',
    time: '10:00',
    status: 'completed',
    source: 'offline',
    note: '',
    diagnosis: '气血两虚证',
    visitCount: 3,
    createTime: '2025-06-24 08:15:00',
    updatedAt: '2025-06-24 10:30:00'
  },
  {
    id: 'a3',
    patientId: 3,
    patient: '张建国',
    phone: '137****9012',
    gender: '男',
    age: 45,
    doctor: '李医生',
    date: '2025-06-24',
    time: '11:00',
    status: 'absent',
    source: 'phone',
    note: '慢性胃炎复诊',
    diagnosis: '肾阳虚证',
    visitCount: 8,
    createTime: '2025-06-23 20:45:00',
    updatedAt: '2025-06-24 11:30:00'
  },
  {
    id: 'a4',
    patientId: 4,
    patient: '李秀英',
    phone: '136****3456',
    gender: '女',
    age: 52,
    doctor: '张医生',
    date: '2025-06-25',
    time: '14:00',
    status: 'pending',
    source: 'online',
    note: '失眠多梦，需要中医调理',
    diagnosis: '痰湿阻肺证',
    visitCount: 12,
    createTime: '2025-06-24 10:20:00',
    updatedAt: '2025-06-24 10:20:00'
  },
  {
    id: 'a5',
    patientId: 5,
    patient: '王德明',
    phone: '135****7890',
    gender: '男',
    age: 38,
    doctor: '赵医生',
    date: '2025-06-25',
    time: '15:30',
    status: 'pending',
    source: 'offline',
    note: '高血压随访',
    diagnosis: '心脾两虚证',
    visitCount: 6,
    createTime: '2025-06-24 11:45:00',
    updatedAt: '2025-06-24 11:45:00'
  },
  {
    id: 'a6',
    patientId: 6,
    patient: '赵丽华',
    phone: '134****1234',
    gender: '女',
    age: 29,
    doctor: '王医生',
    date: '2025-06-25',
    time: '16:00',
    status: 'pending',
    source: 'online',
    note: '肝阳上亢，头晕症状',
    diagnosis: '肝阳上亢证',
    visitCount: 4,
    createTime: '2025-06-24 12:30:00',
    updatedAt: '2025-06-24 12:30:00'
  },
  {
    id: 'a7',
    patientId: 7,
    patient: '钱志强',
    phone: '133****5678',
    gender: '男',
    age: 41,
    doctor: '李医生',
    date: '2025-06-26',
    time: '09:30',
    status: 'pending',
    source: 'phone',
    note: '脾胃虚弱，消化不良',
    diagnosis: '脾胃虚弱证',
    visitCount: 7,
    createTime: '2025-06-24 13:15:00',
    updatedAt: '2025-06-24 13:15:00'
  },
  {
    id: 'a8',
    patientId: 8,
    patient: '孙美玲',
    phone: '132****9012',
    gender: '女',
    age: 35,
    doctor: '张医生',
    date: '2025-06-26',
    time: '10:30',
    status: 'pending',
    source: 'online',
    note: '阴虚火旺，失眠盗汗',
    diagnosis: '阴虚火旺证',
    visitCount: 9,
    createTime: '2025-06-24 14:00:00',
    updatedAt: '2025-06-24 14:00:00'
  }
]

// 模拟数据库查询函数
export const appointmentService = {
  // 获取所有预约
  getAllAppointments(): Promise<Appointment[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([...appointmentsData])
      }, 100)
    })
  },

  // 根据ID获取预约
  getAppointmentById(id: string): Promise<Appointment | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const appointment = appointmentsData.find(a => a.id === id)
        resolve(appointment || null)
      }, 50)
    })
  },

  // 根据患者ID获取预约
  getAppointmentsByPatientId(patientId: number): Promise<Appointment[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const appointments = appointmentsData.filter(a => a.patientId === patientId)
        resolve(appointments)
      }, 100)
    })
  },

  // 根据条件筛选预约
  filterAppointments(filters: {
    date?: string
    dateRange?: { start: string; end: string }
    doctor?: string
    status?: string
    source?: string
    patientName?: string
    patientPhone?: string
  }): Promise<Appointment[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filtered = [...appointmentsData]

        if (filters.date) {
          filtered = filtered.filter(a => a.date === filters.date)
        }

        if (filters.dateRange) {
          filtered = filtered.filter(a => 
            a.date >= filters.dateRange!.start && a.date <= filters.dateRange!.end
          )
        }

        if (filters.doctor) {
          filtered = filtered.filter(a => a.doctor === filters.doctor)
        }

        if (filters.status) {
          filtered = filtered.filter(a => a.status === filters.status)
        }

        if (filters.source) {
          filtered = filtered.filter(a => a.source === filters.source)
        }

        if (filters.patientName) {
          filtered = filtered.filter(a => 
            a.patient.includes(filters.patientName!)
          )
        }

        if (filters.patientPhone) {
          filtered = filtered.filter(a => 
            a.phone.includes(filters.patientPhone!)
          )
        }

        // 按日期和时间排序
        filtered.sort((a, b) => {
          const dateTimeA = new Date(`${a.date} ${a.time}`)
          const dateTimeB = new Date(`${b.date} ${b.time}`)
          return dateTimeA.getTime() - dateTimeB.getTime()
        })

        resolve(filtered)
      }, 150)
    })
  },

  // 创建新预约
  createAppointment(appointment: Omit<Appointment, 'id' | 'createTime' | 'updatedAt'>): Promise<Appointment> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newAppointment: Appointment = {
          ...appointment,
          id: `a${Date.now()}`,
          createTime: new Date().toISOString().replace('T', ' ').slice(0, 19),
          updatedAt: new Date().toISOString().replace('T', ' ').slice(0, 19)
        }
        appointmentsData.push(newAppointment)
        resolve(newAppointment)
      }, 200)
    })
  },

  // 更新预约
  updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = appointmentsData.findIndex(a => a.id === id)
        if (index !== -1) {
          appointmentsData[index] = {
            ...appointmentsData[index],
            ...updates,
            updatedAt: new Date().toISOString().replace('T', ' ').slice(0, 19)
          }
          resolve(appointmentsData[index])
        } else {
          resolve(null)
        }
      }, 150)
    })
  },

  // 删除预约
  deleteAppointment(id: string): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = appointmentsData.findIndex(a => a.id === id)
        if (index !== -1) {
          appointmentsData.splice(index, 1)
          resolve(true)
        } else {
          resolve(false)
        }
      }, 100)
    })
  },

  // 获取今日统计
  getTodayStats(): Promise<{
    total: number
    pending: number
    completed: number
    cancelled: number
    absent: number
  }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const today = new Date().toISOString().split('T')[0]
        const todayAppointments = appointmentsData.filter(a => a.date === today)
        
        const stats = {
          total: todayAppointments.length,
          pending: todayAppointments.filter(a => a.status === 'pending').length,
          completed: todayAppointments.filter(a => a.status === 'completed').length,
          cancelled: todayAppointments.filter(a => a.status === 'cancelled').length,
          absent: todayAppointments.filter(a => a.status === 'absent').length
        }
        
        resolve(stats)
      }, 100)
    })
  }
}
