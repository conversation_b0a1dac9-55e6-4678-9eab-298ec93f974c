"""
工作流程问诊服务 - 多智能体协同预问诊
"""

import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime
from services.agents import InquiryWorkflow

logger = logging.getLogger(__name__)

class WorkflowInquiryService:
    """基于工作流程的问诊服务"""
    
    def __init__(self):
        self.active_workflows: Dict[str, InquiryWorkflow] = {}
    
    def start_inquiry(self, patient_info: Dict[str, Any]) -> Dict[str, Any]:
        """开始预问诊流程"""
        try:
            # 生成会话ID
            session_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(patient_info)) % 100000}"
            
            # 创建工作流程
            workflow = InquiryWorkflow(patient_info)
            self.active_workflows[session_id] = workflow
            
            # 开始问诊
            welcome_message = workflow.start_inquiry()
            
            logger.info(f"开始工作流程问诊: {session_id}, 患者: {patient_info.get('name', '未知')}")
            
            return {
                "session_id": session_id,
                "message": welcome_message,
                "progress": workflow.get_progress(),
                "status": "started"
            }
            
        except Exception as e:
            logger.error(f"启动工作流程问诊失败: {e}")
            return {
                "error": "启动问诊失败",
                "message": "系统初始化出现问题，请稍后重试。"
            }
    
    def process_message(self, session_id: str, user_message: str) -> Dict[str, Any]:
        """处理用户消息"""
        try:
            # 获取工作流程
            workflow = self.active_workflows.get(session_id)
            if not workflow:
                return {
                    "error": "会话不存在",
                    "message": "请重新开始问诊。"
                }
            
            # 处理消息
            response = workflow.process_message(user_message)
            progress = workflow.get_progress()
            
            # 记录对话
            logger.info(f"工作流程对话 {session_id}: 用户='{user_message[:50]}...', 进度={progress['progress_percentage']:.1f}%")
            
            result = {
                "session_id": session_id,
                "message": response,
                "progress": progress,
                "status": "completed" if workflow.is_completed else "in_progress"
            }
            
            # 如果问诊完成，清理会话
            if workflow.is_completed:
                # 保存最终记录
                self._save_final_record(session_id, workflow)
                # 从活跃会话中移除
                del self.active_workflows[session_id]
                logger.info(f"工作流程问诊完成: {session_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"处理工作流程消息失败: {e}")
            return {
                "error": "处理消息失败",
                "message": "处理您的消息时出现错误，请重新描述。"
            }
    
    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        try:
            workflow = self.active_workflows.get(session_id)
            if not workflow:
                return {
                    "error": "会话不存在",
                    "message": "会话可能已过期或不存在。"
                }
            
            return {
                "session_id": session_id,
                "patient_info": workflow.patient_info,
                "progress": workflow.get_progress(),
                "agent_reports": workflow.agent_reports,
                "start_time": workflow.start_time.isoformat(),
                "status": "completed" if workflow.is_completed else "active"
            }
            
        except Exception as e:
            logger.error(f"获取会话信息失败: {e}")
            return {
                "error": "获取会话信息失败"
            }
    
    def get_conversation_history(self, session_id: str) -> Dict[str, Any]:
        """获取对话历史"""
        try:
            workflow = self.active_workflows.get(session_id)
            if not workflow:
                return {
                    "error": "会话不存在",
                    "history": []
                }
            
            # 收集所有智能体的对话历史
            all_history = []
            for agent in workflow.agents:
                all_history.extend(agent.conversation_history)
            
            # 按时间排序
            all_history.sort(key=lambda x: x.get("timestamp", ""))
            
            return {
                "session_id": session_id,
                "history": all_history,
                "total_messages": len(all_history)
            }
            
        except Exception as e:
            logger.error(f"获取对话历史失败: {e}")
            return {
                "error": "获取对话历史失败",
                "history": []
            }
    
    def _save_final_record(self, session_id: str, workflow: InquiryWorkflow):
        """保存最终问诊记录"""
        try:
            # 构建完整记录
            final_record = {
                "session_id": session_id,
                "patient_info": workflow.patient_info,
                "agent_reports": workflow.agent_reports,
                "conversation_history": [],
                "inquiry_summary": {
                    "start_time": workflow.start_time.isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration_minutes": (datetime.now() - workflow.start_time).total_seconds() / 60,
                    "total_agents": len(workflow.agents),
                    "completeness": "100%"
                }
            }
            
            # 收集对话历史
            for agent in workflow.agents:
                final_record["conversation_history"].extend(agent.conversation_history)
            
            # 保存到文件
            import os
            os.makedirs("data/workflow_records", exist_ok=True)
            
            filename = f"data/workflow_records/{session_id}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(final_record, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存工作流程记录: {filename}")
            
        except Exception as e:
            logger.error(f"保存最终记录失败: {e}")
    
    def get_active_sessions(self) -> Dict[str, Any]:
        """获取活跃会话列表"""
        try:
            sessions = []
            for session_id, workflow in self.active_workflows.items():
                sessions.append({
                    "session_id": session_id,
                    "patient_name": workflow.patient_info.get("name", "未知"),
                    "start_time": workflow.start_time.isoformat(),
                    "progress": workflow.get_progress(),
                    "current_agent": workflow.get_current_agent().name if workflow.get_current_agent() else "已完成"
                })
            
            return {
                "active_sessions": sessions,
                "total_count": len(sessions)
            }
            
        except Exception as e:
            logger.error(f"获取活跃会话失败: {e}")
            return {
                "active_sessions": [],
                "total_count": 0,
                "error": "获取会话列表失败"
            }
    
    def cleanup_expired_sessions(self, max_age_hours: int = 2):
        """清理过期会话"""
        try:
            current_time = datetime.now()
            expired_sessions = []
            
            for session_id, workflow in list(self.active_workflows.items()):
                age_hours = (current_time - workflow.start_time).total_seconds() / 3600
                if age_hours > max_age_hours:
                    expired_sessions.append(session_id)
                    del self.active_workflows[session_id]
            
            if expired_sessions:
                logger.info(f"清理过期会话: {expired_sessions}")
            
            return {
                "cleaned_sessions": expired_sessions,
                "count": len(expired_sessions)
            }
            
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            return {
                "cleaned_sessions": [],
                "count": 0,
                "error": "清理失败"
            }

# 创建全局服务实例
workflow_inquiry_service = WorkflowInquiryService()
