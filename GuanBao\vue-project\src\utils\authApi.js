// 认证相关API

// 后端服务配置
const AUTH_API_CONFIG = {
  BASE_URL: "http://192.168.0.92:82",
  TIMEOUT: 10000
};

/**
 * 发送验证码
 * @param {string} phoneNumber 手机号
 * @returns {Promise} 响应数据
 */
export const sendVerificationCodeApi = async (phoneNumber) => {
  try {
    const response = await fetch(`${AUTH_API_CONFIG.BASE_URL}/api/send-verification-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone_number: phoneNumber
      }),
      signal: AbortSignal.timeout(AUTH_API_CONFIG.TIMEOUT)
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP ${response.status}`);
    }

    console.log('验证码发送成功:', {
      phoneNumber,
      message: data.message
    });

    return data;
  } catch (error) {
    console.error('发送验证码API错误:', error);
    
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    } else if (error.message.includes('Failed to fetch')) {
      throw new Error('无法连接到服务器，请检查服务器是否运行');
    } else {
      throw error;
    }
  }
};

/**
 * 验证登录
 * @param {string} phoneNumber 手机号
 * @param {string} verificationCode 验证码
 * @returns {Promise} 响应数据
 */
export const verifyLoginApi = async (phoneNumber, verificationCode) => {
  try {
    const response = await fetch(`${AUTH_API_CONFIG.BASE_URL}/api/verify-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone_number: phoneNumber,
        verification_code: verificationCode
      }),
      signal: AbortSignal.timeout(AUTH_API_CONFIG.TIMEOUT)
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP ${response.status}`);
    }

    console.log('登录验证成功:', {
      phoneNumber,
      message: data.message
    });

    return data;
  } catch (error) {
    console.error('登录验证API错误:', error);
    
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    } else if (error.message.includes('Failed to fetch')) {
      throw new Error('无法连接到服务器，请检查服务器是否运行');
    } else {
      throw error;
    }
  }
};

/**
 * 检查后端服务健康状态
 * @returns {Promise<boolean>} 服务是否健康
 */
export const checkAuthServerHealth = async () => {
  try {
    const response = await fetch(`${AUTH_API_CONFIG.BASE_URL}/api/health`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    if (response.ok) {
      const data = await response.json();
      console.log('认证服务健康检查通过:', data);
      return true;
    } else {
      console.warn('认证服务健康检查失败:', response.status);
      return false;
    }
  } catch (error) {
    console.error('认证服务健康检查错误:', error);
    return false;
  }
};

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息或null
 */
export const getCurrentUser = () => {
  try {
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      const user = JSON.parse(userInfo);
      
      // 检查登录是否过期（24小时）
      const loginTime = user.loginTime || 0;
      const now = Date.now();
      const expireTime = 24 * 60 * 60 * 1000; // 24小时
      
      if (now - loginTime > expireTime) {
        console.log('登录已过期，清除用户信息');
        localStorage.removeItem('userInfo');
        return null;
      }
      
      return user;
    }
    return null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    localStorage.removeItem('userInfo');
    return null;
  }
};

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isUserLoggedIn = () => {
  return getCurrentUser() !== null;
};

/**
 * 用户登出
 */
export const logout = () => {
  localStorage.removeItem('userInfo');
  console.log('用户已登出');
};

/**
 * 更新API配置
 * @param {Object} newConfig 新配置
 */
export const updateAuthApiConfig = (newConfig) => {
  Object.assign(AUTH_API_CONFIG, newConfig);
  console.log('认证API配置已更新:', AUTH_API_CONFIG);
};

/**
 * 获取当前API配置
 * @returns {Object} API配置
 */
export const getAuthApiConfig = () => {
  return { ...AUTH_API_CONFIG };
};
