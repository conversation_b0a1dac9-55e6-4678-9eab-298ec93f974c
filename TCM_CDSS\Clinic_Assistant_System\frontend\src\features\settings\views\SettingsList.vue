<template>
  <div class="settings-container">
    <!-- 密码验证对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="系统设置验证"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="password-verify-content">
        <div class="verify-icon">
          <el-icon size="48" color="#f56c6c"><Lock /></el-icon>
        </div>
        <p class="verify-text">进入系统设置需要管理员密码验证</p>
        <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules">
          <el-form-item prop="password">
            <el-input
              v-model="passwordForm.password"
              type="password"
              placeholder="请输入管理员密码"
              show-password
              size="large"
              @keyup.enter="verifyPassword"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="cancelPasswordVerify" size="large">取消</el-button>
        <el-button type="primary" @click="verifyPassword" :loading="verifyLoading" size="large">
          验证
        </el-button>
      </template>
    </el-dialog>

    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">系统设置</h1>
    </header>
    <div v-if="isPasswordVerified" class="settings-content">
      <div class="settings-nav">
        <div
          v-for="tab in settingsTabs"
          :key="tab.key"
          :class="['nav-item', { active: currentTab === tab.key }]"
          @click="currentTab = tab.key"
        >
          <el-icon><component :is="tab.icon" /></el-icon>
          <span>{{ tab.label }}</span>
        </div>
      </div>
      <div class="settings-panel">
        <div v-if="currentTab === 'basic'" class="settings-section">
          <h3>基本设置</h3>
          <el-form :model="basicSettings" label-width="120px">
            <el-form-item label="诊所名称">
              <el-input v-model="basicSettings.clinicName" placeholder="请输入诊所名称" />
            </el-form-item>
            <el-form-item label="联系电话">
              <el-input v-model="basicSettings.phone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="地址">
              <el-input v-model="basicSettings.address" placeholder="请输入地址" />
            </el-form-item>
            <el-form-item label="营业时间">
              <el-time-picker
                v-model="basicSettings.openTime"
                placeholder="开始时间"
                format="HH:mm"
                style="width: 120px;"
              />
              <span style="margin: 0 8px;">至</span>
              <el-time-picker
                v-model="basicSettings.closeTime"
                placeholder="结束时间"
                format="HH:mm"
                style="width: 120px;"
              />
            </el-form-item>
            <el-form-item label="系统Logo">
              <el-upload
                class="logo-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="beforeLogoUpload"
              >
                <img v-if="basicSettings.logo" :src="basicSettings.logo" class="logo" />
                <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="currentTab === 'users'" class="settings-section">
          <div class="section-header">
            <h3>用户管理</h3>
            <el-button type="primary" size="small" @click="showAddUserDialog = true">
              <el-icon><Plus /></el-icon>
              添加用户
            </el-button>
          </div>
          
          <el-table :data="userList" style="width: 100%">
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="role" label="角色" width="100">
              <template #default="scope">
                <el-tag :type="getRoleType(scope.row.role)">{{ getRoleText(scope.row.role) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                  {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastLogin" label="最后登录" width="180" />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" @click="editUser(scope.row)">编辑</el-button>
                <el-button
                  size="small"
                  :type="scope.row.status === 'active' ? 'danger' : 'success'"
                  @click="toggleUserStatus(scope.row)"
                >
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="currentTab === 'permissions'" class="settings-section">
          <div class="section-header">
            <h3>权限设置</h3>
            <div class="permission-actions">
              <el-button type="primary" @click="savePermissions">
                <el-icon><Check /></el-icon>
                保存配置
              </el-button>
              <el-button @click="resetPermissions">
                <el-icon><RefreshLeft /></el-icon>
                重置默认
              </el-button>
            </div>
          </div>
          <div class="permissions-grid">
            <div
              v-for="role in roles"
              :key="role.key"
              class="role-card"
            >
              <div class="role-header">
                <h4>{{ role.name }}</h4>
                <el-tag :type="getRoleType(role.key)">{{ getRoleText(role.key) }}</el-tag>
              </div>
              <div class="permissions-list">
                <div
                  v-for="permission in permissions"
                  :key="permission.key"
                  class="permission-item"
                >
                  <el-checkbox
                    v-model="role.permissions[permission.key]"
                    @change="updatePermission(role.key, permission.key, $event)"
                  >
                    {{ permission.label }}
                  </el-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="currentTab === 'lingji'" class="settings-section">
          <h3>灵机管理</h3>
          <div class="lingji-content">
            <div class="lingji-status">
              <h4>系统状态</h4>
              <div class="status-grid">
                <div class="status-item">
                  <div class="status-label">AI服务状态</div>
                  <div class="status-value">
                    <el-tag type="success">运行中</el-tag>
                  </div>
                </div>
                <div class="status-item">
                  <div class="status-label">模型版本</div>
                  <div class="status-value">v2.1.0</div>
                </div>
                <div class="status-item">
                  <div class="status-label">响应时间</div>
                  <div class="status-value">平均 1.2s</div>
                </div>
                <div class="status-item">
                  <div class="status-label">今日调用次数</div>
                  <div class="status-value">1,247 次</div>
                </div>
              </div>
            </div>

            <div class="lingji-config">
              <h4>功能配置</h4>
              <el-form :model="lingjiSettings" label-width="120px">
                <el-form-item label="智能诊断">
                  <el-switch v-model="lingjiSettings.smartDiagnosis" />
                  <span style="margin-left: 8px;">启用AI辅助诊断</span>
                </el-form-item>
                <el-form-item label="方剂推荐">
                  <el-switch v-model="lingjiSettings.prescriptionRecommend" />
                  <span style="margin-left: 8px;">启用智能方剂推荐</span>
                </el-form-item>
                <el-form-item label="配伍检查">
                  <el-switch v-model="lingjiSettings.compatibilityCheck" />
                  <span style="margin-left: 8px;">启用药物配伍检查</span>
                </el-form-item>
                <el-form-item label="病历分析">
                  <el-switch v-model="lingjiSettings.recordAnalysis" />
                  <span style="margin-left: 8px;">启用病历智能分析</span>
                </el-form-item>
                <el-form-item label="置信度阈值">
                  <el-slider
                    v-model="lingjiSettings.confidenceThreshold"
                    :min="50"
                    :max="95"
                    :step="5"
                    show-input
                    style="width: 200px;"
                  />
                  <span style="margin-left: 8px;">%</span>
                </el-form-item>
                <el-form-item label="响应模式">
                  <el-radio-group v-model="lingjiSettings.responseMode">
                    <el-radio label="fast">快速模式</el-radio>
                    <el-radio label="balanced">平衡模式</el-radio>
                    <el-radio label="accurate">精确模式</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="saveLingjiSettings">保存设置</el-button>
                  <el-button @click="testLingjiConnection">测试连接</el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="lingji-stats">
              <h4>使用统计</h4>
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-title">本月调用</div>
                  <div class="stat-number">12,456</div>
                  <div class="stat-trend positive">+15.2%</div>
                </div>
                <div class="stat-card">
                  <div class="stat-title">准确率</div>
                  <div class="stat-number">94.8%</div>
                  <div class="stat-trend positive">+2.1%</div>
                </div>
                <div class="stat-card">
                  <div class="stat-title">平均响应</div>
                  <div class="stat-number">1.2s</div>
                  <div class="stat-trend negative">-0.3s</div>
                </div>
                <div class="stat-card">
                  <div class="stat-title">用户满意度</div>
                  <div class="stat-number">4.8/5</div>
                  <div class="stat-trend positive">+0.2</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="currentTab === 'system'" class="settings-section">
          <h3>系统配置</h3>
          <el-form :model="systemSettings" label-width="120px">
            <el-form-item label="数据备份">
              <el-switch v-model="systemSettings.autoBackup" />
              <span style="margin-left: 8px;">自动备份</span>
            </el-form-item>
            <el-form-item label="备份频率">
              <el-select v-model="systemSettings.backupFrequency" :disabled="!systemSettings.autoBackup">
                <el-option label="每日" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
              </el-select>
            </el-form-item>
            <el-form-item label="日志级别">
              <el-select v-model="systemSettings.logLevel">
                <el-option label="调试" value="debug" />
                <el-option label="信息" value="info" />
                <el-option label="警告" value="warning" />
                <el-option label="错误" value="error" />
              </el-select>
            </el-form-item>
            <el-form-item label="会话超时">
              <el-input-number
                v-model="systemSettings.sessionTimeout"
                :min="5"
                :max="480"
                style="width: 120px;"
              />
              <span style="margin-left: 8px;">分钟</span>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveSystemSettings">保存设置</el-button>
              <el-button @click="backupData">立即备份</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="currentTab === 'test'" class="settings-section">
          <ApiTest />
        </div>
        <div v-if="currentTab === 'about'" class="settings-section">
          <h3>关于系统</h3>
          <div class="about-content">
            <div class="system-info">
              <h4>系统信息</h4>
              <div class="info-item">
                <span class="label">系统版本：</span>
                <span>v1.0.0</span>
              </div>
              <div class="info-item">
                <span class="label">构建时间：</span>
                <span>2024-01-15</span>
              </div>
              <div class="info-item">
                <span class="label">数据库版本：</span>
                <span>MySQL 8.0</span>
              </div>
              <div class="info-item">
                <span class="label">服务器环境：</span>
                <span>Node.js 18.0</span>
              </div>
            </div>
            
            <div class="license-info">
              <h4>许可证信息</h4>
              <div class="info-item">
                <span class="label">许可证类型：</span>
                <span>商业版</span>
              </div>
              <div class="info-item">
                <span class="label">到期时间：</span>
                <span>2025-01-15</span>
              </div>
              <div class="info-item">
                <span class="label">授权用户数：</span>
                <span>10</span>
              </div>
            </div>
            
            <div class="contact-info">
              <h4>技术支持</h4>
              <div class="info-item">
                <span class="label">技术支持：</span>
                <span>************</span>
              </div>
              <div class="info-item">
                <span class="label">邮箱：</span>
                <span><EMAIL></span>
              </div>
              <div class="info-item">
                <span class="label">官网：</span>
                <span>www.clinic.com</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="showAddUserDialog"
      title="添加用户"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="医师" value="doctor" />
            <el-option label="护士" value="nurse" />
            <el-option label="药师" value="pharmacist" />
            <el-option label="前台" value="receptionist" />
          </el-select>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddUserDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUser">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/core/stores/auth'
import { users } from '@/core/stores/users'
import {
  ArrowLeft,
  Plus,
  Setting,
  User,
  Lock,
  InfoFilled,
  Tools,
  MagicStick,
  Check,
  RefreshLeft,
  Connection
} from '@element-plus/icons-vue'
import ApiTest from '../components/ApiTest.vue'

const router = useRouter()
const authStore = useAuthStore()

// 密码验证相关
const showPasswordDialog = ref(false)
const passwordFormRef = ref()
const passwordForm = ref({
  password: ''
})
const passwordRules = {
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}
const verifyLoading = ref(false)
const isPasswordVerified = ref(false)

const currentTab = ref('basic')

const settingsTabs = [
  { key: 'basic', label: '基本设置', icon: 'Setting' },
  { key: 'users', label: '用户管理', icon: 'User' },
  { key: 'permissions', label: '权限设置', icon: 'Lock' },
  { key: 'lingji', label: '灵机管理', icon: 'MagicStick' },
  { key: 'system', label: '系统配置', icon: 'Tools' },
  { key: 'test', label: '系统测试', icon: 'Connection' },
  { key: 'about', label: '关于系统', icon: 'InfoFilled' }
]

const basicSettings = ref({
  clinicName: '观建在-中医诊所',
  phone: '************',
  address: '北京市朝阳区xxx街道xxx号',
  openTime: '08:00',
  closeTime: '18:00',
  logo: ''
})

const userList = ref([
  {
    id: 1,
    username: 'admin',
    name: '系统管理员',
    role: 'admin',
    status: 'active',
    lastLogin: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    username: 'zhang',
    name: '张医生',
    role: 'doctor',
    status: 'active',
    lastLogin: '2024-01-15 09:15:00'
  },
  {
    id: 3,
    username: 'wang',
    name: '王护士',
    role: 'nurse',
    status: 'active',
    lastLogin: '2024-01-15 08:45:00'
  }
])

const roles = ref([
  {
    key: 'admin',
    name: '系统管理员',
    permissions: {
      patient_manage: true,
      appointment_manage: true,
      workstation_access: true,
      billing_manage: true,
      pharmacy_manage: true,
      inventory_manage: true,
      scheduling_manage: true,
      staff_manage: true,
      report_view: true,
      family_doctor: true,
      research_center: true,
      app_center: true,
      mall_management: true,
      lingji_manage: true,
      system_settings: true
    }
  },
  {
    key: 'doctor',
    name: '医师',
    permissions: {
      patient_manage: true,
      appointment_manage: true,
      workstation_access: true,
      billing_manage: false,
      pharmacy_manage: false,
      inventory_manage: false,
      scheduling_manage: false,
      staff_manage: false,
      report_view: true,
      family_doctor: true,
      research_center: true,
      app_center: true,
      mall_management: false,
      lingji_manage: false,
      system_settings: false
    }
  },
  {
    key: 'nurse',
    name: '护士',
    permissions: {
      patient_manage: true,
      appointment_manage: true,
      workstation_access: false,
      billing_manage: false,
      pharmacy_manage: false,
      inventory_manage: false,
      scheduling_manage: false,
      staff_manage: false,
      report_view: false,
      family_doctor: false,
      research_center: false,
      app_center: false,
      mall_management: false,
      lingji_manage: false,
      system_settings: false
    }
  },
  {
    key: 'pharmacist',
    name: '药师',
    permissions: {
      patient_manage: false,
      appointment_manage: false,
      workstation_access: false,
      billing_manage: false,
      pharmacy_manage: true,
      inventory_manage: true,
      scheduling_manage: false,
      staff_manage: false,
      report_view: false,
      family_doctor: false,
      research_center: false,
      app_center: false,
      mall_management: false,
      lingji_manage: false,
      system_settings: false
    }
  },
  {
    key: 'receptionist',
    name: '前台',
    permissions: {
      patient_manage: true,
      appointment_manage: true,
      workstation_access: false,
      billing_manage: true,
      pharmacy_manage: false,
      inventory_manage: false,
      scheduling_manage: false,
      staff_manage: false,
      report_view: false,
      family_doctor: false,
      research_center: false,
      app_center: false,
      mall_management: false,
      lingji_manage: false,
      system_settings: false
    }
  }
])

const permissions = [
  { key: 'patient_manage', label: '患者管理' },
  { key: 'appointment_manage', label: '预约管理' },
  { key: 'workstation_access', label: '门诊工作站' },
  { key: 'billing_manage', label: '划价收费' },
  { key: 'pharmacy_manage', label: '药房管理' },
  { key: 'inventory_manage', label: '库存管理' },
  { key: 'scheduling_manage', label: '排班管理' },
  { key: 'staff_manage', label: '员工管理' },
  { key: 'report_view', label: '统计报表' },
  { key: 'family_doctor', label: '家医管理' },
  { key: 'research_center', label: '研学中心' },
  { key: 'app_center', label: '应用中心' },
  { key: 'mall_management', label: '商城管理' },
  { key: 'lingji_manage', label: '灵机管理' },
  { key: 'system_settings', label: '系统设置' }
]

const systemSettings = ref({
  autoBackup: true,
  backupFrequency: 'daily',
  logLevel: 'info',
  sessionTimeout: 30
})

// 灵机管理设置
const lingjiSettings = ref({
  smartDiagnosis: true,
  prescriptionRecommend: true,
  compatibilityCheck: true,
  recordAnalysis: true,
  confidenceThreshold: 80,
  responseMode: 'balanced'
})

const showAddUserDialog = ref(false)

const userFormRef = ref()
const userForm = ref({
  username: '',
  name: '',
  password: '',
  role: '',
  email: ''
})

const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 密码验证方法
const verifyPassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    verifyLoading.value = true

    // 检查当前用户是否为管理员
    const currentUser = authStore.user
    const isAdmin = currentUser && (currentUser.role_name === 'admin' || currentUser.role === 'admin')
    if (!isAdmin) {
      ElMessage.error('只有管理员才能访问系统设置')
      verifyLoading.value = false
      return
    }

    // 验证管理员密码
    // 支持新的认证系统
    const inputPassword = passwordForm.value.password
    const isValidPassword = inputPassword === 'admin' || inputPassword === 'password'

    if (isValidPassword) {
      isPasswordVerified.value = true
      showPasswordDialog.value = false
      // 将验证状态保存到sessionStorage，避免页面刷新后重复验证
      sessionStorage.setItem('settings-verified', 'true')
      ElMessage.success('验证成功')
      // 清空密码输入框
      passwordForm.value.password = ''
    } else {
      ElMessage.error('密码错误，请输入管理员密码')
      passwordForm.value.password = ''
    }
  } catch (error) {
    ElMessage.error('请输入密码')
  } finally {
    verifyLoading.value = false
  }
}

const cancelPasswordVerify = () => {
  router.push('/dashboard')
}

// 检查是否需要密码验证
const checkPasswordVerification = () => {
  const currentUser = authStore.user

  // 如果不是管理员，直接跳转回功能中心
  // 支持新的认证系统字段 role_name 和向后兼容 role 字段
  const isAdmin = currentUser && (currentUser.role_name === 'admin' || currentUser.role === 'admin')
  if (!isAdmin) {
    ElMessage.error('只有管理员才能访问系统设置')
    router.push('/dashboard')
    return
  }

  // 检查本次会话是否已经验证过
  const isVerified = sessionStorage.getItem('settings-verified')
  if (isVerified === 'true') {
    isPasswordVerified.value = true
  } else {
    showPasswordDialog.value = true
  }
}

const goBack = () => {
  // 清除验证状态
  sessionStorage.removeItem('settings-verified')
  router.push('/dashboard')
}

const getRoleType = (role: string) => {
  const typeMap = {
    admin: 'danger',
    doctor: 'primary',
    nurse: 'success',
    pharmacist: 'warning',
    receptionist: 'info'
  }
  return typeMap[role] || 'info'
}

const getRoleText = (role: string) => {
  const textMap = {
    admin: '管理员',
    doctor: '医师',
    nurse: '护士',
    pharmacist: '药师',
    receptionist: '前台'
  }
  return textMap[role] || role
}

const beforeLogoUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  
  const reader = new FileReader()
  reader.onload = (e) => {
    basicSettings.value.logo = e.target?.result as string
  }
  reader.readAsDataURL(file)
  return false
}

const saveBasicSettings = () => {
  ElMessage.success('基本设置保存成功')
}

const editUser = (user: any) => {
  userForm.value = { ...user }
  showAddUserDialog.value = true
}

const toggleUserStatus = (user: any) => {
  user.status = user.status === 'active' ? 'inactive' : 'active'
  ElMessage.success(`用户${user.status === 'active' ? '启用' : '禁用'}成功`)
}

const saveUser = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()
    
    if (userForm.value.id) {
      const index = userList.value.findIndex(item => item.id === userForm.value.id)
      if (index > -1) {
        userList.value[index] = { ...userList.value[index], ...userForm.value }
      }
      ElMessage.success('用户信息更新成功')
    } else {
      const newUser = {
        id: Date.now(),
        ...userForm.value,
        status: 'active',
        lastLogin: '-'
      }
      userList.value.push(newUser)
      ElMessage.success('用户添加成功')
    }
    
    showAddUserDialog.value = false
    
    userForm.value = {
      username: '',
      name: '',
      password: '',
      role: '',
      email: ''
    }
  } catch (error) {
    ElMessage.error('请检查表单信息')
  }
}

const updatePermission = (roleKey: string, permissionKey: string, value: boolean) => {
  const role = roles.value.find(r => r.key === roleKey)
  if (role) {
    role.permissions[permissionKey] = value
    // 保存到localStorage
    localStorage.setItem('rolePermissions', JSON.stringify(roles.value))
    ElMessage.success('权限更新成功')
  }
}

// 保存权限配置
const savePermissions = () => {
  localStorage.setItem('rolePermissions', JSON.stringify(roles.value))
  ElMessage.success('权限配置保存成功')
}

// 重置权限配置
const resetPermissions = () => {
  // 重置为默认配置
  roles.value = [
    {
      key: 'admin',
      name: '系统管理员',
      permissions: {
        patient_manage: true,
        appointment_manage: true,
        workstation_access: true,
        billing_manage: true,
        pharmacy_manage: true,
        inventory_manage: true,
        scheduling_manage: true,
        staff_manage: true,
        report_view: true,
        family_doctor: true,
        research_center: true,
        app_center: true,
        mall_management: true,
        lingji_manage: true,
        system_settings: true
      }
    },
    {
      key: 'doctor',
      name: '医师',
      permissions: {
        patient_manage: true,
        appointment_manage: true,
        workstation_access: true,
        billing_manage: false,
        pharmacy_manage: false,
        inventory_manage: false,
        scheduling_manage: false,
        staff_manage: false,
        report_view: true,
        family_doctor: true,
        research_center: true,
        app_center: true,
        mall_management: false,
        lingji_manage: false,
        system_settings: false
      }
    },
    {
      key: 'nurse',
      name: '护士',
      permissions: {
        patient_manage: true,
        appointment_manage: true,
        workstation_access: false,
        billing_manage: false,
        pharmacy_manage: false,
        inventory_manage: false,
        scheduling_manage: false,
        staff_manage: false,
        report_view: false,
        family_doctor: false,
        research_center: false,
        app_center: false,
        mall_management: false,
        lingji_manage: false,
        system_settings: false
      }
    },
    {
      key: 'pharmacist',
      name: '药师',
      permissions: {
        patient_manage: false,
        appointment_manage: false,
        workstation_access: false,
        billing_manage: false,
        pharmacy_manage: true,
        inventory_manage: true,
        scheduling_manage: false,
        staff_manage: false,
        report_view: false,
        family_doctor: false,
        research_center: false,
        app_center: false,
        mall_management: false,
        lingji_manage: false,
        system_settings: false
      }
    },
    {
      key: 'receptionist',
      name: '前台',
      permissions: {
        patient_manage: true,
        appointment_manage: true,
        workstation_access: false,
        billing_manage: true,
        pharmacy_manage: false,
        inventory_manage: false,
        scheduling_manage: false,
        staff_manage: false,
        report_view: false,
        family_doctor: false,
        research_center: false,
        app_center: false,
        mall_management: false,
        lingji_manage: false,
        system_settings: false
      }
    }
  ]
  localStorage.setItem('rolePermissions', JSON.stringify(roles.value))
  ElMessage.success('权限配置已重置为默认值')
}

// 初始化权限配置
const initPermissions = () => {
  const savedPermissions = localStorage.getItem('rolePermissions')
  if (savedPermissions) {
    try {
      roles.value = JSON.parse(savedPermissions)
    } catch (error) {
      console.error('解析权限配置失败:', error)
      // 如果解析失败，使用默认配置
    }
  }
}

// 页面加载时初始化
onMounted(() => {
  checkPasswordVerification()
  initPermissions()
})

// 页面卸载时清理
onBeforeUnmount(() => {
  // 清除验证状态，确保下次进入时重新验证
  sessionStorage.removeItem('settings-verified')
})

const saveSystemSettings = () => {
  ElMessage.success('系统设置已保存')
}

const backupData = () => {
  ElMessage.success('数据备份已开始')
}

// 灵机管理相关方法
const saveLingjiSettings = () => {
  ElMessage.success('灵机管理设置已保存')
}

const testLingjiConnection = () => {
  ElMessage.success('灵机服务连接正常')
}
</script>

<style scoped>
.settings-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.back-button:hover {
  color: var(--primary-dark);
}

.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.settings-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.settings-nav {
  width: 240px;
  background: var(--surface-color);
  border-right: 1px solid var(--border-color);
  padding: var(--spacing-md) 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
}

.nav-item:hover {
  background: var(--background-color);
  color: var(--primary-color);
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
}

.nav-item .el-icon {
  font-size: var(--font-size-md);
}

.settings-panel {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

.settings-section h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.permission-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.section-header h3 {
  margin: 0;
}

.logo-uploader {
  border: 1px dashed var(--border-color);
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-uploader:hover {
  border-color: var(--primary-color);
}

.logo-uploader-icon {
  font-size: 28px;
  color: var(--text-secondary);
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.role-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.role-header h4 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.permissions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.permission-item {
  padding: var(--spacing-xs) 0;
}

.about-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.system-info,
.license-info,
.contact-info {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
}

.system-info h4,
.license-info h4,
.contact-info h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.info-item .label {
  font-weight: 500;
  color: var(--text-secondary);
}

.info-item span:last-child {
  color: var(--text-primary);
}

/* 灵机管理样式 */
.lingji-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.lingji-status h4,
.lingji-config h4,
.lingji-stats h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-weight: 600;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.status-item {
  background: var(--surface-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.status-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.status-value {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

.lingji-config {
  background: var(--surface-color);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.stat-card {
  background: var(--surface-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
  text-align: center;
}

.stat-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-number {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-trend {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.stat-trend.positive {
  color: #52c41a;
}

.stat-trend.negative {
  color: #ff4d4f;
}

/* 密码验证对话框样式 */
.password-verify-content {
  text-align: center;
  padding: 20px 0;
}

.verify-icon {
  margin-bottom: 16px;
}

.verify-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 24px;
  line-height: 1.5;
}

.password-verify-content .el-form-item {
  margin-bottom: 0;
}

.password-verify-content .el-input {
  border-radius: 8px;
}

.password-verify-content .el-input__inner {
  text-align: center;
  font-size: 16px;
  padding: 12px 16px;
}
</style>