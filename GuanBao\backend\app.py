#!/usr/bin/env python3
"""
GuanBao AI Chat 后端服务
使用Flask提供API服务
"""

import json
import uuid
import time
import random
import sys
import os
from pathlib import Path
from flask import Flask, request, Response, jsonify
from flask_cors import CORS
import logging

# 添加config目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from config.config import get_config

# 加载配置
config = get_config()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 内部邀请的手机号白名单（实际项目中应该存储在数据库中）
INVITED_PHONES = {
    "13900000000",
    "13913977533", 
    "18888888888",
    "13800138000"
}

# 验证码存储（实际项目中应该使用Redis等缓存）
verification_codes = {}

# API密钥（从配置文件读取）
API_KEY = config.api_key

def generate_verification_code():
    """生成6位数字验证码"""
    return str(random.randint(100000, 999999))

def is_phone_invited(phone_number):
    """检查手机号是否在邀请名单中"""
    return phone_number in INVITED_PHONES

@app.route('/api/send-verification-code', methods=['POST'])
def send_verification_code():
    """发送验证码接口"""
    try:
        data = request.json
        phone_number = data.get('phone_number')
        
        if not phone_number:
            return jsonify({"error": "手机号不能为空"}), 400
            
        # 检查手机号格式
        if not phone_number.isdigit() or len(phone_number) != 11:
            return jsonify({"error": "手机号格式不正确"}), 400
            
        # 检查是否为邀请用户
        if not is_phone_invited(phone_number):
            return jsonify({"error": "该手机号未获得使用邀请，请联系管理员"}), 403
            
        # 生成验证码
        code = generate_verification_code()
        
        # 存储验证码（5分钟有效期）
        verification_codes[phone_number] = {
            'code': code,
            'timestamp': time.time(),
            'expires_in': 300  # 5分钟
        }
        
        logger.info(f"为手机号 {phone_number} 生成验证码: {code}")
        
        # 实际项目中这里应该调用短信服务发送验证码
        # 开发环境直接返回验证码（生产环境不应该返回）
        return jsonify({
            "message": "验证码发送成功",
            "code": code,  # 开发环境返回，生产环境删除此行
            "expires_in": 300
        })
        
    except Exception as e:
        logger.error(f"发送验证码失败: {str(e)}")
        return jsonify({"error": "发送验证码失败"}), 500

@app.route('/api/verify-login', methods=['POST'])
def verify_login():
    """验证登录接口"""
    try:
        data = request.json
        phone_number = data.get('phone_number')
        verification_code = data.get('verification_code')
        
        if not phone_number or not verification_code:
            return jsonify({"error": "手机号和验证码不能为空"}), 400
            
        # 检查验证码是否存在
        if phone_number not in verification_codes:
            return jsonify({"error": "验证码不存在或已过期"}), 400
            
        stored_data = verification_codes[phone_number]
        
        # 检查验证码是否过期
        if time.time() - stored_data['timestamp'] > stored_data['expires_in']:
            del verification_codes[phone_number]
            return jsonify({"error": "验证码已过期"}), 400
            
        # 验证验证码
        if stored_data['code'] != verification_code:
            return jsonify({"error": "验证码错误"}), 400
            
        # 验证成功，删除验证码
        del verification_codes[phone_number]
        
        # 生成会话token（实际项目中应该使用JWT等）
        session_token = str(uuid.uuid4())
        
        logger.info(f"用户 {phone_number} 登录成功")
        
        return jsonify({
            "message": "登录成功",
            "session_token": session_token,
            "phone_number": phone_number,
            "api_key": API_KEY
        })
        
    except Exception as e:
        logger.error(f"验证登录失败: {str(e)}")
        return jsonify({"error": "登录验证失败"}), 500

@app.route('/v1/chat/completions', methods=['POST'])
def chat_api():
    """聊天接口"""
    try:
        # 从请求中获取数据
        data = request.json
        request_id = data.get('request_id')
        phone_number = data.get('phone_number')
        query = data.get('query')
        api_key = data.get('api_key')
        
        logger.info("正在处理请求: %s", request_id)
        logger.info("正在处理用户: %s", phone_number)
        logger.info("正在处理问题: %s", query)
        
        # 验证必需参数
        if not request_id or not phone_number or not query:
            return jsonify({"error": "Missing required fields: request_id, phone_number, query"}), 400
            
        # 验证API密钥
        if api_key != API_KEY:
            return jsonify({"error": "Invalid API key"}), 401
            
        # 检查手机号是否为邀请用户
        if not is_phone_invited(phone_number):
            return jsonify({"error": "Unauthorized phone number"}), 403
        
        # 创建一个生成器来逐块返回数据
        def generate_response():
            try:
                for chunk in chat_model.stream(query):
                    json_chunk = json.dumps({
                        "request_id": request_id,
                        "phone_number": phone_number,
                        "response": chunk.content
                    }, ensure_ascii=False)
                    yield json_chunk + '\n'  # 每个 JSON 块后面加上换行符
                    
                # 发送结束标记
                end_chunk = json.dumps({
                    "request_id": request_id,
                    "phone_number": phone_number,
                    "response": ""
                }, ensure_ascii=False)
                yield end_chunk + '\n'
                
            except Exception as e:
                logger.error(f"生成响应时出错: {str(e)}")
                error_chunk = json.dumps({
                    "request_id": request_id,
                    "phone_number": phone_number,
                    "error": str(e)
                }, ensure_ascii=False)
                yield error_chunk + '\n'
        
        # 使用 Flask 的 Response 对象来返回流式响应
        return Response(generate_response(), content_type='application/json')
        
    except Exception as e:
        logger.error(f"聊天接口错误: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "timestamp": time.time(),
        "service": "GuanBao Chat API"
    })

if __name__ == '__main__':
    logger.info("启动GuanBao聊天服务...")
    logger.info(f"邀请用户数量: {len(INVITED_PHONES)}")
    logger.info(f"服务地址: {config.backend_host}:{config.backend_port}")
    app.run(host=config.backend_host, port=config.backend_port, threaded=True, debug=True)
