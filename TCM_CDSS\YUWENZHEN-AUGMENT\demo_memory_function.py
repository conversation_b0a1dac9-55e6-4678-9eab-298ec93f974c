#!/usr/bin/env python3
"""
演示智能体短期记忆功能
"""

import webbrowser
import time

def main():
    """演示记忆功能"""
    print("🧠 智能体短期记忆功能演示")
    print("=" * 50)
    
    print("🌐 正在打开演示页面...")
    
    # 打开主页
    webbrowser.open("http://localhost:8002")
    time.sleep(2)
    
    # 打开iOS演示页面
    webbrowser.open("http://localhost:8002/pages/ios")
    time.sleep(1)
    
    # 打开简化演示页面
    webbrowser.open("http://localhost:8002/pages/simple")
    time.sleep(1)
    
    print("✅ 演示页面已打开")
    
    print("\n🎯 记忆功能演示步骤:")
    print("=" * 50)
    
    print("1. 📱 在iOS演示页面:")
    print("   - 点击'开始问诊'")
    print("   - 输入: '我头痛'")
    print("   - 输入: '在太阳穴附近'")
    print("   - 输入: '已经持续三天了'")
    print("   - 观察AI如何基于历史信息提问")
    
    print("\n2. 🎯 在简化演示页面:")
    print("   - 点击'开始问诊'")
    print("   - 在聊天区域输入症状")
    print("   - 观察AI记忆功能提示")
    
    print("\n3. 🧪 测试记忆效果:")
    print("   - AI应该记住之前提到的症状")
    print("   - 不会重复询问已知信息")
    print("   - 基于历史信息深入询问")
    print("   - 会话ID保持一致")
    
    print("\n💡 记忆功能特色:")
    print("=" * 50)
    print("✅ 智能上下文管理 - AI记住对话历史")
    print("✅ 避免重复询问 - 不会反复问相同问题")
    print("✅ 连贯对话体验 - 基于历史信息回复")
    print("✅ 会话状态管理 - 完整的会话生命周期")
    print("✅ 个性化服务 - 记住患者基本信息")
    
    print("\n🔗 相关地址:")
    print("   主页: http://localhost:8002")
    print("   iOS演示: http://localhost:8002/pages/ios")
    print("   简化演示: http://localhost:8002/pages/simple")
    print("   API文档: http://localhost:8002/api/docs")
    
    print("\n🧪 运行测试:")
    print("   python test_memory_function.py")

if __name__ == "__main__":
    main()
