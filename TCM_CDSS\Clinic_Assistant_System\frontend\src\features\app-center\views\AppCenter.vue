<template>
  <div class="app-center-container">
    <!-- 页面头部 -->
    <div class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        <span>返回</span>
      </div>
      <h1 class="module-title">应用中心</h1>
    </div>

    <!-- 页面内容 -->
    <div class="content-area">
      <div class="app-center-overview">
        <!-- 搜索和筛选 -->
        <div class="search-section">
          <div class="search-bar">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索应用..."
              size="large"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="filter-tabs">
            <el-tabs v-model="activeCategory" @tab-change="handleCategoryChange">
              <el-tab-pane label="全部" name="all" />
              <el-tab-pane label="医疗工具" name="medical" />
              <el-tab-pane label="管理系统" name="management" />
              <el-tab-pane label="数据分析" name="analytics" />
              <el-tab-pane label="教育培训" name="education" />
            </el-tabs>
          </div>
        </div>

        <!-- 应用列表 -->
        <div class="apps-grid">
          <div
            v-for="app in filteredApps"
            :key="app.id"
            class="app-card"
            @click="viewAppDetail(app)"
          >
            <div class="app-icon">
              <el-icon :size="48" :color="app.iconColor">
                <component :is="app.icon" />
              </el-icon>
            </div>
            <div class="app-info">
              <h3 class="app-name">{{ app.name }}</h3>
              <p class="app-description">{{ app.description }}</p>
              <div class="app-meta">
                <span class="app-version">v{{ app.version }}</span>
                <span class="app-size">{{ app.size }}</span>
                <span class="app-rating">
                  <el-icon><Star /></el-icon>
                  {{ app.rating }}
                </span>
              </div>
            </div>
            <div class="app-actions">
              <el-button
                v-if="app.status === 'not-installed'"
                type="primary"
                size="small"
                @click.stop="downloadApp(app)"
                :loading="app.downloading"
              >
                {{ app.downloading ? '下载中' : '下载' }}
              </el-button>
              <el-button
                v-else-if="app.status === 'installed'"
                type="success"
                size="small"
                @click.stop="openApp(app)"
              >
                打开
              </el-button>
              <el-button
                v-else-if="app.status === 'update-available'"
                type="warning"
                size="small"
                @click.stop="updateApp(app)"
                :loading="app.updating"
              >
                {{ app.updating ? '更新中' : '更新' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredApps.length === 0" class="empty-state">
          <el-empty description="暂无应用" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  Search, 
  Star,
  Monitor,
  Document,
  DataAnalysis,
  User,
  Setting,
  Calendar,
  Phone,
  Camera,
  VideoCamera,
  Headset,
  Printer
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const activeCategory = ref('all')

// 应用数据
const apps = ref([
  {
    id: 1,
    name: '智能诊断助手',
    description: '基于AI的智能诊断辅助工具，提供症状分析和诊断建议',
    icon: 'Monitor',
    iconColor: '#007AFF',
    version: '2.1.0',
    size: '45MB',
    rating: 4.8,
    category: 'medical',
    status: 'installed',
    downloading: false,
    updating: false
  },
  {
    id: 2,
    name: '电子病历系统',
    description: '完整的电子病历管理系统，支持病历录入、查询和统计',
    icon: 'Document',
    iconColor: '#34C759',
    version: '3.0.2',
    size: '128MB',
    rating: 4.9,
    category: 'management',
    status: 'update-available',
    downloading: false,
    updating: false
  },
  {
    id: 3,
    name: '数据分析平台',
    description: '医疗数据分析和可视化平台，支持多维度数据分析',
    icon: 'DataAnalysis',
    iconColor: '#FF9500',
    version: '1.5.0',
    size: '89MB',
    rating: 4.6,
    category: 'analytics',
    status: 'not-installed',
    downloading: false,
    updating: false
  },
  {
    id: 4,
    name: '患者管理系统',
    description: '全面的患者信息管理系统，包含预约、随访等功能',
    icon: 'User',
    iconColor: '#5856D6',
    version: '2.3.1',
    size: '67MB',
    rating: 4.7,
    category: 'management',
    status: 'installed',
    downloading: false,
    updating: false
  },
  {
    id: 5,
    name: '中医学习平台',
    description: '中医理论学习和案例分析平台，适合医生继续教育',
    icon: 'Document',
    iconColor: '#AF52DE',
    version: '1.8.0',
    size: '156MB',
    rating: 4.5,
    category: 'education',
    status: 'not-installed',
    downloading: false,
    updating: false
  },
  {
    id: 6,
    name: '远程会诊工具',
    description: '支持视频会诊和远程协作的医疗通讯工具',
    icon: 'VideoCamera',
    iconColor: '#FF3B30',
    version: '2.0.0',
    size: '78MB',
    rating: 4.4,
    category: 'medical',
    status: 'not-installed',
    downloading: false,
    updating: false
  }
])

// 计算属性
const filteredApps = computed(() => {
  let result = apps.value

  // 按分类筛选
  if (activeCategory.value !== 'all') {
    result = result.filter(app => app.category === activeCategory.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(app => 
      app.name.toLowerCase().includes(keyword) ||
      app.description.toLowerCase().includes(keyword)
    )
  }

  return result
})

// 方法
const goBack = () => {
  router.push('/dashboard')
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleCategoryChange = () => {
  // 分类切换逻辑已在计算属性中处理
}

const viewAppDetail = (app: any) => {
  ElMessage.info(`查看应用详情: ${app.name}`)
}

const downloadApp = async (app: any) => {
  app.downloading = true
  try {
    // 模拟下载过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    app.status = 'installed'
    app.downloading = false
    ElMessage.success(`${app.name} 下载完成`)
  } catch (error) {
    app.downloading = false
    ElMessage.error('下载失败，请重试')
  }
}

const openApp = (app: any) => {
  ElMessage.success(`正在打开 ${app.name}`)
}

const updateApp = async (app: any) => {
  app.updating = true
  try {
    // 模拟更新过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    app.status = 'installed'
    app.updating = false
    ElMessage.success(`${app.name} 更新完成`)
  } catch (error) {
    app.updating = false
    ElMessage.error('更新失败，请重试')
  }
}

onMounted(() => {
  // 页面初始化
})
</script>

<style scoped>
.app-center-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.back-button:hover {
  color: var(--primary-dark);
}

.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.content-area {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--spacing-xl);
  overflow-y: auto;
  min-height: 0;
}

.app-center-overview {
  max-width: 1200px;
  width: 100%;
}

.search-section {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-xl);
}

.search-bar {
  margin-bottom: var(--spacing-lg);
}

.filter-tabs {
  margin-top: var(--spacing-md);
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.app-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  gap: var(--spacing-md);
}

.app-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.app-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
}

.app-info {
  flex: 1;
  min-width: 0;
}

.app-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.app-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
  margin: 0 0 var(--spacing-sm) 0;
}

.app-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.app-rating {
  display: flex;
  align-items: center;
  gap: 2px;
}

.app-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

@media (max-width: 768px) {
  .apps-grid {
    grid-template-columns: 1fr;
  }
  
  .app-card {
    flex-direction: column;
    text-align: center;
  }
  
  .app-meta {
    justify-content: center;
  }
}
</style>
