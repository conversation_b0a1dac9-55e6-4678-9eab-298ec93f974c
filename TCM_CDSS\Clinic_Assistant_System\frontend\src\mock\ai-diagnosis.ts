// AI诊断API模拟数据
export interface SyndromeRecommendation {
  syndrome: string;
  formula: string;
  therapy: string;
  selected: boolean | null;
}

export interface ScoreElement {
  syndromeElement: string;
  score: number;
}

export interface MedicinalMaterial {
  medicinalMaterialsId: string;
  medicinalMaterials: string;
  dose: number;
  doseTypeId: string;
  doseType: string;
  modifiedType: string | null;
  modifiedTypeId: string | null;
}

export interface FormulaDose {
  id: string | null;
  formula: string;
  description: string | null;
  dose: MedicinalMaterial[];
}

export interface AIDiagnosisResponse {
  code: number;
  message: string;
  data: {
    recommendList: SyndromeRecommendation[];
    scoresList: ScoreElement[];
    dialecticaAnalysis: string;
    dose: FormulaDose[];
  };
  status: boolean;
  timestamp: string;
}

// 模拟API响应数据
export const mockAIDiagnosisData: AIDiagnosisResponse = {
  "code": 200,
  "message": "请求成功",
  "data": {
    "recommendList": [
      {
        "syndrome": "暑热闭神证",
        "formula": "新加香薷饮",
        "therapy": "清暑健脾",
        "selected": null
      },
      {
        "syndrome": "暑热闭神证",
        "formula": "藿香正气散",
        "therapy": "清暑健脾",
        "selected": null
      },
      {
        "syndrome": "暑湿证",
        "formula": "藿香正气散",
        "therapy": "清暑解表化湿、理气和中",
        "selected": null
      },
      {
        "syndrome": "暑伤津气证",
        "formula": "清暑益气汤",
        "therapy": "清暑泄热、益气生津",
        "selected": null
      }
    ],
    "scoresList": [
      {
        "syndromeElement": "暑",
        "score": 143.0
      }
    ],
    "dialecticaAnalysis": "根据患者症状表现，主要证型为暑热闭神证，伴有暑湿内蕴。患者出现发热、口渴、心烦等症状，符合暑热证的特点。治疗应以清暑泄热为主，兼顾健脾化湿。",
    "dose": [
      {
        "id": null,
        "formula": "新加香薷饮",
        "description": "本方为治疗暑热证之常用方，具有清暑解表、化湿和中之功效。适用于暑热内蕴、表邪未解之证。",
        "dose": [
          {
            "medicinalMaterialsId": "302",
            "medicinalMaterials": "香薷",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "185",
            "medicinalMaterials": "金银花",
            "dose": 9.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "555",
            "medicinalMaterials": "扁豆花",
            "dose": 9.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "41",
            "medicinalMaterials": "厚朴",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "22",
            "medicinalMaterials": "连翘",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          }
        ]
      },
      {
        "id": null,
        "formula": "藿香正气散",
        "description": "本方为治疗夏月感寒伤湿，脾胃失和证之常用方。以恶寒发热，上吐下泻，舌苔白腻为辨证要点。本方解表之力较弱，故\"如欲出汗\",宜\"热服\",且\"衣被盖\"。霍乱吐泻属湿热证者禁服本方。",
        "dose": [
          {
            "medicinalMaterialsId": "89",
            "medicinalMaterials": "大腹皮",
            "dose": 3.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "30",
            "medicinalMaterials": "白芷",
            "dose": 3.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "16",
            "medicinalMaterials": "紫苏叶",
            "dose": 3.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "33",
            "medicinalMaterials": "茯苓",
            "dose": 3.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "597",
            "medicinalMaterials": "半夏曲",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "56",
            "medicinalMaterials": "白术",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "17",
            "medicinalMaterials": "陈皮",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "41",
            "medicinalMaterials": "厚朴",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "31",
            "medicinalMaterials": "桔梗",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "642",
            "medicinalMaterials": "藿香",
            "dose": 9.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "517",
            "medicinalMaterials": "炙甘草",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          }
        ]
      },
      {
        "id": null,
        "formula": "清暑益气汤",
        "description": "本方为治疗暑热气津两伤证之常用方。以身热汗多，口渴心烦，小便短赤，体倦少气，脉虚数为辨证要点。",
        "dose": [
          {
            "medicinalMaterialsId": "81",
            "medicinalMaterials": "西洋参",
            "dose": 5.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "82",
            "medicinalMaterials": "石斛",
            "dose": 15.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "58",
            "medicinalMaterials": "麦冬",
            "dose": 9.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "57",
            "medicinalMaterials": "黄连",
            "dose": 3.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "24",
            "medicinalMaterials": "竹叶",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "556",
            "medicinalMaterials": "荷梗",
            "dose": 15.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "55",
            "medicinalMaterials": "知母",
            "dose": 6.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "15",
            "medicinalMaterials": "甘草",
            "dose": 3.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "524",
            "medicinalMaterials": "粳米",
            "dose": 15.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          },
          {
            "medicinalMaterialsId": "547",
            "medicinalMaterials": "西瓜翠衣",
            "dose": 30.0,
            "doseTypeId": "1",
            "doseType": "克",
            "modifiedType": null,
            "modifiedTypeId": null
          }
        ]
      }
    ]
  },
  "status": true,
  "timestamp": "1749609402043"
};

// 模拟API调用函数
export const getAIDiagnosis = (): Promise<AIDiagnosisResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockAIDiagnosisData);
    }, 500); // 模拟网络延迟
  });
}; 