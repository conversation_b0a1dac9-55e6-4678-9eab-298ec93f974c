/**
 * 路由配置文件
 * 
 * 主要功能：
 * 1. 定义应用的所有路由规则
 * 2. 配置路由守卫，实现登录状态检查
 * 3. 设置路由元信息（如是否需要认证）
 * 
 * 路由结构：
 * - /: 重定向到登录页
 * - /login: 登录页面
 * - /dashboard: 功能中心页面
 * - /appointments: 预约挂号模块
 * - /patients: 患者管理模块
 * - /visits/:visitId/emr: 电子病历模块
 * - /billing: 划价收费模块
 * - /pharmacy: 药房管理模块
 * - /inventory: 库存管理模块
 * - /staff: 员工管理模块
 * - /scheduling: 排班管理模块
 * - /reports: 统计报表模块
 * - /settings: 系统设置模块
 * 
 * 路由守卫逻辑：
 * - 需要认证的页面：检查localStorage中是否有user信息
 * - 已登录用户访问登录页：自动重定向到dashboard
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import SettingsList from '@/features/settings/views/SettingsList.vue'
import StaffList from '@/features/staff/views/StaffList.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/features/auth/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/features/dashboard/views/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/appointments',
    name: 'Appointments',
    component: () => import('@/features/appointments/views/AppointmentList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/patients',
    name: 'Patients',
    component: () => import('@/features/patients/views/PatientList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/patients/:id',
    name: 'PatientDetail',
    component: () => import('@/features/patients/views/PatientDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/visits/:visitId/emr',
    name: 'ElectronicMedicalRecord',
    component: () => import('@/features/emr/views/ElectronicMedicalRecord.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/billing',
    name: 'Billing',
    component: () => import('@/features/billing/views/BillingList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/pharmacy',
    name: 'Pharmacy',
    component: () => import('@/features/pharmacy/views/PharmacyList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory',
    name: 'Inventory',
    component: () => import('@/features/inventory/views/InventoryList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/staff',
    name: 'StaffList',
    component: StaffList,
    meta: { requiresAuth: true }
  },
  {
    path: '/scheduling',
    name: 'SchedulingList',
    component: () => import('@/features/scheduling/views/SchedulingList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/reports',
    name: 'ReportsList',
    component: () => import('@/features/reports/views/ReportsList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: SettingsList,
    meta: { requiresAuth: true }
  },
  {
    path: '/lingji',
    name: 'LingjiManagement',
    component: () => import('@/features/lingji/views/LingjiList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/family-doctor',
    name: 'FamilyDoctorManagement',
    component: () => import('@/features/family-doctor/views/FamilyDoctorList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/research-center',
    name: 'ResearchCenter',
    component: () => import('@/features/research-center/views/ResearchCenterList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/outpatient-workstation',
    name: 'OutpatientWorkstation',
    component: () => import('@/features/outpatient/views/OutpatientWorkstation.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/app-center',
    name: 'AppCenter',
    component: () => import('@/features/app-center/views/AppCenter.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/mall-management',
    name: 'MallManagement',
    component: () => import('@/features/mall-management/views/MallManagement.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/data-center',
    name: 'DataCenter',
    component: () => import('@/features/data-center/views/DataCenterList.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 使用新的认证系统检查登录状态
  const token = localStorage.getItem('auth_token')
  const user = localStorage.getItem('user_info')
  const tokenExpires = localStorage.getItem('token_expires')

  // 检查token是否过期
  let isAuthenticated = false
  if (token && user && tokenExpires) {
    const expiresTime = parseInt(tokenExpires)
    const now = Date.now()
    isAuthenticated = now < expiresTime

    // 如果token过期，清除认证信息
    if (!isAuthenticated) {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      localStorage.removeItem('token_expires')
    }
  }

  console.log('路由守卫 - 目标路径:', to.path)
  console.log('路由守卫 - 认证状态:', isAuthenticated)
  console.log('路由守卫 - 需要认证:', to.meta.requiresAuth)

  if (to.meta.requiresAuth && !isAuthenticated) {
    console.log('路由守卫 - 未认证，重定向到登录页')
    next('/login')
  } else if (to.path === '/login' && isAuthenticated) {
    console.log('路由守卫 - 已登录用户访问登录页，重定向到dashboard')
    next('/dashboard')
  } else {
    console.log('路由守卫 - 正常跳转')
    next()
  }
})

export default router 