# 数据中心模块

## 模块概述

数据中心模块是中医临床决策支持系统的核心数据分析和统计功能模块，为医院管理者和研学中心提供全面的数据支持。

## 功能特性

### 1. 数据统计范围
- **患者数据**: 新增患者、活跃患者、就诊记录等
- **诊疗数据**: 门诊量、诊疗时长、AI辅助使用等
- **处方数据**: 处方类型、用药统计、配伍分析等
- **收费数据**: 收入统计、费用分析、支付方式等
- **库存数据**: 药品库存、消耗统计、采购分析等
- **AI数据**: AI使用频率、准确率、效果评估等

### 2. 核心功能模块
- **数据趋势分析**: 多维度数据趋势图表展示
- **统计报表生成**: 自动生成各类统计报表
- **实时数据看板**: 实时监控各模块数据变化
- **科研数据支持**: 为研学中心提供数据支撑
- **历史数据查询**: 按时间维度查询历史数据
- **数据配置管理**: 灵活配置数据统计规则

### 3. 研学中心数据支持
- **证型分布统计**: 各种证型的分布情况和趋势
- **方剂使用频率**: 经典方剂和现代方剂的使用统计
- **疾病趋势分析**: 常见疾病的发病趋势和季节性变化
- **治疗效果评估**: 不同治疗方法的效果统计和对比
- **药物配伍分析**: 药物配伍的安全性和有效性数据

## 技术架构

### 文件结构
```
data-center/
├── views/
│   └── DataCenterList.vue          # 数据中心主页面
├── services/
│   └── dataStatistics.ts           # 数据统计服务
└── README.md                       # 模块说明文档
```

### 数据服务接口

#### 每日统计数据
```typescript
interface DailyStatistics {
  date: string;
  patientCount: number;
  visitCount: number;
  prescriptionCount: number;
  revenue: number;
  aiUsageCount: number;
  avgDiagnosisTime: number;
}
```

#### 模块使用统计
```typescript
interface ModuleStatistics {
  moduleName: string;
  dailyUsage: number;
  weeklyUsage: number;
  monthlyUsage: number;
  userSatisfaction: number;
  errorRate: number;
}
```

#### AI功能统计
```typescript
interface AIStatistics {
  syndromeAccuracy: number;
  formulaRecommendationRate: number;
  safetyCheckCount: number;
  userFeedbackScore: number;
  improvementSuggestions: string[];
}
```

#### 研学数据
```typescript
interface ResearchData {
  syndromeDistribution: Array<{
    syndrome: string;
    count: number;
    percentage: number;
  }>;
  formulaUsage: Array<{
    formula: string;
    usageCount: number;
    efficacyRate: number;
  }>;
  diseaseTrends: Array<{
    disease: string;
    monthlyData: number[];
    trend: 'increasing' | 'decreasing' | 'stable';
  }>;
  treatmentEffectiveness: Array<{
    treatment: string;
    successRate: number;
    averageTreatmentDays: number;
    patientSatisfaction: number;
  }>;
}
```

## 权限配置

- **管理员**: 完全访问权限，可查看所有数据和导出功能
- **医师**: 可访问基础统计数据和科研数据
- **护士**: 无访问权限
- **药师**: 无访问权限
- **前台**: 无访问权限

## 使用说明

### 1. 访问数据中心
- 从功能中心点击"数据中心"卡片
- 或直接访问路由 `/data-center`

### 2. 查看统计数据
- 页面顶部显示今日核心指标概览
- 卡片悬停可查看详细统计信息
- 支持按时间范围筛选数据

### 3. 数据导出
- 点击页面右上角"导出数据"按钮
- 支持导出CSV格式数据
- 可选择导出的数据类型和时间范围

## 开发状态

### 当前状态
- ✅ 基础页面框架
- ✅ 数据服务接口
- ✅ 权限配置
- ✅ 路由配置
- ✅ 卡片集成

### 待开发功能
- 🔄 实时数据图表
- 🔄 高级筛选功能
- 🔄 自定义报表
- 🔄 数据可视化
- 🔄 导出功能实现
- 🔄 与研学中心数据对接

## 与研学中心的集成

数据中心为研学中心提供以下数据支持：

1. **临床数据**: 为医案学习提供真实的临床数据
2. **统计分析**: 为学术研究提供统计学支持
3. **趋势分析**: 为疾病研究提供流行病学数据
4. **效果评估**: 为治疗方法研究提供疗效数据
5. **知识发现**: 通过数据挖掘发现新的医学知识

## 注意事项

1. **数据安全**: 所有患者数据都经过脱敏处理
2. **权限控制**: 严格按照角色权限控制数据访问
3. **性能优化**: 大数据量查询采用分页和缓存机制
4. **数据一致性**: 确保统计数据的准确性和一致性
5. **隐私保护**: 遵循医疗数据隐私保护相关法规

## 后续规划

1. **Phase 1**: 完善基础统计功能和数据可视化
2. **Phase 2**: 集成高级分析算法和机器学习模型
3. **Phase 3**: 实现实时数据流处理和预警系统
4. **Phase 4**: 构建完整的商业智能(BI)平台
