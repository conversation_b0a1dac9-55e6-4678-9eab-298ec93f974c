# Features 功能模块

功能模块包含应用的具体业务功能，每个模块都是独立的业务单元。

## 目录结构

### dashboard/ - 功能中心
- **views/** - 页面视图
- **components/** - 功能组件
- **composables/** - 组合式函数
- **stores/** - 状态管理
- **types/** - 类型定义
- **constants/** - 常量配置
- **styles/** - 模块样式

### patients/ - 患者管理
- **views/** - 患者列表、详情页面
- **components/** - 患者相关组件
- **composables/** - 患者数据逻辑
- **stores/** - 患者状态管理
- **types/** - 患者类型定义
- **constants/** - 患者相关常量
- **styles/** - 患者模块样式

### outpatient/ - 门诊工作站
- **views/** - 门诊工作站页面
- **components/** - 工作站组件
- **composables/** - 门诊业务逻辑
- **stores/** - 门诊状态管理
- **types/** - 门诊类型定义
- **constants/** - 门诊常量
- **styles/** - 门诊样式

### appointments/ - 预约挂号
- **views/** - 预约相关页面
- **components/** - 预约组件
- **composables/** - 预约逻辑
- **stores/** - 预约状态管理
- **types/** - 预约类型定义
- **constants/** - 预约常量
- **styles/** - 预约样式

### billing/ - 划价收费
### pharmacy/ - 药房管理
### inventory/ - 库存管理
### family-doctor/ - 家医管理
### research-center/ - 研学中心
### app-center/ - 应用中心
### mall-management/ - 商城管理
### scheduling/ - 排班管理
### staff/ - 员工管理
### reports/ - 统计报表
### settings/ - 系统设置

每个功能模块都遵循相同的目录结构：
- **views/** - 页面视图
- **components/** - 功能组件
- **composables/** - 组合式函数
- **stores/** - 状态管理
- **types/** - 类型定义
- **constants/** - 常量配置
- **styles/** - 模块样式 