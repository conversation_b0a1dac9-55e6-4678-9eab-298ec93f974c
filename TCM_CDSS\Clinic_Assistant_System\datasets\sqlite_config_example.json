{
  "database_configs": {
    "development": {
      "dialect": "sqlite",
      "storage": "./datasets/tcm_clinic_system.db",
      "logging": true,
      "pool": {
        "max": 5,
        "min": 0,
        "acquire": 30000,
        "idle": 10000
      },
      "define": {
        "timestamps": true,
        "underscored": true,
        "freezeTableName": true
      }
    },
    "production": {
      "dialect": "sqlite",
      "storage": "./data/tcm_clinic_system.db",
      "logging": false,
      "pool": {
        "max": 10,
        "min": 2,
        "acquire": 30000,
        "idle": 10000
      },
      "define": {
        "timestamps": true,
        "underscored": true,
        "freezeTableName": true
      }
    },
    "test": {
      "dialect": "sqlite",
      "storage": ":memory:",
      "logging": false,
      "pool": {
        "max": 5,
        "min": 0,
        "acquire": 30000,
        "idle": 10000
      },
      "define": {
        "timestamps": true,
        "underscored": true,
        "freezeTableName": true
      }
    }
  },
  "application_configs": {
    "jwt": {
      "secret": "your_jwt_secret_key_here_change_in_production",
      "expiresIn": "24h",
      "issuer": "tcm_clinic_system",
      "audience": "tcm_clinic_users"
    },
    "encryption": {
      "algorithm": "aes-256-gcm",
      "key": "your_encryption_key_here_32_chars_long"
    },
    "file_upload": {
      "max_size": "10MB",
      "allowed_types": ["jpg", "jpeg", "png", "pdf", "doc", "docx"],
      "storage_path": "./uploads",
      "temp_path": "./temp"
    },
    "backup": {
      "enabled": true,
      "schedule": "0 2 * * *",
      "backup_path": "./backups",
      "retention_days": 30,
      "compress": true
    }
  },
  "system_settings": {
    "clinic_name": "中医诊所助手系统",
    "clinic_address": "北京市朝阳区xxx街道xxx号",
    "clinic_phone": "010-12345678",
    "business_hours": {
      "start": "08:30",
      "end": "17:30",
      "lunch_break": {
        "start": "12:00",
        "end": "14:00"
      }
    },
    "appointment_settings": {
      "advance_days": 7,
      "cancel_hours": 2,
      "reminder_hours": 24,
      "max_daily_appointments": 50
    },
    "prescription_settings": {
      "auto_save_interval": 30,
      "require_signature": true,
      "allow_edit_after_confirm": false
    },
    "billing_settings": {
      "auto_generate": true,
      "payment_methods": ["cash", "card", "wechat", "alipay"],
      "invoice_required": false,
      "tax_rate": 0.0
    }
  },
  "sqlite_specific": {
    "pragma_settings": {
      "foreign_keys": "ON",
      "journal_mode": "WAL",
      "synchronous": "NORMAL",
      "cache_size": 10000,
      "temp_store": "MEMORY",
      "mmap_size": *********
    },
    "backup_settings": {
      "auto_backup": true,
      "backup_interval": "daily",
      "max_backups": 7,
      "backup_on_startup": true
    },
    "performance_settings": {
      "connection_pool_size": 5,
      "query_timeout": 30000,
      "busy_timeout": 30000
    }
  },
  "security_settings": {
    "password_policy": {
      "min_length": 8,
      "require_uppercase": true,
      "require_lowercase": true,
      "require_numbers": true,
      "require_symbols": false,
      "expiry_days": 90
    },
    "session_settings": {
      "timeout_minutes": 30,
      "max_concurrent_sessions": 3,
      "remember_me_days": 7
    },
    "audit_settings": {
      "log_all_operations": true,
      "sensitive_operations": [
        "patient_create",
        "patient_edit",
        "prescription_create",
        "billing_payment",
        "staff_create",
        "role_change"
      ],
      "retention_days": 365
    }
  },
  "connection_examples": {
    "node_js_sequelize": {
      "dialect": "sqlite",
      "storage": "./datasets/tcm_clinic_system.db",
      "logging": console.log,
      "define": {
        "timestamps": true,
        "underscored": true
      }
    },
    "node_js_sqlite3": {
      "filename": "./datasets/tcm_clinic_system.db",
      "mode": "OPEN_READWRITE | OPEN_CREATE",
      "verbose": true
    },
    "python_sqlite3": {
      "database": "./datasets/tcm_clinic_system.db",
      "check_same_thread": false,
      "timeout": 30
    },
    "python_sqlalchemy": {
      "url": "sqlite:///./datasets/tcm_clinic_system.db",
      "echo": true,
      "pool_pre_ping": true
    }
  },
  "migration_settings": {
    "migrations_path": "./migrations",
    "models_path": "./models",
    "seeders_path": "./seeders",
    "config_path": "./config/database.json"
  },
  "monitoring": {
    "enable_query_logging": true,
    "slow_query_threshold": 1000,
    "enable_performance_monitoring": true,
    "log_file": "./logs/database.log"
  }
}
