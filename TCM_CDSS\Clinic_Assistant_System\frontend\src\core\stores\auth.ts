/**
 * 认证状态管理
 *
 * 主要功能：
 * 1. 管理用户登录状态
 * 2. 提供登录、登出功能
 * 3. 与后端API集成
 * 4. 提供用户认证状态的计算属性
 *
 * 状态说明：
 * - user: 当前登录用户信息
 * - isAuthenticated: 计算属性，判断用户是否已登录
 * - loading: 登录加载状态
 *
 * 方法说明：
 * - login(credentials): 用户登录，调用后端API
 * - logout(): 用户登出，清除认证信息
 * - checkAuth(): 检查认证状态
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024-01-01
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import AuthService, { type UserInfo, type LoginRequest } from '@/shared/services/authService'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<UserInfo | null>(AuthService.getCurrentUser())
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => AuthService.isLoggedIn() && !!user.value)

  /**
   * 用户登录
   */
  async function login(credentials: LoginRequest): Promise<boolean> {
    loading.value = true
    try {
      // 使用临时登录方法，后续替换为真实API
      const success = await AuthService.tempLogin(credentials)

      if (success) {
        // 更新用户信息
        user.value = AuthService.getCurrentUser()
        return true
      }
      return false
    } catch (error) {
      console.error('Login failed:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 用户登出
   */
  async function logout(): Promise<void> {
    loading.value = true
    try {
      await AuthService.logout()
      user.value = null
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 检查认证状态
   */
  function checkAuth(): boolean {
    const isValid = AuthService.isLoggedIn()
    if (!isValid) {
      user.value = null
    } else {
      user.value = AuthService.getCurrentUser()
    }
    return isValid
  }

  /**
   * 检查用户权限
   */
  function hasPermission(permission: string): boolean {
    return AuthService.hasPermission(permission)
  }

  /**
   * 检查用户角色
   */
  function hasRole(role: string): boolean {
    return AuthService.hasRole(role)
  }

  /**
   * 获取用户显示名称
   */
  const userDisplayName = computed(() => {
    return user.value?.full_name || '未知用户'
  })

  /**
   * 获取用户角色显示名称
   */
  const userRoleDisplayName = computed(() => {
    return user.value?.role_name || '未知角色'
  })

  return {
    // 状态
    user,
    loading,

    // 计算属性
    isAuthenticated,
    userDisplayName,
    userRoleDisplayName,

    // 方法
    login,
    logout,
    checkAuth,
    hasPermission,
    hasRole
  }
})