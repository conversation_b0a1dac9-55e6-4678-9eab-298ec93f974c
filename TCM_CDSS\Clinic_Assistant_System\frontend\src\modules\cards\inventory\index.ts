// 库存管理卡片模块
import type { CardModule } from '../index'

// 库存管理统计数据
export const inventoryStats = {
  lowStockItems: 12,
  totalItems: 456,
  outOfStock: 3,
  recentUpdates: 8
}

// 库存管理卡片配置
export const inventoryCard: CardModule = {
  key: 'inventory',
  name: '库存管理',
  route: '/inventory',
  icon: 'Box',
  color: '#AF52DE',
  info: `${inventoryStats.lowStockItems} 种药品库存不足`,
  hasDetailStats: false,
  statsData: inventoryStats,
  permission: 'inventory_manage'
}

// 库存管理详细统计数据获取函数
export const getInventoryDetailStats = () => {
  return {
    lowStock: inventoryStats.lowStockItems,
    total: inventoryStats.totalItems,
    outOfStock: inventoryStats.outOfStock,
    recentUpdates: inventoryStats.recentUpdates
  }
}

// 库存管理卡片信息更新函数
export const updateInventoryInfo = () => {
  return `${inventoryStats.lowStockItems} 种药品库存不足`
}
