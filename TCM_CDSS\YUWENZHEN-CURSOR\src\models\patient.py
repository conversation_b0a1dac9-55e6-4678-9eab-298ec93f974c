"""
患者信息数据模型

定义患者的基本信息、个人资料等数据结构。
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr


class PatientInfo(BaseModel):
    """患者基本信息模型"""
    
    patient_id: str = Field(description="患者唯一标识符")
    name: str = Field(description="患者姓名")
    gender: str = Field(description="性别", pattern="^(男|女)$")
    age: int = Field(ge=0, le=150, description="年龄")
    birth_date: Optional[datetime] = Field(default=None, description="出生日期")
    id_card: Optional[str] = Field(default=None, description="身份证号", pattern="^\d{17}[\dXx]$")
    phone: Optional[str] = Field(default=None, description="联系电话", pattern="^1[3-9]\d{9}$")
    email: Optional[EmailStr] = Field(default=None, description="电子邮箱")
    address: Optional[str] = Field(default=None, description="居住地址")
    emergency_contact: Optional[str] = Field(default=None, description="紧急联系人")
    emergency_phone: Optional[str] = Field(default=None, description="紧急联系电话", pattern="^1[3-9]\d{9}$")
    
    # 职业信息
    occupation: Optional[str] = Field(default=None, description="职业")
    work_unit: Optional[str] = Field(default=None, description="工作单位")
    
    # 医疗信息
    blood_type: Optional[str] = Field(default=None, description="血型")
    height: Optional[float] = Field(default=None, description="身高(cm)")
    weight: Optional[float] = Field(default=None, description="体重(kg)")
    bmi: Optional[float] = Field(default=None, description="BMI指数")
    
    # 系统信息
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    is_active: bool = Field(default=True, description="是否激活")
    
    class Config:
        json_schema_extra = {
            "example": {
                "patient_id": "P20241201001",
                "name": "张三",
                "gender": "男",
                "age": 35,
                "birth_date": "1988-12-01T00:00:00",
                "id_card": "110101198812011234",
                "phone": "13800138000",
                "email": "<EMAIL>",
                "address": "北京市朝阳区xxx街道xxx号",
                "emergency_contact": "李四",
                "emergency_phone": "13900139000",
                "occupation": "软件工程师",
                "work_unit": "某科技有限公司",
                "blood_type": "A型",
                "height": 175.0,
                "weight": 70.0,
                "bmi": 22.9
            }
        }


class PatientProfile(BaseModel):
    """患者档案模型"""
    
    patient_id: str = Field(description="患者ID")
    medical_history_summary: Optional[str] = Field(default=None, description="病史摘要")
    family_history_summary: Optional[str] = Field(default=None, description="家族史摘要")
    allergy_history: List[str] = Field(default_factory=list, description="过敏史")
    current_medications: List[str] = Field(default_factory=list, description="当前用药")
    lifestyle_factors: dict = Field(default_factory=dict, description="生活方式因素")
    
    # 中医体质信息
    constitution_type: Optional[str] = Field(default=None, description="中医体质类型")
    constitution_description: Optional[str] = Field(default=None, description="体质描述")
    
    # 系统信息
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "patient_id": "P20241201001",
                "medical_history_summary": "患者既往体健，无重大疾病史",
                "family_history_summary": "父亲有高血压病史",
                "allergy_history": ["青霉素", "海鲜"],
                "current_medications": ["维生素D"],
                "lifestyle_factors": {
                    "smoking": False,
                    "drinking": "偶尔",
                    "exercise": "每周3次",
                    "sleep_hours": 7
                },
                "constitution_type": "平和质",
                "constitution_description": "体质平和，适应能力强"
            }
        }


class PatientUpdateRequest(BaseModel):
    """患者信息更新请求模型"""
    
    name: Optional[str] = Field(default=None, description="患者姓名")
    phone: Optional[str] = Field(default=None, description="联系电话", pattern="^1[3-9]\d{9}$")
    email: Optional[EmailStr] = Field(default=None, description="电子邮箱")
    address: Optional[str] = Field(default=None, description="居住地址")
    emergency_contact: Optional[str] = Field(default=None, description="紧急联系人")
    emergency_phone: Optional[str] = Field(default=None, description="紧急联系电话", pattern="^1[3-9]\d{9}$")
    occupation: Optional[str] = Field(default=None, description="职业")
    work_unit: Optional[str] = Field(default=None, description="工作单位")
    height: Optional[float] = Field(default=None, description="身高(cm)")
    weight: Optional[float] = Field(default=None, description="体重(kg)")
    
    class Config:
        json_schema_extra = {
            "example": {
                "phone": "13800138001",
                "email": "<EMAIL>",
                "address": "北京市海淀区xxx街道xxx号",
                "weight": 72.0
            }
        } 