<template>
  <div class="appointment-container">
    <!-- 头部导航栏 -->
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">预约中心</h1>
    </header>

    <!-- 三栏式布局容器 -->
    <div class="main-content">
      <!-- 左侧栏：筛选和搜索 -->
      <div class="left-panel">
        <!-- 搜索区域 -->
        <div class="search-section">
          <div class="search-container">
            <el-icon class="search-icon"><Search /></el-icon>
            <input
              v-model="searchQuery"
              type="text"
              class="search-input"
              placeholder="搜索患者姓名或手机号"
              @input="handleSearch"
            />
          </div>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </div>

        <!-- 日期筛选 -->
        <div class="filter-section">
          <h3 class="filter-title">日期筛选</h3>
          <el-select v-model="dateFilter" placeholder="选择日期" style="width: 100%;">
            <el-option label="今日预约" value="today" />
            <el-option label="明日预约" value="tomorrow" />
            <el-option label="本周预约" value="thisWeek" />
            <el-option label="自定义日期" value="custom" />
          </el-select>

          <el-date-picker
            v-if="dateFilter === 'custom'"
            v-model="customDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%; margin-top: 8px;"
            @change="handleDateChange"
          />
        </div>

        <!-- 医师筛选 -->
        <div class="filter-section">
          <h3 class="filter-title">医师筛选</h3>
          <el-select v-model="selectedDoctor" placeholder="选择医师" clearable style="width: 100%;">
            <el-option label="全部医师" value="" />
            <el-option v-for="doc in doctors" :key="doc" :label="doc" :value="doc" />
          </el-select>
        </div>

        <!-- 状态筛选 -->
        <div class="filter-section">
          <h3 class="filter-title">预约状态</h3>
          <el-select v-model="statusFilter" placeholder="选择状态" clearable style="width: 100%;">
            <el-option label="全部状态" value="" />
            <el-option label="待就诊" value="pending" />
            <el-option label="已就诊" value="completed" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="未到" value="absent" />
          </el-select>
        </div>

        <!-- 预约来源筛选 -->
        <div class="filter-section">
          <h3 class="filter-title">预约来源</h3>
          <el-select v-model="sourceFilter" placeholder="选择来源" clearable style="width: 100%;">
            <el-option label="全部来源" value="" />
            <el-option label="线上预约" value="online" />
            <el-option label="现场挂号" value="offline" />
            <el-option label="电话预约" value="phone" />
          </el-select>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <h3 class="filter-title">今日统计</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.total }}</div>
              <div class="stat-label">总预约</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.pending }}</div>
              <div class="stat-label">待就诊</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.cancelled }}</div>
              <div class="stat-label">已取消</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间栏：预约患者列表 -->
      <div class="center-panel">
        <div class="list-header">
          <div class="header-info">
            <h2>预约患者列表</h2>
            <span class="count-info">共 {{ filteredAppointments.length }} 条预约</span>
          </div>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新建预约
          </el-button>
        </div>

        <div class="appointment-list">
          <div
            v-for="appointment in filteredAppointments"
            :key="appointment.id"
            class="appointment-card"
            :class="{ 'selected': selectedAppointment?.id === appointment.id }"
            @click="selectAppointment(appointment)"
          >
            <div class="card-header">
              <div class="patient-info">
                <div class="patient-avatar">
                  {{ appointment.patient.charAt(0) }}
                </div>
                <div class="patient-details">
                  <div class="patient-name">{{ appointment.patient }}</div>
                  <div class="patient-phone">{{ appointment.phone }}</div>
                </div>
              </div>
              <div class="appointment-status">
                <el-tag :type="getStatusTagType(appointment.status)" size="small">
                  {{ getStatusText(appointment.status) }}
                </el-tag>
              </div>
            </div>

            <div class="card-content">
              <div class="info-row">
                <el-icon class="info-icon"><User /></el-icon>
                <span class="info-text">医师：{{ appointment.doctor }}</span>
              </div>
              <div class="info-row">
                <el-icon class="info-icon"><Clock /></el-icon>
                <span class="info-text">时间：{{ formatDateTime(appointment.date, appointment.time) }}</span>
              </div>
              <div class="info-row">
                <el-icon class="info-icon"><Location /></el-icon>
                <span class="info-text">来源：{{ getSourceText(appointment.source) }}</span>
              </div>
              <div v-if="appointment.note" class="info-row">
                <el-icon class="info-icon"><Document /></el-icon>
                <span class="info-text">备注：{{ appointment.note }}</span>
              </div>
            </div>

            <div class="card-actions">
              <el-button size="small" @click.stop="editAppointment(appointment)">
                编辑
              </el-button>
              <el-button
                v-if="appointment.status === 'pending'"
                size="small"
                type="success"
                @click.stop="startConsultation(appointment)"
              >
                开始接诊
              </el-button>
              <el-button
                v-if="appointment.status === 'pending'"
                size="small"
                type="warning"
                @click.stop="cancelAppointment(appointment)"
              >
                取消预约
              </el-button>
            </div>
          </div>

          <div v-if="filteredAppointments.length === 0" class="empty-state">
            <el-icon size="64" color="#C7C7CC"><Calendar /></el-icon>
            <p>暂无预约数据</p>
            <el-button type="primary" @click="showCreateDialog = true">
              立即创建预约
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧栏：预约详情和操作 -->
      <div class="right-panel">
        <div v-if="selectedAppointment" class="appointment-detail">
          <div class="detail-header">
            <h3>预约详情</h3>
            <el-button size="small" @click="selectedAppointment = null">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>

          <div class="detail-content">
            <!-- 患者基本信息 -->
            <div class="detail-section">
              <h4 class="section-title">患者信息</h4>
              <div class="patient-detail">
                <div class="patient-avatar-large">
                  {{ selectedAppointment.patient.charAt(0) }}
                </div>
                <div class="patient-info-detail">
                  <div class="info-item">
                    <span class="label">姓名：</span>
                    <span class="value">{{ selectedAppointment.patient }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">手机：</span>
                    <span class="value">{{ selectedAppointment.phone }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">性别：</span>
                    <span class="value">{{ selectedAppointment.gender || '未知' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">年龄：</span>
                    <span class="value">{{ selectedAppointment.age || '未知' }}岁</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 预约信息 -->
            <div class="detail-section">
              <h4 class="section-title">预约信息</h4>
              <div class="appointment-info">
                <div class="info-item">
                  <span class="label">医师：</span>
                  <span class="value">{{ selectedAppointment.doctor }}</span>
                </div>
                <div class="info-item">
                  <span class="label">日期：</span>
                  <span class="value">{{ formatDate(selectedAppointment.date) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">时间：</span>
                  <span class="value">{{ selectedAppointment.time }}</span>
                </div>
                <div class="info-item">
                  <span class="label">状态：</span>
                  <el-tag :type="getStatusTagType(selectedAppointment.status)" size="small">
                    {{ getStatusText(selectedAppointment.status) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">来源：</span>
                  <span class="value">{{ getSourceText(selectedAppointment.source) }}</span>
                </div>
                <div v-if="selectedAppointment.note" class="info-item">
                  <span class="label">备注：</span>
                  <span class="value">{{ selectedAppointment.note }}</span>
                </div>
              </div>
            </div>

            <!-- 历史就诊记录 -->
            <div class="detail-section">
              <h4 class="section-title">历史就诊</h4>
              <div class="history-list">
                <div
                  v-for="record in getPatientHistory(selectedAppointment.patient)"
                  :key="record.id"
                  class="history-item"
                >
                  <div class="history-date">{{ formatDate(record.date) }}</div>
                  <div class="history-diagnosis">{{ record.diagnosis }}</div>
                  <div class="history-doctor">{{ record.doctor }}</div>
                </div>
                <div v-if="getPatientHistory(selectedAppointment.patient).length === 0" class="no-history">
                  暂无历史就诊记录
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="detail-actions">
              <el-button type="primary" @click="editAppointment(selectedAppointment)">
                <el-icon><Edit /></el-icon>
                编辑预约
              </el-button>
              <el-button
                v-if="selectedAppointment.status === 'pending'"
                type="success"
                @click="startConsultation(selectedAppointment)"
              >
                <el-icon><User /></el-icon>
                开始接诊
              </el-button>
              <el-button
                v-if="selectedAppointment.status === 'pending'"
                type="warning"
                @click="cancelAppointment(selectedAppointment)"
              >
                <el-icon><Close /></el-icon>
                取消预约
              </el-button>
              <el-button @click="printAppointment(selectedAppointment)">
                <el-icon><Printer /></el-icon>
                打印
              </el-button>
            </div>
          </div>
        </div>

        <div v-else class="no-selection">
          <el-icon size="64" color="#C7C7CC"><Calendar /></el-icon>
          <p>请选择预约查看详情</p>
        </div>
      </div>
    </div>

    <!-- 新建/编辑预约弹窗 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editMode ? '编辑预约' : '新建预约'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="appointmentFormRef"
        :model="appointmentForm"
        :rules="appointmentRules"
        label-width="80px"
      >
        <el-form-item label="选择患者" prop="patientId">
          <PatientSelector
            v-model="appointmentForm.patientId"
            @patient-selected="handlePatientSelected"
          />
        </el-form-item>

        <!-- 患者信息显示 -->
        <div v-if="selectedPatient" class="patient-info-display">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="姓名">{{ selectedPatient.name }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ selectedPatient.gender }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ selectedPatient.age }}岁</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ selectedPatient.phone }}</el-descriptions-item>
            <el-descriptions-item label="诊断" span="2">{{ selectedPatient.diagnosis }}</el-descriptions-item>
            <el-descriptions-item label="就诊次数" span="2">{{ selectedPatient.visitCount }}次</el-descriptions-item>
          </el-descriptions>
        </div>
        <el-form-item label="医师" prop="doctor">
          <el-select v-model="appointmentForm.doctor" placeholder="请选择医师">
            <el-option v-for="doc in doctors" :key="doc" :label="doc" :value="doc" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="appointmentForm.date"
            type="date"
            placeholder="选择预约日期"
            style="width: 100%;"
            :disabled-date="disabledDate"
          />
        </el-form-item>
        <el-form-item label="时间" prop="time">
          <el-time-select
            v-model="appointmentForm.time"
            start="08:00"
            step="00:30"
            end="18:00"
            placeholder="选择预约时间"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="预约来源" prop="source">
          <el-select v-model="appointmentForm.source" placeholder="请选择预约来源">
            <el-option label="线上预约" value="online" />
            <el-option label="现场挂号" value="offline" />
            <el-option label="电话预约" value="phone" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            v-model="appointmentForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAppointment">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Plus, Search, User, Clock, Location, Document, Calendar,
  Close, Edit, Printer
} from '@element-plus/icons-vue'
import { appointmentService, type Appointment } from '@/mock/database/appointments'
import { patientService, type Patient } from '@/mock/database/patients'
import PatientSelector from '../components/PatientSelector.vue'

const router = useRouter()
const goBack = () => router.push('/dashboard')

// 医师列表
const doctors = ['张医生', '王医生', '李医生', '赵医生', '陈医生']

// 预约数据
const appointments = ref<Appointment[]>([])
const loading = ref(false)

// 历史就诊记录
const patientHistory = ref<{ id: string; patient: string; date: string; diagnosis: string; doctor: string }[]>([])

// 搜索和筛选
const searchQuery = ref('')
const dateFilter = ref('today')
const customDate = ref(new Date())
const selectedDoctor = ref('')
const statusFilter = ref('')
const sourceFilter = ref('')

// 选中的预约
const selectedAppointment = ref(null)

// 过滤后的预约
const filteredAppointments = computed(() => {
  let filtered = appointments.value

  // 搜索过滤
  if (searchQuery.value) {
    filtered = filtered.filter(appointment =>
      appointment.patient.includes(searchQuery.value) ||
      appointment.phone.includes(searchQuery.value) ||
      appointment.doctor.includes(searchQuery.value)
    )
  }

  // 日期过滤
  if (dateFilter.value) {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)

    if (dateFilter.value === 'today') {
      const todayStr = today.toISOString().split('T')[0]
      filtered = filtered.filter(appointment => appointment.date === todayStr)
    } else if (dateFilter.value === 'tomorrow') {
      const tomorrowStr = tomorrow.toISOString().split('T')[0]
      filtered = filtered.filter(appointment => appointment.date === tomorrowStr)
    } else if (dateFilter.value === 'thisWeek') {
      const weekStart = new Date(today)
      weekStart.setDate(today.getDate() - today.getDay())
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekStart.getDate() + 6)

      filtered = filtered.filter(appointment => {
        const appointmentDate = new Date(appointment.date)
        return appointmentDate >= weekStart && appointmentDate <= weekEnd
      })
    } else if (dateFilter.value === 'custom' && customDate.value) {
      const customDateStr = customDate.value.toISOString().split('T')[0]
      filtered = filtered.filter(appointment => appointment.date === customDateStr)
    }
  }

  // 医师过滤
  if (selectedDoctor.value) {
    filtered = filtered.filter(appointment => appointment.doctor === selectedDoctor.value)
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(appointment => appointment.status === statusFilter.value)
  }

  // 来源过滤
  if (sourceFilter.value) {
    filtered = filtered.filter(appointment => appointment.source === sourceFilter.value)
  }

  return filtered.sort((a, b) => {
    // 按日期和时间排序
    const dateTimeA = new Date(`${a.date} ${a.time}`)
    const dateTimeB = new Date(`${b.date} ${b.time}`)
    return dateTimeA.getTime() - dateTimeB.getTime()
  })
})

// 今日统计
const todayStats = ref({
  total: 0,
  pending: 0,
  completed: 0,
  cancelled: 0
})

// 新建/编辑预约
const showCreateDialog = ref(false)
const editMode = ref(false)
const appointmentFormRef = ref()
const selectedPatient = ref<Patient | null>(null)
const appointmentForm = reactive({
  id: '',
  patientId: null as number | null,
  patient: '',
  phone: '',
  gender: '',
  age: null as number | null,
  doctor: '',
  date: '',
  time: '',
  source: 'offline',
  note: '',
  diagnosis: '',
  visitCount: 0
})

const appointmentRules = {
  patientId: [{ required: true, message: '请选择患者', trigger: 'change' }],
  doctor: [{ required: true, message: '请选择医师', trigger: 'change' }],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  time: [{ required: true, message: '请选择时间', trigger: 'change' }],
  source: [{ required: true, message: '请选择预约来源', trigger: 'change' }]
}

// 数据加载方法
const loadAppointments = async () => {
  loading.value = true
  try {
    const data = await appointmentService.getAllAppointments()
    appointments.value = data
  } catch (error) {
    console.error('加载预约数据失败:', error)
    ElMessage.error('加载预约数据失败')
  } finally {
    loading.value = false
  }
}

const loadTodayStats = async () => {
  try {
    const stats = await appointmentService.getTodayStats()
    todayStats.value = stats
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 方法
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

const handleDateChange = () => {
  // 自定义日期变化处理
}

const selectAppointment = (appointment: Appointment) => {
  selectedAppointment.value = appointment
  loadPatientHistory(appointment.patientId)
}

// 加载患者历史记录
const loadPatientHistory = async (patientId: number) => {
  try {
    const appointments = await appointmentService.getAppointmentsByPatientId(patientId)
    // 转换为历史记录格式
    patientHistory.value = appointments
      .filter(a => a.status === 'completed')
      .map(a => ({
        id: a.id,
        patient: a.patient,
        date: a.date,
        diagnosis: a.diagnosis || '无诊断信息',
        doctor: a.doctor
      }))
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  } catch (error) {
    console.error('加载患者历史记录失败:', error)
  }
}

// 处理患者选择
const handlePatientSelected = (patient: Patient | null) => {
  selectedPatient.value = patient
  if (patient) {
    appointmentForm.patientId = patient.id
    appointmentForm.patient = patient.name
    appointmentForm.phone = patient.phone
    appointmentForm.gender = patient.gender
    appointmentForm.age = patient.age
    appointmentForm.diagnosis = patient.diagnosis
    appointmentForm.visitCount = patient.visitCount
  } else {
    appointmentForm.patientId = null
    appointmentForm.patient = ''
    appointmentForm.phone = ''
    appointmentForm.gender = ''
    appointmentForm.age = null
    appointmentForm.diagnosis = ''
    appointmentForm.visitCount = 0
  }
}

const saveAppointment = async () => {
  if (!appointmentFormRef.value) return
  try {
    await appointmentFormRef.value.validate()

    if (editMode.value) {
      // 编辑预约
      const updateData = {
        patientId: appointmentForm.patientId!,
        patient: appointmentForm.patient,
        phone: appointmentForm.phone,
        gender: appointmentForm.gender,
        age: appointmentForm.age!,
        doctor: appointmentForm.doctor,
        date: appointmentForm.date,
        time: appointmentForm.time,
        source: appointmentForm.source,
        note: appointmentForm.note,
        diagnosis: appointmentForm.diagnosis,
        visitCount: appointmentForm.visitCount
      }

      const updatedAppointment = await appointmentService.updateAppointment(appointmentForm.id, updateData)
      if (updatedAppointment) {
        const idx = appointments.value.findIndex(a => a.id === appointmentForm.id)
        if (idx > -1) {
          appointments.value[idx] = updatedAppointment
        }
        ElMessage.success('预约已更新')
      }
    } else {
      // 新建预约
      const newAppointmentData = {
        patientId: appointmentForm.patientId!,
        patient: appointmentForm.patient,
        phone: appointmentForm.phone,
        gender: appointmentForm.gender,
        age: appointmentForm.age!,
        doctor: appointmentForm.doctor,
        date: appointmentForm.date,
        time: appointmentForm.time,
        status: 'pending' as const,
        source: appointmentForm.source as 'online' | 'offline' | 'phone',
        note: appointmentForm.note,
        diagnosis: appointmentForm.diagnosis,
        visitCount: appointmentForm.visitCount
      }

      const newAppointment = await appointmentService.createAppointment(newAppointmentData)
      appointments.value.unshift(newAppointment)

      // 更新患者的下次预约时间
      if (selectedPatient.value) {
        await patientService.updatePatient(selectedPatient.value.id, {
          nextAppointment: `${appointmentForm.date} ${appointmentForm.time}`
        })
      }

      ElMessage.success('预约已创建')
    }

    showCreateDialog.value = false
    resetForm()
    loadTodayStats() // 重新加载统计数据
  } catch (error) {
    console.error('保存预约失败:', error)
    ElMessage.error('保存预约失败')
  }
}

const editAppointment = async (appointment: Appointment) => {
  // 加载患者信息
  try {
    const patient = await patientService.getPatientById(appointment.patientId)
    if (patient) {
      selectedPatient.value = patient
    }
  } catch (error) {
    console.error('加载患者信息失败:', error)
  }

  Object.assign(appointmentForm, {
    ...appointment,
    date: new Date(appointment.date)
  })
  editMode.value = true
  showCreateDialog.value = true
}

const startConsultation = (appointment: any) => {
  ElMessageBox.confirm(
    `确定开始为患者 ${appointment.patient} 接诊吗？`,
    '开始接诊',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    // 跳转到门诊工作台
    router.push(`/outpatient?appointmentId=${appointment.id}`)
  }).catch(() => {
    // 取消操作
  })
}

const cancelAppointment = async (appointment: Appointment) => {
  try {
    await ElMessageBox.confirm(
      `确定取消患者 ${appointment.patient} 的预约吗？`,
      '取消预约',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const updatedAppointment = await appointmentService.updateAppointment(appointment.id, {
      status: 'cancelled'
    })

    if (updatedAppointment) {
      const idx = appointments.value.findIndex(a => a.id === appointment.id)
      if (idx > -1) {
        appointments.value[idx] = updatedAppointment
      }

      // 清除患者的下次预约时间
      await patientService.updatePatient(appointment.patientId, {
        nextAppointment: null
      })

      ElMessage.success('预约已取消')
      loadTodayStats() // 重新加载统计数据

      if (selectedAppointment.value?.id === appointment.id) {
        selectedAppointment.value = null
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消预约失败:', error)
      ElMessage.error('取消预约失败')
    }
  }
}

const printAppointment = (appointment: any) => {
  ElMessage.info('打印功能开发中...')
}

const resetForm = () => {
  Object.assign(appointmentForm, {
    id: '',
    patientId: null,
    patient: '',
    phone: '',
    gender: '',
    age: null,
    doctor: '',
    date: '',
    time: '',
    source: 'offline',
    note: '',
    diagnosis: '',
    visitCount: 0
  })
  selectedPatient.value = null
  editMode.value = false

  if (appointmentFormRef.value) {
    appointmentFormRef.value.clearValidate()
  }
}

// 工具方法
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待就诊',
    completed: '已就诊',
    cancelled: '已取消',
    absent: '未到'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const typeMap = {
    pending: 'warning',
    completed: 'success',
    cancelled: 'danger',
    absent: 'info'
  }
  return typeMap[status] || 'default'
}

const getSourceText = (source: string) => {
  const sourceMap = {
    online: '线上预约',
    offline: '现场挂号',
    phone: '电话预约'
  }
  return sourceMap[source] || '未知'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

const formatDateTime = (dateStr: string, timeStr: string) => {
  if (!dateStr || !timeStr) return ''
  const date = new Date(dateStr)
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)

  let dateText = ''
  if (date.toDateString() === today.toDateString()) {
    dateText = '今日'
  } else if (date.toDateString() === tomorrow.toDateString()) {
    dateText = '明日'
  } else {
    dateText = `${date.getMonth() + 1}月${date.getDate()}日`
  }

  return `${dateText} ${timeStr}`
}

const getPatientHistory = (patientName: string) => {
  return patientHistory.value.filter(record => record.patient === patientName)
}

const disabledDate = (time: Date) => {
  // 禁用过去的日期
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 页面初始化
onMounted(async () => {
  // 默认显示今日预约
  dateFilter.value = 'today'

  // 加载数据
  await Promise.all([
    loadAppointments(),
    loadTodayStats()
  ])
})
</script>

<style lang="scss" scoped>
.appointment-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.module-header {
  background: var(--surface-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
  &:hover { color: var(--primary-dark); }
}

.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

/* 三栏布局 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧栏样式 */
.left-panel {
  width: 280px;
  background: var(--surface-color);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.search-section {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.search-container {
  position: relative;
  margin-bottom: var(--spacing-sm);
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}

.search-input {
  width: 100%;
  height: 36px;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 44px;
  border: 1px solid var(--border-color);
  border-radius: 18px;
  background: var(--background-color);
  font-size: var(--font-size-md);
  outline: none;
  transition: all var(--transition-fast);
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.filter-section {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.filter-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.stats-section {
  padding: var(--spacing-md);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-sm);
  background: var(--background-color);
  border-radius: var(--border-radius-small);
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: 2px;
}

/* 中间栏样式 */
.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
  overflow: hidden;
}

.list-header {
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-info h2 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.count-info {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-left: var(--spacing-sm);
}

.appointment-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.appointment-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  cursor: pointer;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-light);
    transform: translateY(-1px);
  }

  &.selected {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.patient-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.patient-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-md);
}

.patient-details {
  .patient-name {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
  }
  .patient-phone {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: 2px;
  }
}

.card-content {
  margin: var(--spacing-sm) 0;
}

.info-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

.info-icon {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.info-text {
  color: var(--text-secondary);
}

.card-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--border-color);
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);

  p {
    margin: var(--spacing-md) 0;
    font-size: var(--font-size-md);
  }
}

/* 右侧栏样式 */
.right-panel {
  width: 320px;
  background: var(--surface-color);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.appointment-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;

  h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
  }
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.detail-section {
  margin-bottom: var(--spacing-lg);

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}

.patient-detail {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
}

.patient-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.patient-info-detail {
  flex: 1;
}

.info-item {
  display: flex;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);

  .label {
    color: var(--text-secondary);
    min-width: 50px;
  }

  .value {
    color: var(--text-primary);
    font-weight: 500;
  }
}

.appointment-info {
  .info-item {
    margin-bottom: var(--spacing-sm);
  }
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  padding: var(--spacing-sm);
  background: var(--background-color);
  border-radius: var(--border-radius-small);
  margin-bottom: var(--spacing-xs);
  border-left: 3px solid var(--primary-color);
}

.history-date {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.history-diagnosis {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 2px;
}

.history-doctor {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.no-history {
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  padding: var(--spacing-lg);
}

.detail-actions {
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.no-selection {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);

  p {
    margin: var(--spacing-md) 0;
    font-size: var(--font-size-md);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .right-panel {
    width: 280px;
  }
}

@media (max-width: 1024px) {
  .left-panel {
    width: 240px;
  }

  .right-panel {
    width: 260px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .left-panel,
  .right-panel {
    width: 100%;
    height: auto;
    max-height: 200px;
  }

  .center-panel {
    flex: 1;
    min-height: 400px;
  }
}

/* 患者信息显示样式 */
.patient-info-display {
  margin: 16px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.patient-info-display :deep(.el-descriptions__body) {
  background: transparent;
}

.patient-info-display :deep(.el-descriptions__table) {
  margin: 0;
}

.patient-info-display :deep(.el-descriptions__label) {
  font-weight: 500;
  color: #495057;
}

.patient-info-display :deep(.el-descriptions__content) {
  color: #212529;
}
</style>