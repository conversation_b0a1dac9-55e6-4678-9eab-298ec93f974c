<template>
  <div class="patient-detail-page">
    <div class="page-header">
      <h2>患者详情</h2>
      <el-button type="text" class="close-btn" @click="$emit('close')">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <div class="page-content">
      <div class="patient-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>姓名：</label>
              <span>{{ selectedPatient?.name || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>性别：</label>
              <span>{{ selectedPatient?.gender || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>年龄：</label>
              <span>{{ selectedPatient?.age || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>身份证号：</label>
              <span>{{ selectedPatient?.idCard || '未提供' }}</span>
            </div>
            <div class="info-item">
              <label>联系电话：</label>
              <span>{{ selectedPatient?.phone || '未提供' }}</span>
            </div>
            <div class="info-item">
              <label>地址：</label>
              <span>{{ selectedPatient?.address || '未提供' }}</span>
            </div>
            <div class="info-item">
              <label>职业：</label>
              <span>{{ selectedPatient?.occupation || '未提供' }}</span>
            </div>
            <div class="info-item">
              <label>婚姻状况：</label>
              <span>{{ selectedPatient?.maritalStatus || '未提供' }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>就诊记录</h3>
          <div class="visit-history">
            <el-table :data="visitHistory" size="small" border>
              <el-table-column prop="date" label="就诊日期" width="120" />
              <el-table-column prop="department" label="科室" width="100" />
              <el-table-column prop="doctor" label="医生" width="100" />
              <el-table-column prop="diagnosis" label="诊断" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)" size="small">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="text" size="small" @click="viewMedicalRecord(scope.row)">
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>过敏史</h3>
          <div class="allergy-list">
            <el-tag 
              v-for="allergy in selectedPatient?.allergies || []" 
              :key="allergy"
              type="danger"
              size="small"
              class="allergy-tag"
            >
              {{ allergy }}
            </el-tag>
            <span v-if="!selectedPatient?.allergies?.length" class="no-data">
              暂无过敏史记录
            </span>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>家族史</h3>
          <p>{{ selectedPatient?.familyHistory || '暂无家族史记录' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>既往史</h3>
          <p>{{ selectedPatient?.pastHistory || '暂无既往史记录' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>生活习惯</h3>
          <div class="habits-grid">
            <div class="habit-item">
              <label>吸烟：</label>
              <span>{{ selectedPatient?.habits?.smoking || '未记录' }}</span>
            </div>
            <div class="habit-item">
              <label>饮酒：</label>
              <span>{{ selectedPatient?.habits?.drinking || '未记录' }}</span>
            </div>
            <div class="habit-item">
              <label>运动：</label>
              <span>{{ selectedPatient?.habits?.exercise || '未记录' }}</span>
            </div>
            <div class="habit-item">
              <label>饮食偏好：</label>
              <span>{{ selectedPatient?.habits?.diet || '未记录' }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>紧急联系人</h3>
          <div class="emergency-contact">
            <div class="contact-item">
              <label>姓名：</label>
              <span>{{ selectedPatient?.emergencyContact?.name || '未提供' }}</span>
            </div>
            <div class="contact-item">
              <label>关系：</label>
              <span>{{ selectedPatient?.emergencyContact?.relationship || '未提供' }}</span>
            </div>
            <div class="contact-item">
              <label>电话：</label>
              <span>{{ selectedPatient?.emergencyContact?.phone || '未提供' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Props {
  selectedPatient?: any
}

defineProps<Props>()
defineEmits<{
  close: []
  viewMedicalRecord: [record: any]
}>()

// 模拟就诊历史数据
const visitHistory = ref([
  {
    date: '2024-01-15',
    department: '中医科',
    doctor: '张医生',
    diagnosis: '感冒',
    status: '已完成'
  },
  {
    date: '2024-01-10',
    department: '中医科',
    doctor: '李医生',
    diagnosis: '胃炎',
    status: '已完成'
  }
])

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '已完成': 'success',
    '进行中': 'warning',
    '已取消': 'danger'
  }
  return typeMap[status] || 'info'
}

const viewMedicalRecord = (record: any) => {
  // 查看病历详情
  ElMessage.info('查看病历详情功能开发中...')
}
</script>

<style scoped>
.patient-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--background-color);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

.page-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.close-btn:hover {
  color: var(--text-primary);
}

.page-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.patient-detail {
  max-width: 900px;
  margin: 0 auto;
}

.detail-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.detail-section h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.detail-section p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-secondary);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.info-item,
.habit-item,
.contact-item {
  display: flex;
  align-items: center;
}

.info-item label,
.habit-item label,
.contact-item label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: var(--spacing-xs);
  min-width: 80px;
}

.info-item span,
.habit-item span,
.contact-item span {
  color: var(--text-secondary);
}

.visit-history {
  margin-top: var(--spacing-sm);
}

.allergy-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.allergy-tag {
  margin: 0;
}

.no-data {
  color: var(--text-secondary);
  font-style: italic;
}

.habits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.emergency-contact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

@media (max-width: 768px) {
  .info-grid,
  .habits-grid {
    grid-template-columns: 1fr;
  }
  
  .page-content {
    padding: var(--spacing-md);
  }
}
</style>
