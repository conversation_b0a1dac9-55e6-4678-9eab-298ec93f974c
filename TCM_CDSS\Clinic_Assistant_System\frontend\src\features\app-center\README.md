# 应用中心模块

## 功能描述
应用中心模块负责管理诊所的各种应用和工具，提供应用安装、更新、管理等功能。

## 目录结构
```
app-center/
├── views/                    # 页面视图
│   └── AppCenter.vue        # 应用中心页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 应用列表展示
- 应用安装管理
- 应用更新检查
- 应用分类管理

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 