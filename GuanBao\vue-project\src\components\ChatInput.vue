<template>
  <div class="chat-input-area">
    <el-form @submit.prevent="sendMessage" class="input-wrapper">
      <el-input
        v-model="message"
        type="textarea"
        :rows="1"
        :autosize="{ minRows: 1, maxRows: 4 }"
        :placeholder="loading ? '正在发送中...' : '输入您的问题...'"
        class="chat-input"
        @keydown.enter.exact.prevent="sendMessage"
        @keydown.enter.shift.exact="handleShiftEnter"
        :disabled="loading"
      />
      <el-button
        type="primary"
        @click="sendMessage"
        :disabled="!message.trim() || loading"
        :loading="loading"
        class="send-button"
        circle
      >
        <el-icon><Promotion /></el-icon>
      </el-button>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Promotion } from '@element-plus/icons-vue';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['send']);

const message = ref('');

const sendMessage = () => {
  const trimmedMessage = message.value.trim();
  if (trimmedMessage && !props.loading) {
    emit('send', trimmedMessage);
    message.value = '';
  }
};

const handleShiftEnter = (e) => {
  // 允许 Shift+Enter 换行
  return true;
};
</script>

<style scoped>
.chat-input-area {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--main-bg, #ffffff);
}

.input-wrapper {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
}

.chat-input {
  flex: 1;
}

.chat-input :deep(.el-textarea__inner) {
  padding: 1rem 1.5rem;
  border-radius: 28px;
  border: 1px solid var(--border-color, #e5e7eb);
  font-size: 1rem;
  resize: none;
  box-sizing: border-box;
  outline: none;
  transition: border-color 0.2s;
}

.chat-input :deep(.el-textarea__inner:focus) {
  border-color: var(--accent-color, #4f46e5);
}

.send-button {
  width: 40px;
  height: 40px;
  background-color: var(--accent-color, #4f46e5);
  border-color: var(--accent-color, #4f46e5);
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: var(--accent-color-hover, #4338ca);
  border-color: var(--accent-color-hover, #4338ca);
}

.send-button:disabled {
  background-color: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
}
</style>