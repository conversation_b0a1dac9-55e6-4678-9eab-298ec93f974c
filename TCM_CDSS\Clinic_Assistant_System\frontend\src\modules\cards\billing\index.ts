// 划价收费卡片模块
import type { CardModule } from '../index'

// 划价收费统计数据
export const billingStats = {
  pendingBills: 5,
  todayRevenue: 12680,
  processedToday: 28,
  averageAmount: 156
}

// 划价收费卡片配置
export const billingCard: CardModule = {
  key: 'billing',
  name: '划价收费',
  route: '/billing',
  icon: 'Money',
  color: '#FF9500',
  info: `${billingStats.pendingBills} 个待收费项目`,
  hasDetailStats: false,
  statsData: billingStats,
  permission: 'billing_manage'
}

// 划价收费详细统计数据获取函数
export const getBillingDetailStats = () => {
  return {
    pending: billingStats.pendingBills,
    todayRevenue: billingStats.todayRevenue,
    processed: billingStats.processedToday,
    averageAmount: billingStats.averageAmount
  }
}

// 划价收费卡片信息更新函数
export const updateBillingInfo = () => {
  return `${billingStats.pendingBills} 个待收费项目`
}
