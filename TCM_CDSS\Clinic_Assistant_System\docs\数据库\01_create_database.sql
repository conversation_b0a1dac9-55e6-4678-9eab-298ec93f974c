-- =====================================================
-- 中医诊所助手系统 - 数据库创建脚本
-- =====================================================
-- 文件: 01_create_database.sql
-- 描述: 创建数据库和基本配置
-- 版本: v1.0
-- 创建日期: 2025-06-24
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `tcm_clinic_system` 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `tcm_clinic_system`;

-- 设置SQL模式
SET SQL_MODE = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- 设置时区
SET time_zone = '+08:00';

-- 创建数据库用户（可选）
-- CREATE USER 'tcm_user'@'localhost' IDENTIFIED BY 'tcm_password_2025';
-- GRANT ALL PRIVILEGES ON tcm_clinic_system.* TO 'tcm_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 数据库配置说明
-- 字符集: utf8mb4 - 支持完整的UTF-8字符集，包括emoji
-- 排序规则: utf8mb4_unicode_ci - Unicode排序，支持多语言
-- 存储引擎: InnoDB - 支持事务、外键约束、行级锁定
-- 时区: +08:00 - 中国标准时间

-- 注意事项:
-- 1. 所有表都使用InnoDB存储引擎
-- 2. 所有文本字段都使用utf8mb4字符集
-- 3. 时间字段统一使用DATETIME类型
-- 4. 金额字段使用DECIMAL(10,2)精确存储
-- 5. 所有主键都使用AUTO_INCREMENT
-- 6. 外键约束确保数据完整性
-- 7. 索引优化查询性能
