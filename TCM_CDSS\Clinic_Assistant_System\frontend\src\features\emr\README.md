# 电子病历模块

## 功能描述
电子病历模块负责管理患者的电子病历信息，包括病历记录、诊断信息、治疗方案等功能。

## 目录结构
```
emr/
├── views/                    # 页面视图
│   └── ElectronicMedicalRecord.vue # 电子病历页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 病历记录管理
- 诊断信息录入
- 治疗方案制定
- 病历查询检索

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 