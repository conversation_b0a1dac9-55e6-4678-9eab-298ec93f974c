#!/usr/bin/env python3
"""
数据库设置脚本 - 为项目添加SQLite数据库支持
"""

import os
import sqlite3
from pathlib import Path
from datetime import datetime

def create_database_schema():
    """创建数据库表结构"""
    
    # 确保data目录存在
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # 数据库文件路径
    db_path = data_dir / "tcm_inquiry.db"
    
    print(f"📊 创建数据库: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 1. 患者信息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            age INTEGER,
            gender VARCHAR(10),
            contact VARCHAR(50),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 2. 会话表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS sessions (
            id VARCHAR(100) PRIMARY KEY,
            patient_id INTEGER,
            session_type VARCHAR(50) NOT NULL,  -- chat, inquiry, workflow
            status VARCHAR(20) DEFAULT 'active', -- active, completed, expired
            start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            end_time DATETIME,
            FOREIGN KEY (patient_id) REFERENCES patients (id)
        )
        ''')
        
        # 3. 消息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id VARCHAR(100) NOT NULL,
            role VARCHAR(20) NOT NULL,          -- user, assistant, system
            content TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES sessions (id)
        )
        ''')
        
        # 4. 智能体报告表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS agent_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id VARCHAR(100) NOT NULL,
            agent_name VARCHAR(100) NOT NULL,
            report_content TEXT NOT NULL,
            collected_info TEXT,               -- JSON格式
            completion_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES sessions (id)
        )
        ''')
        
        # 5. 工作流程记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS workflow_records (
            id VARCHAR(100) PRIMARY KEY,
            patient_info TEXT NOT NULL,        -- JSON格式
            agent_reports TEXT NOT NULL,       -- JSON格式
            conversation_history TEXT NOT NULL, -- JSON格式
            inquiry_summary TEXT NOT NULL,     -- JSON格式
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 6. 系统配置表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_config (
            key VARCHAR(100) PRIMARY KEY,
            value TEXT,
            description TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 提交事务
        conn.commit()
        
        print("✅ 数据库表创建成功")
        
        # 显示表信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📋 创建的数据表 ({len(tables)}个):")
        for table in tables:
            print(f"   📊 {table[0]}")
            
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False
        
    finally:
        conn.close()

def insert_sample_data():
    """插入示例数据"""
    
    db_path = Path("data/tcm_inquiry.db")
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 插入示例患者
        cursor.execute('''
        INSERT OR IGNORE INTO patients (id, name, age, gender, contact)
        VALUES (1, '张三', 30, '男', '13800138000')
        ''')
        
        cursor.execute('''
        INSERT OR IGNORE INTO patients (id, name, age, gender, contact)
        VALUES (2, '李四', 28, '女', '13900139000')
        ''')
        
        # 插入示例会话
        cursor.execute('''
        INSERT OR IGNORE INTO sessions (id, patient_id, session_type, status)
        VALUES ('demo_session_001', 1, 'workflow', 'completed')
        ''')
        
        # 插入示例消息
        cursor.execute('''
        INSERT OR IGNORE INTO messages (session_id, role, content)
        VALUES ('demo_session_001', 'assistant', '您好，我是您的中医问诊助手')
        ''')
        
        cursor.execute('''
        INSERT OR IGNORE INTO messages (session_id, role, content)
        VALUES ('demo_session_001', 'user', '我头痛三天了')
        ''')
        
        # 插入系统配置
        cursor.execute('''
        INSERT OR REPLACE INTO system_config (key, value, description)
        VALUES ('database_version', '1.0', '数据库版本')
        ''')
        
        cursor.execute('''
        INSERT OR REPLACE INTO system_config (key, value, description)
        VALUES ('created_at', ?, '数据库创建时间')
        ''', (datetime.now().isoformat(),))
        
        conn.commit()
        
        print("✅ 示例数据插入成功")
        return True
        
    except Exception as e:
        print(f"❌ 插入示例数据失败: {e}")
        return False
        
    finally:
        conn.close()

def migrate_json_data():
    """迁移JSON文件数据到数据库"""
    
    db_path = Path("data/tcm_inquiry.db")
    if not db_path.exists():
        print("❌ 数据库文件不存在，请先创建数据库")
        return False
    
    # 检查工作流程记录
    records_dir = Path("data/workflow_records")
    if not records_dir.exists():
        print("📭 没有找到工作流程记录目录")
        return True
    
    json_files = list(records_dir.glob("*.json"))
    if not json_files:
        print("📭 没有找到JSON记录文件")
        return True
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        import json
        
        migrated_count = 0
        
        for json_file in json_files:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查是否已经迁移
            cursor.execute('SELECT id FROM workflow_records WHERE id = ?', (data['session_id'],))
            if cursor.fetchone():
                print(f"⏭️ 跳过已存在的记录: {data['session_id']}")
                continue
            
            # 插入工作流程记录
            cursor.execute('''
            INSERT INTO workflow_records (id, patient_info, agent_reports, conversation_history, inquiry_summary)
            VALUES (?, ?, ?, ?, ?)
            ''', (
                data['session_id'],
                json.dumps(data['patient_info'], ensure_ascii=False),
                json.dumps(data['agent_reports'], ensure_ascii=False),
                json.dumps(data['conversation_history'], ensure_ascii=False),
                json.dumps(data['inquiry_summary'], ensure_ascii=False)
            ))
            
            # 插入患者信息
            patient_info = data['patient_info']
            cursor.execute('''
            INSERT OR IGNORE INTO patients (name, age, gender)
            VALUES (?, ?, ?)
            ''', (patient_info['name'], patient_info.get('age'), patient_info.get('gender')))
            
            # 获取患者ID
            cursor.execute('SELECT id FROM patients WHERE name = ? ORDER BY id DESC LIMIT 1', (patient_info['name'],))
            patient_id = cursor.fetchone()[0]
            
            # 插入会话记录
            cursor.execute('''
            INSERT OR IGNORE INTO sessions (id, patient_id, session_type, status, start_time, end_time)
            VALUES (?, ?, 'workflow', 'completed', ?, ?)
            ''', (
                data['session_id'],
                patient_id,
                data['inquiry_summary']['start_time'],
                data['inquiry_summary']['end_time']
            ))
            
            # 插入对话消息
            for msg in data['conversation_history']:
                cursor.execute('''
                INSERT INTO messages (session_id, role, content, timestamp)
                VALUES (?, ?, ?, ?)
                ''', (
                    data['session_id'],
                    msg['role'],
                    msg['content'],
                    msg['timestamp']
                ))
            
            # 插入智能体报告
            for report in data['agent_reports']:
                cursor.execute('''
                INSERT INTO agent_reports (session_id, agent_name, report_content, collected_info, completion_time)
                VALUES (?, ?, ?, ?, ?)
                ''', (
                    data['session_id'],
                    report['agent_name'],
                    report['report'],
                    json.dumps(report['collected_info'], ensure_ascii=False),
                    report['completion_time']
                ))
            
            migrated_count += 1
            print(f"✅ 迁移记录: {data['session_id']}")
        
        conn.commit()
        
        print(f"\n🎉 数据迁移完成，共迁移 {migrated_count} 条记录")
        return True
        
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        return False
        
    finally:
        conn.close()

def view_database_info():
    """查看数据库信息"""
    
    db_path = Path("data/tcm_inquiry.db")
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        print(f"\n📊 数据库信息: {db_path}")
        print(f"📁 文件大小: {db_path.stat().st_size / 1024:.1f} KB")
        
        # 查看表和记录数
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📋 数据表统计:")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   📊 {table_name}: {count} 条记录")
        
        # 查看最近的记录
        cursor.execute("SELECT COUNT(*) FROM workflow_records")
        workflow_count = cursor.fetchone()[0]
        
        if workflow_count > 0:
            cursor.execute("SELECT id, created_at FROM workflow_records ORDER BY created_at DESC LIMIT 1")
            latest = cursor.fetchone()
            print(f"\n🕒 最新工作流程记录:")
            print(f"   ID: {latest[0]}")
            print(f"   时间: {latest[1]}")
        
    except Exception as e:
        print(f"❌ 查看数据库信息失败: {e}")
        
    finally:
        conn.close()

def main():
    """主函数"""
    print("🗄️ 中医预问诊系统 - 数据库设置工具")
    print("=" * 50)
    
    print("\n🎯 可用操作:")
    print("1. 创建数据库表结构")
    print("2. 插入示例数据")
    print("3. 迁移JSON数据到数据库")
    print("4. 查看数据库信息")
    print("5. 执行完整设置 (1+2+3)")
    
    choice = input("\n请选择操作 (1-5): ").strip()
    
    if choice == '1':
        create_database_schema()
    elif choice == '2':
        insert_sample_data()
    elif choice == '3':
        migrate_json_data()
    elif choice == '4':
        view_database_info()
    elif choice == '5':
        print("\n🚀 执行完整数据库设置...")
        if create_database_schema():
            insert_sample_data()
            migrate_json_data()
            view_database_info()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
