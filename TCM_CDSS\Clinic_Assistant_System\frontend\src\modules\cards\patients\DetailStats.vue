<template>
  <div class="patients-stats-detail">
    <div class="stats-grid">
      <div class="stat-item">
        <span class="stat-label">患者总数</span>
        <span class="stat-value total">{{ stats.total }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">活跃患者</span>
        <span class="stat-value active">{{ stats.active }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">男性患者</span>
        <span class="stat-value male">{{ stats.male }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">女性患者</span>
        <span class="stat-value female">{{ stats.female }}</span>
      </div>
    </div>
    <div class="age-breakdown">
      <div class="age-item">
        <span class="age-label">儿童(0-18)</span>
        <span class="age-value">{{ stats.ageGroups.child }}人</span>
      </div>
      <div class="age-item">
        <span class="age-label">成人(19-59)</span>
        <span class="age-value">{{ stats.ageGroups.adult }}人</span>
      </div>
      <div class="age-item">
        <span class="age-label">老年(60+)</span>
        <span class="age-value">{{ stats.ageGroups.senior }}人</span>
      </div>
      <div class="age-item">
        <span class="age-label">有预约</span>
        <span class="age-value">{{ stats.withAppointment }}人</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { patientsStats } from './index'

// 计算属性获取统计数据
const stats = computed(() => patientsStats.value)
</script>

<style scoped>
.patients-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(52, 199, 89, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(52, 199, 89, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .patients-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.stat-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
}

.stat-value.total {
  color: #34C759;
}

.stat-value.active {
  color: #007AFF;
}

.stat-value.male {
  color: #5856D6;
}

.stat-value.female {
  color: #FF3B30;
}

.age-breakdown {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.age-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.age-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.age-value {
  font-size: 11px;
  font-weight: 600;
  color: #34C759;
}
</style>
