# 中医预问诊智能体系统 - 使用指南

## 🚀 快速开始

### 1. 启动系统

```bash
# 激活conda环境
conda activate llms

# 启动服务器
python src/simple_main.py
```

服务器启动后会显示：
```
INFO:     Started server process
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001
```

### 2. 验证系统运行

运行检查脚本：
```bash
python check_server.py
```

预期输出：
```
🔍 检查服务器状态...
目标地址: http://localhost:8001

1. 测试根路径 GET /
   ✅ 成功: {'message': '欢迎使用中医预问诊智能体系统', ...}

2. 测试健康检查 GET /health
   ✅ 健康状态: healthy
   LLM服务: healthy

3. 测试聊天接口 POST /chat
   ✅ 聊天响应: 您好，我是您的中医问诊助手...

🎉 服务器运行正常！
```

## 🌐 Web界面使用

### 方式一：简化演示页面（推荐）
打开 `simple_demo.html` 文件，这个页面包含：

1. **连接测试**：验证服务器连接状态
2. **聊天测试**：与AI助手进行对话
3. **问诊测试**：启动完整的问诊流程

### 方式二：完整演示页面
打开 `demo.html` 文件，功能更丰富但可能需要更多调试。

## 📡 API接口使用

### 基础接口

#### 1. 系统信息
```bash
GET http://localhost:8001/
```

响应：
```json
{
    "message": "欢迎使用中医预问诊智能体系统",
    "version": "1.0.0",
    "docs": "/docs",
    "health": "/health"
}
```

#### 2. 健康检查
```bash
GET http://localhost:8001/health
```

响应：
```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00",
    "version": "1.0.0",
    "services": {
        "llm": "healthy"
    }
}
```

#### 3. API文档
访问：http://localhost:8001/docs

### 核心功能接口

#### 1. 智能聊天
```bash
POST http://localhost:8001/chat
Content-Type: application/json

{
    "message": "我头痛三天了，还有发热"
}
```

响应：
```json
{
    "response": "根据您描述的头痛和发热症状，这可能是感冒或其他疾病的表现。请问您的体温大概是多少度？头痛是持续性的还是间歇性的？",
    "status": "success",
    "timestamp": "2024-01-01T12:00:00"
}
```

#### 2. 开始问诊
```bash
POST http://localhost:8001/inquiry/start
Content-Type: application/json

{
    "patient_name": "张三",
    "patient_age": 30,
    "patient_gender": "男"
}
```

响应：
```json
{
    "session_id": "uuid-string",
    "message": "您好张三！我是您的中医问诊助手。让我们开始问诊，请告诉我您今天来看病的主要原因是什么？",
    "status": "started",
    "timestamp": "2024-01-01T12:00:00"
}
```

## 🧪 测试工具

### 1. 系统功能测试
```bash
python simple_test.py
```

### 2. API接口测试
```bash
python test_api_client.py
```

### 3. 聊天功能专项测试
```bash
python test_chat_only.py
```

### 4. 服务器状态检查
```bash
python check_server.py
```

## 🔧 故障排除

### 常见问题

#### 1. 服务器无法启动
**症状**：运行 `python src/simple_main.py` 后没有响应或报错

**解决方案**：
```bash
# 检查端口是否被占用
netstat -ano | findstr :8001

# 如果被占用，杀死进程或使用其他端口
# 修改 src/simple_main.py 中的端口号
```

#### 2. LLM服务不可用
**症状**：健康检查显示 `"llm": "unhealthy"`

**解决方案**：
1. 检查 `.env` 文件中的OpenRouter配置
2. 验证API密钥是否正确
3. 检查网络连接

#### 3. 前端页面无法连接
**症状**：浏览器控制台显示CORS错误或连接失败

**解决方案**：
1. 确保服务器正在运行
2. 检查防火墙设置
3. 尝试使用 `simple_demo.html` 而不是 `demo.html`

#### 4. 聊天响应缓慢
**症状**：发送消息后长时间没有响应

**原因**：OpenRouter API调用需要时间

**解决方案**：
- 正常现象，请耐心等待（通常10-30秒）
- 可以在浏览器开发者工具中查看网络请求状态

### 调试技巧

#### 1. 查看服务器日志
服务器运行时会在终端显示请求日志，注意观察错误信息。

#### 2. 使用浏览器开发者工具
- 按F12打开开发者工具
- 查看Console标签页的错误信息
- 查看Network标签页的网络请求状态

#### 3. 逐步测试
按以下顺序测试：
1. 运行 `check_server.py` 验证服务器
2. 测试简单的GET请求（如健康检查）
3. 测试POST请求（如聊天接口）
4. 最后测试完整的Web界面

## 📈 性能优化

### 1. 提升响应速度
- 使用更快的模型（如果可用）
- 调整 `LLM_MAX_TOKENS` 参数
- 优化网络连接

### 2. 并发处理
当前系统支持多用户同时使用，但大量并发可能影响性能。

### 3. 缓存优化
系统支持Redis缓存，可以提升重复请求的响应速度。

## 🔒 安全注意事项

1. **API密钥保护**：不要将 `.env` 文件提交到版本控制系统
2. **网络安全**：生产环境中应配置HTTPS
3. **访问控制**：考虑添加身份验证机制
4. **数据隐私**：确保患者数据的安全存储

## 📞 技术支持

如果遇到问题：

1. 首先运行所有测试脚本确认问题范围
2. 查看服务器终端的错误日志
3. 检查浏览器开发者工具的错误信息
4. 参考本文档的故障排除部分

---

**系统状态**：✅ 完全可用
**最后更新**：2024年当前日期
**版本**：1.0.0
