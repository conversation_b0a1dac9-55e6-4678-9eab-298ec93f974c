<template>
  <div class="profile-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">个人中心</h1>
    </header>
    <div class="profile-content">
      <el-card>
        <div class="profile-row">
          <span>姓名：</span>{{ user.name }}
        </div>
        <div class="profile-row">
          <span>手机号：</span>{{ user.phone }}
        </div>
        <div class="profile-row">
          <span>角色：</span>
          <el-tag v-if="user.role === 'doctor'" type="success">医生</el-tag>
          <el-tag v-else-if="user.role === 'assistant'" type="info">前台</el-tag>
          <el-tag v-else type="warning">管理员</el-tag>
        </div>
        <el-button type="primary" @click="showPwdDialog = true">修改密码</el-button>
      </el-card>
    </div>
    <el-dialog v-model="showPwdDialog" title="修改密码" width="400px">
      <el-form :model="pwdForm" label-width="80px">
        <el-form-item label="原密码"><el-input v-model="pwdForm.old" type="password" /></el-form-item>
        <el-form-item label="新密码"><el-input v-model="pwdForm.new" type="password" /></el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPwdDialog = false">取消</el-button>
        <el-button type="primary" @click="changePwd">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
const router = useRouter()
const goBack = () => router.push('/dashboard')
const user = { name: '张医生', phone: '13800138001', role: 'doctor' }
const showPwdDialog = ref(false)
const pwdForm = ref({ old: '', new: '' })
const changePwd = () => {
  showPwdDialog.value = false
  alert('密码已修改')
}
</script>
<style lang="scss" scoped>
.profile-container { height: 100vh; display: flex; flex-direction: column; /* 背景图片现在由全局样式提供 */ }
.module-header { background: var(--surface-color); padding: var(--spacing-md) var(--spacing-xl); display: flex; align-items: center; gap: var(--spacing-lg); border-bottom: 1px solid var(--border-color); box-shadow: var(--shadow-light); }
.back-button { display: flex; align-items: center; gap: var(--spacing-xs); color: var(--primary-color); cursor: pointer; font-size: var(--font-size-sm); transition: color var(--transition-fast); &:hover { color: var(--primary-dark); } }
.module-title { flex: 1; font-size: var(--font-size-xl); font-weight: 600; color: var(--text-primary); margin: 0; text-align: center; }
.profile-content { flex: 1; display: flex; align-items: center; justify-content: center; }
.profile-row { margin-bottom: var(--spacing-md); span { color: var(--text-secondary); margin-right: 8px; } }
</style> 