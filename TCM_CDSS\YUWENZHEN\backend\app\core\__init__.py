"""
核心模块

提供应用程序的核心功能，包括配置管理、数据库连接、缓存管理和安全认证。
"""

from .config import (
    settings,
    get_settings,
    get_database_url,
    get_redis_url,
    get_llm_config,
    get_cors_config,
    is_debug_mode,
    get_log_config
)

from .database import (
    Base,
    engine,
    async_engine,
    SessionLocal,
    AsyncSessionLocal,
    get_db,
    get_async_db,
    create_tables,
    drop_tables,
    check_database_connection,
    db_manager,
    init_database,
    close_database,
    get_database_health,
    BaseRepository,
    transactional
)

from .cache import (
    cache_manager,
    session_manager,
    cached,
    init_cache,
    close_cache,
    get_cache_health
)

from .security import (
    security_manager,
    api_key_manager,
    rate_limiter,
    get_current_user,
    get_current_active_user,
    require_permissions,
    require_roles,
    create_access_token,
    verify_token,
    hash_password,
    verify_password,
    validate_password_strength
)

__all__ = [
    # 配置
    "settings",
    "get_settings",
    "get_database_url", 
    "get_redis_url",
    "get_llm_config",
    "get_cors_config",
    "is_debug_mode",
    "get_log_config",
    
    # 数据库
    "Base",
    "engine",
    "async_engine", 
    "SessionLocal",
    "AsyncSessionLocal",
    "get_db",
    "get_async_db",
    "create_tables",
    "drop_tables",
    "check_database_connection",
    "db_manager",
    "init_database",
    "close_database",
    "get_database_health",
    "BaseRepository",
    "transactional",
    
    # 缓存
    "cache_manager",
    "session_manager",
    "cached",
    "init_cache",
    "close_cache",
    "get_cache_health",
    
    # 安全
    "security_manager",
    "api_key_manager",
    "rate_limiter",
    "get_current_user",
    "get_current_active_user",
    "require_permissions",
    "require_roles",
    "create_access_token",
    "verify_token",
    "hash_password",
    "verify_password",
    "validate_password_strength"
]
