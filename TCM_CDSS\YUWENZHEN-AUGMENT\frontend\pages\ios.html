<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医预问诊助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #1d1d1f;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }

        .back-button {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            z-index: 10;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.05);
        }

        .back-button:active {
            transform: translateY(-50%) scale(0.95);
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 20px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }
        
        .status-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .status-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
        }
        
        .status-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }
        
        .status-subtitle {
            font-size: 14px;
            color: #8e8e93;
            margin-top: 2px;
        }
        
        .progress-container {
            margin: 15px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007AFF 0%, #5856D6 100%);
            border-radius: 4px;
            transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            width: 0%;
        }
        
        .progress-text {
            font-size: 14px;
            color: #8e8e93;
            margin-top: 8px;
            text-align: center;
        }
        
        .chat-container {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }
        
        .chat-header {
            padding: 16px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: rgba(248, 248, 248, 0.8);
        }
        
        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }
        
        .chat-area {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.5);
        }
        
        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin: 0 12px;
            flex-shrink: 0;
        }
        
        .message.user .message-avatar {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            color: white;
        }
        
        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
            color: white;
        }
        
        .message.system .message-avatar {
            background: linear-gradient(135deg, #FF9500 0%, #FF6B35 100%);
            color: white;
        }
        
        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.4;
            position: relative;
        }
        
        .message.user .message-bubble {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            color: white;
            border-bottom-right-radius: 6px;
        }
        
        .message.assistant .message-bubble {
            background: rgba(242, 242, 247, 0.8);
            color: #1d1d1f;
            border-bottom-left-radius: 6px;
            backdrop-filter: blur(10px);
        }
        
        .message.system .message-bubble {
            background: rgba(255, 149, 0, 0.1);
            color: #1d1d1f;
            border-radius: 12px;
            text-align: center;
            font-size: 14px;
        }
        
        .input-container {
            padding: 16px 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            background: rgba(248, 248, 248, 0.8);
            backdrop-filter: blur(10px);
        }
        
        .input-wrapper {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            outline: none;
            transition: all 0.2s ease;
        }
        
        .message-input:focus {
            border-color: #007AFF;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }
        
        .send-button {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            border: none;
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
        }
        
        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
        }
        
        .send-button:active {
            transform: scale(0.95);
        }
        
        .send-button:disabled {
            background: #8e8e93;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .control-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .control-button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
        }
        
        .control-button.primary {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
        }
        
        .control-button.secondary {
            background: rgba(142, 142, 147, 0.1);
            color: #007AFF;
            border: 1px solid rgba(0, 122, 255, 0.2);
        }
        
        .control-button:hover {
            transform: translateY(-1px);
        }
        
        .control-button:active {
            transform: translateY(0);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #8e8e93;
        }
        
        .spinner {
            width: 24px;
            height: 24px;
            border: 2px solid rgba(142, 142, 147, 0.2);
            border-top: 2px solid #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 滚动条样式 */
        .chat-area::-webkit-scrollbar {
            width: 6px;
        }
        
        .chat-area::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }
        
        .chat-area::-webkit-scrollbar-thumb {
            background: rgba(0, 122, 255, 0.3);
            border-radius: 3px;
        }
        
        .chat-area::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 122, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-button">← 返回主页</a>
            <div class="header-content">
                <h1>🏥 中医预问诊助手</h1>
                <p>iOS风格演示页面 - 专业的诊前信息采集服务</p>
            </div>
        </div>
        
        <div class="content">
            <!-- 状态卡片 -->
            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon">🔍</div>
                    <div>
                        <div class="status-title">系统状态</div>
                        <div class="status-subtitle">实时监控服务状态</div>
                    </div>
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备就绪</div>
                </div>
                
                <div class="control-buttons">
                    <button class="control-button primary" onclick="startInquiry()">开始问诊</button>
                    <button class="control-button secondary" onclick="checkHealth()">检查状态</button>
                    <button class="control-button secondary" onclick="resetChat()">重置对话</button>
                </div>
            </div>
            
            <!-- 聊天容器 -->
            <div class="chat-container">
                <div class="chat-header">
                    <div class="chat-title">💬 问诊对话</div>
                </div>
                
                <div class="chat-area" id="chatArea">
                    <div class="message system fade-in">
                        <div class="message-avatar">📢</div>
                        <div class="message-bubble">
                            欢迎使用中医预问诊助手！我将帮助您采集症状信息，为医生诊断做准备。<br><br>
                            <strong>使用说明：</strong><br>
                            1. 点击"开始问诊"启动会话<br>
                            2. 根据AI助手的问题，手动输入您的症状<br>
                            3. AI助手会逐步采集详细信息，不会进行诊断<br>
                            4. 体验完整的中医问诊信息采集流程
                        </div>
                    </div>
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <div>AI正在思考中...</div>
                </div>
                
                <div class="input-container">
                    <div class="input-wrapper">
                        <input type="text" class="message-input" id="messageInput" 
                               placeholder="请描述您的症状..." 
                               onkeypress="handleKeyPress(event)" disabled>
                        <button class="send-button" onclick="sendMessage()" id="sendBtn" disabled>
                            ➤
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let currentSession = null;
        let isInquiryStarted = false;
        let messageCount = 0;
        
        // 开始问诊
        async function startInquiry() {
            try {
                showLoading(true);
                updateProgress(10, "正在启动问诊...");
                
                const response = await fetch(`${API_BASE}/inquiry/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        patient_name: "患者",
                        patient_age: 30,
                        patient_gender: "男"
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentSession = data.session_id;
                    isInquiryStarted = true;
                    
                    addMessage('system', `问诊已开始，会话ID: ${currentSession.substring(0, 8)}... (支持对话记忆)`);
                    addMessage('assistant', data.message);

                    // 启用输入
                    document.getElementById('messageInput').disabled = false;
                    document.getElementById('sendBtn').disabled = false;
                    document.getElementById('messageInput').focus();
                    document.getElementById('messageInput').placeholder = "请描述您的主要症状...";

                    updateProgress(20, "问诊已开始，请手动输入症状");

                    // 显示输入提示
                    setTimeout(() => {
                        addMessage('system', '💡 请您手动输入症状，AI助手会逐步询问详细信息来采集完整的病史资料。');
                    }, 1000);
                } else {
                    throw new Error('启动问诊失败');
                }
            } catch (error) {
                addMessage('system', `启动失败: ${error.message}`);
                updateProgress(0, "启动失败");
            } finally {
                showLoading(false);
            }
        }
        
        // 检查健康状态
        async function checkHealth() {
            try {
                showLoading(true);
                updateProgress(50, "正在检查系统状态...");
                
                const response = await fetch(`${API_BASE}/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    const llmStatus = data.services?.llm || 'unknown';
                    
                    addMessage('system', `系统状态: ${data.status} | LLM服务: ${llmStatus}`);
                    updateProgress(100, "系统运行正常");
                    
                    setTimeout(() => {
                        updateProgress(0, "准备就绪");
                    }, 2000);
                } else {
                    throw new Error('健康检查失败');
                }
            } catch (error) {
                addMessage('system', `健康检查失败: ${error.message}`);
                updateProgress(0, "系统异常");
            } finally {
                showLoading(false);
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) {
                addMessage('system', '请输入您的症状或回答');
                return;
            }

            if (!isInquiryStarted) {
                addMessage('system', '请先点击"开始问诊"启动会话');
                return;
            }

            // 清空输入框并添加用户消息
            input.value = '';
            addMessage('user', message);
            messageCount++;

            // 更新输入框提示
            updateInputPlaceholder();

            try {
                showLoading(true);

                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSession
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    addMessage('assistant', data.response);

                    // 更新进度（基于消息数量）
                    const progressPercentage = Math.min(90, 20 + (messageCount * 8));
                    updateProgress(progressPercentage, `已采集 ${messageCount} 项信息`);

                    // 如果采集了足够信息，提示可以结束
                    if (messageCount >= 8) {
                        setTimeout(() => {
                            addMessage('system', '✅ 基本信息已采集完成！您可以继续补充信息或结束问诊。');
                            updateProgress(100, "信息采集完成");
                        }, 2000);
                    }
                } else {
                    throw new Error('发送失败');
                }
            } catch (error) {
                addMessage('system', `❌ 发送失败: ${error.message}`);
            } finally {
                showLoading(false);
                // 重新聚焦输入框
                input.focus();
            }
        }

        // 更新输入框提示
        function updateInputPlaceholder() {
            const input = document.getElementById('messageInput');
            const placeholders = [
                "请继续描述您的症状...",
                "请回答AI助手的问题...",
                "请提供更多详细信息...",
                "还有其他症状吗？",
                "请描述症状的具体情况...",
                "请回答相关问题...",
                "继续补充症状信息...",
                "还有什么需要说明的吗？"
            ];

            const index = Math.min(messageCount, placeholders.length - 1);
            input.placeholder = placeholders[index];
        }
        
        // 添加消息
        function addMessage(type, content) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type} fade-in`;
            
            let avatar = '';
            switch(type) {
                case 'user':
                    avatar = '👤';
                    break;
                case 'assistant':
                    avatar = '🤖';
                    break;
                case 'system':
                    avatar = '📢';
                    break;
            }
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-bubble">${content}</div>
            `;
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        // 更新进度
        function updateProgress(percentage, text) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = text;
        }
        
        // 显示/隐藏加载状态
        function showLoading(show) {
            const loading = document.getElementById('loading');
            loading.style.display = show ? 'block' : 'none';
        }
        
        // 重置对话
        function resetChat() {
            // 重置状态
            currentSession = null;
            isInquiryStarted = false;
            messageCount = 0;

            // 清空聊天区域
            const chatArea = document.getElementById('chatArea');
            chatArea.innerHTML = `
                <div class="message system fade-in">
                    <div class="message-avatar">📢</div>
                    <div class="message-bubble">
                        欢迎使用中医预问诊助手！我将帮助您采集症状信息，为医生诊断做准备。<br><br>
                        <strong>使用说明：</strong><br>
                        1. 点击"开始问诊"启动会话<br>
                        2. 根据AI助手的问题，手动输入您的症状<br>
                        3. AI助手会逐步采集详细信息，不会进行诊断<br>
                        4. 体验完整的中医问诊信息采集流程
                    </div>
                </div>
            `;

            // 重置输入框
            const input = document.getElementById('messageInput');
            input.disabled = true;
            input.value = '';
            input.placeholder = "请描述您的症状...";

            // 重置按钮
            document.getElementById('sendBtn').disabled = true;

            // 重置进度
            updateProgress(0, "准备就绪");

            addMessage('system', '🔄 对话已重置，可以重新开始问诊');
        }

        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            console.log('iOS风格预问诊助手加载完成');
            updateProgress(0, "准备就绪");

            // 添加使用提示
            setTimeout(() => {
                addMessage('system', '💡 提示：这是一个真实的AI对话体验，请手动输入症状，AI会像真正的医生助手一样逐步采集信息。');
            }, 2000);
        };
    </script>
</body>
</html>
