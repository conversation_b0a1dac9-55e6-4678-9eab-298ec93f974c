# 灵机模块

## 功能描述
灵机模块负责管理智能诊断和医疗辅助功能，包括AI诊断建议、症状分析、智能推荐等功能。

## 目录结构
```
lingji/
├── views/                    # 页面视图
│   └── LingjiList.vue        # 灵机功能页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- AI诊断建议
- 症状智能分析
- 治疗方案推荐
- 医学知识库

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 