"""
安全认证模块

提供JWT认证、密码加密、权限验证等安全功能。
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import CryptContext
from jose import JWTError, jwt
from fastapi import HTTPException, status, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from .config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer认证
security = HTTPBearer()


class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.secret_key = settings.secret_key
        self.algorithm = settings.algorithm
        self.access_token_expire_minutes = settings.access_token_expire_minutes
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError as e:
            logger.warning(f"JWT验证失败: {e}")
            return None
    
    def hash_password(self, password: str) -> str:
        """加密密码"""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """验证密码强度"""
        errors = []
        
        if len(password) < settings.password_min_length:
            errors.append(f"密码长度至少{settings.password_min_length}位")
        
        if not any(c.isupper() for c in password):
            errors.append("密码必须包含大写字母")
        
        if not any(c.islower() for c in password):
            errors.append("密码必须包含小写字母")
        
        if not any(c.isdigit() for c in password):
            errors.append("密码必须包含数字")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("密码必须包含特殊字符")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "strength": self._calculate_password_strength(password)
        }
    
    def _calculate_password_strength(self, password: str) -> str:
        """计算密码强度"""
        score = 0
        
        if len(password) >= 8:
            score += 1
        if len(password) >= 12:
            score += 1
        if any(c.isupper() for c in password):
            score += 1
        if any(c.islower() for c in password):
            score += 1
        if any(c.isdigit() for c in password):
            score += 1
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            score += 1
        
        if score <= 2:
            return "weak"
        elif score <= 4:
            return "medium"
        else:
            return "strong"


# 全局安全管理器实例
security_manager = SecurityManager()


# 认证依赖
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        token = credentials.credentials
        payload = security_manager.verify_token(token)
        
        if payload is None:
            raise credentials_exception
        
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        
        return payload
        
    except Exception as e:
        logger.warning(f"用户认证失败: {e}")
        raise credentials_exception


async def get_current_active_user(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取当前活跃用户"""
    if current_user.get("disabled"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    return current_user


# 权限检查装饰器
def require_permissions(*permissions: str):
    """权限检查装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get("current_user")
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要认证"
                )
            
            user_permissions = current_user.get("permissions", [])
            
            # 检查权限
            for permission in permissions:
                if permission not in user_permissions:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"缺少权限: {permission}"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# 角色检查装饰器
def require_roles(*roles: str):
    """角色检查装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get("current_user")
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要认证"
                )
            
            user_role = current_user.get("role")
            
            if user_role not in roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"需要角色: {', '.join(roles)}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# API密钥验证
class APIKeyManager:
    """API密钥管理器"""
    
    def __init__(self):
        self.api_keys = {}  # 在实际应用中应该从数据库加载
    
    def generate_api_key(self, user_id: str, name: str) -> str:
        """生成API密钥"""
        import secrets
        api_key = f"ywz_{secrets.token_urlsafe(32)}"
        
        self.api_keys[api_key] = {
            "user_id": user_id,
            "name": name,
            "created_at": datetime.utcnow(),
            "last_used": None,
            "active": True
        }
        
        return api_key
    
    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """验证API密钥"""
        key_info = self.api_keys.get(api_key)
        
        if not key_info or not key_info["active"]:
            return None
        
        # 更新最后使用时间
        key_info["last_used"] = datetime.utcnow()
        
        return key_info
    
    def revoke_api_key(self, api_key: str) -> bool:
        """撤销API密钥"""
        if api_key in self.api_keys:
            self.api_keys[api_key]["active"] = False
            return True
        return False


# 全局API密钥管理器实例
api_key_manager = APIKeyManager()


# 速率限制
class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        self.requests = {}  # 在实际应用中应该使用Redis
    
    def is_allowed(self, identifier: str, limit: int, window: int) -> bool:
        """检查是否允许请求"""
        now = datetime.utcnow()
        window_start = now - timedelta(seconds=window)
        
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        # 清理过期请求
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier]
            if req_time > window_start
        ]
        
        # 检查限制
        if len(self.requests[identifier]) >= limit:
            return False
        
        # 记录请求
        self.requests[identifier].append(now)
        return True


# 全局速率限制器实例
rate_limiter = RateLimiter()


# 便捷函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    return security_manager.create_access_token(data, expires_delta)


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证令牌"""
    return security_manager.verify_token(token)


def hash_password(password: str) -> str:
    """加密密码"""
    return security_manager.hash_password(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return security_manager.verify_password(plain_password, hashed_password)


def validate_password_strength(password: str) -> Dict[str, Any]:
    """验证密码强度"""
    return security_manager.validate_password_strength(password)


# 导出主要接口
__all__ = [
    "security_manager",
    "api_key_manager",
    "rate_limiter",
    "get_current_user",
    "get_current_active_user",
    "require_permissions",
    "require_roles",
    "create_access_token",
    "verify_token",
    "hash_password",
    "verify_password",
    "validate_password_strength"
]
