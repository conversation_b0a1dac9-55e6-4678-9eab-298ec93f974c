#!/usr/bin/env python3
"""
启动脚本
Start Script for TCM Clinic Assistant System Backend
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'bcrypt',
        'cryptography',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📦 Missing packages: {', '.join(missing_packages)}")
        print("Please install them with:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_database():
    """检查数据库文件"""
    db_path = Path(__file__).parent.parent / "datasets" / "tcm_clinic_system.db"
    
    if db_path.exists():
        print(f"✅ Database file found: {db_path}")
        return True
    else:
        print(f"❌ Database file not found: {db_path}")
        print("Please run: python scripts/init_database.py")
        return False

async def test_backend():
    """测试后端功能"""
    try:
        # 添加当前目录到Python路径
        sys.path.append(str(Path(__file__).parent))
        
        from database.connection import test_connection
        
        print("🔌 Testing database connection...")
        connected = await test_connection()
        
        if connected:
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
    except Exception as error:
        print(f"❌ Backend test error: {error}")
        return False

def start_server():
    """启动服务器"""
    try:
        print("🚀 Starting FastAPI server...")
        print("📍 Server will be available at: http://localhost:8000")
        print("📚 API Documentation: http://localhost:8000/docs")
        print("📊 Health Check: http://localhost:8000/health")
        print("\nPress Ctrl+C to stop the server")
        print("="*50)
        
        # 启动uvicorn服务器
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], cwd=Path(__file__).parent)
        
    except KeyboardInterrupt:
        print("\n📴 Server stopped by user")
    except Exception as error:
        print(f"❌ Server start error: {error}")

async def main():
    """主函数"""
    print("🏥 TCM Clinic Assistant System - Backend Startup")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    print("\n📦 Checking dependencies...")
    if not check_dependencies():
        return False
    
    print("\n🗄️ Checking database...")
    if not check_database():
        return False
    
    print("\n🧪 Testing backend functionality...")
    if not await test_backend():
        return False
    
    print("\n✅ All checks passed!")
    print("\n" + "="*50)
    
    # 启动服务器
    start_server()
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n📴 Startup cancelled by user")
        sys.exit(0)
    except Exception as error:
        print(f"❌ Startup error: {error}")
        sys.exit(1)
