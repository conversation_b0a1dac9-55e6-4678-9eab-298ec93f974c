<template>
  <div class="family-doctor-container">
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">家医管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showComingSoon">
          <el-icon><Plus /></el-icon>
          新建签约
        </el-button>
      </div>
    </header>

    <div class="content-area">
      <div class="family-doctor-overview">
        <div class="overview-header">
          <div class="icon-container">
            <el-icon :size="60" color="#32D74B">
              <HomeFilled />
            </el-icon>
          </div>
          <h2 class="overview-title">家医管理功能</h2>
          <p class="overview-subtitle">家庭医生服务管理系统</p>
        </div>

        <div class="feature-preview">
          <div class="feature-item">
            <el-icon color="#007AFF"><UserFilled /></el-icon>
            <span>签约患者管理</span>
          </div>
          <div class="feature-item">
            <el-icon color="#34C759"><Calendar /></el-icon>
            <span>健康档案建立</span>
          </div>
          <div class="feature-item">
            <el-icon color="#FF9500"><Phone /></el-icon>
            <span>远程健康咨询</span>
          </div>
          <div class="feature-item">
            <el-icon color="#5856D6"><Monitor /></el-icon>
            <span>健康监测跟踪</span>
          </div>
          <div class="feature-item">
            <el-icon color="#FF3B30"><Bell /></el-icon>
            <span>健康提醒服务</span>
          </div>
          <div class="feature-item">
            <el-icon color="#30B0C7"><TrendCharts /></el-icon>
            <span>健康数据分析</span>
          </div>
          <div class="feature-item">
            <el-icon color="#AF52DE"><Location /></el-icon>
            <span>上门服务管理</span>
          </div>
          <div class="feature-item">
            <el-icon color="#FF6B35"><Document /></el-icon>
            <span>健康教育资源</span>
          </div>
          <div class="feature-item">
            <el-icon color="#8E8E93"><Setting /></el-icon>
            <span>服务包配置</span>
          </div>
        </div>
        <div class="service-types">
          <h3>家医服务内容</h3>
          <div class="service-tags">
            <el-tag type="success" size="large">基本医疗服务</el-tag>
            <el-tag type="success" size="large">健康管理服务</el-tag>
            <el-tag type="success" size="large">慢病管理服务</el-tag>
            <el-tag type="success" size="large">康复指导服务</el-tag>
            <el-tag type="success" size="large">健康教育服务</el-tag>
            <el-tag type="success" size="large">中医养生服务</el-tag>
          </div>
        </div>
        <div class="ai-features">
          <h3>AI智能服务功能</h3>
          <div class="ai-feature-grid">
            <div class="ai-feature-card">
              <h4>心理健康辅导</h4>
              <p>情绪识别与疏导、正念冥想引导、心理问题初筛</p>
            </div>
            <div class="ai-feature-card">
              <h4>辅助就诊</h4>
              <p>症状智能问诊、导诊推荐、结构化病历摘要、检验报告解读</p>
            </div>
            <div class="ai-feature-card">
              <h4>慢病调养</h4>
              <p>药品咨询、多病共管建议、生活方式指导</p>
            </div>
            <div class="ai-feature-card">
              <h4>儿童营养与成长</h4>
              <p>智能食谱建议、营养失衡预警、个性化疫苗提醒</p>
            </div>
            <div class="ai-feature-card">
              <h4>日常健康管理</h4>
              <p>健康问题随问随答、营养分析与膳食推荐、个性化减重计划</p>
            </div>
            <div class="ai-feature-card">
              <h4>疾病预防与早筛</h4>
              <p>慢病风险评估、症状初筛判断、癌症早期征兆识别</p>
            </div>
          </div>
        </div>
        <div class="coming-soon-badge">
          <el-tag type="warning" size="large">敬请期待</el-tag>
        </div>
        <p class="coming-soon-description">
          家医管理功能正在开发中，将为您提供全面的家庭医生服务管理平台，
          包括签约患者管理、健康档案建立、远程健康咨询、健康监测跟踪、健康提醒服务等传统功能，
          以及AI赋能的心理健康辅导、智能辅助就诊、慢病调养、儿童营养与成长、日常健康管理和疾病预防与早筛等智能功能，
          帮助医生更好地为签约家庭提供连续性、综合性的医疗健康服务，
          实现"以家庭为单位、以健康为中心"的服务模式。
        </p>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Plus,
  HomeFilled,
  UserFilled,
  Calendar,
  Phone,
  Location,
  Setting,
  Monitor,
  Bell,
  TrendCharts,
  Document
} from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/dashboard')
}

const showComingSoon = () => {
  ElMessage.info('该功能正在开发中，敬请期待！')
}
</script>

<style scoped>
.family-doctor-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* 背景图片现在由全局样式提供 */
}

.content-area {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--spacing-xl);
  overflow-y: auto;
  min-height: 0;
}

.family-doctor-overview {
  max-width: 800px;
  width: 100%;
  text-align: center;
  margin-top: var(--spacing-lg);
}

.overview-header {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xxl);
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-xl);
}

.icon-container {
  margin-bottom: var(--spacing-lg);
}

.overview-title {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.overview-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.feature-preview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
}

.service-types {
  margin-bottom: var(--spacing-xl);
}

.service-types h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.ai-features {
  margin-bottom: var(--spacing-xl);
}

.ai-features h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.ai-feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.ai-feature-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  text-align: left;
}

.ai-feature-card h4 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.ai-feature-card p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
  margin: 0;
}

.coming-soon-badge {
  margin-bottom: var(--spacing-lg);
}

.coming-soon-description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 680px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .feature-preview {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .ai-feature-grid {
    grid-template-columns: 1fr;
  }

  .overview-header {
    padding: var(--spacing-lg);
  }

  .overview-title {
    font-size: var(--font-size-xl);
  }

  .service-tags {
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .feature-preview {
    grid-template-columns: 1fr;
  }
}
</style>
