# 项目名称

GuanBao 是一个基于 Vue 3 和 Vite 的前端项目，用于实现聊天应用功能。

## 快速开始

1. **安装依赖**：
   ```bash
   npm install
   ```
2. **启动开发服务器**：
   ```bash
   npm run dev
   ```
3. **构建生产版本**：
   ```bash
   npm run build
   ```

## 功能特性

- 实时聊天功能
- 消息历史记录
- 响应式布局

## 开发环境

- Node.js 20.19.0 或更高版本
- npm 或 yarn
- Vue 3.5.18
- Vite 7.0.6

## 贡献指南

1. Fork 项目仓库
2. 创建新分支 (`git checkout -b feature/your-feature`)
3. 提交更改 (`git commit -am 'Add some feature'`)
4. 推送分支 (`git push origin feature/your-feature`)
5. 提交 Pull Request

## 许可证

MIT License