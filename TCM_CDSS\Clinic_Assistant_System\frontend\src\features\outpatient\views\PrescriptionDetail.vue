<template>
  <div class="prescription-detail-page">
    <div class="page-header">
      <h2>{{ selectedMethod?.therapy || '方剂详情' }} - 方剂详情</h2>
      <el-button type="text" class="close-btn" @click="$emit('close')">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <div class="page-content">
      <div class="prescription-detail">
        <div class="detail-section">
          <h3>主方：{{ selectedMethod?.prescription }}</h3>
        </div>
        
        <div class="detail-section">
          <h4>治法：{{ selectedMethod?.therapy }}</h4>
          <p>对应证型：{{ selectedMethod?.syndrome }}</p>
        </div>
        
        <div class="detail-section">
          <h4>方剂描述：</h4>
          <p>{{ selectedMethod?.formulaDetail?.description || '暂无描述' }}</p>
        </div>
        
        <div class="detail-section">
          <h4>组成：</h4>
          <div class="composition-table">
            <el-table :data="selectedMethod?.formulaDetail?.dose || []" size="small" border>
              <el-table-column prop="medicinalMaterials" label="药材" width="100" />
              <el-table-column prop="dose" label="用量" width="80">
                <template #default="scope">
                  {{ scope.row.dose }}{{ scope.row.doseType }}
                </template>
              </el-table-column>
              <el-table-column prop="medicinalMaterialsId" label="药材ID" width="80" />
            </el-table>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>功效与主治：</h4>
          <p>{{ selectedMethod?.formulaDetail?.efficacy || '暂无功效说明' }}</p>
        </div>
        
        <div class="detail-section">
          <h4>用法用量：</h4>
          <p>{{ selectedMethod?.formulaDetail?.usage || '暂无用法说明' }}</p>
        </div>
        
        <div class="detail-section">
          <h4>注意事项：</h4>
          <p>{{ selectedMethod?.formulaDetail?.precautions || '暂无注意事项' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Close } from '@element-plus/icons-vue'

interface Props {
  selectedMethod?: any
}

defineProps<Props>()
defineEmits<{
  close: []
}>()
</script>

<style scoped>
.prescription-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--background-color);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

.page-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.close-btn:hover {
  color: var(--text-primary);
}

.page-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.prescription-detail {
  max-width: 800px;
  margin: 0 auto;
}

.detail-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.detail-section h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.detail-section h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.detail-section p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-secondary);
}

.composition-table {
  margin-top: var(--spacing-sm);
}

.composition-table :deep(.el-table) {
  border-radius: var(--border-radius-small);
}

.composition-table :deep(.el-table th) {
  background: var(--background-color);
  color: var(--text-primary);
  font-weight: 600;
}

.composition-table :deep(.el-table td) {
  color: var(--text-secondary);
}
</style>
