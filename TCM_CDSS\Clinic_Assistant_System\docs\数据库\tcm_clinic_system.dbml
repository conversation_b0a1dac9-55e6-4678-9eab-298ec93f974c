// =====================================================
// 中医诊所助手系统 - 数据库设计 DBML
// =====================================================
// 文件: tcm_clinic_system.dbml
// 描述: 数据库结构设计文档（DBML格式）
// 版本: v1.0
// 创建日期: 2025-06-24
// 工具: 可使用 dbdiagram.io 进行可视化
// =====================================================

Project tcm_clinic_system {
  database_type: 'MySQL'
  Note: '''
    # 中医诊所助手系统数据库设计
    
    ## 系统特色
    - 基于前端功能模块设计
    - 突出中医诊疗特色（四诊、证型、方剂）
    - 完整的业务流程支持
    - 权限管理体系
    - 统计分析支持
    
    ## 主要模块
    - 用户权限管理
    - 患者就诊管理
    - 电子病历系统
    - 药品库存管理
    - 财务收费管理
    - 排班考勤管理
    - 统计报表分析
    - 研学中心
    - 应用商城
  '''
}

// =====================================================
// 4.1 用户与权限组
// =====================================================

Table staff_departments {
  id int [pk, increment, note: '科室ID']
  name varchar(50) [not null, unique, note: '科室名称']
  description text [note: '科室描述']
  head_id int [ref: > staff.id, note: '科室主任ID']
  is_active boolean [not null, default: true, note: '是否启用']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  
  Note: '科室表 - 管理诊所科室信息'
}

Table roles {
  id int [pk, increment, note: '角色ID']
  name varchar(50) [not null, unique, note: '角色名称']
  display_name varchar(100) [not null, note: '显示名称']
  description text [note: '角色描述']
  is_active boolean [not null, default: true, note: '是否启用']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  
  Note: '角色表 - 定义系统角色'
}

Table permissions {
  id int [pk, increment, note: '权限ID']
  name varchar(100) [not null, unique, note: '权限标识']
  display_name varchar(100) [not null, note: '显示名称']
  module varchar(50) [not null, note: '所属模块']
  description text [note: '权限描述']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  
  indexes {
    module
  }
  
  Note: '权限表 - 定义系统权限点'
}

Table role_permissions {
  role_id int [ref: > roles.id, note: '角色ID']
  permission_id int [ref: > permissions.id, note: '权限ID']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  
  indexes {
    (role_id, permission_id) [pk]
  }
  
  Note: '角色权限关联表 - 多对多关系'
}

Table staff {
  id int [pk, increment, note: '员工ID']
  employee_number varchar(20) [not null, unique, note: '员工工号']
  full_name varchar(100) [not null, note: '员工姓名']
  phone varchar(20) [not null, unique, note: '手机号（用于登录）']
  password_hash varchar(255) [not null, note: '密码哈希']
  role_id int [ref: > roles.id, not null, note: '角色ID']
  department_id int [ref: > staff_departments.id, note: '科室ID']
  title varchar(50) [note: '职称/职位']
  specialty text [note: '医师擅长领域']
  avatar varchar(255) [note: '头像URL']
  email varchar(100) [note: '邮箱']
  is_active boolean [not null, default: true, note: '是否启用']
  last_login_at datetime [note: '最后登录时间']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']
  
  indexes {
    phone
    full_name
    role_id
    department_id
  }
  
  Note: '员工表 - 存储所有诊所员工信息'
}

// =====================================================
// 4.2 患者与就诊核心组
// =====================================================

Table patients {
  id int [pk, increment, note: '患者ID']
  patient_number varchar(50) [not null, unique, note: '病历号']
  full_name varchar(100) [not null, note: '姓名']
  gender enum('male', 'female', 'unknown') [not null, note: '性别: male-男, female-女, unknown-未知']
  date_of_birth date [not null, note: '出生日期']
  phone varchar(20) [note: '手机号']
  id_card_number varchar(18) [note: '身份证号']
  address varchar(255) [note: '联系地址']
  allergies text [note: '过敏史']
  medical_history text [note: '既往病史']
  family_history text [note: '家族病史']
  status enum('active', 'inactive') [not null, default: 'active', note: '状态']
  last_visit_date date [note: '最后就诊日期']
  next_appointment_date date [note: '下次预约日期']
  visit_count int [not null, default: 0, note: '就诊次数']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']
  
  indexes {
    phone
    full_name
    id_card_number
    status
    last_visit_date
    next_appointment_date
  }
  
  Note: '患者信息表 - 存储患者基本档案'
}

Table patient_contacts {
  id int [pk, increment, note: '联系人ID']
  patient_id int [ref: > patients.id, not null, note: '患者ID']
  name varchar(100) [not null, note: '联系人姓名']
  phone varchar(20) [not null, note: '联系电话']
  relationship varchar(50) [not null, note: '关系']
  is_emergency boolean [not null, default: false, note: '是否紧急联系人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  
  indexes {
    patient_id
    is_emergency
  }
  
  Note: '患者联系人表 - 存储紧急联系人信息'
}

Table appointments {
  id int [pk, increment, note: '预约ID']
  appointment_number varchar(50) [not null, unique, note: '预约号']
  patient_id int [ref: > patients.id, not null, note: '患者ID']
  doctor_id int [ref: > staff.id, not null, note: '医生ID']
  appointment_date date [not null, note: '预约日期']
  appointment_time time [not null, note: '预约时间']
  status enum('pending', 'confirmed', 'completed', 'cancelled', 'absent') [not null, default: 'pending', note: '预约状态']
  source enum('online', 'offline', 'phone') [not null, note: '预约来源']
  chief_complaint text [note: '主诉']
  note text [note: '备注信息']
  created_by int [ref: > staff.id, not null, note: '创建人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']
  
  indexes {
    patient_id
    doctor_id
    appointment_date
    appointment_time
    status
    source
    (doctor_id, appointment_date)
  }
  
  Note: '预约记录表 - 存储所有预约信息'
}

Table visits {
  id int [pk, increment, note: '就诊ID']
  visit_number varchar(50) [not null, unique, note: '就诊号']
  patient_id int [ref: > patients.id, not null, note: '患者ID']
  doctor_id int [ref: > staff.id, not null, note: '医生ID']
  appointment_id int [ref: > appointments.id, note: '关联预约ID']
  visit_date date [not null, note: '就诊日期']
  visit_start_time datetime [not null, note: '就诊开始时间']
  visit_end_time datetime [note: '就诊结束时间']
  status enum('waiting', 'in_progress', 'completed', 'billed') [not null, default: 'waiting', note: '就诊状态']
  visit_type enum('first_visit', 'follow_up', 'emergency') [not null, default: 'first_visit', note: '就诊类型']
  chief_complaint text [note: '主诉']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']
  
  indexes {
    patient_id
    doctor_id
    appointment_id
    visit_date
    status
    visit_type
    (patient_id, visit_date)
    (doctor_id, visit_date)
  }
  
  Note: '就诊记录表 - 存储每次就诊基本信息'
}

// =====================================================
// 4.3 电子病历组 (EMR - Electronic Medical Record)
// =====================================================

Table medical_records {
  id int [pk, increment, note: '病历ID']
  visit_id int [ref: - visits.id, not null, note: '就诊ID（一对一）']
  patient_id int [ref: > patients.id, not null, note: '患者ID']
  doctor_id int [ref: > staff.id, not null, note: '医生ID']
  chief_complaint text [note: '主诉']
  present_illness text [note: '现病史']
  past_history text [note: '既往史']
  family_history text [note: '家族史']
  personal_history text [note: '个人史']
  // 中医四诊
  inspection text [note: '望诊（神、色、形、态、舌象等）']
  auscultation text [note: '闻诊（语音、呼吸、咳嗽等）']
  inquiry text [note: '问诊（寒热、汗、痛、睡眠、二便等）']
  palpation text [note: '切诊（脉象、按诊等）']
  // 中医诊断
  tcm_diagnosis text [note: '中医诊断']
  tcm_syndrome text [note: '中医证型']
  western_diagnosis text [note: '西医诊断']
  treatment_principle text [note: '治法']
  doctors_orders text [note: '医嘱']
  follow_up_plan text [note: '随访计划']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    visit_id [unique]
    patient_id
    doctor_id
    created_at
  }

  Note: '电子病历表 - 存储详细病历内容（突出中医特色）'
}

Table prescriptions {
  id int [pk, increment, note: '处方ID']
  prescription_number varchar(50) [not null, unique, note: '处方号']
  visit_id int [ref: > visits.id, not null, note: '就诊ID']
  patient_id int [ref: > patients.id, not null, note: '患者ID']
  doctor_id int [ref: > staff.id, not null, note: '医生ID']
  prescription_type enum('herbal', 'patent', 'western', 'injection') [not null, note: '处方类型']
  status enum('draft', 'confirmed', 'billed', 'dispensed', 'cancelled') [not null, default: 'draft', note: '处方状态']
  dosage_instructions text [note: '服法（如: 每日一剂, 水煎服, 分两次服用）']
  decoction_method text [note: '煎煮方法（如: 先煎...后下...）']
  total_doses int [note: '总剂数']
  total_amount decimal(10,2) [note: '处方总金额']
  note text [note: '备注']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    visit_id
    patient_id
    doctor_id
    status
    prescription_type
    created_at
  }

  Note: '处方主表 - 存储完整处方信息'
}

Table prescription_items {
  id int [pk, increment, note: '处方明细ID']
  prescription_id int [ref: > prescriptions.id, not null, note: '处方ID']
  product_id int [ref: > products.id, not null, note: '产品ID']
  quantity decimal(10,2) [not null, note: '数量']
  unit varchar(20) [not null, note: '单位']
  unit_price decimal(10,2) [not null, note: '单价']
  total_price decimal(10,2) [not null, note: '小计']
  usage_method varchar(100) [note: '用法（如: 先煎, 后下, 包煎等）']
  note text [note: '备注']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    prescription_id
    product_id
  }

  Note: '处方明细表 - 存储处方中的每个药品'
}

Table medical_images {
  id int [pk, increment, note: '影像ID']
  visit_id int [ref: > visits.id, not null, note: '就诊ID']
  patient_id int [ref: > patients.id, not null, note: '患者ID']
  image_type enum('xray', 'ct', 'mri', 'ultrasound', 'photo', 'other') [not null, note: '影像类型']
  file_name varchar(255) [not null, note: '文件名']
  file_path varchar(500) [not null, note: '文件路径']
  file_size bigint [not null, note: '文件大小（字节）']
  mime_type varchar(100) [not null, note: 'MIME类型']
  description text [note: '描述']
  created_by int [ref: > staff.id, not null, note: '上传人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    visit_id
    patient_id
    image_type
    created_by
  }

  Note: '医疗影像表 - 存储医疗影像文件信息'
}

// =====================================================
// 4.4 药品与库存组
// =====================================================

Table suppliers {
  id int [pk, increment, note: '供应商ID']
  name varchar(100) [not null, note: '供应商名称']
  contact_person varchar(50) [note: '联系人']
  phone varchar(20) [note: '联系电话']
  address varchar(255) [note: '地址']
  email varchar(100) [note: '邮箱']
  business_license varchar(100) [note: '营业执照号']
  is_active boolean [not null, default: true, note: '是否启用']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    name
    is_active
  }

  Note: '供应商表 - 存储供应商信息'
}

Table products {
  id int [pk, increment, note: '产品ID']
  product_code varchar(50) [not null, unique, note: '产品编码']
  name varchar(100) [not null, note: '项目名称']
  pinyin_code varchar(50) [note: '拼音码, 用于快速搜索']
  product_type enum('herbal', 'patent', 'western', 'service', 'consumable') [not null, note: '产品类型']
  category varchar(50) [note: '分类']
  specifications varchar(100) [note: '规格']
  unit varchar(20) [not null, note: '基本单位']
  cost_price decimal(10,2) [note: '成本价']
  selling_price decimal(10,2) [not null, note: '销售价']
  supplier_id int [ref: > suppliers.id, note: '供应商ID']
  manufacturer varchar(100) [note: '生产厂家']
  approval_number varchar(100) [note: '批准文号']
  expiry_days int [note: '有效期天数']
  storage_condition varchar(100) [note: '储存条件']
  is_prescription boolean [not null, default: false, note: '是否处方药']
  is_active boolean [not null, default: true, note: '是否启用']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    name
    pinyin_code
    product_type
    category
    supplier_id
    is_active
  }

  Note: '产品表 - 定义所有可销售、可使用的项目'
}

Table inventory_stock {
  product_id int [pk, ref: - products.id, note: '产品ID（一对一）']
  quantity_on_hand decimal(10,2) [not null, default: 0, note: '当前库存量']
  reserved_quantity decimal(10,2) [not null, default: 0, note: '预留数量']
  available_quantity decimal(10,2) [note: '可用数量（计算字段）']
  location varchar(100) [note: '存储位置']
  low_stock_threshold decimal(10,2) [note: '低库存预警阈值']
  last_updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '最后更新时间']

  indexes {
    (low_stock_threshold, quantity_on_hand)
    available_quantity
  }

  Note: '库存表 - 跟踪实体物品的库存水平'
}

Table inventory_transactions {
  id int [pk, increment, note: '变动记录ID']
  product_id int [ref: > products.id, not null, note: '产品ID']
  transaction_type enum('in', 'out', 'adjust', 'expired', 'damaged') [not null, note: '变动类型']
  quantity decimal(10,2) [not null, note: '变动数量（正数为入库, 负数为出库）']
  unit_cost decimal(10,2) [note: '单位成本']
  reference_type enum('purchase', 'prescription', 'adjustment', 'waste') [note: '关联类型']
  reference_id int [note: '关联ID']
  batch_number varchar(50) [note: '批次号']
  expiry_date date [note: '有效期']
  note text [note: '备注']
  operator_id int [ref: > staff.id, not null, note: '操作人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    product_id
    transaction_type
    created_at
    operator_id
    (reference_type, reference_id)
  }

  Note: '库存变动记录表 - 记录所有库存变动'
}

// =====================================================
// 4.5 财务组
// =====================================================

Table billings {
  id int [pk, increment, note: '账单ID']
  billing_number varchar(50) [not null, unique, note: '账单号']
  visit_id int [ref: - visits.id, not null, note: '就诊ID（一对一）']
  patient_id int [ref: > patients.id, not null, note: '患者ID']
  total_amount decimal(10,2) [not null, note: '总金额']
  paid_amount decimal(10,2) [not null, default: 0, note: '已付金额']
  discount_amount decimal(10,2) [not null, default: 0, note: '优惠金额']
  status enum('pending', 'partial', 'paid', 'cancelled') [not null, default: 'pending', note: '账单状态']
  billing_date date [not null, note: '账单日期']
  due_date date [note: '到期日期']
  note text [note: '备注']
  created_by int [ref: > staff.id, not null, note: '创建人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    visit_id [unique]
    patient_id
    status
    billing_date
  }

  Note: '账单表 - 为每一次就诊生成一张总账单'
}

Table billing_items {
  id int [pk, increment, note: '账单明细ID']
  billing_id int [ref: > billings.id, not null, note: '账单ID']
  item_type enum('service', 'prescription', 'material', 'other') [not null, note: '项目类型']
  item_name varchar(100) [not null, note: '项目名称']
  product_id int [ref: > products.id, note: '关联产品ID']
  prescription_id int [ref: > prescriptions.id, note: '关联处方ID']
  quantity decimal(10,2) [not null, note: '数量']
  unit_price decimal(10,2) [not null, note: '单价']
  total_price decimal(10,2) [not null, note: '小计']
  discount_rate decimal(5,2) [not null, default: 0, note: '折扣率']
  discount_amount decimal(10,2) [not null, default: 0, note: '折扣金额']
  final_amount decimal(10,2) [not null, note: '最终金额']
  note text [note: '备注']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    billing_id
    product_id
    prescription_id
  }

  Note: '账单明细表 - 记录账单中包含的所有费用项'
}

Table payments {
  id int [pk, increment, note: '支付记录ID']
  payment_number varchar(50) [not null, unique, note: '支付单号']
  billing_id int [ref: > billings.id, not null, note: '账单ID']
  payment_method enum('cash', 'card', 'wechat', 'alipay', 'bank_transfer') [not null, note: '支付方式']
  amount decimal(10,2) [not null, note: '支付金额']
  transaction_id varchar(100) [note: '第三方交易号']
  status enum('pending', 'success', 'failed', 'cancelled') [not null, default: 'pending', note: '支付状态']
  payment_date datetime [not null, note: '支付时间']
  note text [note: '备注']
  operator_id int [ref: > staff.id, not null, note: '操作员']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    billing_id
    payment_date
    payment_method
  }

  Note: '支付记录表 - 记录所有支付信息'
}

Table refunds {
  id int [pk, increment, note: '退款记录ID']
  refund_number varchar(50) [not null, unique, note: '退款单号']
  payment_id int [ref: > payments.id, not null, note: '支付记录ID']
  billing_id int [ref: > billings.id, not null, note: '账单ID']
  refund_amount decimal(10,2) [not null, note: '退款金额']
  refund_reason text [not null, note: '退款原因']
  status enum('pending', 'approved', 'rejected', 'completed') [not null, default: 'pending', note: '退款状态']
  refund_date datetime [note: '退款时间']
  approved_by int [ref: > staff.id, note: '审批人']
  operator_id int [ref: > staff.id, not null, note: '操作员']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    payment_id
    billing_id
    status
  }

  Note: '退款记录表 - 记录退款信息'
}

// =====================================================
// 4.6 排班与员工管理组
// =====================================================

Table schedules {
  id int [pk, increment, note: '排班ID']
  staff_id int [ref: > staff.id, not null, note: '员工ID']
  schedule_date date [not null, note: '排班日期']
  shift_type enum('morning', 'afternoon', 'evening', 'night', 'full_day') [not null, note: '班次类型']
  start_time time [not null, note: '开始时间']
  end_time time [not null, note: '结束时间']
  max_appointments int [note: '最大预约数']
  status enum('scheduled', 'confirmed', 'cancelled', 'completed') [not null, default: 'scheduled', note: '排班状态']
  note text [note: '备注']
  created_by int [ref: > staff.id, not null, note: '创建人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    (staff_id, schedule_date)
    schedule_date
    status
  }

  Note: '排班表 - 存储员工排班信息'
}

Table attendance {
  id int [pk, increment, note: '考勤ID']
  staff_id int [ref: > staff.id, not null, note: '员工ID']
  attendance_date date [not null, note: '考勤日期']
  check_in_time datetime [note: '签到时间']
  check_out_time datetime [note: '签退时间']
  work_hours decimal(4,2) [note: '工作小时数']
  status enum('present', 'absent', 'late', 'early_leave', 'overtime') [not null, note: '考勤状态']
  note text [note: '备注']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    (staff_id, attendance_date)
    attendance_date
  }

  Note: '考勤记录表 - 记录员工考勤信息'
}

// =====================================================
// 4.7 统计与报表组
// =====================================================

Table daily_statistics {
  id int [pk, increment, note: '统计ID']
  stat_date date [not null, unique, note: '统计日期']
  patient_count int [not null, default: 0, note: '患者数量']
  new_patient_count int [not null, default: 0, note: '新患者数量']
  visit_count int [not null, default: 0, note: '就诊次数']
  appointment_count int [not null, default: 0, note: '预约数量']
  prescription_count int [not null, default: 0, note: '处方数量']
  total_revenue decimal(12,2) [not null, default: 0, note: '总收入']
  service_revenue decimal(12,2) [not null, default: 0, note: '服务收入']
  medicine_revenue decimal(12,2) [not null, default: 0, note: '药品收入']
  avg_visit_duration int [note: '平均就诊时长（分钟）']
  ai_usage_count int [not null, default: 0, note: 'AI使用次数']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    stat_date
  }

  Note: '日统计表 - 存储每日业务统计数据'
}

Table module_usage_stats {
  id int [pk, increment, note: '统计ID']
  module_name varchar(50) [not null, note: '模块名称']
  stat_date date [not null, note: '统计日期']
  usage_count int [not null, default: 0, note: '使用次数']
  unique_users int [not null, default: 0, note: '独立用户数']
  avg_session_duration int [note: '平均会话时长（秒）']
  error_count int [not null, default: 0, note: '错误次数']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    (module_name, stat_date)
  }

  Note: '模块使用统计表 - 统计各功能模块的使用情况'
}

Table ai_usage_stats {
  id int [pk, increment, note: '统计ID']
  feature_name varchar(50) [not null, note: 'AI功能名称']
  stat_date date [not null, note: '统计日期']
  usage_count int [not null, default: 0, note: '使用次数']
  success_count int [not null, default: 0, note: '成功次数']
  accuracy_rate decimal(5,2) [note: '准确率']
  avg_response_time int [note: '平均响应时间（毫秒）']
  user_satisfaction decimal(3,2) [note: '用户满意度']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    (feature_name, stat_date)
  }

  Note: 'AI使用统计表 - 统计AI功能的使用情况和效果'
}

// =====================================================
// 4.8 系统配置组
// =====================================================

Table clinic_info {
  id int [pk, increment, note: '诊所信息ID']
  name varchar(100) [not null, note: '诊所名称']
  license_number varchar(100) [note: '医疗机构执业许可证号']
  legal_person varchar(50) [note: '法人代表']
  phone varchar(20) [note: '联系电话']
  address varchar(255) [note: '地址']
  business_hours_start time [note: '营业开始时间']
  business_hours_end time [note: '营业结束时间']
  logo_url varchar(255) [note: '诊所Logo']
  description text [note: '诊所简介']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  Note: '诊所信息表 - 存储诊所基本信息'
}

Table system_settings {
  id int [pk, increment, note: '设置ID']
  setting_key varchar(100) [not null, unique, note: '设置键']
  setting_value text [note: '设置值']
  setting_type enum('string', 'number', 'boolean', 'json') [not null, note: '数据类型']
  category varchar(50) [not null, note: '分类']
  description text [note: '描述']
  is_public boolean [not null, default: false, note: '是否公开']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    category
  }

  Note: '系统设置表 - 存储系统配置参数'
}

Table lingji_settings {
  id int [pk, increment, note: '设置ID']
  feature_name varchar(50) [not null, unique, note: '功能名称']
  is_enabled boolean [not null, default: true, note: '是否启用']
  confidence_threshold decimal(3,2) [note: '置信度阈值']
  response_mode enum('fast', 'balanced', 'accurate') [not null, default: 'balanced', note: '响应模式']
  max_suggestions int [note: '最大建议数']
  config_json json [note: '详细配置']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  Note: '灵机管理设置表 - 存储AI灵机功能的配置参数'
}

// =====================================================
// 4.9 研学中心组
// =====================================================

Table knowledge_base {
  id int [pk, increment, note: '知识库ID']
  title varchar(200) [not null, note: '标题']
  content_type enum('syndrome', 'formula', 'herb', 'acupoint', 'theory', 'case') [not null, note: '内容类型']
  content longtext [not null, note: '内容']
  keywords text [note: '关键词']
  category varchar(50) [note: '分类']
  source varchar(100) [note: '来源']
  author varchar(50) [note: '作者']
  difficulty_level enum('beginner', 'intermediate', 'advanced') [not null, default: 'intermediate', note: '难度级别']
  view_count int [not null, default: 0, note: '浏览次数']
  like_count int [not null, default: 0, note: '点赞次数']
  is_published boolean [not null, default: true, note: '是否发布']
  created_by int [ref: > staff.id, not null, note: '创建人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    content_type
    category
    keywords
  }

  Note: '知识库表 - 存储中医知识库内容'
}

Table case_studies {
  id int [pk, increment, note: '医案ID']
  title varchar(200) [not null, note: '医案标题']
  patient_info text [not null, note: '患者信息（脱敏）']
  chief_complaint text [not null, note: '主诉']
  diagnosis_process longtext [not null, note: '诊断过程']
  treatment_plan longtext [not null, note: '治疗方案']
  follow_up text [note: '随访记录']
  outcome text [note: '治疗结果']
  analysis longtext [note: '医案分析']
  teaching_points text [note: '教学要点']
  tags text [note: '标签']
  difficulty_level enum('beginner', 'intermediate', 'advanced') [not null, note: '难度级别']
  is_classic boolean [not null, default: false, note: '是否经典医案']
  created_by int [ref: > staff.id, not null, note: '创建人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    tags
    difficulty_level
  }

  Note: '医案研究表 - 存储医案研究数据'
}

Table study_materials {
  id int [pk, increment, note: '资料ID']
  title varchar(200) [not null, note: '资料标题']
  material_type enum('video', 'audio', 'document', 'image', 'link') [not null, note: '资料类型']
  file_path varchar(500) [note: '文件路径']
  file_size bigint [note: '文件大小']
  duration int [note: '时长（秒）']
  description text [note: '描述']
  category varchar(50) [note: '分类']
  tags text [note: '标签']
  difficulty_level enum('beginner', 'intermediate', 'advanced') [not null, note: '难度级别']
  download_count int [not null, default: 0, note: '下载次数']
  is_free boolean [not null, default: true, note: '是否免费']
  price decimal(8,2) [note: '价格']
  created_by int [ref: > staff.id, not null, note: '创建人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    material_type
    category
  }

  Note: '学习资料表 - 存储学习资料信息'
}

Table research_data {
  id int [pk, increment, note: '研究数据ID']
  research_type varchar(50) [not null, note: '研究类型']
  data_source varchar(100) [not null, note: '数据来源']
  time_period_start date [not null, note: '时间段开始']
  time_period_end date [not null, note: '时间段结束']
  data_content json [not null, note: '数据内容']
  analysis_result text [note: '分析结果']
  created_by int [ref: > staff.id, not null, note: '创建人']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    research_type
    (time_period_start, time_period_end)
  }

  Note: '研究数据表 - 存储用于科研的统计数据'
}

// =====================================================
// 4.10 应用与商城组
// =====================================================

Table applications {
  id int [pk, increment, note: '应用ID']
  name varchar(100) [not null, note: '应用名称']
  package_name varchar(100) [not null, unique, note: '包名']
  version varchar(20) [not null, note: '版本号']
  description text [note: '应用描述']
  icon_url varchar(255) [note: '图标URL']
  download_url varchar(500) [not null, note: '下载链接']
  file_size bigint [not null, note: '文件大小']
  category varchar(50) [note: '分类']
  developer varchar(100) [note: '开发者']
  is_free boolean [not null, default: true, note: '是否免费']
  price decimal(8,2) [note: '价格']
  rating decimal(2,1) [note: '评分']
  download_count int [not null, default: 0, note: '下载次数']
  is_active boolean [not null, default: true, note: '是否启用']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    category
    package_name
  }

  Note: '应用表 - 存储应用中心的应用信息'
}

Table app_downloads {
  id int [pk, increment, note: '下载记录ID']
  application_id int [ref: > applications.id, not null, note: '应用ID']
  user_id int [ref: > staff.id, not null, note: '用户ID']
  download_time datetime [not null, note: '下载时间']
  ip_address varchar(45) [note: 'IP地址']
  user_agent text [note: '用户代理']
  status enum('started', 'completed', 'failed') [not null, default: 'started', note: '下载状态']

  indexes {
    application_id
    user_id
    download_time
  }

  Note: '应用下载记录表 - 记录应用下载历史'
}

Table mall_products {
  id int [pk, increment, note: '商品ID']
  product_code varchar(50) [not null, unique, note: '商品编码']
  name varchar(100) [not null, note: '商品名称']
  description text [note: '商品描述']
  category varchar(50) [note: '商品分类']
  brand varchar(50) [note: '品牌']
  specifications varchar(100) [note: '规格']
  unit varchar(20) [not null, note: '单位']
  price decimal(10,2) [not null, note: '价格']
  stock_quantity int [not null, default: 0, note: '库存数量']
  min_order_quantity int [not null, default: 1, note: '最小订购量']
  images json [note: '商品图片']
  supplier_id int [ref: > suppliers.id, note: '供应商ID']
  is_active boolean [not null, default: true, note: '是否启用']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    category
    product_code
  }

  Note: '商城商品表 - 存储商城商品信息'
}

Table mall_orders {
  id int [pk, increment, note: '订单ID']
  order_number varchar(50) [not null, unique, note: '订单号']
  user_id int [ref: > staff.id, not null, note: '下单用户']
  total_amount decimal(10,2) [not null, note: '订单总金额']
  shipping_fee decimal(8,2) [not null, default: 0, note: '运费']
  discount_amount decimal(8,2) [not null, default: 0, note: '优惠金额']
  final_amount decimal(10,2) [not null, note: '最终金额']
  status enum('pending', 'paid', 'shipped', 'delivered', 'cancelled') [not null, default: 'pending', note: '订单状态']
  shipping_address text [not null, note: '收货地址']
  contact_phone varchar(20) [not null, note: '联系电话']
  note text [note: '备注']
  order_date datetime [not null, note: '下单时间']
  payment_date datetime [note: '支付时间']
  shipping_date datetime [note: '发货时间']
  delivery_date datetime [note: '收货时间']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '更新时间']

  indexes {
    user_id
    status
    order_date
  }

  Note: '商城订单表 - 存储商城订单信息'
}

Table mall_order_items {
  id int [pk, increment, note: '订单明细ID']
  order_id int [ref: > mall_orders.id, not null, note: '订单ID']
  product_id int [ref: > mall_products.id, not null, note: '商品ID']
  quantity int [not null, note: '数量']
  unit_price decimal(10,2) [not null, note: '单价']
  total_price decimal(10,2) [not null, note: '小计']
  created_at datetime [not null, default: `CURRENT_TIMESTAMP`, note: '创建时间']

  indexes {
    order_id
    product_id
  }

  Note: '商城订单明细表 - 存储订单商品明细'
}
