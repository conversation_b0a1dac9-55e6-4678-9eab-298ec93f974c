import requests
import json
import os
from typing import List, Dict, Optional, Any
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.outputs import ChatResult, ChatGeneration
from src.config import settings
from src.utils.ollama_client import OllamaLLMClient


class OpenRouterClient:
    """OpenRouter客户端"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        self.api_key = api_key or settings.openrouter_api_key
        self.base_url = base_url or settings.openrouter_base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
    
    def chat_completion(
        self, 
        messages: List[Dict], 
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        **kwargs
    ) -> Dict:
        """调用OpenRouter聊天接口"""
        model = model or settings.default_model
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            **kwargs
        }
        
        try:
            response = self.session.post(f"{self.base_url}/chat/completions", json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"OpenRouter API调用失败: {e}")
    
    def get_available_models(self) -> List[Dict]:
        """获取可用模型列表"""
        try:
            response = self.session.get(f"{self.base_url}/models")
            response.raise_for_status()
            return response.json()["data"]
        except requests.exceptions.RequestException as e:
            raise Exception(f"获取模型列表失败: {e}")
    
    def get_model_info(self, model_id: str) -> Optional[Dict]:
        """获取特定模型信息"""
        models = self.get_available_models()
        for model in models:
            if model["id"] == model_id:
                return model
        return None


class OpenRouterChatModel(BaseChatModel):
    """OpenRouter聊天模型包装器"""
    
    model_name: str
    temperature: float = 0.7
    max_tokens: int = 2000
    model_config = {"extra": "allow"}
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = OpenRouterClient()
    
    @property
    def _llm_type(self) -> str:
        return "openrouter"
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs
    ) -> ChatResult:
        """生成聊天响应"""
        # 转换消息格式
        openrouter_messages = []
        for message in messages:
            if isinstance(message, SystemMessage):
                openrouter_messages.append({"role": "system", "content": message.content})
            elif isinstance(message, HumanMessage):
                openrouter_messages.append({"role": "user", "content": message.content})
            elif isinstance(message, AIMessage):
                openrouter_messages.append({"role": "assistant", "content": message.content})
        
        # 调用OpenRouter API
        response = self.client.chat_completion(
            messages=openrouter_messages,
            model=self.model_name,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            **kwargs
        )
        
        # 解析响应
        if "choices" in response and len(response["choices"]) > 0:
            choice = response["choices"][0]
            message_content = choice["message"]["content"]
            
            # 创建AIMessage
            ai_message = AIMessage(content=message_content)
            
            # 创建ChatGeneration
            generation = ChatGeneration(message=ai_message)
            
            return ChatResult(generations=[generation])
        else:
            raise Exception("OpenRouter响应格式错误")
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs
    ) -> ChatResult:
        """异步生成聊天响应"""
        # 目前使用同步方法，可以后续优化为真正的异步
        return self._generate(messages, stop, run_manager, **kwargs)


class ModelSelector:
    """模型选择器"""
    
    def __init__(self):
        self.backend = os.getenv("LLM_BACKEND", "openrouter")
        self.model = os.getenv("LLM_MODEL", "gpt-4")
    
    def get_model_for_agent(self, agent_type: str) -> BaseChatModel:
        """获取智能体对应的模型实例"""
        if self.backend == "openrouter":
            return OpenRouterChatModel(model_name=self.model)
        elif self.backend == "ollama":
            return OllamaLLMClient(model_name=self.model)
        else:
            raise ValueError(f"不支持的 LLM_BACKEND: {self.backend}") 