"""
聊天API
"""

from fastapi import APIRouter, HTTPException
from models.request_models import ChatRequest
from models.response_models import ChatResponse
from services.chat_service import chat_service

router = APIRouter()

@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口 - 支持对话历史记忆"""
    try:
        # 处理消息，获取回复和会话ID
        response_text, session_id = chat_service.process_message(
            message=request.message,
            session_id=request.session_id
        )

        return ChatResponse(
            status="success",
            response=response_text,
            session_id=session_id
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"处理消息失败: {str(e)}"
        )

@router.get("/chat/history/{session_id}")
async def get_chat_history(session_id: str):
    """获取聊天历史"""
    try:
        history = chat_service.get_session_history(session_id)
        summary = chat_service.get_session_summary(session_id)

        return {
            "status": "success",
            "session_id": session_id,
            "summary": summary,
            "history": history
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取历史失败: {str(e)}"
        )

@router.delete("/chat/history/{session_id}")
async def clear_chat_history(session_id: str):
    """清空聊天历史"""
    try:
        chat_service.clear_session(session_id)

        return {
            "status": "success",
            "message": f"会话 {session_id} 历史已清空"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"清空历史失败: {str(e)}"
        )
