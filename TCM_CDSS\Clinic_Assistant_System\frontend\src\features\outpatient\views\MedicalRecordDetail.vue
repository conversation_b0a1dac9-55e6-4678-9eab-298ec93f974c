<template>
  <div class="medical-record-detail-page">
    <div class="page-header">
      <h2>病历详情</h2>
      <el-button type="text" class="close-btn" @click="$emit('close')">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <div class="page-content">
      <div class="record-detail">
        <div class="detail-section">
          <h3>患者信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>姓名：</label>
              <span>{{ selectedRecord?.patientName || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>性别：</label>
              <span>{{ selectedRecord?.gender || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>年龄：</label>
              <span>{{ selectedRecord?.age || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>就诊时间：</label>
              <span>{{ selectedRecord?.visitDate || '未知' }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>主诉</h3>
          <p>{{ selectedRecord?.chiefComplaint || '暂无主诉信息' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>现病史</h3>
          <p>{{ selectedRecord?.presentIllness || '暂无现病史信息' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>既往史</h3>
          <p>{{ selectedRecord?.pastHistory || '暂无既往史信息' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>体格检查</h3>
          <p>{{ selectedRecord?.physicalExam || '暂无体格检查信息' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>中医四诊</h3>
          <div class="tcm-diagnosis">
            <div class="diagnosis-item">
              <h4>望诊：</h4>
              <p>{{ selectedRecord?.tcmDiagnosis?.inspection || '暂无望诊信息' }}</p>
            </div>
            <div class="diagnosis-item">
              <h4>闻诊：</h4>
              <p>{{ selectedRecord?.tcmDiagnosis?.auscultation || '暂无闻诊信息' }}</p>
            </div>
            <div class="diagnosis-item">
              <h4>问诊：</h4>
              <p>{{ selectedRecord?.tcmDiagnosis?.inquiry || '暂无问诊信息' }}</p>
            </div>
            <div class="diagnosis-item">
              <h4>切诊：</h4>
              <p>{{ selectedRecord?.tcmDiagnosis?.palpation || '暂无切诊信息' }}</p>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>诊断</h3>
          <div class="diagnosis-content">
            <div class="diagnosis-item">
              <h4>西医诊断：</h4>
              <p>{{ selectedRecord?.westernDiagnosis || '暂无西医诊断' }}</p>
            </div>
            <div class="diagnosis-item">
              <h4>中医诊断：</h4>
              <p>{{ selectedRecord?.tcmDiagnosis?.diagnosis || '暂无中医诊断' }}</p>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>治疗方案</h3>
          <p>{{ selectedRecord?.treatment || '暂无治疗方案' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>处方</h3>
          <p>{{ selectedRecord?.prescription || '暂无处方信息' }}</p>
        </div>
        
        <div class="detail-section">
          <h3>医嘱</h3>
          <p>{{ selectedRecord?.doctorAdvice || '暂无医嘱信息' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Close } from '@element-plus/icons-vue'

interface Props {
  selectedRecord?: any
}

defineProps<Props>()
defineEmits<{
  close: []
}>()
</script>

<style scoped>
.medical-record-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--background-color);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

.page-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.close-btn:hover {
  color: var(--text-primary);
}

.page-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.record-detail {
  max-width: 800px;
  margin: 0 auto;
}

.detail-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
}

.detail-section h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.detail-section h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.detail-section p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-secondary);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: var(--spacing-xs);
}

.info-item span {
  color: var(--text-secondary);
}

.tcm-diagnosis,
.diagnosis-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

.diagnosis-item {
  padding: var(--spacing-md);
  background: var(--surface-color);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.diagnosis-item h4 {
  margin-bottom: var(--spacing-xs);
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .page-content {
    padding: var(--spacing-md);
  }
}
</style>
