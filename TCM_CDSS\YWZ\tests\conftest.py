"""pytest配置文件"""

import pytest
import asyncio
from typing import Generator
from fastapi.testclient import TestClient

from src.main import app
from src.models.state_models import InquiryState, PatientInfo


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> Generator:
    """测试客户端"""
    with TestClient(app) as c:
        yield c


@pytest.fixture
def sample_patient_info() -> PatientInfo:
    """示例患者信息"""
    return PatientInfo(
        patient_id="test_001",
        name="测试患者",
        age=30,
        gender="男"
    )


@pytest.fixture
def sample_inquiry_state(sample_patient_info) -> InquiryState:
    """示例问诊状态"""
    return InquiryState(
        session_id="test_session_001",
        patient_info=sample_patient_info
    )


@pytest.fixture
def mock_llm_response():
    """模拟LLM响应"""
    return "这是一个模拟的LLM响应"
