# YUWENZHEN - 中医预问诊智能体系统依赖包 (简化版)

# ==================== 核心框架 ====================
# <PERSON><PERSON><PERSON><PERSON>生态系统
langchain>=0.2.0,<0.3.0
langgraph>=0.2.0,<0.3.0
langchain-core>=0.2.0,<0.3.0
langchain-ollama>=0.2.0,<0.3.0

# Web框架
fastapi>=0.104.0,<0.105.0
uvicorn[standard]>=0.24.0,<0.25.0

# ==================== 数据验证和模型 ====================
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# ==================== 数据库 ====================
# SQLAlchemy ORM
sqlalchemy>=2.0.0,<2.1.0

# SQLite异步支持
aiosqlite>=0.19.0,<0.20.0

# ==================== HTTP客户端 ====================
httpx>=0.25.0,<0.26.0

# ==================== 安全和认证 ====================
python-jose[cryptography]>=3.3.0,<4.0.0
passlib[bcrypt]>=1.7.4,<2.0.0

# ==================== 配置和环境 ====================
python-dotenv>=1.0.0,<2.0.0

# ==================== 时间和日期 ====================
python-dateutil>=2.8.0,<3.0.0

# ==================== 开发和测试 (可选) ====================
pytest>=8.0.0,<9.0.0
pytest-asyncio>=0.24.0,<0.25.0

# ==================== 版本说明 ====================
#
# 简化版依赖包，专注于核心功能:
# - LangChain: AI智能体框架
# - FastAPI: Web API框架
# - SQLite: 轻量级数据库
# - Ollama: 本地LLM服务
#
# 安装命令:
# pip install -r requirements.txt
#
# 开发环境额外安装:
# pip install pytest pytest-asyncio
