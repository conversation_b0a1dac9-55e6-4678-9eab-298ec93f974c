# YUWENZHEN - 中医预问诊智能体系统依赖包

# ==================== 核心框架 ====================
# <PERSON><PERSON><PERSON><PERSON>生态系统 - 最新版本
langchain>=0.2.0,<0.3.0
langgraph>=0.2.0,<0.3.0
langchain-core>=0.2.0,<0.3.0
langchain-community>=0.2.0,<0.3.0
langchain-ollama>=0.2.0,<0.3.0

# Web框架
fastapi>=0.104.0,<0.105.0
uvicorn[standard]>=0.24.0,<0.25.0
websockets>=12.0,<13.0

# ==================== 数据验证和模型 ====================
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# ==================== 数据库 ====================
# SQLAlchemy ORM
sqlalchemy>=2.0.0,<2.1.0
alembic>=1.13.0,<2.0.0

# 数据库驱动
# PostgreSQL
psycopg2-binary>=2.9.0,<3.0.0
asyncpg>=0.29.0,<0.30.0

# SQLite (内置，但需要异步支持)
aiosqlite>=0.19.0,<0.20.0

# ==================== 缓存 ====================
redis>=5.0.0,<6.0.0

# ==================== HTTP客户端 ====================
httpx>=0.25.0,<0.26.0
requests>=2.31.0,<3.0.0

# ==================== 安全和认证 ====================
python-jose[cryptography]>=3.3.0,<4.0.0
passlib[bcrypt]>=1.7.4,<2.0.0
python-multipart>=0.0.6,<0.1.0

# ==================== 配置和环境 ====================
python-dotenv>=1.0.0,<2.0.0

# ==================== 文件处理 ====================
aiofiles>=24.1.0,<25.0.0

# ==================== 模板引擎 ====================
jinja2>=3.1.0,<4.0.0

# ==================== 监控和日志 ====================
structlog>=24.4.0,<25.0.0
prometheus-client>=0.21.0,<0.22.0

# ==================== 时间和日期 ====================
python-dateutil>=2.8.0,<3.0.0

# ==================== JSON处理 ====================
orjson>=3.9.0,<4.0.0

# ==================== 异步支持 ====================
asyncio-mqtt>=0.16.0,<0.17.0

# ==================== 测试框架 ====================
pytest>=8.0.0,<9.0.0
pytest-asyncio>=0.24.0,<0.25.0
pytest-cov>=5.0.0,<6.0.0
pytest-mock>=3.12.0,<4.0.0

# ==================== 代码质量 ====================
black>=24.0.0,<25.0.0
isort>=5.13.0,<6.0.0
flake8>=7.0.0,<8.0.0
mypy>=1.11.0,<2.0.0

# ==================== 类型检查 ====================
types-redis>=4.6.0
types-requests>=2.31.0

# ==================== 开发工具 ====================
pre-commit>=3.6.0,<4.0.0
bandit>=1.7.0,<2.0.0

# ==================== 文档生成 ====================
mkdocs>=1.5.0,<2.0.0
mkdocs-material>=9.4.0,<10.0.0

# ==================== 性能分析 ====================
py-spy>=0.3.0,<0.4.0
memory-profiler>=0.61.0,<0.62.0

# ==================== 数据处理 ====================
pandas>=2.1.0,<3.0.0
numpy>=1.24.0,<2.0.0

# ==================== 图像处理 (可选) ====================
Pillow>=10.0.0,<11.0.0

# ==================== 邮件支持 (可选) ====================
aiosmtplib>=3.0.0,<4.0.0

# ==================== 任务队列 (可选) ====================
celery>=5.3.0,<6.0.0

# ==================== 限流 ====================
slowapi>=0.1.9,<0.2.0

# ==================== 健康检查 ====================
healthcheck>=1.3.3,<2.0.0

# ==================== 配置验证 ====================
cerberus>=1.3.5,<2.0.0

# ==================== 加密 ====================
cryptography>=41.0.0,<42.0.0

# ==================== 网络工具 ====================
dnspython>=2.4.0,<3.0.0

# ==================== 系统监控 ====================
psutil>=5.9.0,<6.0.0

# ==================== 开发依赖 (仅开发环境) ====================
# 这些依赖仅在开发环境安装
# pip install -r requirements-dev.txt

# ==================== 生产优化 ====================
# 生产环境推荐的额外包
gunicorn>=21.2.0,<22.0.0  # WSGI服务器
gevent>=23.9.0,<24.0.0    # 异步支持

# ==================== 版本说明 ====================
#
# 版本策略:
# - 主要依赖使用范围版本 (>=x.y.z,<x+1.0.0)
# - 确保兼容性的同时允许小版本更新
# - 生产环境建议锁定具体版本
#
# 安装命令:
# pip install -r requirements.txt
#
# 开发环境:
# pip install -r requirements.txt -r requirements-dev.txt
#
# 生产环境:
# pip install -r requirements.txt --no-dev
#
# 更新依赖:
# pip-compile requirements.in
# pip-sync requirements.txt
