<template>
  <div class="data-center-container">
    <!-- 模块头部导航栏 -->
    <header class="module-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        功能中心
      </div>
      <h1 class="module-title">数据中心</h1>
      <div class="header-actions">
        <el-button type="primary" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </header>

    <!-- 数据概览卡片 -->
    <div class="overview-section">
      <div class="overview-cards">
        <div class="overview-card primary">
          <div class="card-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="card-content">
            <h3>{{ todayStats.totalPatients }}</h3>
            <p>今日就诊人次</p>
          </div>
        </div>
        <div class="overview-card success">
          <div class="card-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="card-content">
            <h3>{{ todayStats.totalPrescriptions }}</h3>
            <p>今日处方数量</p>
          </div>
        </div>
        <div class="overview-card warning">
          <div class="card-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="card-content">
            <h3>¥{{ todayStats.totalRevenue.toLocaleString() }}</h3>
            <p>今日营业收入</p>
          </div>
        </div>
        <div class="overview-card info">
          <div class="card-icon">
            <el-icon><MagicStick /></el-icon>
          </div>
          <div class="card-content">
            <h3>{{ todayStats.aiDiagnosis }}</h3>
            <p>AI辅助诊断</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能预留区域 -->
    <div class="content-section">
      <div class="coming-soon">
        <div class="coming-soon-content">
          <el-icon class="coming-soon-icon"><DataAnalysis /></el-icon>
          <h2>数据中心功能开发中</h2>
          <p>我们正在为您构建强大的数据分析和统计功能</p>
          
          <!-- 功能预览 -->
          <div class="feature-preview">
            <h3>即将推出的功能</h3>
            <div class="feature-grid">
              <div class="feature-item">
                <el-icon><TrendCharts /></el-icon>
                <h4>数据趋势分析</h4>
                <p>多维度数据趋势图表展示</p>
              </div>
              <div class="feature-item">
                <el-icon><PieChart /></el-icon>
                <h4>统计报表生成</h4>
                <p>自动生成各类统计报表</p>
              </div>
              <div class="feature-item">
                <el-icon><DataBoard /></el-icon>
                <h4>实时数据看板</h4>
                <p>实时监控各模块数据变化</p>
              </div>
              <div class="feature-item">
                <el-icon><Monitor /></el-icon>
                <h4>科研数据支持</h4>
                <p>为研学中心提供数据支撑</p>
              </div>
              <div class="feature-item">
                <el-icon><Calendar /></el-icon>
                <h4>历史数据查询</h4>
                <p>按时间维度查询历史数据</p>
              </div>
              <div class="feature-item">
                <el-icon><Setting /></el-icon>
                <h4>数据配置管理</h4>
                <p>灵活配置数据统计规则</p>
              </div>
            </div>
          </div>

          <!-- 数据类型说明 -->
          <div class="data-types">
            <h3>数据统计范围</h3>
            <div class="data-type-list">
              <div class="data-type-item">
                <span class="type-label">患者数据</span>
                <span class="type-desc">新增患者、活跃患者、就诊记录等</span>
              </div>
              <div class="data-type-item">
                <span class="type-label">诊疗数据</span>
                <span class="type-desc">门诊量、诊疗时长、AI辅助使用等</span>
              </div>
              <div class="data-type-item">
                <span class="type-label">处方数据</span>
                <span class="type-desc">处方类型、用药统计、配伍分析等</span>
              </div>
              <div class="data-type-item">
                <span class="type-label">收费数据</span>
                <span class="type-desc">收入统计、费用分析、支付方式等</span>
              </div>
              <div class="data-type-item">
                <span class="type-label">库存数据</span>
                <span class="type-desc">药品库存、消耗统计、采购分析等</span>
              </div>
              <div class="data-type-item">
                <span class="type-label">AI数据</span>
                <span class="type-desc">AI使用频率、准确率、效果评估等</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  Download, 
  User, 
  Document, 
  Money, 
  MagicStick,
  DataAnalysis,
  TrendCharts,
  PieChart,
  DataBoard,
  Monitor,
  Calendar,
  Setting
} from '@element-plus/icons-vue'
import { dataCenterStats } from '@/modules/cards/data-center'

const router = useRouter()

// 今日统计数据
const todayStats = computed(() => dataCenterStats.value.todayStats)

// 返回功能中心
const goBack = () => {
  router.push('/dashboard')
}

// 导出数据功能
const exportData = () => {
  ElMessage.info('数据导出功能开发中，敬请期待')
}

// 页面初始化
onMounted(() => {
  console.log('数据中心页面已加载')
})
</script>

<style scoped>
.data-center-container {
  min-height: 100vh;
  background: url('@/assets/bg.jpg') center/cover;
  background-color: rgba(255, 255, 255, 0.5);
  background-blend-mode: overlay;
}

.module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid #e8e8e8;
  backdrop-filter: blur(10px);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.3s;
}

.back-button:hover {
  color: #40a9ff;
}

.module-title {
  flex: 1;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0;
  text-align: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.overview-section {
  padding: 20px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.overview-card.primary .card-icon { background: #1890ff; }
.overview-card.success .card-icon { background: #52c41a; }
.overview-card.warning .card-icon { background: #fa8c16; }
.overview-card.info .card-icon { background: #722ed1; }

.card-content h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #262626;
}

.card-content p {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

.content-section {
  padding: 0 20px 20px;
}

.coming-soon {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.coming-soon-icon {
  font-size: 64px;
  color: #722ed1;
  margin-bottom: 20px;
}

.coming-soon h2 {
  font-size: 24px;
  color: #262626;
  margin: 0 0 8px 0;
}

.coming-soon > p {
  font-size: 16px;
  color: #8c8c8c;
  margin: 0 0 40px 0;
}

.feature-preview {
  margin-bottom: 40px;
}

.feature-preview h3 {
  font-size: 18px;
  color: #262626;
  margin: 0 0 20px 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.feature-item {
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.feature-item:hover {
  background: #f0f9ff;
  border-color: #1890ff;
}

.feature-item .el-icon {
  font-size: 32px;
  color: #722ed1;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 14px;
  color: #262626;
  margin: 0 0 8px 0;
}

.feature-item p {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
}

.data-types h3 {
  font-size: 18px;
  color: #262626;
  margin: 0 0 20px 0;
}

.data-type-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
}

.data-type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #722ed1;
}

.type-label {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.type-desc {
  font-size: 12px;
  color: #8c8c8c;
}
</style>
