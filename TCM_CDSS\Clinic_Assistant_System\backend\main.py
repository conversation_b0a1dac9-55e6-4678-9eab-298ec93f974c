"""
中医诊所助手系统 - 后端应用入口
TCM Clinic Assistant System - Backend Application Entry Point
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import J<PERSON>NResponse
from contextlib import asynccontextmanager
import uvicorn
import os
from datetime import datetime
from utils.logger import Logger
log = Logger()

# 导入数据库连接和服务
from database.connection import test_connection, health_check, get_database_stats
from services.mapping_service import MappingService
from utils.encryption import EncryptionUtils

# 应用配置
PORT = int(os.getenv("PORT", 8000))
HOST = os.getenv("HOST", "0.0.0.0")

# 生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    print("🚀 TCM Clinic Assistant System API Starting...")

    # 测试数据库连接
    print("🔌 Testing database connection...")
    connected = await test_connection()

    if not connected:
        raise Exception("Database connection failed")

    print("✅ Database connection established")
    yield

    # 关闭时执行
    print("📴 TCM Clinic Assistant System API Shutting down...")

# 创建FastAPI应用
app = FastAPI(
    title="TCM Clinic Assistant System API",
    description="中医诊所助手系统后端API",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# =====================================================
# 中间件配置
# =====================================================

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:8080",
        "http://localhost:3000",
        os.getenv("FRONTEND_URL", "http://localhost:8080")
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization"],
)

# 压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# =====================================================
# 基础路由
# =====================================================

# 根路径
@app.get("/")
async def root():
    """根路径，返回API基本信息"""
    return {
        "name": "TCM Clinic Assistant System API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "health": "/health",
            "stats": "/api/stats",
            "mappings": "/api/mappings",
            "test_encrypt": "/api/test/encrypt",
            "docs": "/docs",
            "redoc": "/redoc"
        }
    }

# 健康检查端点
@app.get("/health")
async def health_endpoint():
    """健康检查端点"""
    try:
        health = await health_check()
        status_code = 200 if health.get("status") == "healthy" else 503
        return JSONResponse(content=health, status_code=status_code)
    except Exception as error:
        return JSONResponse(
            content={
                "status": "unhealthy",
                "error": str(error),
                "timestamp": datetime.now().isoformat()
            },
            status_code=503
        )

# 数据库统计端点
@app.get("/api/stats")
async def stats_endpoint():
    """数据库统计端点"""
    try:
        stats = await get_database_stats()
        return {
            "success": True,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(error),
                "timestamp": datetime.now().isoformat()
            }
        )

# 映射数据端点
@app.get("/api/mappings")
async def mappings_endpoint(type: str = "all", category: str = None):
    """映射数据端点"""
    try:
        mapping_service = MappingService()

        if type == "genders":
            data = await mapping_service.get_all_genders()
        elif type == "roles":
            data = await mapping_service.get_all_roles()
        elif type == "departments":
            data = await mapping_service.get_all_departments()
        elif type == "statuses" and category:
            data = await mapping_service.get_statuses_by_category(category)
        elif type == "all":
            data = await mapping_service.create_mapping_cache()
        else:
            raise HTTPException(
                status_code=400,
                detail={"success": False, "error": "Invalid mapping type"}
            )

        return {
            "success": True,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(error),
                "timestamp": datetime.now().isoformat()
            }
        )

# 测试加密端点
@app.post("/api/test/encrypt")
async def test_encrypt_endpoint(request: dict):
    """测试加密端点"""
    try:
        text = request.get("text")
        if not text:
            raise HTTPException(
                status_code=400,
                detail={"success": False, "error": "Text is required"}
            )

        encryption_utils = EncryptionUtils()

        encrypted = encryption_utils.encrypt(text)
        decrypted = encryption_utils.decrypt(encrypted)
        phone_hash = encryption_utils.hash_phone("13800138000")
        masked_phone = encryption_utils.mask_sensitive_data("13800138000", "phone")

        return {
            "success": True,
            "data": {
                "original": text,
                "encrypted": encrypted,
                "decrypted": decrypted,
                "phone_hash": phone_hash,
                "masked_phone": masked_phone
            },
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(error),
                "timestamp": datetime.now().isoformat()
            }
        )

# =====================================================
# 错误处理
# =====================================================

@app.exception_handler(404)
async def not_found_handler(request, exc):
    """404错误处理"""
    return JSONResponse(
        status_code=404,
        content={
            "success": False,
            "error": "Endpoint not found",
            "path": str(request.url.path),
            "method": request.method,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """500错误处理"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "timestamp": datetime.now().isoformat()
        }
    )

# =====================================================
# 服务器启动
# =====================================================

if __name__ == "__main__":
    print("🚀 TCM Clinic Assistant System API Server Starting")
    print("=" * 50)
    print(f"📍 Server running on: http://{HOST}:{PORT}")
    print(f"🌍 Environment: {os.getenv('ENVIRONMENT', 'development')}")
    print(f"📊 Health check: http://{HOST}:{PORT}/health")
    print(f"📈 Statistics: http://{HOST}:{PORT}/api/stats")
    print(f"🗂️  Mappings: http://{HOST}:{PORT}/api/mappings?type=all")
    print(f"📚 API Docs: http://{HOST}:{PORT}/docs")
    print("=" * 50)
    print("✅ Server is ready to accept connections")

    uvicorn.run(
        "main:app",
        host=HOST,
        port=PORT,
        reload=True if os.getenv("ENVIRONMENT") == "development" else False,
        log_level="info"
    )
