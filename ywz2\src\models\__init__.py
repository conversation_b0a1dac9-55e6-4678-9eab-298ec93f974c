"""
数据模型模块

包含所有数据模型的定义，用于智能体系统的数据交换和存储。
"""

from .patient import PatientInfo, PatientUpdateRequest
from .consultation import ConsultationSession, ConsultationMessage, SessionCreateRequest
from .medical_record import MedicalRecord, MedicalEntity
from .symptom import SymptomEntity

__all__ = [
    "PatientInfo",
    "PatientUpdateRequest", 
    "ConsultationSession",
    "ConsultationMessage",
    "SessionCreateRequest",
    "MedicalRecord",
    "MedicalEntity",
    "SymptomEntity"
] 