"""
问诊会话数据模型

定义问诊会话、消息等相关的数据结构。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class ConsultationSession(BaseModel):
    """问诊会话模型"""
    
    session_id: str = Field(description="会话唯一标识符")
    patient_id: str = Field(description="患者ID")
    status: str = Field(description="会话状态")
    current_agent: str = Field(description="当前智能体")
    progress: float = Field(description="进度", ge=0.0, le=1.0)
    collected_data: Dict[str, Any] = Field(description="已收集数据")
    conversation_history: List[Dict[str, str]] = Field(description="对话历史")
    
    # 系统信息
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "S20241201001",
                "patient_id": "P20241201001",
                "status": "进行中",
                "current_agent": "主诉采集智能体",
                "progress": 0.3,
                "collected_data": {
                    "chief_complaint": "头痛"
                },
                "conversation_history": [
                    {
                        "role": "patient",
                        "content": "我头痛",
                        "timestamp": "2024-12-01T10:00:00"
                    }
                ],
                "created_at": "2024-12-01T10:00:00",
                "updated_at": "2024-12-01T10:00:00"
            }
        }


class ConsultationMessage(BaseModel):
    """问诊消息模型"""
    
    message_id: str = Field(description="消息唯一标识符")
    session_id: str = Field(description="会话ID")
    role: str = Field(description="角色")
    content: str = Field(description="内容")
    message_type: str = Field(description="消息类型")
    timestamp: datetime = Field(description="时间戳")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message_id": "MSG001",
                "session_id": "S20241201001",
                "role": "patient",
                "content": "我头痛",
                "message_type": "text",
                "timestamp": "2024-12-01T10:00:00",
                "metadata": {}
            }
        }


class SessionCreateRequest(BaseModel):
    """创建会话请求模型"""
    
    patient_id: str = Field(description="患者ID")
    name: str = Field(description="姓名")
    gender: str = Field(description="性别", pattern="^(男|女)$")
    age: int = Field(ge=0, le=150, description="年龄")
    phone: str = Field(description="联系电话", pattern="^1[3-9]\d{9}$")
    id_number: str = Field(description="身份证号", pattern="^\d{17}[\dXx]$")
    address: str = Field(description="家庭住址")
    contact_person: str = Field(description="紧急联系人")
    contact_phone: str = Field(description="紧急联系人电话", pattern="^1[3-9]\d{9}$")
    
    class Config:
        json_schema_extra = {
            "example": {
                "patient_id": "P20241201001",
                "name": "张三",
                "gender": "男",
                "age": 35,
                "phone": "13800000000",
                "id_number": "11010519491231002X",
                "address": "北京市东城区王府井大街",
                "contact_person": "李四",
                "contact_phone": "13800000000"
            }
        }


class MessageSendRequest(BaseModel):
    """发送消息请求模型"""
    
    message: str = Field(description="消息内容")
    message_type: str = Field(default="text", description="消息类型")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "我头痛",
                "message_type": "text",
                "metadata": {}
            }
        }


class SessionStatus(BaseModel):
    """会话状态模型"""
    
    session_id: str = Field(description="会话ID")
    status: str = Field(description="状态")
    current_agent: str = Field(description="当前智能体")
    progress: float = Field(description="进度")
    total_messages: int = Field(description="总消息数")
    last_message_time: Optional[datetime] = Field(default=None, description="最后消息时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "S20241201001",
                "status": "进行中",
                "current_agent": "主诉采集智能体",
                "progress": 0.3,
                "total_messages": 5,
                "last_message_time": "2024-12-01T10:00:00"
            }
        } 