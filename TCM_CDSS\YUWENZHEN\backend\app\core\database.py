"""
数据库管理模块

提供数据库连接、会话管理和基础操作功能。
支持PostgreSQL和SQLite，自动根据配置选择。
"""

import logging
from typing import AsyncGenerator, Optional
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# 创建基础模型类
Base = declarative_base()
metadata = MetaData()

# 数据库引擎
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None


def get_database_url() -> str:
    """获取数据库连接URL"""
    db_url = settings.database_url
    
    # 如果是SQLite，确保目录存在
    if db_url.startswith("sqlite"):
        db_path = db_url.replace("sqlite:///", "")
        if not db_path.startswith("/"):  # 相对路径
            db_path = settings.project_root / db_path
            db_path.parent.mkdir(parents=True, exist_ok=True)
            db_url = f"sqlite:///{db_path}"
    
    return db_url


def get_async_database_url() -> str:
    """获取异步数据库连接URL"""
    db_url = get_database_url()
    
    # 转换为异步URL
    if db_url.startswith("sqlite"):
        return db_url.replace("sqlite://", "sqlite+aiosqlite://")
    elif db_url.startswith("postgresql"):
        return db_url.replace("postgresql://", "postgresql+asyncpg://")
    else:
        return db_url


def create_database_engine():
    """创建数据库引擎"""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    db_url = get_database_url()
    async_db_url = get_async_database_url()
    
    # 同步引擎配置
    engine_kwargs = {
        "echo": settings.database_echo,
    }
    
    # SQLite特殊配置
    if db_url.startswith("sqlite"):
        engine_kwargs.update({
            "poolclass": StaticPool,
            "connect_args": {
                "check_same_thread": False,
                "timeout": 20,
            }
        })
    
    # 创建同步引擎
    engine = create_engine(db_url, **engine_kwargs)
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    )
    
    # 异步引擎配置
    async_engine_kwargs = {
        "echo": settings.database_echo,
    }
    
    # SQLite异步特殊配置
    if async_db_url.startswith("sqlite"):
        async_engine_kwargs.update({
            "poolclass": StaticPool,
            "connect_args": {
                "check_same_thread": False,
            }
        })
    
    # 创建异步引擎
    async_engine = create_async_engine(async_db_url, **async_engine_kwargs)
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    logger.info(f"数据库引擎已创建: {db_url}")


def get_db() -> Session:
    """获取同步数据库会话"""
    if SessionLocal is None:
        create_database_engine()
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    if AsyncSessionLocal is None:
        create_database_engine()
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def create_tables():
    """创建数据库表"""
    if async_engine is None:
        create_database_engine()
    
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("数据库表已创建")


async def drop_tables():
    """删除数据库表"""
    if async_engine is None:
        create_database_engine()
    
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    logger.info("数据库表已删除")


async def check_database_connection() -> bool:
    """检查数据库连接"""
    try:
        if async_engine is None:
            create_database_engine()
        
        async with async_engine.begin() as conn:
            await conn.execute("SELECT 1")
        
        logger.info("数据库连接正常")
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.async_engine = None
        self.session_local = None
        self.async_session_local = None
    
    async def initialize(self):
        """初始化数据库"""
        create_database_engine()
        await create_tables()
        logger.info("数据库管理器已初始化")
    
    async def close(self):
        """关闭数据库连接"""
        if self.async_engine:
            await self.async_engine.dispose()
        if self.engine:
            self.engine.dispose()
        logger.info("数据库连接已关闭")
    
    async def health_check(self) -> dict:
        """数据库健康检查"""
        try:
            is_connected = await check_database_connection()
            return {
                "database": "healthy" if is_connected else "unhealthy",
                "url": get_database_url().split("@")[-1] if "@" in get_database_url() else get_database_url(),
                "engine": "async" if async_engine else "sync"
            }
        except Exception as e:
            return {
                "database": "unhealthy",
                "error": str(e)
            }


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 便捷函数
async def init_database():
    """初始化数据库"""
    await db_manager.initialize()


async def close_database():
    """关闭数据库"""
    await db_manager.close()


async def get_database_health() -> dict:
    """获取数据库健康状态"""
    return await db_manager.health_check()


# 数据库操作基类
class BaseRepository:
    """数据库操作基类"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(self, obj):
        """创建对象"""
        self.session.add(obj)
        await self.session.commit()
        await self.session.refresh(obj)
        return obj
    
    async def get(self, model, obj_id):
        """根据ID获取对象"""
        return await self.session.get(model, obj_id)
    
    async def update(self, obj):
        """更新对象"""
        await self.session.commit()
        await self.session.refresh(obj)
        return obj
    
    async def delete(self, obj):
        """删除对象"""
        await self.session.delete(obj)
        await self.session.commit()
    
    async def commit(self):
        """提交事务"""
        await self.session.commit()
    
    async def rollback(self):
        """回滚事务"""
        await self.session.rollback()


# 事务装饰器
def transactional(func):
    """事务装饰器"""
    async def wrapper(*args, **kwargs):
        async with AsyncSessionLocal() as session:
            try:
                result = await func(session, *args, **kwargs)
                await session.commit()
                return result
            except Exception as e:
                await session.rollback()
                raise e
    return wrapper
