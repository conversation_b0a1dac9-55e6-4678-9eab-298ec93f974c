#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库表结构和插入基础数据
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from database.connection import test_connection, execute_query, db_manager
from utils.encryption import EncryptionUtils

# SQL文件路径
SCHEMA_DIR = Path(__file__).parent.parent / "database" / "schema"
DATASETS_DIR = Path(__file__).parent.parent.parent / "datasets"

def read_sql_file(file_path: Path) -> str:
    """
    读取SQL文件内容

    Args:
        file_path: SQL文件路径

    Returns:
        SQL内容
    """
    try:
        return file_path.read_text(encoding='utf-8')
    except Exception as error:
        print(f"❌ Error reading SQL file {file_path}: {error}")
        return None

async def execute_sql_file(file_path: Path, description: str):
    """
    执行SQL文件

    Args:
        file_path: SQL文件路径
        description: 描述
    """
    try:
        print(f"📝 {description}...")

        sql_content = read_sql_file(file_path)
        if not sql_content:
            raise Exception(f"Failed to read SQL file: {file_path}")

        # 分割SQL语句（以分号分隔）
        statements = [
            stmt.strip()
            for stmt in sql_content.split(';')
            if stmt.strip() and not stmt.strip().startswith('--')
        ]

        conn = await db_manager.get_connection()
        for statement in statements:
            if statement.strip():
                await conn.execute(statement)
        await conn.commit()

        print(f"✅ {description} completed successfully")
    except Exception as error:
        print(f"❌ Error in {description}: {error}")
        raise error

def check_database_file():
    """检查数据库文件是否存在"""
    db_path = DATASETS_DIR / 'tcm_clinic_system.db'

    if not db_path.exists():
        print('📁 Creating database file...')
        # 确保目录存在
        DATASETS_DIR.mkdir(parents=True, exist_ok=True)
        # 创建空的数据库文件
        db_path.touch()

    print(f'📍 Database file: {db_path}')

async def table_exists(table_name: str) -> bool:
    """
    检查表是否存在

    Args:
        table_name: 表名

    Returns:
        是否存在
    """
    try:
        result = await execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
            (table_name,)
        )
        return len(result) > 0
    except Exception:
        return False

async def create_sample_staff():
    """创建示例员工数据"""
    try:
        print('👥 Creating sample staff data...')

        # 检查是否已有员工数据
        existing_staff = await execute_query('SELECT COUNT(*) as count FROM staff')
        if existing_staff[0]['count'] > 0:
            print('✅ Staff data already exists, skipping...')
            return

        # 获取映射ID
        admin_role = await execute_query("SELECT id FROM role_mappings WHERE code = 'ADMIN'")
        doctor_role = await execute_query("SELECT id FROM role_mappings WHERE code = 'DOCTOR'")
        receptionist_role = await execute_query("SELECT id FROM role_mappings WHERE code = 'RECEPTIONIST'")

        admin_dept = await execute_query("SELECT id FROM department_mappings WHERE code = 'ADMINISTRATION'")
        internal_dept = await execute_query("SELECT id FROM department_mappings WHERE code = 'TCM_INTERNAL'")

        # 插入示例员工
        encryption_utils = EncryptionUtils()

        sample_staff = [
            {
                'employee_number': 'admin',
                'full_name_encrypted': encryption_utils.encrypt('系统管理员'),
                'phone_hash': encryption_utils.hash_phone('admin'),
                'password_hash': encryption_utils.hash_password('admin'),
                'role_mapping_id': admin_role[0]['id'],
                'department_mapping_id': admin_dept[0]['id'],
                'title': '系统管理员'
            },
            {
                'employee_number': 'ADM001',
                'full_name_encrypted': encryption_utils.encrypt('系统管理员'),
                'phone_hash': encryption_utils.hash_phone('13000000000'),
                'password_hash': encryption_utils.hash_password('password'),
                'role_mapping_id': admin_role[0]['id'],
                'department_mapping_id': admin_dept[0]['id'],
                'title': '系统管理员'
            },
            {
                'employee_number': 'DOC001',
                'full_name_encrypted': encryption_utils.encrypt('李时珍'),
                'phone_hash': encryption_utils.hash_phone('13800138000'),
                'password_hash': encryption_utils.hash_password('password'),
                'role_mapping_id': doctor_role[0]['id'],
                'department_mapping_id': internal_dept[0]['id'],
                'title': '主任医师'
            },
            {
                'employee_number': 'REC001',
                'full_name_encrypted': encryption_utils.encrypt('前台小王'),
                'phone_hash': encryption_utils.hash_phone('13600136000'),
                'password_hash': encryption_utils.hash_password('password'),
                'role_mapping_id': receptionist_role[0]['id'],
                'department_mapping_id': admin_dept[0]['id'],
                'title': '前台接待'
            }
        ]

        conn = await db_manager.get_connection()
        for staff in sample_staff:
            await conn.execute(
                """INSERT INTO staff (employee_number, full_name_encrypted, phone_hash, password_hash,
                   role_mapping_id, department_mapping_id, title, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))""",
                (
                    staff['employee_number'],
                    staff['full_name_encrypted'],
                    staff['phone_hash'],
                    staff['password_hash'],
                    staff['role_mapping_id'],
                    staff['department_mapping_id'],
                    staff['title']
                )
            )
        await conn.commit()

        print('✅ Sample staff data created successfully')
    except Exception as error:
        print(f'❌ Error creating sample staff: {error}')
        raise error

async def initialize_database():
    """主初始化函数"""
    try:
        print('🚀 Starting database initialization...\n')

        # 检查数据库文件
        check_database_file()

        # 测试数据库连接
        print('🔌 Testing database connection...')
        connected = await test_connection()
        if not connected:
            raise Exception('Database connection failed')
        print('✅ Database connection established\n')

        # 检查是否需要初始化
        mapping_tables_exist = await table_exists('gender_mappings')
        main_tables_exist = await table_exists('staff')

        if not mapping_tables_exist or not main_tables_exist:
            # 使用简化的初始化SQL
            init_sql_path = Path(__file__).parent.parent / "database" / "init_simple.sql"
            if init_sql_path.exists():
                await execute_sql_file(init_sql_path, 'Creating database structure')
            else:
                print('⚠️ Init SQL file not found, database may need manual setup')

            # 创建示例员工数据
            if await table_exists('staff'):
                await create_sample_staff()
        else:
            print('✅ Database already initialized, skipping...')

        # 显示完成信息
        print('\n🎉 Database initialization completed successfully!')
        print('\n📋 Default Login Credentials:')
        print('   Admin: 13000000000 / password')
        print('   Doctor: 13800138000 / password')
        print('   Receptionist: 13600136000 / password')
        print('\n⚠️  Please change default passwords in production!')

    except Exception as error:
        print(f'\n❌ Database initialization failed: {error}')
        sys.exit(1)
    finally:
        await db_manager.close_connection()

if __name__ == "__main__":
    asyncio.run(initialize_database())
