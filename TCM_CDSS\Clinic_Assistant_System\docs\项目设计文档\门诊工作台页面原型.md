zheng# 门诊工作台页面原型
1.0 总体设计哲学与视觉氛围
- 核心定位：一个以"电子病历工单"为绝对核心的、高效智能的单页工作站（Single Page Application）。
- 设计目标：最大化医生的诊疗效率，减少界面跳转带来的心智负担，通过智能辅助降低差错率，并提供全面的决策支持信息。
- 视觉风格：界面采用专业、冷静的现代设计风格。以大面积的灰白色为基调，辅以代表健康与科技的蓝绿色作为高亮和交互色。通过清晰的卡片式布局、功能分区和充足的留白，确保在高信息密度下，界面依然保持清晰、有序，不产生压迫感。
2.0 页面架构：三栏式布局
整个页面采用经典的三栏式布局，将屏幕功能区明确划分为三个垂直部分，每个部分承担不同的职责。
- 左侧栏：流程控制与高频工具入口。屏幕宽度占比：18%。
- 中间栏：核心工作区，即电子病历的完整画布。屏幕宽度占比：60%。
- 右侧栏：参考信息与智能决策支持面板。屏幕宽度占比：18%。
---
3.0 左侧栏：流程控制与工具面板
此栏固定在屏幕左侧，是医生管理日常工作流的主控台。
- 3.1 门诊单搜索框位置：左侧栏最顶部。描述：一个简洁的搜索输入框，内有放大镜图标和"搜索患者姓名或门诊号..."的提示文字。用于快速从数据库中调取任何患者的病历。
- 3.2 接诊按钮位置：紧邻搜索框右侧。描述：一个圆形的、以品牌主色（蓝绿色）填充的、包含"+ 接诊"文字与图标的按钮。用于为未预约的临时患者快速创建一张新的、空白的电子病历工单。
- 3.3 患者列表预览区位置：搜索框下方，占据左侧栏大部分空间。描述：一个可垂直滚动的列表，实时展示"今日门诊队列"的全体患者。
    - 列表标题：区域顶部有"今日队列 (25)"的标题，实时显示队列总人数。
    - 列表项结构：每个列表项代表一位患者，包含以下信息：
        - 核心信息：患者姓名、接诊医生、预约时间。
        - 预约来源标签：显示患者预约渠道的标签。
            - 线上 (蓝色): 通过网络平台、APP、微信小程序等渠道预约。
            - 线下 (绿色): 通过电话、现场挂号等传统方式预约。
        - 就诊状态标签：一个颜色鲜明的胶囊状标签，用于即时识别患者状态。
            - 待诊 (蓝色): 患者已报到，正在等候。
            - 诊中 (绿色，带有微光动画): 当前正在处理的患者，该列表项有高亮边框。
            - 回诊 (橙色): 患者检查后返回，需再次关注。
            - 已诊 (灰色): 诊疗流程已结束。
            - 未收费 (紫色): 诊疗已结束但患者尚未缴费。
        - 交互：点击任何"待诊"或"回诊"的患者，中间的核心病历区会立即加载并完整显示该患者的病历工单。
- 3.4 工具区位置：患者列表预览区下方。描述：一组设计简约的图标按钮，提供高频功能的一键直达入口。
    - 叫号 (喇叭图标): 联动诊所的物理叫号系统。
    - 门诊看板 (数据图表图标): 弹窗打开诊所实时运营数据看板。
    - 预约看板 (日历图标): 弹窗打开可视化的医生个人预约日历。
    - 用药助手 (药典书本图标): 弹窗打开独立的药品信息查询工具。
---
4.0 中间栏：电子病历工单画布
这是页面的核心，占据屏幕最主要的空间。它是一个可平滑滚动的、自上而下的结构化文档。
- 4.1 顶部右侧操作栏位置：在病历内容区的右上方，以悬浮或固定形式存在。描述：包含四个关键操作按钮。
    - 费用预览: 点击后弹窗显示当前工单的费用明细。
    - 保存病历: 点击后弹窗进行二次确认。弹窗包含内容提醒区、取消和确认按钮。点击确认，结构化病历保存至数据库；点击取消，返回病历编辑状态。
    - 继续接诊: 点击后将当前患者状态置为"回诊"，并自动加载下一位"待诊"患者的病历，同时弹窗提示"已自动叫号下一位"。
    - 打印: 点击后弹出打印预览窗口，可选择打印处方、病历等。
- 4.2 基本信息与就诊属性区位置：病历工单的最顶部。描述：此区域以单行的形式，将患者基本信息与本次就诊属性并列展示，背景色为浅灰色以突出。
    - 基本信息 (只读): "患者姓名"、"性别"、"年龄"、"手机号"。
    - 就诊属性 (可交互):初诊/复诊: 一个下拉菜单。费用类别: 一个下拉菜单，选项为"自费"和"医保"。
- 4.3 病历区描述：病历的核心书写区域。
    - 主诉、现病史、既往史: 均为支持多行输入的富文本编辑框。
    - 体格检查: 一个结构化的录入模块，内嵌中医特色的"望闻问切"四诊信息录入功能。每个子项（望、闻、问、切）的右侧都有一个"一键从灵机获取信息"的AI辅助填充按钮。
    - 诊断: 支持输入"中医诊断"和"西医诊断"的文本框，并可从标准诊断库中选择。
- 4.4 适宜技术区描述：一个动态列表，用于开具非药物的治疗方案。
    - 操作：区域顶部有"+"按钮。
    - 项目卡片：每次点击按钮，下方会新增一个结构化的"适宜技术卡片"，包含技术名称、操作部位、具体穴位选择等字段。
    - 可选性：每个项目卡片前都有一个复选框，允许医生临时启用或禁用某个方案。
- 4.5 处方医嘱区描述：一个专门用于开具各类处方的操作区域。
    - 操作：区域右侧排列着多个按钮："中成药处方"、"输注处方"、"中药处方"、"历史处方"、"处方模板"。点击任一按钮，会弹出对应的、功能完备的弹窗进行编辑和开立。
- 4.6 医嘱事项区描述：一个列表区域，用于清晰地展示所有已开具的医嘱事项（包括处方和非药物治疗）。
- 4.7 审计校验区描述：位于病历工单的倒数第二部分，是一个由AI驱动的自动化合规性检查区域。
    - 功能：当医生在上方添加药品或项目时，此区域会实时分析并以列表形式显示提示信息。
    - 方剂审核 (药瓶图标): 提示配伍禁忌、超剂量等风险。
    - 医保合规性审核 (盾牌图标): 提示医保目录内外、报销限制等信息。
- 4.8 工单信息展示区位置：病历工单的最底部，靠右对齐。描述：以小号字体展示当前工单的元信息："病历ID: [ID号]"、"就诊时间: [今天的年月日]"。
---
5.0 右侧栏：参考信息与智能辅助面板
此栏固定在屏幕右侧，是医生查阅资料和获取决策支持的区域，由四个标签页构成。
- 5.1 "历史"标签页描述：以垂直时间轴的形式，清晰展示该患者的历次就诊记录。点击任一时间节点可展开查看该次就诊的诊断与处方摘要。
- 5.2 "报告"标签页描述：以列表形式，集中展示患者所有的化验单、检查报告。点击可直接在线预览。
- 5.3 "影像"标签页描述：以缩略图库形式，展示患者的X光、CT等影像资料，并提供内置的简易影像查看器。
- 5.4 "AI诊断"标签页描述：一个交互式的AI辅助诊断模块，能实时分析中间区正在编辑的病历。
    - 核心功能：
        - 证型辨析: 根据四诊信息，列出可能的证型、置信度及判断依据。
        - 方剂与治法推荐: 针对推断的证型，推荐经典方剂和治疗原则。
        - 智能问答: 提供一个对话框，医生可直接用自然语言提问，AI结合病历上下文给出建议。

---
# 页面布局修改规范
### 修改原则
1. 文档同步原则：任何页面布局、样式、交互的修改都必须同步更新到本页面描述文档中
2. 详细记录原则：修改需求和修改内容都需要详细记录，包括具体的尺寸、颜色、间距、交互方式等
3. 适用范围标注：明确标注修改内容适用的页面范围
4. 版本记录原则：重要修改需要标注修改时间和版本信息，请用工具获取当前实时时间。
## 修改流程
1. 开发前：先更新门诊工作台页面原型文档，明确修改内容
2. 开发中：按照文档描述进行开发
3. 开发后：验证实现与文档描述的一致性
4. 发布前：最终确认文档与代码的同步性
## 文档更新要求
1. 搜索栏相关修改：必须更新搜索栏的详细描述，包括布局、尺寸、交互方式
2. 新增页面：必须添加完整的页面描述，包括整体氛围、布局元素、交互方式
3. 样式调整：必须更新相关的样式描述，包括颜色、尺寸、间距等
4. 功能变更：必须更新相关的功能描述和交互方式
## 当前版本信息
- 文档版本：v3.4
- 最后更新：2025-06-21
- 主要更新：功能中心页面新增"研学中心"卡片，预留中医研学与学术平台功能入口
- 影响页面：功能中心页面、路由配置
## 修改记录
[此处记录每一次的修改记录]

- **2025-01-21 v3.5**：门诊工作台页面宽度占比修正
  - 修正三栏式布局的宽度占比设置
  - 左侧栏：从20%调整为18%，符合页面原型文档要求
  - 中间栏：保持60%不变，符合页面原型文档要求  
  - 右侧栏：从20%调整为18%，符合页面原型文档要求
  - 确保页面布局与门诊工作台页面原型文档完全一致
  - 优化页面显示效果，解决三栏分布混乱的问题

- **2025-01-21 v3.6**：门诊工作台页面布局结构修正
  - 修正三栏式布局的HTML结构，从垂直分布改为水平分布
  - 添加main-content容器包装三栏式布局
  - 修改CSS选择器，确保三栏水平排列（左中右）
  - 左侧栏：流程控制与工具面板（18%宽度）
  - 中间栏：电子病历工单画布（60%宽度）
  - 右侧栏：参考信息与智能辅助面板（18%宽度）
  - 解决三栏垂直分布问题，实现正确的水平三栏布局

- **2025-01-21 v3.7**：门诊工作台页面界面优化
  - 搜索图标位置优化：调整搜索图标位置到搜索框内靠左（left: 12px）
  - 接诊按钮样式调整：从圆形改为方形（border-radius: 6px），尺寸调整为60x36px，字体减小到12px
  - 患者列表布局优化：从卡片形式改为列表形式，一行展示一个患者信息，采用紧凑布局
  - 工具栏对齐优化：统一工具栏按钮高度为36px，字体大小为12px，确保对齐
  - 左右栏宽度调整：从18%调整为20%，中间栏保持60%
  - 就诊属性控件宽度优化：设置下拉框宽度为120px，确保文字可见
  - 医嘱事项功能增强：添加只读文本展示区，显示医嘱详细内容
  - 处方医嘱弹窗功能：点击处方按钮显示对应的弹窗，包含药品选择、剂量设置等功能说明
  - 审核图标对齐优化：为审核图标添加margin-top和flex-shrink属性，确保居中对齐
  - 整体界面更加紧凑、美观，提升用户体验

- **2025-01-21 v3.8**：门诊工作台页面AI诊断子页面功能优化
  - 将证型和方剂弹窗改为子页面形式，提升用户体验
  - 子页面设计：宽度占屏幕50%，高度占屏幕80%，中心居中显示
  - 遮罩层效果：背景半透明黑色(rgba(0,0,0,0.6))，四周模糊处理(backdrop-filter: blur(8px))
  - 子页面容器：白色背景，圆角12px，阴影效果，固定尺寸50%x80%
  - 子页面头部：包含标题和关闭按钮，灰色背景区分
  - 关闭按钮：圆形设计，悬停缩放效果，右上角位置
  - 证型子页面：显示主要证型、置信度、症状表现、病理分析、治疗原则
  - 方剂子页面：显示主方组成、功效、适应症、加减法、注意事项
  - 内容区域：可滚动，24px内边距，清晰的层级结构
  - 交互优化：点击遮罩层或关闭按钮可关闭子页面
  - 视觉效果：专业、现代的卡片式设计，符合iOS风格
  - 提升医生查看详细信息的体验，避免弹窗遮挡问题

- **2025-01-21 v3.9**：门诊工作台页面右侧栏方剂与治法模块优化
  - 将方剂与治法推荐改为多张治法卡片的形式
  - 治法卡片容器：垂直布局，支持滚动，最大高度300px
  - 每张治法卡片包含：治法名称、治法描述、对应方剂、置信度
  - 治法名称采用醒目设计：16px字体，蓝色高亮，加粗显示
  - 治法数据包含4个示例：疏肝解郁、清热解毒、解表祛湿、健脾益气
  - 每个治法对应详细方剂信息：组成、功效、适应症、加减法、注意事项
  - 点击治法卡片打开子页面，显示对应方剂的详细信息
  - 子页面标题动态显示："{治法名称} - 方剂详情"
  - 子页面内容根据选中的治法动态加载对应的方剂信息
  - 治法卡片悬停效果：背景变蓝、边框高亮、轻微上浮
  - 提升医生选择治法的体验，治法名称更加醒目易识别
  - 支持多个治法的展示和选择，符合中医诊疗的实际需求

- **2025-01-21 v3.10**：门诊工作台页面治法子页面方剂组成表格化
  - 将治法子页面的方剂组成改为表格形式展示
  - 表格结构：行为药材，列为药材属性（药材名、用量、性味、归经、功效）
  - 药材属性包含：药材名称、用量（如9g、6g）、性味（如苦、微寒）、归经（如肝、胆）、功效描述
  - 表格样式：小尺寸表格，带边框，紧凑布局
  - 表格头部：灰色背景，居中对齐，加粗字体
  - 药材名称列：蓝色高亮，加粗显示，突出重要性
  - 功效列：左对齐，增加内边距，便于阅读长文本
  - 更新4个治法的药材数据：逍遥散加减、黄连解毒汤、羌活胜湿汤、四君子汤
  - 每个药材包含完整的属性信息，符合中医理论规范
  - 表格响应式设计，适应不同屏幕尺寸
  - 提升医生查看方剂组成的效率，信息更加清晰直观
  - 符合中医诊疗的专业要求，便于医生理解和应用

- **2025-01-21 v3.11**：门诊工作台页面AI诊断API数据集成
  - 创建AI诊断API模拟数据文件：frontend/src/mock/ai-diagnosis.ts
  - 定义完整的TypeScript接口：SyndromeRecommendation、ScoreElement、MedicinalMaterial、FormulaDose、AIDiagnosisResponse
  - 模拟API响应数据包含：推荐列表、证型评分、辩证分析、方剂剂量信息
  - 集成真实API数据结构到门诊工作台页面
  - 更新证型子页面：显示主要证型、证型评分、推荐证型列表、辩证分析
  - 更新治法子页面：显示治法名称、对应证型、方剂描述、药材组成表格
  - 药材组成表格展示：药材名称、用量（含单位）、药材ID
  - 证型评分列表：显示证型元素和对应评分，采用卡片式布局
  - 推荐证型列表：显示证型名称、对应方剂、治法描述，支持悬停效果
  - 辩证分析内容：从API数据动态获取，支持实时更新
  - 加载状态管理：添加isLoadingAI状态，优化用户体验
  - 错误处理：API调用失败时显示错误提示
  - 样式优化：新增证型评分和推荐列表的专用样式
  - 提升AI诊断功能的专业性和实用性，符合中医诊疗实际需求

- **2025-01-21 v3.12**：门诊工作台页面证型展示形式优化
  - 取消证型置信度显示，简化证型卡片信息展示
  - 将证型评分改为证素得分，更符合中医理论术语
  - 证型子页面证型列表改为多卡片形式展示，类似治法容器布局
  - 证型卡片包含：证型名称、对应方剂、治法描述
  - 证型卡片支持悬停效果：背景变蓝、边框高亮、轻微上浮
  - 证素标签样式优化：蓝色背景、圆角设计、紧凑布局
  - 方剂与治法容器显示对应证型信息，增强关联性
  - 治法卡片显示"对应证型"字段，明确证型与治法的关系
  - 证型卡片容器：垂直布局，支持滚动，最大高度300px
  - 新增证型卡片专用样式：syndrome-methods-container、syndrome-method-card
  - 证素标签样式：element-tag，蓝色主题，标签式设计
  - 优化证型与治法的关联展示，提升医生理解证型-治法-方剂关系
  - 界面更加清晰直观，符合中医诊疗思维逻辑

- **2025-01-21 v3.13**：门诊工作台页面容器高度和滚动功能优化
  - 治法容器高度优化：移除最大高度限制，容器动态适应所有卡片高度
  - 证型容器高度优化：移除最大高度限制，容器动态适应所有卡片高度
  - 治法卡片容器：overflow-y改为visible，支持内容完全展示
  - 证型卡片容器：overflow-y改为visible，支持内容完全展示
  - AI辅助页面滚动优化：为.ai-diagnosis添加overflow-y: auto
  - 右侧栏标签页内容区域：保持原有滚动设置，确保内容可滚动
  - 容器高度自适应：根据卡片数量动态调整容器高度
  - 滚动功能完善：AI辅助页面支持上下滚动，内容超出时可滚动查看
  - 用户体验提升：避免内容被截断，确保所有信息完整显示
  - 布局优化：容器高度与内容高度匹配，避免空白区域
  - 响应式设计：适应不同数量的证型和治法卡片
  - 界面完整性：确保所有AI诊断信息都能完整展示和访问

- **2025-01-21 v3.14**：门诊工作台页面AI辅助结构重构
  - AI辅助页面结构重新设计：将证型子页面内容整合到AI辅助页面中
  - 页面结构优化：从上到下依次为辩证分析、证素得分、证型容器列表
  - 证型分组展示：按证型名称分组，每个证型容器包含该证型对应的多个治法卡片
  - 证型组容器：每个证型组有独立的标题和治法容器
  - 治法卡片展示：每个治法卡片显示治法名称、证型、方剂、对应证型信息
  - 证素得分卡片：独立展示证素得分信息，采用列表形式
  - 辩证分析卡片：保持独立展示，内容从API动态获取
  - 移除证型子页面：不再需要单独的证型子页面，内容已整合
  - 新增计算属性：getSyndromeGroups按证型分组治法推荐
  - 证型组样式：syndrome-group-container、syndrome-group-title专用样式
  - 页面结构简化：减少页面跳转，提升用户体验
  - 信息层次清晰：证型→治法的逻辑关系更加明确
  - 界面统一性：所有AI诊断信息集中在一个页面展示

- **2025-01-21 v3.15**：门诊工作台页面AI辅助数据加载修复
  - 修复AI辅助页面数据加载问题：添加自动数据加载机制
  - 标签页切换监听：监听activeTab变化，切换到AI辅助时自动加载数据
  - 页面初始化加载：页面加载时如果当前是AI辅助标签页则自动加载数据
  - 加载状态显示：添加isLoadingAI状态，显示加载中的提示信息
  - 条件渲染优化：根据加载状态和数据状态显示不同内容
  - 加载状态UI：显示加载动画和"正在加载AI诊断数据..."提示
  - 无数据状态UI：显示空状态组件和"暂无AI诊断数据"提示
  - 数据加载逻辑：确保AI诊断数据在需要时自动加载
  - 用户体验优化：避免空白页面，提供清晰的加载反馈
  - 错误处理完善：数据加载失败时显示错误提示
  - 响应式设计：加载状态适配不同屏幕尺寸
  - 界面友好性：提供清晰的状态反馈，提升用户体验

- **2025-01-21 v3.16**：门诊工作台页面治法卡片和证型组容器优化
  - 治法卡片简化：只保留治法名称和对应方剂，移除冗余信息
  - 治法卡片内容：显示治法名称（蓝色高亮）和方剂名称（绿色显示）
  - 证型组容器卡片化：将证型组容器改为卡片形式展示
  - 证型组卡片结构：包含头部（证型名称）和内容区（治法卡片列表）
  - 证型组卡片样式：白色背景、圆角边框、悬停阴影效果
  - 证型组头部设计：灰色背景、蓝色标题、底部边框分隔
  - 证型组内容区：内边距16px，包含治法卡片容器
  - 卡片悬停效果：轻微上浮、阴影增强，提升交互体验
  - 信息层次优化：证型→治法的逻辑关系更加清晰
  - 界面简洁性：移除冗余信息，突出核心内容
  - 视觉统一性：证型组卡片与治法卡片风格保持一致
  - 用户体验提升：信息展示更加简洁明了，操作更加直观

- **2025-01-21 v3.17**：门诊工作台页面证型颜色优化
  - 证型颜色调整：将证型组标题颜色从蓝色(#1890ff)改为绿色(#52c41a)
  - 颜色统一性：证型标题与方剂名称使用相同的绿色，增强视觉关联
  - 信息层次优化：通过颜色区分不同层级信息，证型绿色、治法蓝色
  - 视觉平衡：绿色证型标题与蓝色治法名称形成良好的色彩对比
  - 界面协调性：证型绿色与方剂绿色保持一致，治法蓝色保持独立
  - 用户体验提升：颜色搭配更加和谐，信息识别更加直观

- **2025-01-21 v3.18**：门诊工作台页面治法卡片布局优化
  - 治法卡片布局调整：将治法和方剂名称改为左右分布布局
  - 布局结构优化：新增method-info-row容器，使用flex布局实现左右分布
  - 治法名称位置：左侧显示，蓝色高亮，flex: 1占据左半部分空间
  - 方剂名称位置：右侧显示，绿色显示，text-align: right右对齐
  - 空间分配：治法和方剂各占50%宽度，实现均匀分布
  - 对齐方式：垂直居中对齐，确保视觉效果平衡
  - 信息层次清晰：治法蓝色、方剂绿色，形成良好的视觉对比
  - 界面简洁性：一行显示两个关键信息，节省垂直空间
  - 用户体验提升：信息展示更加紧凑，一目了然

- **2025-01-21 v3.19**：门诊工作台页面界面全面优化
  - 颜色搭配优化：调整证型、治法、方剂名称颜色，使用更协调的色调
    - 证型标题：深灰色(#2c3e50)，专业稳重
    - 治法名称：深蓝灰色(#34495e)，清晰易读
    - 方剂名称：浅灰色(#7f8c8d)，柔和协调
  - 搜索框区域重构：将搜索图标改为方形"叫号"按钮
    - 叫号按钮：60x36px方形设计，灰色背景，简洁实用
    - 按钮位置：搜索框左侧，便于快速叫号操作
    - 移除工具区原有叫号按钮，避免功能重复
  - 工具区功能升级：将叫号工具改为医案助手
    - 医案助手：提供中医医案查询和管理功能
    - 所有工具点击后打开子页面，统一交互体验
    - 子页面样式与治法方剂子页面保持一致
  - 子页面系统完善：
    - 医案助手、门诊看板、预约看板、用药助手四个工具子页面
    - 统一显示"功能开发中"提示，预留功能扩展空间
    - 子页面尺寸：50%宽度x80%高度，居中显示
    - 遮罩层效果：半透明黑色背景，模糊处理
  - 交互体验优化：
    - 工具按钮悬停效果，提升操作反馈
    - 子页面关闭按钮，支持点击遮罩层关闭
    - 统一的视觉风格和交互模式
  - 界面协调性：颜色搭配更加专业，避免过于花哨
  - 功能完整性：为未来功能扩展预留完整框架

- **2025-01-21 v3.20**：门诊工作台页面布局和数据处理优化
  - 右侧栏标签页居中对齐：
    - 标签页头部居中对齐，增加底部间距16px
    - 标签导航容器使用flex布局，justify-content: center
    - 标签导航宽度100%，确保居中效果
    - 标签项文本居中，flex: 1均匀分布，最大宽度60px
    - 提升标签页视觉效果，更加平衡美观
  - 患者队列数据优化：
    - 集成mock患者数据库，从patients.ts获取真实患者数据
    - 筛选今天预约的患者（2025-01-21），确保数据准确性
    - 新增25个预约今天的患者，丰富队列显示
    - 患者数据包含：姓名、性别、年龄、诊断、就诊次数等完整信息
    - 自动生成预约时间：从8:00开始，30分钟间隔
    - 智能分配患者状态：第一个患者"诊中"，其他患者"待诊"
    - 数据格式转换：将mock数据转换为门诊工作台所需格式
  - 数据处理逻辑优化：
    - 新增loadTodayPatients方法，动态加载今日患者队列
    - 新增getAppointmentTime方法，根据索引生成预约时间
    - 新增getPatientStatus方法，智能分配患者状态
    - 页面初始化时自动加载患者队列数据
    - 确保患者队列与就诊时间（2025-01-21）保持一致
  - 用户体验提升：
    - 患者队列显示更多真实数据，提升界面丰富度
    - 标签页布局更加平衡，视觉效果更佳
    - 数据来源更加可靠，便于后续功能扩展

- **2025-01-21 v3.21**：门诊工作台页面标签页字体显示优化
  - AI辅助标签页字体显示修复：
    - 标签项最大宽度从60px调整为80px，确保文字完整显示
    - 新增最小宽度60px，保持标签页基本尺寸
    - 增加内边距8px，提升文字可读性
    - 字体大小调整为13px，优化显示效果
    - 解决"AI 辅助"等较长文字被截断的问题
  - 标签页布局优化：
    - 保持flex布局和居中对齐效果
    - 确保所有标签页文字都能完整显示
    - 提升标签页的可用性和美观度
  - 用户体验提升：
    - 文字显示更加清晰完整
    - 标签页操作更加便捷
    - 界面视觉效果更加协调

- **2025-01-21 v3.22**：门诊工作台页面患者队列布局优化
  - 患者队列标题固定功能：
    - 重新设计患者队列容器结构，分离标题和列表区域
    - 队列标题（今日队列）固定在顶部，不随滚动移动
    - 患者列表区域独立滚动，提升用户体验
    - 标题区域设置flex-shrink: 0，确保不被压缩
    - 列表容器设置flex: 1，占据剩余空间
  - 布局结构优化：
    - 新增patient-queue-container作为主容器
    - 新增queue-list-container作为滚动容器
    - 队列标题设置z-index: 10，确保层级正确
    - 背景色设置为白色，与整体风格保持一致
  - 滚动体验优化：
    - 只有患者列表支持垂直滚动
    - 队列标题始终可见，便于用户了解当前队列状态
    - 滚动区域设置overflow-x: hidden，防止水平滚动
    - 提升用户浏览患者队列的便利性
  - 界面稳定性：
    - 队列标题固定，避免滚动时信息丢失
    - 患者列表滚动流畅，支持大量患者数据
    - 布局结构更加稳定，适应不同屏幕尺寸

- **2025-01-21 v3.23**：门诊工作台页面中间栏固定头部功能
  - 中间栏布局重构：
    - 重新设计病历编辑区容器结构，分离固定区域和滚动区域
    - 固定区域包含：顶部操作栏和基本信息与就诊属性区
    - 滚动区域包含：病历区、适宜技术区、处方医嘱区等所有编辑内容
    - 使用flex布局实现固定头部和可滚动内容的分离
  - 固定头部功能实现：
    - 固定区域设置position: sticky，top: 0，实现粘性定位
    - 设置z-index: 10，确保固定头部在滚动内容之上
    - 添加box-shadow阴影效果，增强视觉层次感
    - 背景色设置为白色，与整体风格保持一致
  - 滚动区域优化：
    - 滚动区域设置flex: 1，占据剩余空间
    - 设置overflow-y: auto，支持垂直滚动
    - 保持原有内边距20px，确保内容布局不变
    - 滚动时固定头部始终可见，便于快速操作
  - 用户体验提升：
    - 顶部操作栏（费用预览、保存病历等）始终可访问
    - 患者基本信息始终可见，避免滚动时信息丢失
    - 就诊属性（初诊/复诊、费用类别）始终可操作
    - 提升医生编辑病历的效率和便利性
  - 界面稳定性：
    - 固定头部不随滚动移动，保持界面稳定性
    - 支持大量病历内容而不影响操作便利性
    - 布局结构更加合理，符合用户操作习惯

- **2025-01-21 v3.24**：门诊工作台页面AI辅助功能样式修复
  - AI辅助卡片样式恢复：
    - 重新添加ai-diagnosis容器样式，确保AI辅助页面正常显示
    - 恢复ai-card卡片样式，包括背景色、边框、圆角、悬停效果
    - 修复ai-card-title和ai-card-content样式，确保标题和内容正常显示
    - 恢复analysis-text样式，确保辩证分析文本正常显示
  - 治法卡片样式修复：
    - 重新添加treatment-methods-container容器样式
    - 恢复treatment-method-card卡片样式，包括背景色、边框、悬停效果
    - 修复method-info-row布局样式，确保治法和方剂左右分布
    - 恢复method-name和method-prescription样式，确保文字颜色和大小正确
  - 治法子页面样式恢复：
    - 重新添加sub-page-overlay遮罩层样式，确保子页面正常显示
    - 恢复sub-page-container容器样式，包括尺寸、背景色、阴影效果
    - 修复sub-page-header头部样式，包括标题和关闭按钮
    - 恢复sub-page-content内容区域样式，确保内容可滚动
    - 重新添加prescription-detail样式，确保方剂详情正常显示
    - 恢复composition-table表格样式，确保药材组成表格正常显示
  - 功能完整性恢复：
    - AI辅助页面的辩证分析、证素得分、证型容器正常显示
    - 治法卡片的悬停效果和点击交互正常
    - 治法子页面的打开、关闭、内容展示功能正常
    - 方剂详情页面的药材组成表格正常显示
  - 用户体验保障：
    - 确保AI辅助功能的所有交互正常
    - 保持治法子页面的专业视觉效果
    - 维护整体界面的功能完整性

- **2025-01-21 v3.25**：门诊工作台页面左侧栏工具区布局优化
  - 工具区滚动功能取消：
    - 移除tools-section的overflow-y: auto滚动设置
    - 移除height: 50%和max-height: 200px高度限制
    - 设置flex-shrink: 0，确保工具区不被压缩
    - 取消margin-top: auto，避免工具区自动下沉
  - 工具按钮双栏布局：
    - 新增tools-grid容器，使用grid布局实现双栏展示
    - 设置grid-template-columns: 1fr 1fr，确保两列等宽
    - 工具按钮宽度设置为100%，适应网格单元格
    - 间距设置为8px，保持按钮间的适当距离
  - 布局结构优化：
    - 工具按钮从垂直排列改为2x2网格排列
    - 医案助手和门诊看板在第一行
    - 预约看板和用药助手在第二行
    - 按钮高度保持36px，确保视觉一致性
  - 空间利用优化：
    - 工具区高度自适应，不再占用固定空间
    - 患者队列区域获得更多显示空间
    - 整体布局更加紧凑高效
  - 用户体验提升：
    - 所有工具按钮一目了然，无需滚动查看
    - 双栏布局节省垂直空间，提升信息密度
    - 工具区布局更加稳定，不受内容影响

- **2025-01-21 v3.25**：门诊工作台页面预约来源功能
  - 预约来源标签功能：
    - 为今日预约患者列表添加预约来源显示
    - 支持线上预约（蓝色标签）和线下预约（绿色标签）
    - 预约来源标签与状态标签并排显示，提升信息密度
    - 标签样式统一，视觉效果协调
  - 数据结构扩展：
    - Patient接口新增appointmentSource字段
    - 支持'online'（线上）和'offline'（线下）两种预约来源
    - Mock数据中为25个今日预约患者添加预约来源信息
    - 预约来源分布合理，约60%线上，40%线下
  - 界面布局优化：
    - 患者列表项改为双行布局：第一行显示基本信息，第二行显示标签
    - 新增patient-tags-row容器，统一管理状态和来源标签
    - 标签间距优化，确保信息清晰可读
    - 响应式设计，适配不同屏幕尺寸
  - 功能函数完善：
    - 新增getAppointmentSourceText函数，获取预约来源显示文本
    - 新增getAppointmentSourceType函数，获取预约来源标签样式
    - 更新loadTodayPatients函数，使用mock数据中的预约来源
    - 保持向后兼容，默认预约来源为线下
  - 用户体验提升：
    - 医生可快速识别患者预约渠道，便于统计分析
    - 预约来源信息有助于优化预约流程
    - 界面信息更加丰富，提升工作效率
    - 标签颜色区分明显，降低认知负担

- **2025-01-21 v3.26**：门诊工作台页面工具区自定义新增工具功能
  - 新增工具按钮：
    - 在工具区添加"新增工具"按钮，位于双栏布局的第五个位置
    - 按钮样式采用蓝色主题，与现有工具按钮区分
    - 背景色为浅蓝色(#f0f9ff)，边框和文字为蓝色(#1890ff)
    - 悬停效果：背景色变深，边框和文字颜色加深
  - 新增工具子页面：
    - 子页面样式与治法子页面保持一致，使用相同的遮罩层和容器样式
    - 页面标题为"新增工具"，包含关闭按钮
    - 内容区域包含完整的工具配置表单
  - 工具配置表单：
    - 工具名称：必填输入框，用于设置工具显示名称
    - 工具图标：下拉选择框，提供6种常用图标选项
      - 文档、数据分析、日历、设置、用户、图表
    - 工具描述：多行文本输入框，用于描述工具功能
    - 工具链接：可选输入框，用于设置工具跳转链接
    - 操作按钮：确认添加和取消按钮
  - 表单验证功能：
    - 工具名称为必填项，空值时显示警告提示
    - 工具图标为必选项，未选择时显示警告提示
    - 验证通过后自动添加到自定义工具列表
  - 数据管理：
    - 新增customTools响应式数组，存储自定义工具
    - 新增newToolForm响应式对象，管理表单数据
    - 工具添加成功后显示成功提示并关闭子页面
    - 表单重置功能，每次打开时清空表单数据
  - 用户体验优化：
    - 表单布局清晰，标签宽度统一为80px
    - 按钮间距合理，操作便捷
    - 表单最大宽度400px，避免过宽影响视觉效果
    - 与现有工具子页面保持一致的交互体验
  - 功能扩展性：
    - 为后续自定义工具的动态展示预留完整框架
    - 支持工具链接配置，便于集成外部系统
    - 图标选择系统可扩展，支持更多图标类型
    - 表单验证机制完善，确保数据完整性

- **2025-01-21 v3.27**：门诊工作台页面工具子页面内容居中显示
  - tool-content样式优化：
    - 添加display: flex布局，实现内容垂直和水平居中
    - 设置flex-direction: column，确保内容垂直排列
    - 使用align-items: center实现水平居中
    - 使用justify-content: center实现垂直居中
    - 设置text-align: center，确保文本内容居中
    - 添加min-height: 200px，确保居中效果在较小内容时也能体现
  - 视觉效果提升：
    - 所有工具子页面的内容都居中显示，包括"功能开发中"提示
    - 新增工具表单在子页面中居中显示，提升视觉平衡
    - 内容布局更加美观，符合现代UI设计规范
    - 提升用户查看工具子页面的体验
  - 布局一致性：
    - 所有工具子页面（医案助手、门诊看板、预约看板、用药助手、新增工具）都采用相同的居中布局
    - 保持与治法子页面的视觉一致性
    - 统一的用户体验，提升界面专业度

- **2025-01-21 v3.28**：门诊工作台页面工具子页面内容宽度优化
  - tool-content内容宽度调整：
    - 为tool-content的直接子元素设置width: 60%，限制内容宽度
    - 添加max-width: 400px，防止内容在大屏幕上过宽
    - 使用.tool-content > *选择器，确保所有直接子元素都应用宽度限制
  - 布局优化效果：
    - 工具子页面内容不再占满整个容器宽度，更加紧凑美观
    - 文本内容和表单在子页面中居中显示，宽度适中
    - 提升内容的可读性和视觉层次感
    - 避免内容在大屏幕上过于分散
  - 响应式设计：
    - 在小屏幕上内容宽度为容器的60%
    - 在大屏幕上内容最大宽度不超过400px
    - 确保在不同屏幕尺寸下都有良好的显示效果
    - 保持内容的合理密度和可读性

- **2025-01-21 v3.29**：功能中心门诊工作站卡片悬停统计信息功能
  - 门诊工作站统计信息展示：
    - 为门诊工作站卡片添加悬停统计信息子页面
    - 统计信息包括：今日待接诊、已接诊、未接诊、未收费、诊中、回诊
    - 每个统计项都有对应的颜色标识，便于快速识别
    - 统计信息以3列网格形式展示，布局紧凑美观
  - 统计数据类型和颜色：
    - 今日待接诊：橙色(#FF9500)，表示需要关注的患者
    - 已接诊：绿色(#34C759)，表示已完成诊疗的患者
    - 未接诊：灰色(#8E8E93)，表示尚未开始诊疗的患者
    - 未收费：红色(#FF3B30)，表示需要收费的患者
    - 诊中：紫色(#5856D6)，表示正在诊疗的患者
    - 回诊：粉色(#FF2D92)，表示需要再次诊疗的患者
  - 汇总信息展示：
    - 今日总收入：显示当日诊疗收入，橙色显示
    - 平均就诊时长：显示平均每次诊疗时长，紫色显示
    - 汇总信息位于统计网格下方，用分隔线区分
  - 数据计算逻辑：
    - 基于患者数据库动态计算各种状态的统计数据
    - 模拟真实的门诊工作流程数据
    - 统计数据实时更新，反映当前门诊状态
    - 卡片信息动态显示待接诊数量
  - 视觉效果优化：
    - 统计信息子页面使用紫色主题，与门诊工作站卡片颜色一致
    - 悬停时从底部滑入，动画效果流畅
    - 统计项标签字体较小，数值字体较大，层次清晰
    - 整体布局与患者管理卡片统计信息保持一致
  - 用户体验提升：
    - 医生可以快速了解当日门诊工作状态
    - 通过颜色区分不同状态，提高信息识别效率
    - 悬停即可查看，无需额外点击操作
    - 统计信息全面，便于工作安排和决策

- **2025-01-21 v3.30**：修复门诊工作站统计信息JavaScript语法错误
  - 变量命名修复：
    - 将JavaScript保留字`return`改为`returnVisit`，避免语法错误
    - 更新相关的变量引用和对象属性名
    - 确保代码符合JavaScript语法规范
  - 功能完整性保证：
    - 回诊统计功能正常工作，显示正确的统计数据
    - CSS样式`.stat-value.return`保持不变，确保视觉效果一致
    - 所有统计信息正常显示，无语法错误
  - 代码质量提升：
    - 避免使用JavaScript保留字作为变量名
    - 提高代码的可读性和维护性
    - 确保编译和运行无错误