"""
现病史采集智能体

负责详细采集当前疾病相关信息，采用"先问有无，再深入追问"的策略。
"""

import re
from typing import Dict, List, Optional, Any
from datetime import datetime

from langchain.prompts import PromptTemplate
from langchain_core.prompts import PromptTemplate as LangchainPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from src.agents.base_agent import BaseAgent, AgentContext, AgentResponse


class PresentIllnessAgent(BaseAgent):
    """现病史采集智能体"""
    
    required_fields = ["main_symptom", "onset_time", "severity", "development", "accompanying_symptoms"]
    
    def __init__(self):
        super().__init__(
            name="现病史采集智能体",
            description="详细采集当前疾病相关信息",
            agent_type="present_illness"
        )
        
        # 症状类别和当前索引
        self.symptom_categories = [
            "寒热症状", "汗症", "疼痛症状", "头身胸腹不适",
            "耳目症状", "睡眠情况", "饮食口味", "二便情况"
        ]
        self.current_category_index = 0
    
    def _create_prompt_template(self) -> PromptTemplate:
        """创建支持历史的提示模板"""
        return LangchainPromptTemplate(
            input_variables=["patient_input", "context", "history"],
            template="""
你是一名专业的中医医生，负责采集患者的现病史信息。

[对话历史]
{history}

[患者描述]
{patient_input}

请提取以下信息:
1. 主要症状
2. 起病时间
3. 严重程度
4. 发展过程
5. 伴随症状

请严格只输出如下JSON格式，不要输出任何其他内容或解释：

{{
    "main_symptom": "主要症状描述",
    "onset_time": "起病时间",
    "severity": "严重程度",
    "development": "发展过程",
    "accompanying_symptoms": "伴随症状",
    "confidence": 0.95
}}

注意：如果患者描述不完整，请根据已有信息填写，缺失字段用null表示。
"""
        )
    
    def _create_output_parser(self) -> JsonOutputParser:
        """创建输出解析器"""
        return JsonOutputParser()
    
    def ask_has_or_not(self, context: Dict[str, Any]) -> str:
        """询问当前症状类别有无"""
        current_category = self.symptom_categories[self.current_category_index]
        
        questions = {
            "寒热症状": "您最近有发热或怕冷的感觉吗？",
            "汗症": "您最近出汗情况如何？",
            "疼痛症状": "您有疼痛的感觉吗？",
            "头身胸腹不适": "头部、身体、胸部或腹部有不适感吗？",
            "耳目症状": "眼睛或耳朵有异常感觉吗？",
            "睡眠情况": "您的睡眠情况如何？",
            "饮食口味": "您的食欲和口味有什么异常吗？",
            "二便情况": "大便和小便情况如何？"
        }
        
        return questions.get(current_category, "您有其他不适吗？")
    
    def ask_details_if_has(self, context: Dict[str, Any], has_result: Dict[str, Any]) -> str:
        """如果有症状，追问详情"""
        current_category = self.symptom_categories[self.current_category_index]
        
        detail_questions = {
            "寒热症状": """
            请详细说明：
            1. 发热或怕冷的程度如何？
            2. 发热或怕冷的时间规律？
            3. 是否同时出现其他症状？
            4. 什么情况下会加重或减轻？
            """,
            "汗症": """
            请详细说明：
            1. 出汗的颜色和质地？
            2. 出汗的时间和量？
            3. 出汗的部位？
            4. 什么情况下会出汗？
            """,
            "疼痛症状": """
            请详细说明：
            1. 疼痛的性质（胀痛、刺痛、隐痛等）？
            2. 疼痛的部位？
            3. 疼痛的时间和规律？
            4. 什么情况下会加重或减轻？
            """,
            "头身胸腹不适": """
            请详细说明：
            1. 具体哪个部位不适？
            2. 不适的性质和程度？
            3. 不适的时间和规律？
            4. 什么情况下会加重或减轻？
            """,
            "耳目症状": """
            请详细说明：
            1. 眼睛或耳朵有什么异常？
            2. 异常的程度和时间？
            3. 是否影响日常生活？
            4. 什么情况下会加重或减轻？
            """,
            "睡眠情况": """
            请详细说明：
            1. 睡眠时间长短？
            2. 入睡是否困难？
            3. 睡眠是否踏实？
            4. 是否做梦？
            """,
            "饮食口味": """
            请详细说明：
            1. 食欲如何？
            2. 口味有什么变化？
            3. 是否有特殊偏好？
            4. 饮食后有什么感觉？
            """,
            "二便情况": """
            请详细说明：
            1. 大便的次数和性状？
            2. 小便的次数和量？
            3. 是否有异常感觉？
            4. 什么情况下会变化？
            """
        }
        
        return detail_questions.get(current_category, "请详细描述一下。")
    
    def next_category(self):
        """移动到下一个症状类别"""
        if self.current_category_index < len(self.symptom_categories) - 1:
            self.current_category_index += 1
            return True
        return False
    
    def reset_categories(self):
        """重置症状类别索引"""
        self.current_category_index = 0
    
    def extract_entities(self, response: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取医疗实体"""
        entities = []
        
        # 提取症状实体
        symptom_patterns = {
            "寒热": r"发热|怕冷|寒战|恶寒|畏寒|潮热|五心烦热|手足心热",
            "汗症": r"自汗|盗汗|大汗|微汗|无汗|冷汗|热汗",
            "疼痛": r"胀痛|刺痛|隐痛|绞痛|灼痛|冷痛|酸痛|重痛",
            "睡眠": r"失眠|多梦|易醒|早醒|嗜睡|睡眠浅|睡眠深",
            "饮食": r"食欲不振|食欲亢进|口渴|口干|口苦|口淡|口甜|口酸",
            "二便": r"便秘|腹泻|便溏|便血|尿频|尿急|尿痛|尿血|夜尿"
        }
        
        for symptom_type, pattern in symptom_patterns.items():
            matches = re.findall(pattern, response)
            for match in matches:
                entity = {
                    "entity_id": f"present_symptom_{len(entities)}",
                    "entity_type": "present_symptom",
                    "entity_value": match,
                    "confidence": 0.9,
                    "source_agent": self.name,
                    "extracted_at": datetime.now().isoformat()
                }
                entities.append(entity)
        
        # 提取程度实体
        severity_patterns = [
            r"轻微|轻度|中等|中度|严重|重度|极重",
            r"不明显|明显|很|非常|特别"
        ]
        
        for pattern in severity_patterns:
            matches = re.findall(pattern, response)
            for match in matches:
                entity = {
                    "entity_id": f"severity_{len(entities)}",
                    "entity_type": "severity",
                    "entity_value": match,
                    "confidence": 0.8,
                    "source_agent": self.name,
                    "extracted_at": datetime.now().isoformat()
                }
                entities.append(entity)
        
        # 提取时间实体
        time_patterns = [
            r"(\d+)\s*(天|周|月|年)",
            r"(早上|中午|下午|晚上|夜间|凌晨)",
            r"(持续|间歇|阵发|规律|不规律)"
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, response)
            for match in matches:
                if isinstance(match, tuple):
                    time_value = "".join(match)
                else:
                    time_value = match
                
                entity = {
                    "entity_id": f"time_{len(entities)}",
                    "entity_type": "time",
                    "entity_value": time_value,
                    "confidence": 0.8,
                    "source_agent": self.name,
                    "extracted_at": datetime.now().isoformat()
                }
                entities.append(entity)
        
        return entities
    
    def generate_followup(self, missing_fields):
        mapping = {
            "main_symptom": "请描述当前最主要的不适症状。",
            "onset_time": "该症状从什么时候开始的？",
            "severity": "症状的严重程度如何？",
            "development": "症状的发展过程是怎样的？",
            "accompanying_symptoms": "还有其他伴随症状吗？"
        }
        return "；".join([mapping.get(f, f"请补充{f}") for f in missing_fields])
    
    def generate_summary(self, extracted_data):
        ms = extracted_data.get("main_symptom", "")
        ot = extracted_data.get("onset_time", "")
        sv = extracted_data.get("severity", "")
        dv = extracted_data.get("development", "")
        ac = extracted_data.get("accompanying_symptoms", "")
        return f"现病史: {ms}，起病于{ot}，严重程度{sv}，发展过程{dv}，伴随症状{ac}"
    
    def get_capabilities(self) -> List[str]:
        """获取智能体能力列表"""
        return [
            "系统性症状采集",
            "智能追问策略",
            "症状分类识别",
            "程度评估",
            "时间规律分析",
            "性别年龄适配"
        ]
    
    def get_required_inputs(self) -> List[str]:
        """获取所需输入列表"""
        return [
            "患者基本信息",
            "主诉信息",
            "症状回答"
        ]
    
    def get_outputs(self) -> List[str]:
        """获取输出列表"""
        return [
            "寒热症状",
            "汗症",
            "疼痛症状",
            "头身胸腹不适",
            "耳目症状",
            "睡眠情况",
            "饮食口味",
            "二便情况",
            "经带胎产",
            "小儿专项"
        ]
    
    def get_current_section_progress(self) -> Dict[str, Any]:
        """获取当前问诊阶段进度"""
        return {
            "current_section": self.sections[self.current_section] if self.current_section < len(self.sections) else None,
            "total_sections": len(self.sections),
            "completed_sections": self.current_section,
            "progress_percentage": (self.current_section / len(self.sections)) * 100 if self.sections else 0
        } 