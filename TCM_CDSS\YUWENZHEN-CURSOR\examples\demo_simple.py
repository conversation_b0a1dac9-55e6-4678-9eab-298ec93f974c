#!/usr/bin/env python3
"""
简化版演示脚本
展示智能体的基本功能，不依赖LangGraph
"""

import os
import sys
import json
from dotenv import load_dotenv
import argparse

# 加载环境变量
load_dotenv()

# 动态将项目根目录加入 sys.path
ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)

try:
    from src.agents.chief_complaint_agent import ChiefComplaintAgent
    from src.agents.present_illness_agent import PresentIllnessAgent
    from src.agents.past_history_agent import PastHistoryAgent
    from src.agents.family_history_agent import FamilyHistoryAgent
    from src.agents.allergy_history_agent import AllergyHistoryAgent
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)


def demo_agents(auto_patient: bool = False):
    """演示各个智能体的功能"""
    print("=" * 80)
    print("🤖 中医诊前预问诊智能体系统 - 简化版演示")
    print("基于OpenRouter + LangChain")
    print("=" * 80)
    
    # 创建智能体实例
    agents = {
        "主诉采集": ChiefComplaintAgent(),
        "现病史采集": PresentIllnessAgent(),
        "既往史采集": PastHistoryAgent(),
        "家族史采集": FamilyHistoryAgent(),
        "过敏史采集": AllergyHistoryAgent()
    }
    
    # 演示每个智能体
    for name, agent in agents.items():
        print(f"\n{'='*60}")
        print(f"🤖 {name}智能体演示")
        print(f"{'='*60}")
        
        # 显示智能体信息
        print(f"智能体名称: {agent.name}")
        print(f"智能体类型: {agent.agent_type}")
        print(f"描述: {agent.description}")
        
        # 演示询问逻辑
        history = []
        context = {"conversation_history": history}
        has_question = agent.ask_has_or_not(context)
        print(f"\n📝 询问模板: {has_question}")
        
        if auto_patient:
            from src.utils.ollama_client import OllamaLLMClient
            ollama = OllamaLLMClient()
            prompt = f"[对话历史]\n\n[医生提问]\n{has_question}\n请模拟患者真实、简洁地回答医生的问题。"
            patient_response = ollama.invoke(prompt)
            if hasattr(patient_response, 'content'):
                patient_response = patient_response.content
            print(f"患者回答: {patient_response}")
        else:
            patient_response = input("患者回答: ")
        
        history.append({"role": "agent", "content": has_question})
        history.append({"role": "patient", "content": patient_response})
        has_result = agent.parse_has_or_not_response(patient_response)
        print(f"解析结果: {has_result}")
        
        if has_result.get("has", False):
            details_question = agent.ask_details_if_has(context, has_result)
            print(f"📝 追问模板: {details_question[:100]}...")
            
            if auto_patient:
                prompt = f"[对话历史]\n" + "\n".join([f"{msg['role']}: {msg['content']}" for msg in history[-5:]]) + f"\n[医生追问]\n{details_question}\n请模拟患者真实、简洁地回答医生的问题。"
                detailed_response = ollama.invoke(prompt)
                if hasattr(detailed_response, 'content'):
                    detailed_response = detailed_response.content
                print(f"患者详细回答: {detailed_response}")
            else:
                detailed_response = input("患者详细回答: ")
            
            history.append({"role": "agent", "content": details_question})
            history.append({"role": "patient", "content": detailed_response})
            context = {"patient_response": patient_response, "detailed_response": detailed_response, "conversation_history": history}
            response = agent.agent_workflow(context)
            print(f"\n🔍 提取的实体: {response.entities}")
            print(f"📊 提取的数据: {response.extracted_data}")
            print(f"✅ 完成状态: {response.is_complete}")
        else:
            context = {"patient_response": patient_response, "conversation_history": history}
            response = agent.agent_workflow(context)
            print(f"智能体响应: {response.response}")
            print(f"✅ 完成状态: {response.is_complete}")


def demo_workflow_logic():
    """演示工作流逻辑（简化版）"""
    print(f"\n{'='*80}")
    print("🔄 工作流逻辑说明（简化版）")
    print(f"{'='*80}")
    
    print("""
    📋 智能体工作流程:
    
    1️⃣ 询问有无阶段
       - 智能体询问患者是否有相关症状/病史
       - 使用ask_has_or_not()方法
    
    2️⃣ 解析回答阶段
       - 解析患者的回答（有/无）
       - 使用parse_has_or_not_response()方法
    
    3️⃣ 追问详情阶段（如果有）
       - 如果有相关症状/病史，则追问详情
       - 使用ask_details_if_has()方法
    
    4️⃣ 数据提取阶段
       - 从患者回答中提取结构化数据
       - 使用extract_entities()方法
    
    5️⃣ 完成判断阶段
       - 判断是否收集到足够信息
       - 决定是否继续或结束
    
    🎯 核心优势:
    - 标准化流程，易于理解和维护
    - 减少无效追问，提高问诊效率
    - 确保信息收集的完整性和一致性
    - 支持灵活的状态管理和工作流编排
    """)


def demo_agent_sequence():
    """演示智能体执行顺序"""
    print(f"\n{'='*80}")
    print("📋 智能体执行顺序")
    print(f"{'='*80}")
    
    sequence = [
        "主诉采集智能体",
        "现病史采集智能体", 
        "既往史采集智能体",
        "家族史采集智能体",
        "过敏史采集智能体"
    ]
    
    print("智能体执行顺序:")
    for i, agent_name in enumerate(sequence, 1):
        print(f"{i}. {agent_name}")
    
    print(f"\n📊 执行逻辑:")
    print("- 按顺序执行每个智能体")
    print("- 每个智能体完成后再执行下一个")
    print("- 支持动态跳过（如果患者明确表示没有相关情况）")
    print("- 最终生成完整的中医病历")


def demo_data_models():
    """演示数据模型"""
    print(f"\n{'='*80}")
    print("📊 数据模型说明")
    print(f"{'='*80}")
    
    print("""
    🔍 核心数据模型:
    
    1️⃣ AgentResponse
       - response: 智能体响应文本
       - extracted_data: 提取的结构化数据
       - entities: 识别的医疗实体
       - confidence: 置信度
       - is_complete: 是否完成
    
    2️⃣ 医疗实体类型
       - 症状实体: 头痛、发热、咳嗽等
       - 疾病实体: 高血压、糖尿病等
       - 药物实体: 阿司匹林、青霉素等
       - 时间实体: 3天前、去年等
       - 程度实体: 轻微、严重等
    
    3️⃣ 结构化数据
       - JSON格式输出
       - 标准化的字段命名
       - 支持嵌套结构
       - 便于后续处理
    """)


def main():
    """主函数"""
    parser = argparse.ArgumentParser()
    parser.add_argument('--auto', action='store_true', help='自动模拟患者输入')
    parser.add_argument('--manual', action='store_true', help='手动输入患者回答')
    args = parser.parse_args()
    auto_patient = args.auto and not args.manual
    
    print("🤖 中医诊前预问诊智能体系统 - 简化版演示")
    print("基于OpenRouter + LangChain")
    print("统一逻辑：先问有无，若有则追问，无则结束")
    
    # 检查环境变量
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key or api_key == "your-openrouter-api-key-here":
        print("\n⚠️  注意: 未设置OPENROUTER_API_KEY环境变量")
        print("部分功能（如数据提取）需要API密钥才能正常工作")
        print("在.env文件中设置：OPENROUTER_API_KEY=your-actual-api-key")
    
    try:
        # 运行演示
        demo_agents(auto_patient=auto_patient)
        demo_workflow_logic()
        demo_agent_sequence()
        demo_data_models()
        
        print("\n🎉 演示完成！")
        print("\n💡 提示:")
        print("- 这是简化版演示，展示了智能体的基本架构")
        print("- 所有智能体都遵循统一的标准化流程")
        print("- 设置OpenRouter API密钥后可以体验完整功能")
        print("- 完整版包含LangGraph工作流和FastAPI接口")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查依赖是否正确安装")


if __name__ == "__main__":
    main() 