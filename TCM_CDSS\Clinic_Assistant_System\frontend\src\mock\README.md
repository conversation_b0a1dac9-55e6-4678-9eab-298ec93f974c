# Mock 模拟数据

模拟数据模块提供开发阶段的假数据，用于前端开发和测试。

## 目录结构

### database/ - 数据库模拟
- **index.ts** - 数据库初始化
- **patients.ts** - 患者数据
- **appointments.ts** - 预约数据
- **users.ts** - 用户数据

### api/ - API模拟
- **auth.ts** - 认证API
- **patients.ts** - 患者API
- **appointments.ts** - 预约API

### services/ - 服务模拟
- **ai-diagnosis.ts** - AI诊断服务
- **notification.ts** - 通知服务

## 功能说明

### 数据模拟
- 提供真实的业务数据结构
- 支持数据的增删改查操作
- 模拟网络延迟和错误情况

### API模拟
- 模拟后端API接口
- 提供统一的请求响应格式
- 支持异步操作和错误处理

### 服务模拟
- 模拟外部服务调用
- 提供业务逻辑的模拟实现
- 支持复杂的交互场景

## 使用说明

### 开发环境
- 在开发阶段使用模拟数据
- 可以快速构建和测试功能
- 不依赖后端服务

### 生产环境
- 生产环境会替换为真实API
- 模拟数据仅用于开发测试
- 确保数据安全和隐私

## 模拟数据库 (Mock Database)

## 概述

这个文件夹包含了模拟数据库的实现，用于在开发阶段模拟真实数据库的行为。每个文件对应数据库中的一个表，提供了完整的CRUD操作接口。

## 文件结构

```
mock/
├── database/
│   ├── index.ts          # 数据库统一导出
│   ├── patients.ts       # 患者表
│   └── README.md         # 本文件
└── README.md             # 模拟数据说明
```

## 数据表

### 患者表 (patients.ts)

**接口定义：**
```typescript
interface Patient {
  id: number
  name: string
  phone: string
  gender: '男' | '女'
  age: number
  diagnosis: string
  lastVisit: string | null
  nextAppointment: string | null
  visitCount: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}
```

**数据统计：**
- **总患者数**：38人
- **性别分布**：男19人，女19人
- **年龄分布**：25-58岁
- **证型分布**：
  - 肝郁脾虚证：8人
  - 气血两虚证：6人
  - 肾阳虚证：5人
  - 痰湿阻肺证：5人
  - 心脾两虚证：4人
  - 肝阳上亢证：4人
  - 脾胃虚弱证：4人
  - 阴虚火旺证：2人
- **状态分布**：活跃24人，非活跃14人
- **预约情况**：有预约24人，无预约14人

**服务方法：**
- `getAllPatients()` - 获取所有患者
- `getPatientById(id)` - 根据ID获取患者
- `searchPatients(query)` - 搜索患者
- `filterPatients(filters)` - 根据条件筛选患者
- `addPatient(patient)` - 添加新患者
- `updatePatient(id, updates)` - 更新患者信息
- `deletePatient(id)` - 删除患者

## 使用方法

### 1. 导入服务

```typescript
import { patientService, type Patient } from '@/mock/database/patients'
```

### 2. 在组件中使用

```typescript
// 获取所有患者
const patients = ref<Patient[]>([])
const loading = ref(false)

const fetchPatients = async () => {
  loading.value = true
  try {
    const data = await patientService.getAllPatients()
    patients.value = data
  } catch (error) {
    console.error('获取患者数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索患者
const searchPatients = async (query: string) => {
  const results = await patientService.searchPatients(query)
  return results
}

// 添加患者
const addNewPatient = async (patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>) => {
  const newPatient = await patientService.addPatient(patientData)
  return newPatient
}
```

## 模拟特性

### 1. 异步操作
所有数据库操作都返回Promise，模拟真实的异步网络请求。

### 2. 网络延迟
操作包含随机延迟，模拟真实网络环境：
- 查询操作：50-150ms
- 修改操作：100-200ms

### 3. 数据持久化
数据在内存中持久化，页面刷新后重置。

### 4. 错误处理
包含基本的错误处理机制。

## 扩展指南

### 添加新数据表

1. 在 `database/` 文件夹中创建新的表文件，如 `appointments.ts`
2. 定义接口和模拟数据
3. 实现服务方法
4. 在 `index.ts` 中导出

### 示例：创建预约表

```typescript
// appointments.ts
export interface Appointment {
  id: number
  patientId: number
  doctorId: number
  date: string
  time: string
  status: 'scheduled' | 'completed' | 'cancelled'
  notes?: string
}

export const appointmentsData: Appointment[] = [
  // 模拟数据
]

export const appointmentService = {
  getAllAppointments() { /* ... */ },
  getAppointmentById(id: number) { /* ... */ },
  // 其他方法
}
```

## 迁移到真实数据库

当准备连接真实数据库时：

1. 保持接口定义不变
2. 将服务方法改为调用真实API
3. 更新错误处理逻辑
4. 移除模拟延迟

### 示例迁移

```typescript
// 从模拟服务
const patients = await patientService.getAllPatients()

// 迁移到真实API
const patients = await api.get('/patients')
```

## 注意事项

1. **数据重置**：模拟数据在页面刷新后会重置，不要依赖数据持久化
2. **性能考虑**：大量数据时考虑分页加载
3. **类型安全**：使用TypeScript接口确保类型安全
4. **测试友好**：模拟数据便于单元测试和集成测试

## 版本信息

- **当前版本**：v1.0
- **最后更新**：2024-01-15
- **支持的表**：患者表
- **计划添加**：预约表、医生表、药品表等 