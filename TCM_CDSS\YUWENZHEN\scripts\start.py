#!/usr/bin/env python3
"""
YUWENZHEN 项目启动脚本

提供开发和生产环境的一键启动功能。
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path


def get_project_root():
    """获取项目根目录"""
    return Path(__file__).parent.parent


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 9):
        print("❌ 错误: 需要Python 3.9或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    print(f"✅ Python版本检查通过: {sys.version}")


def check_dependencies():
    """检查依赖是否安装"""
    project_root = get_project_root()
    requirements_file = project_root / "backend" / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ 错误: requirements.txt文件不存在")
        sys.exit(1)
    
    print("🔍 检查依赖包...")
    try:
        import fastapi
        import langchain
        import langgraph
        print("✅ 核心依赖检查通过")
    except ImportError as e:
        print(f"❌ 错误: 缺少依赖包 {e}")
        print("请运行: pip install -r backend/requirements.txt")
        sys.exit(1)


def setup_environment():
    """设置环境变量"""
    project_root = get_project_root()
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if not env_file.exists():
        if env_example.exists():
            print("📋 创建环境配置文件...")
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ 已从.env.example创建.env文件")
            print("⚠️  请编辑.env文件配置您的环境变量")
        else:
            print("❌ 错误: .env.example文件不存在")
            sys.exit(1)
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
        print("✅ 环境变量加载成功")
    except ImportError:
        print("⚠️  警告: python-dotenv未安装，跳过环境变量加载")


def create_directories():
    """创建必要的目录"""
    project_root = get_project_root()
    directories = [
        project_root / "data",
        project_root / "logs",
        project_root / "data" / "uploads",
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构创建完成")


def check_services():
    """检查外部服务"""
    print("🔍 检查外部服务...")
    
    # 检查Redis (可选)
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis连接正常")
    except Exception:
        print("⚠️  警告: Redis连接失败，将使用本地缓存")
    
    # 检查Ollama (如果配置了)
    llm_service = os.getenv("LLM_SERVICE", "openrouter")
    if llm_service == "ollama":
        try:
            import httpx
            response = httpx.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                print("✅ Ollama服务正常")
            else:
                print("⚠️  警告: Ollama服务响应异常")
        except Exception:
            print("⚠️  警告: Ollama服务连接失败")


def start_backend(dev_mode=True):
    """启动后端服务"""
    project_root = get_project_root()
    backend_dir = project_root / "backend"
    
    print("🚀 启动后端服务...")
    
    if dev_mode:
        # 开发模式
        cmd = [
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "info"
        ]
    else:
        # 生产模式
        workers = os.getenv("PROD_WORKERS", "4")
        cmd = [
            sys.executable, "-m", "gunicorn",
            "app.main:app",
            "-w", workers,
            "-k", "uvicorn.workers.UvicornWorker",
            "--bind", "0.0.0.0:8000",
            "--log-level", "info"
        ]
    
    try:
        subprocess.run(cmd, cwd=backend_dir, check=True)
    except KeyboardInterrupt:
        print("\n🛑 后端服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 后端服务启动失败: {e}")
        sys.exit(1)


def start_frontend():
    """启动前端服务"""
    project_root = get_project_root()
    frontend_dir = project_root / "frontend"
    
    if not frontend_dir.exists():
        print("⚠️  警告: 前端目录不存在，跳过前端启动")
        return
    
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("⚠️  警告: package.json不存在，跳过前端启动")
        return
    
    print("🚀 启动前端服务...")
    
    # 检查是否安装了依赖
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("📦 安装前端依赖...")
        subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
    
    # 启动开发服务器
    try:
        subprocess.run(["npm", "run", "dev"], cwd=frontend_dir, check=True)
    except KeyboardInterrupt:
        print("\n🛑 前端服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 前端服务启动失败: {e}")


def run_tests():
    """运行测试"""
    project_root = get_project_root()
    backend_dir = project_root / "backend"
    
    print("🧪 运行测试...")
    
    cmd = [sys.executable, "-m", "pytest", "tests/", "-v"]
    
    try:
        subprocess.run(cmd, cwd=backend_dir, check=True)
        print("✅ 所有测试通过")
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="YUWENZHEN 项目启动脚本")
    parser.add_argument(
        "command",
        choices=["dev", "prod", "backend", "frontend", "test", "check"],
        help="启动模式"
    )
    parser.add_argument(
        "--skip-checks",
        action="store_true",
        help="跳过环境检查"
    )
    
    args = parser.parse_args()
    
    print("🏥 YUWENZHEN - 中医预问诊智能体系统")
    print("=" * 50)
    
    if not args.skip_checks:
        check_python_version()
        check_dependencies()
        setup_environment()
        create_directories()
        check_services()
        print("=" * 50)
    
    if args.command == "check":
        print("✅ 环境检查完成")
        return
    
    elif args.command == "test":
        run_tests()
        return
    
    elif args.command == "backend":
        start_backend(dev_mode=True)
    
    elif args.command == "frontend":
        start_frontend()
    
    elif args.command == "dev":
        print("🔧 开发模式启动")
        print("后端地址: http://localhost:8000")
        print("API文档: http://localhost:8000/docs")
        print("前端地址: http://localhost:3000")
        print("按 Ctrl+C 停止服务")
        print("-" * 50)
        
        # 在开发模式下，只启动后端
        # 前端需要单独启动
        start_backend(dev_mode=True)
    
    elif args.command == "prod":
        print("🚀 生产模式启动")
        print("服务地址: http://localhost:8000")
        print("按 Ctrl+C 停止服务")
        print("-" * 50)
        
        start_backend(dev_mode=False)


if __name__ == "__main__":
    main()
