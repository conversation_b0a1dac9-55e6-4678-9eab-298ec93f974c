"""
缓存管理模块

提供Redis缓存和本地缓存功能，支持会话管理、数据缓存和分布式锁。
"""

import json
import logging
import asyncio
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
import redis.asyncio as redis
from redis.asyncio import Redis
from functools import wraps

from .config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# 全局Redis客户端
redis_client: Optional[Redis] = None


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.redis_client: Optional[Redis] = None
        self.local_cache: Dict[str, Any] = {}
        self.local_cache_ttl: Dict[str, datetime] = {}
    
    async def initialize(self):
        """初始化缓存"""
        try:
            # 解析Redis URL
            redis_url = settings.redis_url
            
            # 创建Redis客户端
            self.redis_client = redis.from_url(
                redis_url,
                db=settings.redis_db,
                password=settings.redis_password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info(f"Redis缓存已连接: {redis_url}")
            
        except Exception as e:
            logger.warning(f"Redis连接失败，使用本地缓存: {e}")
            self.redis_client = None
    
    async def close(self):
        """关闭缓存连接"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Redis连接已关闭")
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存"""
        try:
            # 序列化值
            serialized_value = json.dumps(value, ensure_ascii=False, default=str)
            
            if self.redis_client:
                # 使用Redis
                if ttl:
                    await self.redis_client.setex(key, ttl, serialized_value)
                else:
                    await self.redis_client.set(key, serialized_value)
                return True
            else:
                # 使用本地缓存
                self.local_cache[key] = serialized_value
                if ttl:
                    self.local_cache_ttl[key] = datetime.now() + timedelta(seconds=ttl)
                return True
                
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            if self.redis_client:
                # 使用Redis
                value = await self.redis_client.get(key)
                if value:
                    return json.loads(value)
                return None
            else:
                # 使用本地缓存
                if key in self.local_cache:
                    # 检查TTL
                    if key in self.local_cache_ttl:
                        if datetime.now() > self.local_cache_ttl[key]:
                            # 已过期
                            del self.local_cache[key]
                            del self.local_cache_ttl[key]
                            return None
                    
                    value = self.local_cache[key]
                    return json.loads(value)
                return None
                
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            if self.redis_client:
                # 使用Redis
                await self.redis_client.delete(key)
                return True
            else:
                # 使用本地缓存
                if key in self.local_cache:
                    del self.local_cache[key]
                if key in self.local_cache_ttl:
                    del self.local_cache_ttl[key]
                return True
                
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            if self.redis_client:
                return await self.redis_client.exists(key) > 0
            else:
                if key in self.local_cache:
                    # 检查TTL
                    if key in self.local_cache_ttl:
                        if datetime.now() > self.local_cache_ttl[key]:
                            del self.local_cache[key]
                            del self.local_cache_ttl[key]
                            return False
                    return True
                return False
                
        except Exception as e:
            logger.error(f"检查缓存存在失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间"""
        try:
            if self.redis_client:
                return await self.redis_client.expire(key, ttl)
            else:
                if key in self.local_cache:
                    self.local_cache_ttl[key] = datetime.now() + timedelta(seconds=ttl)
                    return True
                return False
                
        except Exception as e:
            logger.error(f"设置缓存过期时间失败 {key}: {e}")
            return False
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        try:
            if self.redis_client:
                return await self.redis_client.keys(pattern)
            else:
                import fnmatch
                return [key for key in self.local_cache.keys() if fnmatch.fnmatch(key, pattern)]
                
        except Exception as e:
            logger.error(f"获取键列表失败 {pattern}: {e}")
            return []
    
    async def clear(self, pattern: str = "*") -> int:
        """清空匹配的缓存"""
        try:
            keys = await self.keys(pattern)
            if not keys:
                return 0
            
            if self.redis_client:
                return await self.redis_client.delete(*keys)
            else:
                count = 0
                for key in keys:
                    if key in self.local_cache:
                        del self.local_cache[key]
                        count += 1
                    if key in self.local_cache_ttl:
                        del self.local_cache_ttl[key]
                return count
                
        except Exception as e:
            logger.error(f"清空缓存失败 {pattern}: {e}")
            return 0
    
    async def health_check(self) -> dict:
        """缓存健康检查"""
        try:
            if self.redis_client:
                await self.redis_client.ping()
                info = await self.redis_client.info()
                return {
                    "cache": "healthy",
                    "type": "redis",
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory": info.get("used_memory_human", "unknown")
                }
            else:
                return {
                    "cache": "healthy",
                    "type": "local",
                    "keys_count": len(self.local_cache)
                }
        except Exception as e:
            return {
                "cache": "unhealthy",
                "error": str(e)
            }


# 全局缓存管理器实例
cache_manager = CacheManager()


# 会话管理器
class SessionManager:
    """会话管理器"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.session_prefix = "session:"
        self.default_ttl = settings.session_ttl
    
    async def create_session(self, session_id: str, data: dict) -> bool:
        """创建会话"""
        key = f"{self.session_prefix}{session_id}"
        return await self.cache.set(key, data, self.default_ttl)
    
    async def get_session(self, session_id: str) -> Optional[dict]:
        """获取会话"""
        key = f"{self.session_prefix}{session_id}"
        return await self.cache.get(key)
    
    async def update_session(self, session_id: str, data: dict) -> bool:
        """更新会话"""
        key = f"{self.session_prefix}{session_id}"
        return await self.cache.set(key, data, self.default_ttl)
    
    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        key = f"{self.session_prefix}{session_id}"
        return await self.cache.delete(key)
    
    async def extend_session(self, session_id: str, ttl: Optional[int] = None) -> bool:
        """延长会话时间"""
        key = f"{self.session_prefix}{session_id}"
        return await self.cache.expire(key, ttl or self.default_ttl)
    
    async def list_sessions(self) -> List[str]:
        """列出所有会话"""
        keys = await self.cache.keys(f"{self.session_prefix}*")
        return [key.replace(self.session_prefix, "") for key in keys]


# 全局会话管理器实例
session_manager = SessionManager(cache_manager)


# 缓存装饰器
def cached(ttl: int = 3600, key_prefix: str = "cache"):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 存储到缓存
            await cache_manager.set(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator


# 便捷函数
async def init_cache():
    """初始化缓存"""
    await cache_manager.initialize()


async def close_cache():
    """关闭缓存"""
    await cache_manager.close()


async def get_cache_health() -> dict:
    """获取缓存健康状态"""
    return await cache_manager.health_check()


# 导出主要接口
__all__ = [
    "cache_manager",
    "session_manager", 
    "cached",
    "init_cache",
    "close_cache",
    "get_cache_health"
]
