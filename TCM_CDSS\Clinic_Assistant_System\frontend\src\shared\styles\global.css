@import './variables.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden; /* 防止整体页面滚动 */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background-image: url('@/assets/bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  overflow: hidden; /* 确保应用容器不滚动 */
  position: relative;
}

/* 为所有页面添加半透明遮罩层，确保内容可读性 */
#app::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.55);
  z-index: -1;
  pointer-events: none;
}

/* 通用导航栏样式 */
.module-header {
  background: var(--surface-color);
  padding: var(--spacing-sm) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  min-height: 48px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.back-button:hover {
  color: var(--primary-dark);
}

.module-title {
  flex: 1;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

/* iOS风格按钮 */
.ios-button {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.ios-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.ios-button:active {
  transform: translateY(0);
}

/* iOS风格卡片 */
.ios-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.ios-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

/* iOS风格输入框 */
.ios-input {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-md);
  transition: all var(--transition-fast);
}

.ios-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
} 