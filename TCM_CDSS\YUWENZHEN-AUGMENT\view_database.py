#!/usr/bin/env python3
"""
数据库查看工具
"""

import json
import os
import requests
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

def print_header(title: str):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"📊 {title}")
    print("=" * 60)

def print_section(title: str):
    """打印章节"""
    print(f"\n🔸 {title}")
    print("-" * 40)

def view_workflow_records():
    """查看工作流程记录"""
    print_section("工作流程问诊记录")
    
    records_dir = Path("data/workflow_records")
    
    if not records_dir.exists():
        print("❌ 工作流程记录目录不存在")
        return
        
    records = list(records_dir.glob("*.json"))
    
    if not records:
        print("📭 暂无工作流程记录")
        return
        
    print(f"📊 找到 {len(records)} 条工作流程记录:\n")
    
    for i, record_file in enumerate(records, 1):
        try:
            with open(record_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            print(f"📋 记录 {i}: {record_file.name}")
            print(f"   👤 患者: {data['patient_info']['name']}")
            print(f"   🎂 年龄: {data['patient_info'].get('age', '未提供')}")
            print(f"   ⚧ 性别: {data['patient_info'].get('gender', '未提供')}")
            print(f"   📅 开始时间: {data['inquiry_summary']['start_time']}")
            print(f"   ⏱️ 问诊时长: {data['inquiry_summary']['duration_minutes']:.1f}分钟")
            print(f"   📊 完成度: {data['inquiry_summary']['completeness']}")
            print(f"   🤖 智能体报告: {len(data['agent_reports'])}个")
            print(f"   💬 对话消息: {len(data['conversation_history'])}条")
            
            # 显示智能体报告摘要
            if data['agent_reports']:
                print("   📋 智能体工作:")
                for report in data['agent_reports']:
                    print(f"      ✅ {report['agent_name']}")
            
            print()
            
        except Exception as e:
            print(f"❌ 读取记录失败 {record_file.name}: {e}")

def view_detailed_record(record_name: str = None):
    """查看详细记录"""
    print_section("详细记录查看")
    
    records_dir = Path("data/workflow_records")
    
    if record_name:
        record_file = records_dir / record_name
        if not record_file.exists():
            print(f"❌ 记录文件不存在: {record_name}")
            return
        records = [record_file]
    else:
        records = list(records_dir.glob("*.json"))
        if not records:
            print("📭 暂无记录")
            return
        # 选择最新的记录
        records = [max(records, key=lambda x: x.stat().st_mtime)]
    
    for record_file in records:
        try:
            with open(record_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📋 详细记录: {record_file.name}")
            print(f"会话ID: {data['session_id']}")
            
            # 患者信息
            print(f"\n👤 患者信息:")
            patient = data['patient_info']
            print(f"   姓名: {patient['name']}")
            print(f"   年龄: {patient.get('age', '未提供')}")
            print(f"   性别: {patient.get('gender', '未提供')}")
            print(f"   联系方式: {patient.get('contact', '未提供')}")
            
            # 智能体报告
            print(f"\n🤖 智能体工作报告:")
            for i, report in enumerate(data['agent_reports'], 1):
                print(f"\n   {i}. {report['agent_name']}")
                print(f"      完成时间: {report['completion_time']}")
                print(f"      收集信息: {report['collected_info']}")
                print(f"      工作报告:")
                for line in report['report'].split('\n'):
                    print(f"         {line}")
            
            # 对话历史
            print(f"\n💬 对话历史 ({len(data['conversation_history'])}条消息):")
            for i, msg in enumerate(data['conversation_history'], 1):
                role = "👤 患者" if msg['role'] == 'user' else "🤖 AI助手"
                timestamp = msg['timestamp'][:19]  # 去掉微秒
                print(f"   {i:2d}. [{timestamp}] {role}:")
                print(f"       {msg['content']}")
            
            # 问诊总结
            print(f"\n📊 问诊总结:")
            summary = data['inquiry_summary']
            print(f"   开始时间: {summary['start_time']}")
            print(f"   结束时间: {summary['end_time']}")
            print(f"   问诊时长: {summary['duration_minutes']:.1f}分钟")
            print(f"   智能体数量: {summary['total_agents']}")
            print(f"   完成度: {summary['completeness']}")
            
        except Exception as e:
            print(f"❌ 读取详细记录失败: {e}")

def view_active_sessions():
    """查看活跃会话"""
    print_section("活跃会话 (内存中)")
    
    try:
        # 检查服务器状态
        response = requests.get("http://localhost:8002/api/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行")
            return
            
        # 获取活跃的工作流程会话
        response = requests.get("http://localhost:8002/api/workflow/sessions", timeout=5)
        if response.status_code == 200:
            data = response.json()
            sessions = data.get('active_sessions', [])
            
            if sessions:
                print(f"📊 活跃工作流程会话: {len(sessions)}个\n")
                for i, session in enumerate(sessions, 1):
                    print(f"   {i}. 会话ID: {session['session_id']}")
                    print(f"      患者: {session['patient_name']}")
                    print(f"      开始时间: {session['start_time']}")
                    print(f"      当前智能体: {session['current_agent']}")
                    print(f"      进度: {session['progress']['progress_percentage']:.1f}%")
                    print()
            else:
                print("📭 暂无活跃的工作流程会话")
        else:
            print(f"❌ 获取工作流程会话失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保服务器正在运行: python quick_start_mvp.py")

def view_data_statistics():
    """查看数据统计"""
    print_section("数据统计")
    
    # 工作流程记录统计
    records_dir = Path("data/workflow_records")
    workflow_count = len(list(records_dir.glob("*.json"))) if records_dir.exists() else 0
    
    # 其他目录统计
    sessions_dir = Path("data/sessions")
    reports_dir = Path("data/reports")
    uploads_dir = Path("data/uploads")
    
    sessions_count = len(list(sessions_dir.glob("*"))) if sessions_dir.exists() else 0
    reports_count = len(list(reports_dir.glob("*"))) if reports_dir.exists() else 0
    uploads_count = len(list(uploads_dir.glob("*"))) if uploads_dir.exists() else 0
    
    print(f"📊 数据存储统计:")
    print(f"   🔄 工作流程记录: {workflow_count}条")
    print(f"   💬 会话记录: {sessions_count}条")
    print(f"   📋 报告文件: {reports_count}个")
    print(f"   📁 上传文件: {uploads_count}个")
    
    # 磁盘使用统计
    data_dir = Path("data")
    if data_dir.exists():
        total_size = sum(f.stat().st_size for f in data_dir.rglob('*') if f.is_file())
        print(f"   💾 总存储大小: {total_size / 1024:.1f} KB")
    
    # 最近活动
    if workflow_count > 0:
        records = list(records_dir.glob("*.json"))
        latest_record = max(records, key=lambda x: x.stat().st_mtime)
        mtime = datetime.fromtimestamp(latest_record.stat().st_mtime)
        print(f"   🕒 最近记录: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    print_header("中医预问诊系统 - 数据库查看工具")
    
    print("\n🎯 可用功能:")
    print("1. 查看工作流程记录概览")
    print("2. 查看最新详细记录")
    print("3. 查看活跃会话")
    print("4. 查看数据统计")
    print("5. 查看所有数据")
    
    while True:
        print("\n" + "=" * 40)
        choice = input("请选择功能 (1-5, q退出): ").strip()
        
        if choice == 'q':
            print("👋 再见！")
            break
        elif choice == '1':
            view_workflow_records()
        elif choice == '2':
            view_detailed_record()
        elif choice == '3':
            view_active_sessions()
        elif choice == '4':
            view_data_statistics()
        elif choice == '5':
            view_workflow_records()
            view_active_sessions()
            view_data_statistics()
        else:
            print("❌ 无效选择，请输入 1-5 或 q")

if __name__ == "__main__":
    main()
