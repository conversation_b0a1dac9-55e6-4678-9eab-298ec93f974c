{"project": {"name": "GuanBao AI Chat", "version": "1.0.0", "description": "基于Vue 3和FastAPI的AI聊天应用"}, "frontend": {"host": "localhost", "port": 83, "dev": {"host": "localhost", "port": 5173, "open": true, "cors": true}, "build": {"outDir": "dist", "assetsDir": "assets"}}, "backend": {"host": "0.0.0.0", "port": 82, "dev": {"host": "0.0.0.0", "port": 82, "debug": true, "reload": true}, "prod": {"host": "0.0.0.0", "port": 82, "debug": false, "workers": 4}}, "api": {"base_url": "http://localhost:82", "endpoints": {"health": "/api/health", "send_verification_code": "/api/send-verification-code", "verify_login": "/api/verify-login", "chat_completions": "/v1/chat/completions"}, "timeout": 30000, "retry_attempts": 3}, "database": {"type": "sqlite", "url": "sqlite:///./guanbao.db", "pool_size": 10, "max_overflow": 20}, "security": {"secret_key": "GuanBao_2024_SECRET_KEY", "api_key": "GuanBao_2024_API_KEY", "session_timeout": 86400, "cors_origins": ["http://localhost:5173", "http://127.0.0.1:5173"]}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/app.log", "max_size": "10MB", "backup_count": 5}, "features": {"registration_enabled": false, "invited_users_only": true, "max_sessions_per_user": 50, "max_message_length": 4000, "stream_response": true}}