# 统计报表模块

## 功能描述
统计报表模块负责生成和管理各类业务报表，包括患者统计、收入统计、药品统计等功能。

## 目录结构
```
reports/
├── views/                    # 页面视图
│   └── ReportsList.vue       # 报表列表页面
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 主要功能
- 患者统计报表
- 收入统计报表
- 药品统计报表
- 自定义报表

## 开发计划
- [ ] 拆分组件
- [ ] 添加组合式函数
- [ ] 完善类型定义
- [ ] 优化样式结构 