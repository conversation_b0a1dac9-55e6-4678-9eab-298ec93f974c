/**
 * 数据服务
 * 
 * 主要功能：
 * 1. 提供业务数据的CRUD操作
 * 2. 与后端API集成
 * 3. 数据缓存和状态管理
 * 4. 错误处理和重试机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApiService, mappingApi, statsApi } from './api'

// 患者信息接口
export interface Patient {
  id: number
  patient_number: string
  full_name: string
  gender: string
  birth_date: string
  phone: string
  address?: string
  emergency_contact?: string
  emergency_phone?: string
  created_at: string
  updated_at: string
}

// 预约信息接口
export interface Appointment {
  id: number
  patient_id: number
  doctor_id: number
  appointment_date: string
  appointment_time: string
  status: string
  notes?: string
  created_at: string
  updated_at: string
}

// 就诊记录接口
export interface Visit {
  id: number
  patient_id: number
  doctor_id: number
  visit_date: string
  chief_complaint?: string
  present_illness?: string
  diagnosis?: string
  treatment_plan?: string
  status: string
  created_at: string
  updated_at: string
}

// 数据服务类
export class DataService {
  /**
   * 患者管理
   */
  static patients = {
    /**
     * 获取患者列表
     */
    async getList(params?: {
      page?: number
      limit?: number
      search?: string
      gender?: string
    }) {
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.search) queryParams.append('search', params.search)
      if (params?.gender) queryParams.append('gender', params.gender)

      return ApiService.get(`/api/patients?${queryParams.toString()}`)
    },

    /**
     * 获取患者详情
     */
    async getById(id: number) {
      return ApiService.get(`/api/patients/${id}`)
    },

    /**
     * 创建患者
     */
    async create(patient: Omit<Patient, 'id' | 'created_at' | 'updated_at'>) {
      return ApiService.post('/api/patients', patient)
    },

    /**
     * 更新患者
     */
    async update(id: number, patient: Partial<Patient>) {
      return ApiService.put(`/api/patients/${id}`, patient)
    },

    /**
     * 删除患者
     */
    async delete(id: number) {
      return ApiService.delete(`/api/patients/${id}`)
    }
  }

  /**
   * 预约管理
   */
  static appointments = {
    /**
     * 获取预约列表
     */
    async getList(params?: {
      page?: number
      limit?: number
      date?: string
      doctor_id?: number
      status?: string
    }) {
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.date) queryParams.append('date', params.date)
      if (params?.doctor_id) queryParams.append('doctor_id', params.doctor_id.toString())
      if (params?.status) queryParams.append('status', params.status)

      return ApiService.get(`/api/appointments?${queryParams.toString()}`)
    },

    /**
     * 获取预约详情
     */
    async getById(id: number) {
      return ApiService.get(`/api/appointments/${id}`)
    },

    /**
     * 创建预约
     */
    async create(appointment: Omit<Appointment, 'id' | 'created_at' | 'updated_at'>) {
      return ApiService.post('/api/appointments', appointment)
    },

    /**
     * 更新预约
     */
    async update(id: number, appointment: Partial<Appointment>) {
      return ApiService.put(`/api/appointments/${id}`, appointment)
    },

    /**
     * 取消预约
     */
    async cancel(id: number, reason?: string) {
      return ApiService.put(`/api/appointments/${id}/cancel`, { reason })
    }
  }

  /**
   * 就诊记录管理
   */
  static visits = {
    /**
     * 获取就诊记录列表
     */
    async getList(params?: {
      page?: number
      limit?: number
      patient_id?: number
      doctor_id?: number
      date_from?: string
      date_to?: string
    }) {
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.patient_id) queryParams.append('patient_id', params.patient_id.toString())
      if (params?.doctor_id) queryParams.append('doctor_id', params.doctor_id.toString())
      if (params?.date_from) queryParams.append('date_from', params.date_from)
      if (params?.date_to) queryParams.append('date_to', params.date_to)

      return ApiService.get(`/api/visits?${queryParams.toString()}`)
    },

    /**
     * 获取就诊记录详情
     */
    async getById(id: number) {
      return ApiService.get(`/api/visits/${id}`)
    },

    /**
     * 创建就诊记录
     */
    async create(visit: Omit<Visit, 'id' | 'created_at' | 'updated_at'>) {
      return ApiService.post('/api/visits', visit)
    },

    /**
     * 更新就诊记录
     */
    async update(id: number, visit: Partial<Visit>) {
      return ApiService.put(`/api/visits/${id}`, visit)
    }
  }

  /**
   * 映射数据管理
   */
  static mappings = {
    /**
     * 获取所有映射数据
     */
    async getAll() {
      return mappingApi.getAll()
    },

    /**
     * 获取性别映射
     */
    async getGenders() {
      return mappingApi.getGenders()
    },

    /**
     * 获取角色映射
     */
    async getRoles() {
      return mappingApi.getRoles()
    },

    /**
     * 获取科室映射
     */
    async getDepartments() {
      return mappingApi.getDepartments()
    },

    /**
     * 获取状态映射
     */
    async getStatuses(category: string) {
      return mappingApi.getStatuses(category)
    }
  }

  /**
   * 统计数据
   */
  static stats = {
    /**
     * 获取数据库统计
     */
    async getDatabaseStats() {
      return statsApi.getDatabaseStats()
    },

    /**
     * 获取工作台统计
     */
    async getDashboardStats() {
      // 这里可以调用专门的工作台统计接口
      return ApiService.get('/api/dashboard/stats')
    },

    /**
     * 获取今日统计
     */
    async getTodayStats() {
      return ApiService.get('/api/stats/today')
    }
  }

  /**
   * 系统管理
   */
  static system = {
    /**
     * 获取系统信息
     */
    async getInfo() {
      return ApiService.get('/')
    },

    /**
     * 健康检查
     */
    async healthCheck() {
      return ApiService.get('/health')
    }
  }
}

export default DataService
