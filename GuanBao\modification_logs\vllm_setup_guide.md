# vLLM 服务启动指南

## 概述
本指南帮助您启动vLLM服务，为前端提供API支持。

## 前提条件
- Python 3.8+
- CUDA 支持的GPU（推荐）
- 足够的显存（7B模型需要约14GB显存）

## 安装vLLM

### 1. 安装vLLM
```bash
# 使用pip安装
pip install vllm

# 或者使用conda
conda install -c conda-forge vllm
```

### 2. 验证安装
```bash
python -c "import vllm; print('vLLM安装成功')"
```

## 启动vLLM服务

### 1. 基本启动命令
```bash
python -m vllm.entrypoints.openai.api_server \
  --model GuanBao_BianCang_qwen2.5_7b_v0.0.1 \
  --host 0.0.0.0 \
  --port 8000 \
  --served-model-name GuanBao_BianCang_qwen2.5_7b_v0.0.1
```

### 2. 完整启动参数
```bash
python -m vllm.entrypoints.openai.api_server \
  --model G<PERSON><PERSON>ao_BianCang_qwen2.5_7b_v0.0.1 \
  --host 0.0.0.0 \
  --port 8000 \
  --served-model-name GuanBao_BianCang_qwen2.5_7b_v0.0.1 \
  --max-model-len 4096 \
  --tensor-parallel-size 1 \
  --gpu-memory-utilization 0.9 \
  --disable-log-requests
```

### 3. 参数说明
- `--model`: 模型路径或名称
- `--host`: 服务监听地址（0.0.0.0表示所有接口）
- `--port`: 服务端口（默认8000）
- `--served-model-name`: API中使用的模型名称
- `--max-model-len`: 最大序列长度
- `--tensor-parallel-size`: 张量并行大小
- `--gpu-memory-utilization`: GPU内存使用率
- `--disable-log-requests`: 禁用请求日志

## 验证服务

### 1. 检查服务状态
```bash
curl http://localhost:8000/health
```

### 2. 获取模型列表
```bash
curl http://localhost:8000/v1/models
```

### 3. 测试对话
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "GuanBao_BianCang_qwen2.5_7b_v0.0.1",
    "messages": [{"role": "user", "content": "你好"}],
    "stream": false
  }'
```

### 4. 测试流式对话
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "GuanBao_BianCang_qwen2.5_7b_v0.0.1",
    "messages": [{"role": "user", "content": "你好"}],
    "stream": true
  }'
```

## 常见问题

### 1. 模型加载失败
**问题**: 找不到模型文件
**解决**: 
- 确认模型路径正确
- 检查模型文件完整性
- 确保有足够的磁盘空间

### 2. 显存不足
**问题**: CUDA out of memory
**解决**:
- 降低 `--gpu-memory-utilization` 参数
- 减少 `--max-model-len` 参数
- 使用CPU推理（添加 `--device cpu`）

### 3. 端口被占用
**问题**: Address already in use
**解决**:
- 更改端口号（如 `--port 8001`）
- 杀死占用端口的进程
- 检查防火墙设置

### 4. 连接超时
**问题**: 前端无法连接到服务
**解决**:
- 检查服务是否正常启动
- 确认端口号匹配
- 检查防火墙和网络设置

## 性能优化

### 1. GPU优化
```bash
# 多GPU并行
--tensor-parallel-size 2

# 优化内存使用
--gpu-memory-utilization 0.8

# 启用KV缓存
--enable-prefix-caching
```

### 2. 推理优化
```bash
# 批处理大小
--max-num-batched-tokens 4096

# 序列长度
--max-model-len 2048

# 并发请求数
--max-num-seqs 256
```

## 监控和日志

### 1. 启用详细日志
```bash
--log-level debug
```

### 2. 监控GPU使用
```bash
# 实时监控
nvidia-smi -l 1

# 或使用htop监控CPU
htop
```

### 3. 性能指标
- 吞吐量（tokens/second）
- 延迟（首字符时间）
- GPU利用率
- 内存使用率

## Docker部署（可选）

### 1. Dockerfile示例
```dockerfile
FROM nvidia/cuda:11.8-devel-ubuntu20.04

RUN pip install vllm

EXPOSE 8000

CMD ["python", "-m", "vllm.entrypoints.openai.api_server", \
     "--model", "GuanBao_BianCang_qwen2.5_7b_v0.0.1", \
     "--host", "0.0.0.0", \
     "--port", "8000"]
```

### 2. 启动容器
```bash
docker run --gpus all -p 8000:8000 your-vllm-image
```

## 与前端集成

### 1. 确认配置匹配
- 服务地址: `http://localhost:8000`
- 模型名称: `GuanBao_BianCang_qwen2.5_7b_v0.0.1`
- API端点: `/v1/chat/completions`

### 2. 测试连接
1. 启动vLLM服务
2. 启动前端项目
3. 在前端点击调试按钮
4. 执行健康检查和连接测试

### 3. 开始对话
- 确认服务状态为"正常"
- 点击建议卡片或直接输入消息
- 观察流式响应效果

## 故障排除

### 1. 服务无法启动
- 检查Python环境和依赖
- 确认模型文件存在
- 查看错误日志

### 2. 响应缓慢
- 检查GPU利用率
- 调整批处理参数
- 优化模型配置

### 3. 前端连接失败
- 确认服务正在运行
- 检查端口和地址配置
- 查看浏览器控制台错误

## 总结

按照本指南启动vLLM服务后，前端应该能够正常连接并进行流式对话。如遇问题，请检查日志并参考故障排除部分。
