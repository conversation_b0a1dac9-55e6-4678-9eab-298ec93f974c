<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医问诊系统 - 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1, h2 {
            color: #333;
        }
        
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f9f9f9;
            border-left: 4px solid #4CAF50;
        }
        
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        
        .success {
            border-left-color: #4CAF50;
            background: #e8f5e8;
        }
        
        textarea, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 5px 0;
            box-sizing: border-box;
        }
        
        .chat-area {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
            margin-bottom: 10px;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }
        
        .user-message {
            background: #e3f2fd;
            text-align: right;
        }
        
        .assistant-message {
            background: #f1f8e9;
        }
        
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .back-button {
            background: #2196F3;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            margin-bottom: 20px;
            display: inline-block;
            transition: all 0.2s ease;
        }

        .back-button:hover {
            background: #1976D2;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <a href="/" class="back-button">← 返回主页</a>
    <h1>🏥 中医问诊系统 - 简化演示页面</h1>
    
    <!-- 连接测试 -->
    <div class="container">
        <h2>🔗 连接测试</h2>
        <button onclick="testConnection()">测试服务器连接</button>
        <button onclick="testHealth()">检查健康状态</button>
        <div id="connectionResult"></div>
    </div>
    
    <!-- 聊天测试 -->
    <div class="container">
        <h2>💬 聊天测试</h2>
        <div class="chat-area" id="chatArea">
            <div class="message assistant-message">
                <strong>系统:</strong> 聊天功能已准备就绪，请输入消息测试...
            </div>
        </div>
        <input type="text" id="messageInput" placeholder="输入您的消息..." onkeypress="handleKeyPress(event)">
        <button onclick="sendMessage()">发送消息</button>
        <button onclick="clearChat()">清空聊天</button>
    </div>
    
    <!-- 问诊测试 -->
    <div class="container">
        <h2>🚀 问诊测试</h2>
        <input type="text" id="patientName" placeholder="患者姓名" value="张三">
        <input type="number" id="patientAge" placeholder="年龄" value="30">
        <select id="patientGender">
            <option value="男">男</option>
            <option value="女">女</option>
        </select>
        <button onclick="startInquiry()">开始问诊</button>
        <div id="inquiryResult"></div>
    </div>

    <script>
        const API_BASE = '/api';

        // 全局会话变量
        window.currentSession = null;

        // 测试服务器连接
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="result">正在测试连接...</div>';
            
            try {
                console.log('测试连接到:', API_BASE);
                
                const response = await fetch(`${API_BASE}/`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                console.log('连接响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('连接响应数据:', data);
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>✅ 连接成功！</strong><br>
                            状态码: ${response.status}<br>
                            消息: ${data.message}<br>
                            版本: ${data.version}
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('连接错误:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ 连接失败</strong><br>
                        错误: ${error.message}<br>
                        请确保服务器运行在: ${API_BASE}
                    </div>
                `;
            }
        }
        
        // 测试健康状态
        async function testHealth() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="result">正在检查健康状态...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    const llmStatus = data.services?.llm || 'unknown';
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>✅ 健康检查完成</strong><br>
                            系统状态: ${data.status}<br>
                            LLM服务: ${llmStatus}<br>
                            检查时间: ${new Date().toLocaleString()}
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ 健康检查失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }
        
        // 发送聊天消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                alert('请输入消息');
                return;
            }
            
            const chatArea = document.getElementById('chatArea');
            
            // 显示用户消息
            chatArea.innerHTML += `
                <div class="message user-message">
                    <strong>用户:</strong> ${message}
                </div>
            `;
            
            // 显示加载状态
            chatArea.innerHTML += `
                <div class="message loading" id="loadingMessage">
                    正在处理您的消息...
                </div>
            `;
            
            // 清空输入框
            input.value = '';
            
            // 滚动到底部
            chatArea.scrollTop = chatArea.scrollHeight;
            
            try {
                console.log('发送聊天消息:', message);
                
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: window.currentSession
                    })
                });
                
                console.log('聊天响应状态:', response.status);
                
                // 移除加载消息
                const loadingMsg = document.getElementById('loadingMessage');
                if (loadingMsg) {
                    loadingMsg.remove();
                }
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('聊天响应数据:', data);
                    
                    chatArea.innerHTML += `
                        <div class="message assistant-message">
                            <strong>助手:</strong> ${data.response || '抱歉，没有收到有效响应'}
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('聊天错误:', error);
                
                // 移除加载消息
                const loadingMsg = document.getElementById('loadingMessage');
                if (loadingMsg) {
                    loadingMsg.remove();
                }
                
                chatArea.innerHTML += `
                    <div class="message assistant-message error">
                        <strong>错误:</strong> ${error.message}
                    </div>
                `;
            }
            
            // 滚动到底部
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // 清空聊天
        function clearChat() {
            document.getElementById('chatArea').innerHTML = `
                <div class="message assistant-message">
                    <strong>系统:</strong> 聊天记录已清空
                </div>
            `;
        }
        
        // 开始问诊
        async function startInquiry() {
            const name = document.getElementById('patientName').value;
            const age = document.getElementById('patientAge').value;
            const gender = document.getElementById('patientGender').value;
            
            if (!name || !age) {
                alert('请填写患者姓名和年龄');
                return;
            }
            
            const resultDiv = document.getElementById('inquiryResult');
            resultDiv.innerHTML = '<div class="result">正在启动问诊...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/inquiry/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        patient_name: name,
                        patient_age: parseInt(age),
                        patient_gender: gender
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    window.currentSession = data.session_id;  // 保存会话ID
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>✅ 问诊启动成功！(支持对话记忆)</strong><br>
                            会话ID: ${data.session_id}<br>
                            状态: ${data.status}<br>
                            消息: ${data.message}<br>
                            <small>现在可以在聊天区域进行对话，AI会记住之前的对话内容</small>
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ 问诊启动失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }
        
        // 页面加载时自动测试连接
        window.onload = function() {
            console.log('页面加载完成，自动测试连接...');
            testConnection();
        };
    </script>
</body>
</html>
