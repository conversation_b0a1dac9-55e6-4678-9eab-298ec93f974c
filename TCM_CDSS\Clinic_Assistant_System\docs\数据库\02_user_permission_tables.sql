-- =====================================================
-- 中医诊所助手系统 - 用户与权限组表结构
-- =====================================================
-- 文件: 02_user_permission_tables.sql
-- 描述: 创建用户权限相关表
-- 版本: v1.0
-- 创建日期: 2025-06-24
-- =====================================================

USE `tcm_clinic_system`;

-- =====================================================
-- 4.1 用户与权限组
-- =====================================================

-- 4.1.1 科室表 (需要先创建，因为staff表引用)
CREATE TABLE `staff_departments` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '科室ID',
    `name` VARCHAR(50) NOT NULL COMMENT '科室名称',
    `description` TEXT NULL COMMENT '科室描述',
    `head_id` INT NULL COMMENT '科室主任ID',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_departments_name` (`name`),
    KEY `idx_departments_head` (`head_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='科室表';

-- 4.1.2 角色表
CREATE TABLE `roles` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `display_name` VARCHAR(100) NOT NULL COMMENT '显示名称',
    `description` TEXT NULL COMMENT '角色描述',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_roles_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 4.1.3 权限表
CREATE TABLE `permissions` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `name` VARCHAR(100) NOT NULL COMMENT '权限标识',
    `display_name` VARCHAR(100) NOT NULL COMMENT '显示名称',
    `module` VARCHAR(50) NOT NULL COMMENT '所属模块',
    `description` TEXT NULL COMMENT '权限描述',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permissions_name` (`name`),
    KEY `idx_permissions_module` (`module`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 4.1.4 角色权限关联表
CREATE TABLE `role_permissions` (
    `role_id` INT NOT NULL COMMENT '角色ID',
    `permission_id` INT NOT NULL COMMENT '权限ID',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`role_id`, `permission_id`),
    KEY `idx_role_permissions_role` (`role_id`),
    KEY `idx_role_permissions_permission` (`permission_id`),
    CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_role_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 4.1.5 员工表
CREATE TABLE `staff` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '员工ID',
    `employee_number` VARCHAR(20) NOT NULL COMMENT '员工工号',
    `full_name` VARCHAR(100) NOT NULL COMMENT '员工姓名',
    `phone` VARCHAR(20) NOT NULL COMMENT '手机号（用于登录）',
    `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
    `role_id` INT NOT NULL COMMENT '角色ID',
    `department_id` INT NULL COMMENT '科室ID',
    `title` VARCHAR(50) NULL COMMENT '职称/职位',
    `specialty` TEXT NULL COMMENT '医师擅长领域',
    `avatar` VARCHAR(255) NULL COMMENT '头像URL',
    `email` VARCHAR(100) NULL COMMENT '邮箱',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `last_login_at` DATETIME NULL COMMENT '最后登录时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_staff_employee_number` (`employee_number`),
    UNIQUE KEY `uk_staff_phone` (`phone`),
    KEY `idx_staff_role` (`role_id`),
    KEY `idx_staff_department` (`department_id`),
    KEY `idx_staff_name` (`full_name`),
    CONSTRAINT `fk_staff_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
    CONSTRAINT `fk_staff_department` FOREIGN KEY (`department_id`) REFERENCES `staff_departments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 添加科室主任外键约束
ALTER TABLE `staff_departments` 
ADD CONSTRAINT `fk_departments_head` FOREIGN KEY (`head_id`) REFERENCES `staff` (`id`);

-- =====================================================
-- 初始化基础数据
-- =====================================================

-- 插入默认角色
INSERT INTO `roles` (`name`, `display_name`, `description`) VALUES
('admin', '管理员', '系统管理员，拥有所有权限'),
('doctor', '医生', '医生角色，拥有诊疗相关权限'),
('nurse', '护士', '护士角色，拥有护理相关权限'),
('pharmacist', '药师', '药师角色，拥有药房管理权限'),
('receptionist', '前台', '前台角色，拥有预约挂号权限');

-- 插入默认科室
INSERT INTO `staff_departments` (`name`, `description`) VALUES
('内科', '中医内科'),
('外科', '中医外科'),
('妇科', '中医妇科'),
('儿科', '中医儿科'),
('针灸科', '针灸推拿科'),
('药房', '中药房'),
('行政科', '行政管理科');

-- 插入系统权限
INSERT INTO `permissions` (`name`, `display_name`, `module`, `description`) VALUES
-- 患者管理权限
('patient:view', '查看患者', 'patients', '查看患者信息'),
('patient:create', '新增患者', 'patients', '创建新患者档案'),
('patient:edit', '编辑患者', 'patients', '编辑患者信息'),
('patient:delete', '删除患者', 'patients', '删除患者档案'),

-- 预约管理权限
('appointment:view', '查看预约', 'appointments', '查看预约信息'),
('appointment:create', '新增预约', 'appointments', '创建新预约'),
('appointment:edit', '编辑预约', 'appointments', '编辑预约信息'),
('appointment:cancel', '取消预约', 'appointments', '取消预约'),

-- 门诊工作站权限
('workstation:access', '门诊工作站', 'outpatient', '访问门诊工作站'),
('medical_record:create', '创建病历', 'emr', '创建电子病历'),
('medical_record:edit', '编辑病历', 'emr', '编辑电子病历'),
('prescription:create', '开具处方', 'pharmacy', '开具处方'),

-- 收费管理权限
('billing:view', '查看账单', 'billing', '查看收费账单'),
('billing:create', '创建账单', 'billing', '创建收费账单'),
('billing:payment', '收费操作', 'billing', '执行收费操作'),

-- 药房管理权限
('pharmacy:view', '查看处方', 'pharmacy', '查看处方信息'),
('pharmacy:dispense', '发药操作', 'pharmacy', '执行发药操作'),
('inventory:manage', '库存管理', 'inventory', '管理药品库存'),

-- 员工管理权限
('staff:view', '查看员工', 'staff', '查看员工信息'),
('staff:create', '新增员工', 'staff', '创建员工档案'),
('staff:edit', '编辑员工', 'staff', '编辑员工信息'),
('staff:delete', '删除员工', 'staff', '删除员工档案'),

-- 排班管理权限
('schedule:view', '查看排班', 'scheduling', '查看排班信息'),
('schedule:manage', '管理排班', 'scheduling', '管理排班安排'),

-- 统计报表权限
('report:view', '查看报表', 'reports', '查看统计报表'),
('report:export', '导出报表', 'reports', '导出统计数据'),

-- 系统设置权限
('system:settings', '系统设置', 'settings', '管理系统设置'),
('system:backup', '数据备份', 'settings', '执行数据备份'),

-- 研学中心权限
('research:view', '查看研学', 'research-center', '查看研学内容'),
('research:manage', '管理研学', 'research-center', '管理研学内容'),

-- 应用中心权限
('app:view', '查看应用', 'app-center', '查看应用列表'),
('app:download', '下载应用', 'app-center', '下载应用'),

-- 商城管理权限
('mall:view', '查看商城', 'mall-management', '查看商城商品'),
('mall:manage', '管理商城', 'mall-management', '管理商城商品'),
('mall:order', '订单管理', 'mall-management', '管理商城订单');

-- 分配管理员所有权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 1, `id` FROM `permissions`;

-- 分配医生基本权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 2, `id` FROM `permissions` 
WHERE `name` IN (
    'patient:view', 'patient:create', 'patient:edit',
    'appointment:view', 'appointment:create', 'appointment:edit',
    'workstation:access', 'medical_record:create', 'medical_record:edit',
    'prescription:create', 'billing:view',
    'research:view', 'app:view', 'app:download'
);

-- 分配前台基本权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 5, `id` FROM `permissions` 
WHERE `name` IN (
    'patient:view', 'patient:create', 'patient:edit',
    'appointment:view', 'appointment:create', 'appointment:edit', 'appointment:cancel',
    'billing:view', 'billing:create', 'billing:payment'
);

-- 分配药师基本权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 4, `id` FROM `permissions` 
WHERE `name` IN (
    'pharmacy:view', 'pharmacy:dispense', 'inventory:manage',
    'patient:view', 'prescription:create'
);
