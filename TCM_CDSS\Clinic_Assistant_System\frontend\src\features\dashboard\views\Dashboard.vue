<template>
  <div class="dashboard-container">
    <!-- 顶部状态栏 -->
    <header class="top-bar">
      <div class="clinic-info">
        <div class="clinic-logo-title">
          <img src="@/assets/logo.jpg" alt="Clinic Logo" class="clinic-logo" />
          <div class="clinic-title-info">
            <h1>观建在-智慧中医诊所</h1>
            <div class="user-role-info">
              <el-tag size="small" :type="getRoleTagType(currentRole)">
                {{ getRoleDisplayName(currentRole) }}
              </el-tag>
              <span class="module-count">{{ availableModules.length }} 个可用功能</span>
            </div>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <!-- 布局预设选择器 -->
        <el-dropdown
          v-if="!isEditMode"
          @command="handlePresetCommand"
          class="layout-preset-dropdown"
        >
          <el-button size="small" type="text">
            <el-icon><Grid /></el-icon>
            预设
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="preset in layoutPresets"
                :key="preset.id"
                :command="preset.id"
                :class="{ 'is-active': currentPresetId === preset.id }"
              >
                <div class="preset-item">
                  <span class="preset-name">{{ preset.name }}</span>
                  <span class="preset-desc">{{ preset.description }}</span>
                </div>
              </el-dropdown-item>
              <el-dropdown-item divided command="reset">
                <el-icon><Refresh /></el-icon>
                重置为默认
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 编辑模式切换按钮 -->
        <el-button
          :type="isEditMode ? 'primary' : 'default'"
          size="small"
          @click="toggleEditMode"
          class="edit-mode-btn"
        >
          <el-icon><Edit /></el-icon>
          {{ isEditMode ? '完成' : '编辑' }}
        </el-button>

        <div class="user-profile" @click="showUserMenu = !showUserMenu">
          <div class="avatar">{{ user?.full_name?.charAt(0) || user?.name?.charAt(0) || '管' }}</div>
        </div>
      </div>

    </header>

    <!-- 功能卡片网格 -->
    <main class="function-grid" :class="{ 'edit-mode': isEditMode }">
      <!-- 编辑模式提示 -->
      <div v-if="isEditMode" class="edit-mode-tip">
        <el-icon><InfoFilled /></el-icon>
        <span>拖拽卡片可调整位置，点击"完成编辑"保存布局</span>
      </div>

      <div
        v-for="(module, index) in sortedModules"
        :key="module.key"
        class="function-card"
        :class="{
          'edit-mode': isEditMode,
          'dragging': draggedIndex === index
        }"
        :draggable="isEditMode"
        @click="handleCardClick(module.route, $event)"
        @dragstart="handleDragStart(index, $event)"
        @dragover="handleDragOver($event)"
        @drop="handleDrop(index, $event)"
        @dragend="handleDragEnd"
      >
        <!-- 编辑模式下的拖拽指示器 -->
        <div v-if="isEditMode" class="drag-indicator">
          <el-icon><Rank /></el-icon>
        </div>
        <!-- 默认垂直布局 -->
        <div class="card-content-vertical">
          <div class="card-icon">
            <el-icon :size="32" :color="module.color">
              <component :is="module.icon" />
            </el-icon>
          </div>
          <div class="card-title">{{ module.name }}</div>
          <div class="card-info">{{ typeof module.info === 'function' ? module.info() : module.info }}</div>
        </div>
        
        <!-- 悬停时的水平布局 -->
        <div class="card-content-horizontal">
          <div class="card-icon">
            <el-icon :size="32" :color="module.color">
              <component :is="module.icon" />
            </el-icon>
          </div>
          <div class="card-text">
            <div class="card-title">{{ module.name }}</div>
            <div class="card-info">{{ typeof module.info === 'function' ? module.info() : module.info }}</div>
          </div>
        </div>
        


        <!-- 患者管理卡片的详细统计信息 -->
        <div v-if="module.key === 'patients'" class="patients-stats-detail">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">患者总数</span>
              <span class="stat-value total">{{ patientStats.total || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">活跃患者</span>
              <span class="stat-value active">{{ patientStats.active || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">男性患者</span>
              <span class="stat-value male">{{ patientStats.male || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">女性患者</span>
              <span class="stat-value female">{{ patientStats.female || 0 }}</span>
            </div>
          </div>
          <div class="age-breakdown">
            <div class="age-item">
              <span class="age-label">儿童(0-18)</span>
              <span class="age-value">{{ (patientStats.ageGroups?.child || 0) }}人</span>
            </div>
            <div class="age-item">
              <span class="age-label">成人(19-59)</span>
              <span class="age-value">{{ (patientStats.ageGroups?.adult || 0) }}人</span>
            </div>
            <div class="age-item">
              <span class="age-label">老年(60+)</span>
              <span class="age-value">{{ (patientStats.ageGroups?.senior || 0) }}人</span>
            </div>
            <div class="age-item">
              <span class="age-label">有预约</span>
              <span class="age-value">{{ patientStats.withAppointment || 0 }}人</span>
            </div>
          </div>
        </div>

        <!-- 门诊工作站卡片的详细统计信息 -->
        <div v-if="module.key === 'workstation'" class="workstation-stats-detail">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">等待接诊</span>
              <span class="stat-value waiting">{{ workstationStats.waiting || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">正在接诊</span>
              <span class="stat-value progress">{{ workstationStats.inProgress || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已完成</span>
              <span class="stat-value completed">{{ workstationStats.completed || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">未收费</span>
              <span class="stat-value unpaid">{{ workstationStats.unpaid || 0 }}</span>
            </div>
          </div>
          <div class="summary-breakdown">
            <div class="summary-item">
              <span class="summary-label">今日收入</span>
              <span class="summary-value income">¥{{ workstationStats.todayIncome || '0' }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">平均时长</span>
              <span class="summary-value duration">{{ workstationStats.avgDuration || 0 }}分钟</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">回诊患者</span>
              <span class="summary-value return">{{ workstationStats.returnVisit || 0 }}人</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">未接诊</span>
              <span class="summary-value not-admitted">{{ workstationStats.notAdmitted || 0 }}人</span>
            </div>
          </div>
        </div>

        <!-- 应用中心卡片的详细统计信息 -->
        <div v-if="module.key === 'app-center'" class="app-center-stats-detail">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">应用总数</span>
              <span class="stat-value total">{{ appCenterStats.totalApps || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已安装</span>
              <span class="stat-value installed">{{ appCenterStats.installedApps || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">可更新</span>
              <span class="stat-value updates">{{ appCenterStats.availableUpdates || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">今日下载</span>
              <span class="stat-value downloads">{{ appCenterStats.downloadToday || 0 }}</span>
            </div>
          </div>
          <div class="category-breakdown">
            <div class="category-item">
              <span class="category-label">医疗工具</span>
              <span class="category-value">{{ (appCenterStats.categories?.medical || 0) }}个</span>
            </div>
            <div class="category-item">
              <span class="category-label">管理系统</span>
              <span class="category-value">{{ (appCenterStats.categories?.management || 0) }}个</span>
            </div>
            <div class="category-item">
              <span class="category-label">数据分析</span>
              <span class="category-value">{{ (appCenterStats.categories?.analytics || 0) }}个</span>
            </div>
            <div class="category-item">
              <span class="category-label">教育培训</span>
              <span class="category-value">{{ (appCenterStats.categories?.education || 0) }}个</span>
            </div>
          </div>
        </div>

        <!-- 商城管理卡片的详细统计信息 -->
        <div v-if="module.key === 'mall-management'" class="mall-management-stats-detail">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">商品总数</span>
              <span class="stat-value total">{{ mallManagementStats.totalProducts || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">今日订单</span>
              <span class="stat-value orders">{{ mallManagementStats.todayOrders || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">月销售额</span>
              <span class="stat-value sales">¥{{ ((mallManagementStats.monthlySales || 0) / 1000).toFixed(1) }}K</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">活跃用户</span>
              <span class="stat-value users">{{ mallManagementStats.activeUsers || 0 }}</span>
            </div>
          </div>
          <div class="order-status-breakdown">
            <div class="status-item">
              <span class="status-label">待处理</span>
              <span class="status-value pending">{{ (mallManagementStats.orderStatus?.pending || 0) }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">处理中</span>
              <span class="status-value processing">{{ (mallManagementStats.orderStatus?.processing || 0) }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">已发货</span>
              <span class="status-value shipped">{{ (mallManagementStats.orderStatus?.shipped || 0) }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">已送达</span>
              <span class="status-value delivered">{{ (mallManagementStats.orderStatus?.delivered || 0) }}</span>
            </div>
          </div>
        </div>

        <!-- 数据中心卡片的详细统计信息 -->
        <div v-if="module.key === 'data-center'" class="data-center-stats-detail">
          <DetailStats />
        </div>
      </div>
    </main>

    <!-- 用户菜单 -->
    <teleport to="body">
      <transition name="fade">
        <div v-if="showUserMenu" class="user-menu-overlay" @click="closeUserMenu">
          <div class="user-menu" @click.stop>
            <div class="menu-item" @click="goToProfile">
              <el-icon><User /></el-icon>
              <span>个人中心</span>
            </div>
            <div class="menu-item" @click="goToChangePassword">
              <el-icon><EditPen /></el-icon>
              <span>修改密码</span>
            </div>
            <div v-if="user?.role_name === 'admin' || user?.role === 'admin'" class="menu-item" @click="promptForAdminPassword">
              <el-icon><Setting /></el-icon>
              <span>系统设置</span>
            </div>
            <div class="menu-item" @click="handleLogout">
              <el-icon><SwitchButton /></el-icon>
              <span>退出登录</span>
            </div>
          </div>
        </div>
      </transition>
    </teleport>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { SwitchButton, EditPen, User, Setting, Edit, InfoFilled, Rank, Grid, ArrowDown, Refresh } from '@element-plus/icons-vue'
import { useAuthStore } from "@/core/stores/auth"
import { usePermissions } from "@/shared/composables/usePermissions"
import { patientService } from "@/mock/database/patients"
import type { Patient } from "@/shared/types"
import { allCardModules } from "@/modules/cards"
import { dataCenterStats } from "@/modules/cards/data-center"
import DetailStats from "@/modules/cards/data-center/DetailStats.vue"
import { layoutService } from "@/shared/services/layoutService"

const router = useRouter()
const authStore = useAuthStore()
const { getAccessibleModules, currentRole } = usePermissions()

const showUserMenu = ref(false)
const user = computed(() => authStore.user)

// 编辑模式相关状态
const isEditMode = ref(false)
const draggedIndex = ref<number | null>(null)
const moduleOrder = ref<string[]>([]) // 存储模块顺序

// 布局预设相关状态
const layoutPresets = ref(layoutService.getPresets())
const currentPresetId = ref('default')

// 患者数据
const patients = ref<Patient[]>([])

// 获取患者数据
const fetchPatients = async () => {
  try {
    const data = await patientService.getAllPatients()
    patients.value = data
  } catch (error) {
    console.error('获取患者数据失败:', error)
  }
}

// 患者统计信息
const patientStats = computed(() => {
  if (patients.value.length === 0) {
    return {
      total: 0,
      active: 0,
      inactive: 0,
      male: 0,
      female: 0,
      withAppointment: 0,
      withoutAppointment: 0,
      avgVisits: 0,
      ageGroups: {
        child: 0,  // 0-18岁
        adult: 0,  // 19-59岁
        senior: 0  // 60岁以上
      }
    }
  }

  const total = patients.value.length
  const active = patients.value.filter(p => p.status === 'active').length
  const inactive = total - active
  const male = patients.value.filter(p => p.gender === '男').length
  const female = total - male
  const withAppointment = patients.value.filter(p => p.nextAppointment).length
  const withoutAppointment = total - withAppointment
  
  // 计算平均就诊次数
  const totalVisits = patients.value.reduce((sum, p) => sum + (p.visitCount || 0), 0)
  const avgVisits = total > 0 ? Math.round(totalVisits / total) : 0
  
  // 年龄分组统计
  const ageGroups = {
    child: patients.value.filter(p => p.age >= 0 && p.age <= 18).length,
    adult: patients.value.filter(p => p.age >= 19 && p.age <= 59).length,
    senior: patients.value.filter(p => p.age >= 60).length
  }

  return {
    total,
    active,
    inactive,
    male,
    female,
    withAppointment,
    withoutAppointment,
    avgVisits,
    ageGroups
  }
})

// 患者管理卡片信息
const patientCardInfo = computed(() => {
  const stats = patientStats.value
  if (stats.total === 0) return '加载中...'

  return `${stats.total}位患者 | 活跃${stats.active}人 | 男${stats.male}女${stats.female} | 预约${stats.withAppointment}人`
})

// 门诊工作站统计信息
const workstationStats = computed(() => {
  // 模拟门诊工作站统计数据
  const today = new Date().toISOString().split('T')[0] // 获取今天的日期

  // 模拟各种状态的统计数据
  const waiting = Math.floor(Math.random() * 10) + 5 // 5-15个待接诊
  const completed = Math.floor(Math.random() * 8) + 3 // 3-11个已接诊
  const notAdmitted = Math.floor(Math.random() * 5) + 2 // 2-7个未接诊
  const unpaid = Math.floor(Math.random() * 6) + 1 // 1-7个未收费
  const inProgress = Math.floor(Math.random() * 3) + 1 // 1-4个诊中
  const returnVisit = Math.floor(Math.random() * 4) + 1 // 1-5个回诊

  // 模拟今日收入和平均就诊时长
  const todayIncome = (Math.floor(Math.random() * 5000) + 2000).toLocaleString() // 2000-7000元
  const avgDuration = Math.floor(Math.random() * 20) + 15 // 15-35分钟

  return {
    waiting,
    completed,
    notAdmitted,
    unpaid,
    inProgress,
    returnVisit,
    todayIncome,
    avgDuration
  }
})

const workstationCardInfo = computed(() => {
  return `${workstationStats.value.waiting} 个待接诊`
})

// 数据中心卡片信息
const dataCenterCardInfo = computed(() => {
  const stats = dataCenterStats.value.todayStats
  return `今日${stats.totalPatients}人次就诊，收入¥${stats.totalRevenue.toLocaleString()}`
})

// 应用中心统计数据
const appCenterStats = ref({
  totalApps: 24,
  installedApps: 8,
  availableUpdates: 3,
  downloadToday: 5,
  categories: {
    medical: 8,
    management: 6,
    analytics: 4,
    education: 6
  }
})

// 商城管理统计数据
const mallManagementStats = ref({
  totalProducts: 156,
  todayOrders: 23,
  monthlySales: 89650,
  activeUsers: 1234,
  categories: {
    herbs: 89,
    medicine: 45,
    equipment: 22
  },
  orderStatus: {
    pending: 8,
    processing: 12,
    shipped: 15,
    delivered: 18
  }
})

// 使用模块化的卡片配置，并添加动态信息
const allModules = computed(() => {
  return allCardModules.map(module => {
    // 为特定模块添加动态信息
    switch (module.key) {
      case 'patients':
        return {
          ...module,
          info: () => patientCardInfo.value
        }
      case 'workstation':
        return {
          ...module,
          info: () => workstationCardInfo.value
        }
      case 'data-center':
        return {
          ...module,
          info: () => dataCenterCardInfo.value
        }
      default:
        return module
    }
  })
})

// 根据用户权限过滤可用模块
const availableModules = computed(() => {
  return getAccessibleModules(allModules.value)
})

// 根据用户自定义顺序排序的模块
const sortedModules = computed(() => {
  const modules = availableModules.value

  if (moduleOrder.value.length === 0) {
    // 如果没有自定义顺序，使用默认顺序
    return modules
  }

  // 按照自定义顺序排序
  const sortedList = []
  const moduleMap = new Map(modules.map(m => [m.key, m]))

  // 先添加已排序的模块
  for (const key of moduleOrder.value) {
    if (moduleMap.has(key)) {
      sortedList.push(moduleMap.get(key)!)
      moduleMap.delete(key)
    }
  }

  // 添加新增的模块（不在排序列表中的）
  sortedList.push(...Array.from(moduleMap.values()))

  return sortedList
})

// 保存布局到本地存储
const saveLayout = () => {
  const userId = user.value?.id || 'default'
  const success = layoutService.saveLayout(moduleOrder.value, userId)

  if (success) {
    console.log('布局已保存')
  } else {
    console.error('保存布局失败')
  }
}

// 从本地存储加载布局
const loadLayout = () => {
  const userId = user.value?.id || 'default'
  const savedOrder = layoutService.loadLayout(userId)

  if (savedOrder.length > 0) {
    moduleOrder.value = savedOrder
    console.log('布局已加载')
  } else {
    // 如果没有保存的布局，使用默认顺序
    moduleOrder.value = availableModules.value.map(m => m.key)
  }
}

// 重置布局为默认顺序
const resetLayout = () => {
  const userId = user.value?.id || 'default'
  const success = layoutService.resetToDefault(userId)

  if (success) {
    moduleOrder.value = layoutService.getDefaultLayout()
    console.log('布局已重置为默认')
  }
}

// 页面加载时获取患者数据
onMounted(() => {
  fetchPatients()
  // 初始化模块化统计数据
  initializeModuleStats()
  // 加载用户自定义布局
  loadLayout()
})

// 初始化模块统计数据
const initializeModuleStats = () => {
  // 导入并初始化各模块的统计数据
  import("@/features/dashboard/constants/patients").then(({ updatePatientsStats }) => {
    // 更新患者统计
    const stats = patientStats.value
    updatePatientsStats && updatePatientsStats(stats)
  })

  import("@/features/dashboard/constants/workstation").then(({ calculateWorkstationStats }) => {
    // 计算门诊工作站统计
    calculateWorkstationStats && calculateWorkstationStats(patients.value)
  })
}

const navigateToModule = (route: string) => {
  router.push(route).catch((error) => {
    console.error('跳转失败:', error)
  })
}

// 处理卡片点击事件
const handleCardClick = (route: string, event: Event) => {
  if (isEditMode.value) {
    // 编辑模式下阻止导航
    event.preventDefault()
    event.stopPropagation()
    return
  }
  navigateToModule(route)
}

// 切换编辑模式
const toggleEditMode = () => {
  isEditMode.value = !isEditMode.value

  if (!isEditMode.value) {
    // 退出编辑模式时保存布局
    saveLayout()
  } else {
    // 进入编辑模式时初始化顺序
    if (moduleOrder.value.length === 0) {
      moduleOrder.value = availableModules.value.map(m => m.key)
    }
  }
}

// 拖拽开始
const handleDragStart = (index: number, event: DragEvent) => {
  if (!isEditMode.value) return

  draggedIndex.value = index
  event.dataTransfer!.effectAllowed = 'move'
  event.dataTransfer!.setData('text/html', index.toString())
}

// 拖拽悬停
const handleDragOver = (event: DragEvent) => {
  if (!isEditMode.value) return

  event.preventDefault()
  event.dataTransfer!.dropEffect = 'move'
}

// 拖拽放置
const handleDrop = (targetIndex: number, event: DragEvent) => {
  if (!isEditMode.value || draggedIndex.value === null) return

  event.preventDefault()

  const sourceIndex = draggedIndex.value
  if (sourceIndex === targetIndex) return

  // 重新排列模块顺序
  const newOrder = [...moduleOrder.value]
  const [movedItem] = newOrder.splice(sourceIndex, 1)
  newOrder.splice(targetIndex, 0, movedItem)

  moduleOrder.value = newOrder
}

// 拖拽结束
const handleDragEnd = () => {
  draggedIndex.value = null
}

// 处理预设命令
const handlePresetCommand = (command: string) => {
  const userId = user.value?.id || 'default'

  if (command === 'reset') {
    resetLayout()
    currentPresetId.value = 'default'
    return
  }

  const success = layoutService.applyPreset(command, userId)
  if (success) {
    moduleOrder.value = layoutService.loadLayout(userId)
    currentPresetId.value = command
    console.log('预设已应用:', command)
  } else {
    console.error('应用预设失败:', command)
  }
}

const closeUserMenu = () => {
  showUserMenu.value = false
}

const goToProfile = () => {
  showUserMenu.value = false
  // 跳转到个人中心
}

const goToChangePassword = () => {
  showUserMenu.value = false
  // 跳转到修改密码页面
}

const promptForAdminPassword = () => {
  showUserMenu.value = false
    router.push('/settings')
}

const handleLogout = async () => {
  showUserMenu.value = false
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await authStore.logout()
    // 强制跳转到登录页面
    await router.replace('/login')
  } catch {
    // 用户取消
  }
}

// 获取角色显示名称
const getRoleDisplayName = (role: string) => {
  const roleMap: Record<string, string> = {
    'admin': '系统管理员',
    'doctor': '医师',
    'nurse': '护士',
    'pharmacist': '药师',
    'receptionist': '前台'
  }
  return roleMap[role] || '未知角色'
}

// 获取角色标签类型
const getRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    'admin': 'danger',
    'doctor': 'success',
    'nurse': 'primary',
    'pharmacist': 'warning',
    'receptionist': 'info'
  }
  return typeMap[role] || 'info'
}
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  /* 背景图片和遮罩层现在由全局样式提供 */
}

.top-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: var(--spacing-sm) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  min-height: 48px;
  position: relative;
  z-index: 1;
}

.clinic-logo-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.clinic-logo {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  object-fit: cover;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.clinic-title-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.clinic-info h1 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.user-role-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-count {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.user-profile {
  position: relative;
  cursor: pointer;
  z-index: 100000;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-md);
}

.user-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999;
  pointer-events: auto;
}

.user-menu {
  position: absolute;
  top: 60px;
  right: 24px;
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid var(--border-color);
  min-width: 160px;
  z-index: 1000000;
}

.menu-item {
  padding: var(--spacing-xs) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  transition: background-color var(--transition-fast);
}

.menu-item:hover {
  background: var(--background-color);
}

.menu-item:first-child {
  border-radius: var(--border-radius-medium) var(--border-radius-medium) 0 0;
}

.menu-item:last-child {
  border-radius: 0 0 var(--border-radius-medium) var(--border-radius-medium);
}

.function-grid {
  flex: 1;
  padding: var(--spacing-lg) var(--spacing-xl);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  grid-auto-rows: min-content;
  gap: var(--spacing-lg);
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

/* 限制每行最多8个卡片 */
@media (min-width: 1800px) {
  .function-grid {
    grid-template-columns: repeat(8, minmax(220px, 1fr));
  }
}

.function-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid transparent;
  aspect-ratio: 3/4;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.function-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.card-content-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  transition: all var(--transition-normal);
}

.card-content-horizontal {
  display: none;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  padding-top: var(--spacing-xs);
  transition: all var(--transition-normal);
}

.function-card:hover .card-content-vertical {
  display: none;
}

.function-card:hover .card-content-horizontal {
  display: flex;
}

.card-icon {
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(0, 122, 255, 0.1);
  border-radius: var(--border-radius-medium);
  flex-shrink: 0;
}

.card-content-horizontal .card-icon {
  margin-bottom: 0;
  width: 40px;
  height: 40px;
}

.card-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.2;
}

.card-content-horizontal .card-title {
  font-size: var(--font-size-sm);
  margin-bottom: 1px;
}

.card-info {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.2;
}

.card-content-horizontal .card-info {
  font-size: 10px;
  line-height: 1.1;
}

.card-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 0;
  margin-left: var(--spacing-sm);
  padding-top: 2px;
}

.patient-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(52, 199, 89, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(52, 199, 89, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .patient-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.workstation-stats-detail .stats-grid {
  grid-template-columns: repeat(3, 1fr);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding-top: var(--spacing-xs);
}

.stat-label {
  font-size: 10px;
  color: var(--text-secondary);
  margin-bottom: 1px;
}

.stat-value {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-value.active {
  color: #34C759;
}

.stat-value.inactive {
  color: #8E8E93;
}

.stat-value.male {
  color: #007AFF;
}

.stat-value.female {
  color: #FF2D92;
}

.stat-value.appointment {
  color: #FF9500;
}

.stat-value.no-appointment {
  color: #8E8E93;
}

.stat-value.visits {
  color: #5856D6;
}

.stat-value.waiting {
  color: #FF9500;
}

.stat-value.completed {
  color: #34C759;
}

.stat-value.not-admitted {
  color: #8E8E93;
}

.stat-value.unpaid {
  color: #FF3B30;
}

.stat-value.in-progress {
  color: #5856D6;
}

.stat-value.return {
  color: #FF2D92;
}

.age-distribution {
  display: flex;
  justify-content: space-between;
  padding-top: var(--spacing-xs);
  border-top: 1px solid rgba(52, 199, 89, 0.1);
}

.age-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding-top: var(--spacing-sm);
  flex: 1;
}

.age-label {
  font-size: 10px;
  color: var(--text-secondary);
  margin-bottom: 1px;
}

.age-value {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
}

.workstation-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(88, 86, 214, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(88, 86, 214, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .workstation-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.workstation-summary {
  display: flex;
  justify-content: space-between;
  padding-top: var(--spacing-xs);
  border-top: 1px solid rgba(52, 199, 89, 0.1);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding-top: var(--spacing-sm);
  flex: 1;
}

.summary-label {
  font-size: 10px;
  color: var(--text-secondary);
  margin-bottom: 1px;
}

.summary-value {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
}

.summary-value.income {
  color: #FF9500;
}

.summary-value.duration {
  color: #5856D6;
}

/* 患者管理统计详情样式 */
.patients-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(52, 199, 89, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(52, 199, 89, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .patients-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.patients-stats-detail .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.patients-stats-detail .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.patients-stats-detail .stat-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.patients-stats-detail .stat-value {
  font-size: 12px;
  font-weight: 600;
}

.patients-stats-detail .stat-value.total {
  color: #34C759;
}

.patients-stats-detail .stat-value.active {
  color: #007AFF;
}

.patients-stats-detail .stat-value.male {
  color: #5856D6;
}

.patients-stats-detail .stat-value.female {
  color: #FF3B30;
}

.age-breakdown {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.age-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.age-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.age-value {
  font-size: 11px;
  font-weight: 600;
  color: #34C759;
}

/* 门诊工作站统计详情样式 */
.workstation-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(88, 86, 214, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(88, 86, 214, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .workstation-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.workstation-stats-detail .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.workstation-stats-detail .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.workstation-stats-detail .stat-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.workstation-stats-detail .stat-value {
  font-size: 12px;
  font-weight: 600;
}

.workstation-stats-detail .stat-value.waiting {
  color: #007AFF;
}

.workstation-stats-detail .stat-value.progress {
  color: #34C759;
}

.workstation-stats-detail .stat-value.completed {
  color: #5856D6;
}

.workstation-stats-detail .stat-value.unpaid {
  color: #FF9500;
}

.summary-breakdown {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.summary-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.workstation-stats-detail .summary-value {
  font-size: 11px;
  font-weight: 600;
}

.workstation-stats-detail .summary-value.income {
  color: #34C759;
}

.workstation-stats-detail .summary-value.duration {
  color: #5856D6;
}

.workstation-stats-detail .summary-value.return {
  color: #FF9500;
}

.workstation-stats-detail .summary-value.not-admitted {
  color: #FF3B30;
}

/* 应用中心统计详情样式 */
.app-center-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(0, 122, 255, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(0, 122, 255, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .app-center-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.app-center-stats-detail .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.category-breakdown {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.category-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.category-value {
  font-size: 11px;
  font-weight: 600;
  color: #007AFF;
}

/* 商城管理统计详情样式 */
.mall-management-stats-detail {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(255, 107, 53, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(255, 107, 53, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 15px;
  min-height: 180px;
}

.function-card:hover .mall-management-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.mall-management-stats-detail .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.order-status-breakdown {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.status-label {
  font-size: 10px;
  color: var(--text-secondary);
}

.status-value {
  font-size: 11px;
  font-weight: 600;
}

.status-value.pending {
  color: #FF9500;
}

.status-value.processing {
  color: #007AFF;
}

.status-value.shipped {
  color: #5856D6;
}

.status-value.delivered {
  color: #34C759;
}

/* 应用中心统计值颜色 */
.app-center-stats-detail .stat-value.total {
  color: #007AFF;
}

.app-center-stats-detail .stat-value.installed {
  color: #34C759;
}

.app-center-stats-detail .stat-value.updates {
  color: #FF9500;
}

.app-center-stats-detail .stat-value.downloads {
  color: #5856D6;
}

/* 商城管理统计值颜色 */
.mall-management-stats-detail .stat-value.total {
  color: #FF6B35;
}

.mall-management-stats-detail .stat-value.orders {
  color: #007AFF;
}

.mall-management-stats-detail .stat-value.sales {
  color: #34C759;
}

.mall-management-stats-detail .stat-value.users {
  color: #5856D6;
}

/* 数据中心统计信息样式 */
.data-center-stats-detail {
  margin-top: var(--spacing-xs);
  padding: 8px;
  background: rgba(114, 46, 209, 0.05);
  border-radius: var(--border-radius-medium);
  border: 1px solid rgba(114, 46, 209, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 12px;
  max-height: 160px;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}

.function-card:hover .data-center-stats-detail {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* 确保数据中心统计信息不超出卡片边界 */
.data-center-stats-detail .data-center-stats {
  max-height: 140px;
  overflow-y: auto;
  padding: 0;
  margin: 0;
  box-shadow: none;
  background: transparent;
  border-radius: 0;
}

/* 滚动条样式优化 */
.data-center-stats-detail .data-center-stats::-webkit-scrollbar {
  width: 4px;
}

.data-center-stats-detail .data-center-stats::-webkit-scrollbar-track {
  background: rgba(114, 46, 209, 0.1);
  border-radius: 2px;
}

.data-center-stats-detail .data-center-stats::-webkit-scrollbar-thumb {
  background: rgba(114, 46, 209, 0.3);
  border-radius: 2px;
}

.data-center-stats-detail .data-center-stats::-webkit-scrollbar-thumb:hover {
  background: rgba(114, 46, 209, 0.5);
}

/* 编辑模式样式 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 布局预设下拉菜单样式 */
.layout-preset-dropdown {
  margin-right: 8px;
}

.layout-preset-dropdown .el-button {
  color: #606266;
  border: 1px solid #dcdfe6;
  background: white;
  transition: all 0.3s ease;
}

.layout-preset-dropdown .el-button:hover {
  color: #1890ff;
  border-color: #1890ff;
  background: #f0f9ff;
}

.preset-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 4px 0;
}

.preset-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.preset-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.2;
}

.el-dropdown-menu__item.is-active {
  background: #f0f9ff;
  color: #1890ff;
}

.el-dropdown-menu__item.is-active .preset-name {
  color: #1890ff;
}

.edit-mode-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.edit-mode-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.function-grid.edit-mode {
  padding-top: 60px; /* 为编辑提示留出空间 */
}

.edit-mode-tip {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.function-card.edit-mode {
  cursor: grab;
  position: relative;
  transition: all 0.3s ease;
  border: 2px dashed transparent;
}

.function-card.edit-mode:hover {
  border-color: #1890ff;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.2);
}

.function-card.dragging {
  opacity: 0.6;
  transform: rotate(5deg) scale(1.05);
  cursor: grabbing;
  z-index: 1000;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.drag-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 12px;
  opacity: 0;
  transition: all 0.3s ease;
}

.function-card.edit-mode .drag-indicator {
  opacity: 1;
}

.function-card.edit-mode:hover .drag-indicator {
  background: rgba(24, 144, 255, 0.2);
  transform: scale(1.1);
}

/* 编辑模式下禁用卡片悬停效果 */
.function-card.edit-mode:hover .patients-stats-detail,
.function-card.edit-mode:hover .workstation-stats-detail,
.function-card.edit-mode:hover .app-center-stats-detail,
.function-card.edit-mode:hover .mall-management-stats-detail,
.function-card.edit-mode:hover .data-center-stats-detail {
  opacity: 0;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .edit-mode-tip {
    font-size: 12px;
    padding: 10px 16px;
    left: 20px;
    right: 20px;
    transform: none;
  }

  .edit-mode-btn {
    font-size: 12px;
    padding: 6px 12px;
  }

  .function-grid.edit-mode {
    padding-top: 80px;
  }
}
</style>