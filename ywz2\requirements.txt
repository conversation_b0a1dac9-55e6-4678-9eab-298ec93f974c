# 核心框架
langchain>=0.2.0
langgraph>=0.2.0
langchain-openai>=0.1.0
langchain-community>=0.2.0
langchain-core>=0.2.0

# OpenRouter集成
openrouter>=0.1.0
requests>=2.31.0

# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0

# 数据模型和验证
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 数据库
sqlalchemy>=2.0.0
alembic>=1.13.0
psycopg2-binary>=2.9.0
redis>=5.0.0

# 安全和认证
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# 配置和环境
python-dotenv>=1.0.0

# 测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.25.0

# 代码质量
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.7.0

# 文档
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# 日志和工具
structlog>=23.2.0
python-dateutil>=2.8.0
orjson>=3.9.0
asyncio-mqtt>=0.16.0 