"""
系统配置管理
"""

import os
from pathlib import Path
from typing import Optional

class Settings:
    """系统设置"""
    
    def __init__(self):
        # 项目路径
        self.project_root = Path(__file__).parent.parent.parent
        
        # 服务器配置
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8002"))
        self.debug = os.getenv("DEBUG", "true").lower() == "true"
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        
        # LLM服务选择
        self.llm_service = os.getenv("LLM_SERVICE", "ollama").lower()
        
        # LLM通用配置
        self.llm_temperature = float(os.getenv("LLM_TEMPERATURE", "0.7"))
        self.llm_max_tokens = int(os.getenv("LLM_MAX_TOKENS", "2048"))
        self.llm_timeout = int(os.getenv("LLM_TIMEOUT", "30"))

        # Ollama配置
        self.ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://************:8080")
        self.ollama_model = os.getenv("OLLAMA_MODEL", "qwen2.5:14b")

        # OpenRouter配置
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY", "")
        self.openrouter_base_url = os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
        self.default_model = os.getenv("DEFAULT_MODEL", "meta-llama/llama-3.2-3b-instruct:free")
        
        # CORS配置
        self.cors_origins = self._parse_list(os.getenv("CORS_ORIGINS", "*"))
        self.cors_credentials = os.getenv("CORS_CREDENTIALS", "true").lower() == "true"
        
        # 前端配置
        self.frontend_path = self.project_root / "frontend"
        self.static_path = self.frontend_path / "assets"
        
        # 日志配置
        self.log_path = self.project_root / "logs"
        self.log_path.mkdir(exist_ok=True)
    
    def _parse_list(self, value: str) -> list:
        """解析逗号分隔的列表"""
        if value == "*":
            return ["*"]
        return [item.strip() for item in value.split(",") if item.strip()]
    
    @property
    def is_ollama_enabled(self) -> bool:
        """是否启用Ollama"""
        return self.llm_service == "ollama"
    
    @property
    def is_openrouter_enabled(self) -> bool:
        """是否启用OpenRouter"""
        return self.llm_service == "openrouter"
    
    def get_llm_config(self) -> dict:
        """获取LLM配置"""
        if self.is_ollama_enabled:
            return {
                "service": "ollama",
                "base_url": self.ollama_base_url,
                "model": self.ollama_model,
                "temperature": self.llm_temperature,
                "max_tokens": self.llm_max_tokens,
                "timeout": self.llm_timeout
            }
        else:
            return {
                "service": "openrouter",
                "api_key": self.openrouter_api_key,
                "base_url": self.openrouter_base_url,
                "model": self.default_model,
                "temperature": self.llm_temperature,
                "max_tokens": self.llm_max_tokens,
                "timeout": self.llm_timeout
            }

# 全局设置实例
settings = Settings()
