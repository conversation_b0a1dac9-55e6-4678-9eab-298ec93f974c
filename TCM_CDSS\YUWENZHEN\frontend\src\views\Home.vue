<template>
  <div class="page-container">
    <div class="card-container">
      <!-- 头部标题 -->
      <el-card class="header-card" shadow="never">
        <div class="header-content">
          <div class="title-section">
            <h1 class="main-title">
              <el-icon class="title-icon"><Stethoscope /></el-icon>
              YUWENZHEN
            </h1>
            <p class="subtitle">中医预问诊智能体系统</p>
          </div>
          <div class="description">
            <p>基于先进AI技术的中医智能问诊系统，为您提供专业、便捷的中医预问诊服务</p>
          </div>
        </div>
      </el-card>

      <!-- 功能导航 -->
      <el-row :gutter="20" class="feature-grid">
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card" shadow="hover" @click="goToConsultation">
            <div class="feature-content">
              <el-icon class="feature-icon" color="#409EFF"><ChatDotRound /></el-icon>
              <h3>开始问诊</h3>
              <p>与AI中医师进行智能对话，获得专业的中医诊断建议</p>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card" shadow="hover" @click="goToRecords">
            <div class="feature-content">
              <el-icon class="feature-icon" color="#67C23A"><Document /></el-icon>
              <h3>问诊记录</h3>
              <p>查看历史问诊记录，追踪健康状况变化</p>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card" shadow="hover" @click="goToAbout">
            <div class="feature-content">
              <el-icon class="feature-icon" color="#E6A23C"><InfoFilled /></el-icon>
              <h3>系统介绍</h3>
              <p>了解系统功能特点和使用方法</p>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 系统状态 -->
      <el-card class="status-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>系统状态</span>
            <el-button type="primary" size="small" @click="checkSystemHealth">
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>
          </div>
        </template>
        
        <div class="status-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="status-item">
                <el-icon :class="['status-icon', systemStatus.api ? 'status-success' : 'status-error']">
                  <CircleCheck v-if="systemStatus.api" />
                  <CircleClose v-else />
                </el-icon>
                <span>API服务</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <el-icon :class="['status-icon', systemStatus.database ? 'status-success' : 'status-error']">
                  <CircleCheck v-if="systemStatus.database" />
                  <CircleClose v-else />
                </el-icon>
                <span>数据库</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <el-icon :class="['status-icon', systemStatus.llm ? 'status-success' : 'status-error']">
                  <CircleCheck v-if="systemStatus.llm" />
                  <CircleClose v-else />
                </el-icon>
                <span>AI模型</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 快速开始 -->
      <el-card class="quick-start-card" shadow="never">
        <template #header>
          <span>快速开始</span>
        </template>
        
        <div class="quick-start-content">
          <el-steps :active="0" align-center>
            <el-step title="填写基本信息" description="输入姓名、年龄、性别等基本信息"></el-step>
            <el-step title="描述症状" description="详细描述您的身体不适症状"></el-step>
            <el-step title="智能问诊" description="AI医师会根据您的回答进行深入询问"></el-step>
            <el-step title="获得建议" description="获得专业的中医诊断建议和调理方案"></el-step>
          </el-steps>
          
          <div class="start-button-container">
            <el-button type="primary" size="large" @click="goToConsultation">
              <el-icon><Right /></el-icon>
              立即开始问诊
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { apiService } from '@/services/api'

const router = useRouter()

// 系统状态
const systemStatus = ref({
  api: false,
  database: false,
  llm: false
})

// 检查系统健康状态
const checkSystemHealth = async () => {
  try {
    const response = await apiService.health()
    systemStatus.value = {
      api: true,
      database: response.status === 'healthy',
      llm: response.status === 'healthy'
    }
    ElMessage.success('系统状态检查完成')
  } catch (error) {
    systemStatus.value = {
      api: false,
      database: false,
      llm: false
    }
    ElMessage.error('系统连接失败，请检查后端服务')
  }
}

// 导航方法
const goToConsultation = () => {
  router.push('/consultation')
}

const goToRecords = () => {
  router.push('/records')
}

const goToAbout = () => {
  router.push('/about')
}

// 组件挂载时检查系统状态
onMounted(() => {
  checkSystemHealth()
})
</script>

<style scoped>
.header-card {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-content {
  text-align: center;
  padding: 20px 0;
}

.title-section {
  margin-bottom: 20px;
}

.main-title {
  font-size: 3rem;
  font-weight: bold;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.title-icon {
  font-size: 3rem;
}

.subtitle {
  font-size: 1.2rem;
  margin: 10px 0 0 0;
  opacity: 0.9;
}

.description p {
  font-size: 1rem;
  opacity: 0.8;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.feature-grid {
  margin-bottom: 20px;
}

.feature-card {
  cursor: pointer;
  transition: transform 0.3s ease;
  height: 200px;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-content {
  text-align: center;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.feature-content h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.feature-content p {
  color: #606266;
  line-height: 1.5;
  margin: 0;
}

.status-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  padding: 20px 0;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 1rem;
}

.status-icon {
  font-size: 1.5rem;
}

.status-success {
  color: #67C23A;
}

.status-error {
  color: #F56C6C;
}

.quick-start-card {
  margin-bottom: 20px;
}

.quick-start-content {
  padding: 20px 0;
}

.start-button-container {
  text-align: center;
  margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-title {
    font-size: 2rem;
  }
  
  .title-icon {
    font-size: 2rem;
  }
  
  .feature-card {
    margin-bottom: 15px;
  }
}
</style>
