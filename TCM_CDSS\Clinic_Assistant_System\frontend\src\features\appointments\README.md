# 预约中心模块

## 功能概述

预约中心模块专注于显示预约患者和创建预约工单，采用三栏式布局设计，为前台工作人员提供高效的预约管理功能。

## 主要特性 (v3.9)

### 1. 三栏式布局设计
- **左侧栏**: 搜索和筛选功能
- **中间栏**: 预约患者列表
- **右侧栏**: 预约详情和操作

### 2. 智能筛选功能
- **患者搜索**: 支持按姓名、手机号搜索
- **日期筛选**: 今日、明日、本周、自定义日期
- **医师筛选**: 按医师过滤预约
- **状态筛选**: 待就诊、已就诊、已取消、未到
- **来源筛选**: 线上预约、现场挂号、电话预约

### 3. 预约患者管理
- **只显示预约患者**: 不显示无预约的患者
- **预约卡片**: 显示患者头像、基本信息、预约详情
- **状态管理**: 实时更新预约状态
- **快速操作**: 编辑、接诊、取消、打印

### 4. 预约工单创建
- **患者信息**: 姓名、手机号、性别、年龄
- **预约信息**: 医师、日期、时间、来源
- **表单验证**: 完整的输入验证规则
- **预约来源**: 支持多种预约渠道

## 目录结构
```
appointments/
├── views/                    # 页面视图
│   └── AppointmentList.vue   # 预约列表页面（三栏布局）
├── components/               # 功能组件（待开发）
├── composables/              # 组合式函数（待开发）
├── stores/                   # 状态管理（待开发）
├── types/                    # 类型定义（待开发）
├── constants/                # 常量配置（待开发）
└── styles/                   # 模块样式（待开发）
```

## 核心功能

### 预约筛选
- 多维度筛选：日期、医师、状态、来源
- 实时搜索：患者姓名、手机号
- 快捷日期选择：今日、明日、本周

### 预约管理
- 预约状态跟踪：pending/completed/cancelled/absent
- 预约来源管理：online/offline/phone
- 一键开始接诊：跳转到门诊工作台

### 工单创建
- 完整患者信息录入
- 预约时间冲突检测
- 表单验证和错误提示

## 技术特点

### 布局设计
- 参考门诊工作台的三栏布局
- 响应式设计适配不同屏幕
- 左侧筛选、中间列表、右侧详情

### 数据管理
- 只显示有预约的患者
- 实时统计今日预约数据
- 历史就诊记录关联

### 用户体验
- 卡片式预约展示
- 快捷操作按钮
- 确认提示避免误操作

## 数据库联动 (v3.10)

### 患者数据库集成
- **患者选择器**: 支持搜索现有患者和新建患者
- **数据关联**: 预约与患者通过patientId建立关联关系
- **信息同步**: 预约操作自动更新患者的下次预约时间
- **历史记录**: 从预约数据库动态加载患者历史就诊记录

### 预约数据库
- **完整API**: appointmentService提供增删改查功能
- **数据结构**: 包含患者关联和冗余字段设计
- **状态管理**: 支持预约状态的完整生命周期
- **统计功能**: 实时计算今日预约统计数据

### 组件架构
```
appointments/
├── views/
│   └── AppointmentList.vue     # 主页面（集成数据库）
├── components/
│   └── PatientSelector.vue     # 患者选择器组件
└── README.md
```

### 数据流程
1. **创建预约**: 选择患者 → 填写预约信息 → 保存到数据库 → 更新患者信息
2. **编辑预约**: 加载患者信息 → 修改预约 → 更新数据库
3. **取消预约**: 更新预约状态 → 清除患者下次预约时间
4. **查看详情**: 加载预约信息 → 获取患者历史记录

## 开发状态

### 已完成 ✅
- 三栏式布局重构
- 多维度筛选功能
- 预约状态管理
- 预约工单创建
- 预约详情展示
- 历史记录查看
- 响应式设计
- **患者数据库联动**
- **患者选择器组件**
- **预约数据库集成**
- **数据关联和同步**

### 计划中 🔄
- 组件拆分优化
- 状态管理重构
- 类型定义完善
- 预约冲突检测
- 批量操作功能
- 预约提醒功能

## 更新日志

### v3.10 (2025-06-24)
- 集成患者数据库联动功能
- 创建患者选择器组件
- 实现预约与患者数据关联
- 支持搜索现有患者和新建患者
- 自动同步预约和患者信息

### v3.9 (2025-06-24)
- 重新设计为三栏式布局
- 专注显示预约患者
- 增强筛选和搜索功能
- 优化预约工单创建
- 添加预约详情和操作
- 支持直接开始接诊