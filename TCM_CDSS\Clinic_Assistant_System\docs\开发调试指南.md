# 开发调试指南

## 概述
本文档提供了中医诊所助手系统的开发调试方法和功能扩展指导，帮助开发者快速定位问题、调试功能和扩展新特性。

## 目录
1. [调试方法](#调试方法)
2. [常见问题排查](#常见问题排查)
3. [功能扩展指导](#功能扩展指导)
4. [测试工具](#测试工具)
5. [性能优化](#性能优化)

---

## 调试方法

### 1. 路由跳转调试

#### 问题描述
页面点击无法跳转，常见于功能中心卡片点击后无反应。

#### 调试步骤
1. **添加路由跳转日志**
```javascript
// 在 Dashboard.vue 的 navigateToModule 函数中添加
const navigateToModule = (route: string) => {
  console.log('点击模块，准备跳转到:', route)
  console.log('当前路由:', router.currentRoute.value.path)
  router.push(route).then(() => {
    console.log('跳转成功，新路由:', router.currentRoute.value.path)
  }).catch((error) => {
    console.error('跳转失败:', error)
  })
}
```

2. **添加页面加载日志**
```javascript
// 在目标页面的 <script setup> 中添加
console.log('页面已加载:', '页面名称')
```

3. **检查路由守卫**
```javascript
// 在 router/index.ts 中添加详细日志
router.beforeEach((to, _from, next) => {
  const user = localStorage.getItem('user')
  console.log('路由守卫 - 目标路径:', to.path)
  console.log('路由守卫 - 用户状态:', user)
  console.log('路由守卫 - 需要认证:', to.meta.requiresAuth)
  // ... 其他逻辑
})
```

#### 测试工具
```javascript
// 在 Dashboard.vue 中添加测试按钮（开发时显示，生产时隐藏）
const testPharmacyNavigation = () => {
  console.log('测试药房跳转')
  console.log('当前路由:', router.currentRoute.value.path)
  router.push('/pharmacy').then(() => {
    console.log('药房跳转成功，新路由:', router.currentRoute.value.path)
    ElMessage.success('药房跳转成功')
  }).catch((error) => {
    console.error('药房跳转失败:', error)
    ElMessage.error('药房跳转失败: ' + error.message)
  })
}

// 模板中
<div v-show="false" style="position: fixed; top: 100px; right: 20px; z-index: 1000;">
  <el-button type="primary" @click="testPharmacyNavigation">测试药房跳转</el-button>
</div>
```

### 2. 组件渲染调试

#### 问题描述
页面白屏、组件无法渲染、图标不显示等。

#### 调试步骤
1. **检查控制台错误**
   - 按 F12 打开开发者工具
   - 查看 Console 标签页的错误信息
   - 特别注意模块导入错误

2. **检查图标导入**
```javascript
// 常见错误：图标不存在
import { MedicineBox } from '@element-plus/icons-vue' // ❌ 不存在
import { Box } from '@element-plus/icons-vue' // ✅ 存在
```

3. **TypeScript 类型检查**
```bash
cd frontend
npx tsc --noEmit
```

### 3. 状态管理调试

#### 问题描述
登录状态丢失、用户信息不显示、权限控制失效等。

#### 调试步骤
1. **检查 localStorage**
```javascript
// 在浏览器控制台执行
console.log('用户信息:', localStorage.getItem('user'))
console.log('认证状态:', localStorage.getItem('auth'))
```

2. **检查 Pinia Store**
```javascript
// 在组件中添加
import { useAuthStore } from '@/stores/auth'
const authStore = useAuthStore()
console.log('Store 用户状态:', authStore.user)
console.log('Store 登录状态:', authStore.isLoggedIn)
```

---

## 常见问题排查

### 1. 路由跳转失败

#### 可能原因
- 路由配置错误
- 路由守卫拦截
- 组件导入失败
- 权限验证失败

#### 排查步骤
1. 检查 `router/index.ts` 中的路由配置
2. 确认组件文件存在且路径正确
3. 检查路由守卫逻辑
4. 验证用户登录状态

### 2. 页面白屏

#### 可能原因
- JavaScript 错误
- 组件渲染失败
- 依赖缺失
- 网络问题

#### 排查步骤
1. 查看浏览器控制台错误
2. 检查网络请求状态
3. 验证依赖安装
4. 检查组件语法

### 3. 图标不显示

#### 可能原因
- 图标名称错误
- 导入失败
- 样式冲突

#### 排查步骤
1. 检查图标名称是否正确
2. 验证 Element Plus 版本
3. 查看官方图标文档

---

## 功能扩展指导

### 1. 添加新页面

#### 步骤
1. **创建页面组件**
```bash
# 在 src/views/ 下创建新目录和文件
mkdir src/views/newmodule
touch src/views/newmodule/NewModuleList.vue
```

2. **配置路由**
```javascript
// 在 router/index.ts 中添加
{
  path: '/newmodule',
  name: 'NewModule',
  component: () => import('@/views/newmodule/NewModuleList.vue'),
  meta: { requiresAuth: true }
}
```

3. **添加功能卡片**
```javascript
// 在 Dashboard.vue 的 availableModules 中添加
{
  key: 'newmodule',
  name: '新模块',
  route: '/newmodule',
  icon: 'IconName',
  color: '#HEXCODE',
  info: '模块描述信息'
}
```

4. **导入图标**
```javascript
import { IconName } from '@element-plus/icons-vue'
```

### 2. 添加新功能

#### 步骤
1. **设计功能结构**
   - 确定功能需求
   - 设计数据模型
   - 规划用户界面

2. **实现核心逻辑**
   - 创建组件
   - 实现业务逻辑
   - 添加状态管理

3. **集成到系统**
   - 添加路由配置
   - 更新导航菜单
   - 配置权限控制

### 3. 修改现有功能

#### 步骤
1. **备份当前代码**
2. **分析现有逻辑**
3. **实现修改**
4. **测试功能**
5. **更新文档**

---

## 测试工具

### 1. 开发环境测试

#### 启动开发服务器
```bash
cd frontend
npm run dev
```

#### 类型检查
```bash
npx tsc --noEmit
```

#### 代码格式化
```bash
npx prettier --write src/
```

### 2. 调试工具

#### 浏览器开发者工具
- Console: 查看日志和错误
- Network: 监控网络请求
- Elements: 检查 DOM 结构
- Sources: 调试 JavaScript 代码

#### Vue DevTools
- 安装 Vue DevTools 浏览器扩展
- 查看组件状态
- 监控状态变化
- 调试组件树

### 3. 测试方法

#### 单元测试
```bash
npm run test:unit
```

#### 集成测试
```bash
npm run test:integration
```

#### 端到端测试
```bash
npm run test:e2e
```

---

## 性能优化

### 1. 代码分割

#### 路由懒加载
```javascript
// 使用动态导入
component: () => import('@/views/ComponentName.vue')
```

#### 组件懒加载
```javascript
// 在需要时加载组件
const AsyncComponent = defineAsyncComponent(() => import('./AsyncComponent.vue'))
```

### 2. 资源优化

#### 图片优化
- 使用 WebP 格式
- 压缩图片大小
- 使用 CDN 加速

#### 代码压缩
```bash
npm run build
```

### 3. 缓存策略

#### 浏览器缓存
- 设置合适的缓存头
- 使用版本号控制缓存

#### 应用缓存
- 使用 localStorage 缓存数据
- 实现离线功能

---

## 最佳实践

### 1. 代码规范

#### 命名规范
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 变量名使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

#### 文件组织
```
src/
├── views/          # 页面组件
├── components/     # 通用组件
├── stores/         # 状态管理
├── router/         # 路由配置
├── styles/         # 样式文件
└── utils/          # 工具函数
```

### 2. 错误处理

#### 全局错误处理
```javascript
// 在 main.ts 中添加
app.config.errorHandler = (err, instance, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
}
```

#### 组件错误处理
```javascript
// 使用 try-catch 包装异步操作
try {
  await someAsyncOperation()
} catch (error) {
  console.error('操作失败:', error)
  ElMessage.error('操作失败，请重试')
}
```

### 3. 日志记录

#### 开发日志
```javascript
// 开发环境详细日志
if (import.meta.env.DEV) {
  console.log('调试信息:', data)
}
```

#### 生产日志
```javascript
// 生产环境错误日志
if (import.meta.env.PROD) {
  // 发送错误到日志服务
  logError(error)
}
```

---

## 版本信息

- **文档版本**: v1.0.0
- **最后更新**: 2024-01-15
- **适用版本**: 项目 v1.1+

## 更新记录

- **2024-01-15 v1.0.0**: 初始版本
  - 添加调试方法
  - 添加功能扩展指导
  - 添加测试工具说明
  - 添加性能优化建议
  - 添加最佳实践

---

## 联系方式

如有问题或建议，请联系开发团队。 