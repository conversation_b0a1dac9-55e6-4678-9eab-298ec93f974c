# 中医诊前预问诊智能体系统 - 设计文档总览

## 📋 项目概述

基于最新LangChain和LangGraph技术栈开发的中医诊前预问诊智能体系统，通过多智能体协同工作流，实现结构化的中医病史信息采集，为医生诊疗提供准确的患者背景资料。

## 🎯 核心目标

- **智能对话采集**: 通过自然对话方式采集患者病史信息
- **结构化信息提取**: 提取结构化的医疗实体和关键信息  
- **标准化病历生成**: 生成符合中医病历标准的格式化文档
- **智能追问补全**: 支持智能追问和信息补全机制

## 🏗️ 技术架构

### 核心技术栈
- **LangChain 0.3.0+**: 使用LCEL（LangChain Expression Language）管道开发
- **LangGraph 0.2.0+**: 多智能体工作流编排
- **Pydantic 2.8+**: 数据验证和结构化输出
- **FastAPI**: 现代化Web API框架
- **Ollama**: 本地LLM服务集成

### 架构特色
- 🤖 **多智能体协同**: 10个专业化智能体分工协作
- 🔄 **动态工作流**: LangGraph状态图编排复杂流程
- ⚡ **异步流式**: 支持实时响应和流式输出
- 🛡️ **错误恢复**: 完善的异常处理和状态恢复机制

## 📚 文档结构

### 1. [系统设计文档](./系统设计文档.md)
**完整的系统架构和设计方案**
- 整体架构设计和技术选型
- 智能体系统详细设计
- 数据模型和状态管理
- LangGraph工作流设计
- LCEL链设计详解
- 提示词工程和验证机制
- API接口设计
- 部署运维方案
- 测试策略和安全考虑
- 项目路线图

### 2. [技术实现指南](./技术实现指南.md)
**核心代码实现和开发指导**
- 环境搭建和依赖管理
- 数据模型完整实现
- LLM连接器设计
- LCEL链具体实现
- 智能体基类和专业智能体实现
- 现病史详细采集策略
- 错误处理和恢复机制

### 3. [LangGraph工作流实现](./LangGraph工作流实现.md)
**工作流编排的详细实现**
- 工作流架构概述
- 主工作流图定义
- 动态路由实现
- 并行处理机制
- 错误处理和恢复
- 自定义工作流示例

### 4. [项目结构与配置](./项目结构与配置.md)
**完整的项目组织和配置管理**
- 详细的目录结构
- 核心配置文件
- 环境变量管理
- 日志和数据库配置
- Docker容器化配置

## 🤖 智能体系统

### 监督层
- **SupervisorAgent**: 流程控制和智能体协调

### 专业智能体层
| 智能体 | 功能描述 | 采集内容 |
|--------|----------|----------|
| ChiefComplaintAgent | 主诉采集 | 主要症状、持续时间、严重程度 |
| PresentIllnessAgent | 现病史采集 | 寒热、汗、疼痛、睡眠、饮食、二便等 |
| MedicalHistoryAgent | 既往史采集 | 既往疾病、手术史、治疗史 |
| GeneticHistoryAgent | 遗传病史采集 | 家族遗传疾病情况 |
| InfectiousHistoryAgent | 传染病史采集 | 传染病接触史和患病史 |
| VaccinationHistoryAgent | 疫苗接种史采集 | 疫苗接种记录 |
| AllergyHistoryAgent | 过敏史采集 | 药物、食物等过敏信息 |
| SurgeryHistoryAgent | 手术史采集 | 手术和外伤史 |
| MarriageFertilityAgent | 婚育史采集 | 婚姻和生育相关信息 |
| FamilyHistoryAgent | 家族史采集 | 家族疾病史 |

### 现病史详细采集规范

**PresentIllnessAgent** 采用"先问有无，再深入追问"策略，系统性采集：

1. **寒热症状**: 有无、程度、时间、特征、兼症
2. **汗症**: 有无、色质、时间、多少、部位、兼症  
3. **疼痛症状**: 有无、性质、部位、时间、喜恶
4. **头身胸腹不适**: 有无、部位、性质、时间、喜恶
5. **耳目症状**: 有无、部位、性质、时间、喜恶
6. **睡眠情况**: 时间长短、入睡难易、睡眠深浅、做梦情况
7. **饮食口味**: 口渴、饮水、食欲食量、口味异常
8. **二便情况**: 大便和小便的详细情况
9. **经带胎产**: 女性专项采集
10. **小儿专项**: 儿童患者专项采集

## 🔄 工作流程

```mermaid
graph TD
    A[开始问诊] --> B[监督智能体]
    B --> C{决策路由}
    C -->|主诉未采集| D[主诉采集智能体]
    C -->|现病史未采集| E[现病史采集智能体]
    C -->|其他病史| F[专业智能体群]
    C -->|采集完成| G[报告生成]
    
    D --> B
    E --> B
    F --> B
    G --> H[结束]
    
    subgraph "现病史采集详细流程"
        E1[寒热症状] --> E2[汗症]
        E2 --> E3[疼痛症状]
        E3 --> E4[睡眠情况]
        E4 --> E5[饮食口味]
        E5 --> E6[二便情况]
    end
```

## 📊 数据模型

### 核心状态模型
```python
class InquiryState(BaseModel):
    session_id: str
    patient_info: PatientInfo
    messages: List[BaseMessage]
    current_agent: str
    completed_agents: List[str]
    
    # 病史数据
    chief_complaint: Optional[ChiefComplaintData]
    present_illness: Optional[PresentIllnessData]
    medical_history: Optional[Dict]
    # ... 其他病史字段
    
    is_complete: bool
    confidence_score: float
```

### 现病史数据模型
```python
class PresentIllnessData(BaseModel):
    cold_heat: Optional[ColdHeatSymptoms]
    sweat: Optional[SweatSymptoms]
    pain: Optional[PainSymptoms]
    sleep: Optional[SleepSymptoms]
    diet_taste: Optional[DietTasteSymptoms]
    bowel_urination: Optional[BowelUrination]
    completion_status: Dict[str, bool]
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 创建虚拟环境
python -m venv tcm_inquiry_env
source tcm_inquiry_env/bin/activate

# 安装依赖
pip install langchain==0.3.0 langchain-ollama==0.2.0 langgraph==0.2.0
pip install pydantic==2.8.0 fastapi==0.115.0 uvicorn==0.32.0
```

### 2. Ollama设置
```bash
# 安装并启动Ollama
ollama serve

# 下载模型
ollama pull qwen2.5:32b
```

### 3. 运行系统
```bash
# 启动API服务
uvicorn src.main:app --host 0.0.0.0 --port 8000

# 或使用Docker
docker-compose up -d
```

## 🧪 测试策略

### 测试层次
- **单元测试**: 智能体、链、模型的独立功能测试
- **集成测试**: 工作流集成和API接口测试  
- **端到端测试**: 完整问诊流程测试
- **性能测试**: 并发请求和响应时间测试

### 测试覆盖
- 智能体功能完整性
- LCEL链正确性
- LangGraph工作流稳定性
- 数据提取准确性
- 错误处理机制

## 📈 项目路线图

### 第一阶段：核心功能开发（4周）
- [x] 基础架构搭建
- [x] 核心智能体实现
- [x] LangGraph工作流框架
- [x] 基础测试用例

### 第二阶段：功能完善（6周）
- [ ] 专业智能体开发
- [ ] 高级功能实现
- [ ] 数据验证和质量控制
- [ ] 报告生成功能

### 第三阶段：系统集成（4周）
- [ ] API和界面开发
- [ ] WebSocket实时通信
- [ ] 完整系统测试
- [ ] 性能优化

### 第四阶段：部署上线（2周）
- [ ] 生产环境部署
- [ ] 监控系统搭建
- [ ] 用户培训
- [ ] 上线运行

## 🔧 开发指南

### 代码规范
- 遵循PEP 8标准
- 使用类型注解
- 编写详细文档字符串
- 实现全面的错误处理

### 提交规范
```
feat: 新功能
fix: 修复bug  
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🛡️ 安全考虑

- **数据加密**: 患者信息加密存储
- **访问控制**: JWT认证和权限管理
- **输入验证**: Pydantic模型验证
- **日志脱敏**: 敏感信息自动脱敏
- **本地部署**: 支持完全本地化部署

## 📞 技术支持

### 问题反馈
- 技术问题请提交Issue
- 功能建议请提交Feature Request
- 紧急问题请联系维护团队

### 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证，详见[LICENSE](../LICENSE)文件。

---

**注意**: 本设计文档为第一阶段的完整设计方案，为后续开发提供了详细的技术路线图和实现指导。请严格按照设计文档进行开发，确保系统的一致性和质量。
