"""
聊天服务 - 处理用户对话，支持短期记忆
"""

import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
from services.llm_service import llm_service

logger = logging.getLogger(__name__)

class ChatService:
    """聊天服务类 - 支持对话历史记忆"""

    def __init__(self):
        # 会话存储：session_id -> 对话历史
        self.sessions: Dict[str, List[Dict[str, Any]]] = {}

        # 基础系统提示词
        self.base_system_prompt = """你是一个专业的中医问诊助手。你的职责是采集患者的症状信息，不进行诊断或治疗建议。

请遵循以下原则：
1. 只询问症状相关信息，不提供诊断
2. 每次回复控制在50字以内
3. 语气温和专业
4. 逐步深入了解症状细节
5. 根据对话历史，避免重复询问已知信息
6. 不要使用<think>标签或冗长解释

示例回复格式：
- "请问头痛主要在哪个部位？"
- "这种疼痛持续多长时间了？"
- "还有其他伴随症状吗？"""

    def get_or_create_session(self, session_id: Optional[str] = None) -> str:
        """获取或创建会话"""
        if not session_id:
            session_id = f"chat_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

        if session_id not in self.sessions:
            self.sessions[session_id] = []
            logger.info(f"Created new chat session: {session_id}")

        return session_id

    def add_message_to_history(self, session_id: str, role: str, content: str):
        """添加消息到对话历史"""
        if session_id in self.sessions:
            self.sessions[session_id].append({
                "role": role,
                "content": content,
                "timestamp": datetime.now().isoformat()
            })

            # 限制历史记录长度，保留最近10轮对话
            if len(self.sessions[session_id]) > 20:  # 10轮对话 = 20条消息
                self.sessions[session_id] = self.sessions[session_id][-20:]

    def build_context_prompt(self, session_id: str) -> str:
        """构建包含对话历史的上下文提示词"""
        if session_id not in self.sessions or not self.sessions[session_id]:
            return self.base_system_prompt

        # 获取对话历史
        history = self.sessions[session_id]

        # 构建对话历史文本
        history_text = "\n\n=== 对话历史 ===\n"
        for msg in history[-10:]:  # 只显示最近5轮对话
            role_name = "患者" if msg["role"] == "user" else "医生"
            history_text += f"{role_name}: {msg['content']}\n"

        # 组合完整提示词
        full_prompt = f"""{self.base_system_prompt}

{history_text}

=== 当前任务 ===
基于以上对话历史，继续进行问诊。请注意：
1. 不要重复询问已经了解的信息
2. 根据患者已提供的症状，深入询问相关细节
3. 保持问诊的逻辑性和连贯性
4. 每次只问一个重点问题"""

        return full_prompt

    def process_message(self, message: str, session_id: Optional[str] = None) -> Tuple[str, str]:
        """处理用户消息，返回(回复, 会话ID)"""
        try:
            # 获取或创建会话
            session_id = self.get_or_create_session(session_id)

            logger.info(f"Processing message in session {session_id}: {message}")

            # 添加用户消息到历史
            self.add_message_to_history(session_id, "user", message)

            # 构建包含历史的提示词
            context_prompt = self.build_context_prompt(session_id)

            # 调用LLM服务
            response = llm_service.call_llm(message, context_prompt)

            # 添加AI回复到历史
            self.add_message_to_history(session_id, "assistant", response)

            logger.info(f"AI response in session {session_id}: {response}")
            return response, session_id

        except Exception as e:
            logger.error(f"Chat processing error: {e}")
            return "抱歉，处理您的消息时出现错误，请稍后再试。", session_id or ""

    def get_session_history(self, session_id: str) -> List[Dict[str, Any]]:
        """获取会话历史"""
        return self.sessions.get(session_id, [])

    def clear_session(self, session_id: str):
        """清空会话历史"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"Cleared session: {session_id}")

    def get_session_summary(self, session_id: str) -> str:
        """获取会话摘要"""
        if session_id not in self.sessions:
            return "无对话历史"

        history = self.sessions[session_id]
        if not history:
            return "无对话历史"

        # 统计信息
        user_messages = [msg for msg in history if msg["role"] == "user"]
        assistant_messages = [msg for msg in history if msg["role"] == "assistant"]

        return f"对话轮数: {len(user_messages)}, 最后更新: {history[-1]['timestamp'][:19]}"

    def get_welcome_message(self) -> str:
        """获取欢迎消息"""
        return "您好，我是您的中医问诊助手。请详细描述您的主要症状。"

# 全局聊天服务实例
chat_service = ChatService()
