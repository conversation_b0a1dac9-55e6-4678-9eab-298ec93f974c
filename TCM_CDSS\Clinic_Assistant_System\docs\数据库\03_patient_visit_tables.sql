-- =====================================================
-- 中医诊所助手系统 - 患者与就诊核心组表结构
-- =====================================================
-- 文件: 03_patient_visit_tables.sql
-- 描述: 创建患者和就诊相关表
-- 版本: v1.0
-- 创建日期: 2025-06-24
-- =====================================================

USE `tcm_clinic_system`;

-- =====================================================
-- 4.2 患者与就诊核心组
-- =====================================================

-- 4.2.1 患者信息表
CREATE TABLE `patients` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '患者ID',
    `patient_number` VARCHAR(50) NOT NULL COMMENT '病历号',
    `full_name` VARCHAR(100) NOT NULL COMMENT '姓名',
    `gender` ENUM('男', '女', '未知') NOT NULL COMMENT '性别',
    `date_of_birth` DATE NOT NULL COMMENT '出生日期',
    `phone` VARCHAR(20) NULL COMMENT '手机号',
    `id_card_number` VARCHAR(18) NULL COMMENT '身份证号',
    `address` VARCHAR(255) NULL COMMENT '联系地址',
    `allergies` TEXT NULL COMMENT '过敏史',
    `medical_history` TEXT NULL COMMENT '既往病史',
    `family_history` TEXT NULL COMMENT '家族病史',
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
    `last_visit_date` DATE NULL COMMENT '最后就诊日期',
    `next_appointment_date` DATE NULL COMMENT '下次预约日期',
    `visit_count` INT NOT NULL DEFAULT 0 COMMENT '就诊次数',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_patients_number` (`patient_number`),
    KEY `idx_patients_phone` (`phone`),
    KEY `idx_patients_name` (`full_name`),
    KEY `idx_patients_id_card` (`id_card_number`),
    KEY `idx_patients_status` (`status`),
    KEY `idx_patients_last_visit` (`last_visit_date`),
    KEY `idx_patients_next_appointment` (`next_appointment_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者信息表';

-- 4.2.2 患者联系人表
CREATE TABLE `patient_contacts` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
    `patient_id` INT NOT NULL COMMENT '患者ID',
    `name` VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    `phone` VARCHAR(20) NOT NULL COMMENT '联系电话',
    `relationship` VARCHAR(50) NOT NULL COMMENT '关系',
    `is_emergency` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否紧急联系人',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_patient_contacts_patient` (`patient_id`),
    KEY `idx_patient_contacts_emergency` (`is_emergency`),
    CONSTRAINT `fk_patient_contacts_patient` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者联系人表';

-- 4.2.3 预约记录表
CREATE TABLE `appointments` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '预约ID',
    `appointment_number` VARCHAR(50) NOT NULL COMMENT '预约号',
    `patient_id` INT NOT NULL COMMENT '患者ID',
    `doctor_id` INT NOT NULL COMMENT '医生ID',
    `appointment_date` DATE NOT NULL COMMENT '预约日期',
    `appointment_time` TIME NOT NULL COMMENT '预约时间',
    `status` ENUM('pending', 'confirmed', 'completed', 'cancelled', 'absent') NOT NULL DEFAULT 'pending' COMMENT '预约状态',
    `source` ENUM('online', 'offline', 'phone') NOT NULL COMMENT '预约来源',
    `chief_complaint` TEXT NULL COMMENT '主诉',
    `note` TEXT NULL COMMENT '备注信息',
    `created_by` INT NOT NULL COMMENT '创建人',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_appointments_number` (`appointment_number`),
    KEY `idx_appointments_patient` (`patient_id`),
    KEY `idx_appointments_doctor` (`doctor_id`),
    KEY `idx_appointments_date` (`appointment_date`),
    KEY `idx_appointments_time` (`appointment_time`),
    KEY `idx_appointments_status` (`status`),
    KEY `idx_appointments_source` (`source`),
    KEY `idx_appointments_created_by` (`created_by`),
    KEY `idx_appointments_doctor_date` (`doctor_id`, `appointment_date`),
    CONSTRAINT `fk_appointments_patient` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`),
    CONSTRAINT `fk_appointments_doctor` FOREIGN KEY (`doctor_id`) REFERENCES `staff` (`id`),
    CONSTRAINT `fk_appointments_created_by` FOREIGN KEY (`created_by`) REFERENCES `staff` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预约记录表';

-- 4.2.4 就诊记录表
CREATE TABLE `visits` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '就诊ID',
    `visit_number` VARCHAR(50) NOT NULL COMMENT '就诊号',
    `patient_id` INT NOT NULL COMMENT '患者ID',
    `doctor_id` INT NOT NULL COMMENT '医生ID',
    `appointment_id` INT NULL COMMENT '关联预约ID',
    `visit_date` DATE NOT NULL COMMENT '就诊日期',
    `visit_start_time` DATETIME NOT NULL COMMENT '就诊开始时间',
    `visit_end_time` DATETIME NULL COMMENT '就诊结束时间',
    `status` ENUM('waiting', 'in_progress', 'completed', 'billed') NOT NULL DEFAULT 'waiting' COMMENT '就诊状态',
    `visit_type` ENUM('first_visit', 'follow_up', 'emergency') NOT NULL DEFAULT 'first_visit' COMMENT '就诊类型',
    `chief_complaint` TEXT NULL COMMENT '主诉',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_visits_number` (`visit_number`),
    KEY `idx_visits_patient` (`patient_id`),
    KEY `idx_visits_doctor` (`doctor_id`),
    KEY `idx_visits_appointment` (`appointment_id`),
    KEY `idx_visits_date` (`visit_date`),
    KEY `idx_visits_status` (`status`),
    KEY `idx_visits_type` (`visit_type`),
    KEY `idx_visits_patient_date` (`patient_id`, `visit_date`),
    KEY `idx_visits_doctor_date` (`doctor_id`, `visit_date`),
    CONSTRAINT `fk_visits_patient` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`),
    CONSTRAINT `fk_visits_doctor` FOREIGN KEY (`doctor_id`) REFERENCES `staff` (`id`),
    CONSTRAINT `fk_visits_appointment` FOREIGN KEY (`appointment_id`) REFERENCES `appointments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='就诊记录表';

-- =====================================================
-- 创建触发器：自动生成编号
-- =====================================================

-- 患者编号生成触发器
DELIMITER $$
CREATE TRIGGER `tr_patients_generate_number` 
BEFORE INSERT ON `patients`
FOR EACH ROW
BEGIN
    IF NEW.patient_number IS NULL OR NEW.patient_number = '' THEN
        SET NEW.patient_number = CONCAT('P', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(LAST_INSERT_ID() + 1, 4, '0'));
    END IF;
END$$
DELIMITER ;

-- 预约编号生成触发器
DELIMITER $$
CREATE TRIGGER `tr_appointments_generate_number` 
BEFORE INSERT ON `appointments`
FOR EACH ROW
BEGIN
    IF NEW.appointment_number IS NULL OR NEW.appointment_number = '' THEN
        SET NEW.appointment_number = CONCAT('A', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(LAST_INSERT_ID() + 1, 4, '0'));
    END IF;
END$$
DELIMITER ;

-- 就诊编号生成触发器
DELIMITER $$
CREATE TRIGGER `tr_visits_generate_number` 
BEFORE INSERT ON `visits`
FOR EACH ROW
BEGIN
    IF NEW.visit_number IS NULL OR NEW.visit_number = '' THEN
        SET NEW.visit_number = CONCAT('V', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(LAST_INSERT_ID() + 1, 4, '0'));
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 创建触发器：更新患者统计信息
-- =====================================================

-- 预约创建时更新患者下次预约时间
DELIMITER $$
CREATE TRIGGER `tr_appointments_update_patient` 
AFTER INSERT ON `appointments`
FOR EACH ROW
BEGIN
    UPDATE `patients` 
    SET `next_appointment_date` = NEW.appointment_date,
        `updated_at` = NOW()
    WHERE `id` = NEW.patient_id;
END$$
DELIMITER ;

-- 预约取消时清除患者下次预约时间
DELIMITER $$
CREATE TRIGGER `tr_appointments_cancel_patient` 
AFTER UPDATE ON `appointments`
FOR EACH ROW
BEGIN
    IF NEW.status = 'cancelled' AND OLD.status != 'cancelled' THEN
        UPDATE `patients` 
        SET `next_appointment_date` = NULL,
            `updated_at` = NOW()
        WHERE `id` = NEW.patient_id;
    END IF;
END$$
DELIMITER ;

-- 就诊完成时更新患者信息
DELIMITER $$
CREATE TRIGGER `tr_visits_update_patient` 
AFTER UPDATE ON `visits`
FOR EACH ROW
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        UPDATE `patients` 
        SET `last_visit_date` = NEW.visit_date,
            `visit_count` = `visit_count` + 1,
            `updated_at` = NOW()
        WHERE `id` = NEW.patient_id;
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 插入示例数据
-- =====================================================

-- 插入示例患者数据
INSERT INTO `patients` (`patient_number`, `full_name`, `gender`, `date_of_birth`, `phone`, `address`, `status`) VALUES
('P20250624001', '李小明', '男', '1990-05-15', '13800138001', '北京市朝阳区xxx街道', 'active'),
('P20250624002', '王小红', '女', '1995-08-22', '13900139002', '北京市海淀区xxx路', 'active'),
('P20250624003', '张三', '男', '1985-12-10', '13700137003', '北京市西城区xxx胡同', 'active'),
('P20250624004', '李四', '女', '1992-03-18', '13600136004', '北京市东城区xxx大街', 'active'),
('P20250624005', '王五', '男', '1988-07-25', '13500135005', '北京市丰台区xxx小区', 'active');

-- 插入示例联系人数据
INSERT INTO `patient_contacts` (`patient_id`, `name`, `phone`, `relationship`, `is_emergency`) VALUES
(1, '李大明', '13800138011', '父亲', TRUE),
(2, '王大红', '13900139012', '母亲', TRUE),
(3, '张小三', '13700137013', '配偶', TRUE),
(4, '李小四', '13600136014', '女儿', FALSE),
(5, '王小五', '13500135015', '儿子', FALSE);

-- 插入默认管理员账号
INSERT INTO `staff` (`employee_number`, `full_name`, `phone`, `password_hash`, `role_id`, `title`) VALUES
('ADMIN001', '系统管理员', '13000000000', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, '系统管理员'),
('DOC001', '李时珍', '13800138000', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 2, '主任医师'),
('DOC002', '华佗', '13900139000', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 2, '副主任医师'),
('REC001', '前台小王', '13700137000', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 5, '前台接待'),
('PHA001', '药师小李', '13600136000', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 4, '主管药师');

-- 注意：密码哈希对应的明文密码是 "password"，实际使用时应该使用更安全的密码
