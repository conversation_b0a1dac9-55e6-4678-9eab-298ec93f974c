"""
LLM服务 - 统一的大语言模型接口
"""

import requests
import logging
from typing import Optional
from config.settings import settings

logger = logging.getLogger(__name__)

class LLMService:
    """LLM服务类"""
    
    def __init__(self):
        self.config = settings.get_llm_config()
        self.service_type = self.config["service"]
        
    def call_llm(self, message: str, system_prompt: Optional[str] = None) -> str:
        """调用LLM模型"""
        try:
            if self.service_type == "ollama":
                return self._call_ollama(message, system_prompt)
            elif self.service_type == "openrouter":
                return self._call_openrouter(message, system_prompt)
            else:
                raise ValueError(f"不支持的LLM服务类型: {self.service_type}")
                
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            return "抱歉，系统暂时无法响应，请稍后再试。"
    
    def _call_ollama(self, message: str, system_prompt: Optional[str] = None) -> str:
        """调用Ollama模型"""
        try:
            # 构建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": message})
            
            # 构建请求数据
            data = {
                "model": self.config["model"],
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": self.config["temperature"],
                    "num_predict": self.config["max_tokens"]
                }
            }
            
            # 发送请求
            response = requests.post(
                f"{self.config['base_url']}/api/chat",
                json=data,
                timeout=self.config["timeout"]
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("message", {}).get("content", "")
                return self._clean_response(content)
            else:
                logger.error(f"Ollama API error: {response.status_code} - {response.text}")
                return "抱歉，系统暂时无法响应，请稍后再试。"
                
        except requests.exceptions.Timeout:
            logger.error("Ollama request timeout")
            return "系统响应超时，请稍后再试。"
        except requests.exceptions.ConnectionError:
            logger.error("Ollama connection error")
            return "无法连接到AI服务，请检查网络连接。"
        except Exception as e:
            logger.error(f"Ollama call failed: {e}")
            return "系统出现错误，请稍后再试。"
    
    def _call_openrouter(self, message: str, system_prompt: Optional[str] = None) -> str:
        """调用OpenRouter模型"""
        try:
            # 构建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": message})
            
            # 构建请求数据
            data = {
                "model": self.config["model"],
                "messages": messages,
                "temperature": self.config["temperature"],
                "max_tokens": self.config["max_tokens"]
            }
            
            headers = {
                "Authorization": f"Bearer {self.config['api_key']}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            response = requests.post(
                f"{self.config['base_url']}/chat/completions",
                json=data,
                headers=headers,
                timeout=self.config["timeout"]
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                return self._clean_response(content)
            else:
                logger.error(f"OpenRouter API error: {response.status_code} - {response.text}")
                return "抱歉，系统暂时无法响应，请稍后再试。"
                
        except requests.exceptions.Timeout:
            logger.error("OpenRouter request timeout")
            return "系统响应超时，请稍后再试。"
        except requests.exceptions.ConnectionError:
            logger.error("OpenRouter connection error")
            return "无法连接到AI服务，请检查网络连接。"
        except Exception as e:
            logger.error(f"OpenRouter call failed: {e}")
            return "系统出现错误，请稍后再试。"
    
    def _clean_response(self, content: str) -> str:
        """清理响应内容"""
        content = content.strip()
        
        # 移除可能的think标签
        if "<think>" in content:
            content = content.split("</think>")[-1].strip()
        
        # 限制长度
        if len(content) > 100:
            content = content[:100] + "..."
        
        return content if content else "请详细描述您的症状。"
    
    def check_health(self) -> bool:
        """检查LLM服务健康状态"""
        try:
            if self.service_type == "ollama":
                response = requests.get(f"{self.config['base_url']}/api/tags", timeout=5)
                return response.status_code == 200
            elif self.service_type == "openrouter":
                # OpenRouter没有专门的健康检查接口，这里简单检查连通性
                return bool(self.config.get("api_key"))
            return False
        except:
            return False

# 全局LLM服务实例
llm_service = LLMService()
