# Shared 共享资源模块

共享资源模块包含在整个应用中可复用的组件、工具和样式。

## 目录结构

### components/ - 通用组件
- **ui/** - UI基础组件
  - **Button/** - 按钮组件
  - **Modal/** - 模态框组件
  - **Table/** - 表格组件
- **business/** - 业务组件
  - **PatientCard/** - 患者卡片组件
  - **StatCard/** - 统计卡片组件
  - **SearchForm/** - 搜索表单组件
- **layout/** - 布局组件
  - **Header/** - 头部组件
  - **Sidebar/** - 侧边栏组件
  - **Footer/** - 底部组件

### composables/ - 共享组合式函数
- **usePermissions.ts** - 权限管理
- **useTheme.ts** - 主题管理
- **useLoading.ts** - 加载状态管理
- **useNotification.ts** - 通知管理

### types/ - 全局类型定义
- **api.ts** - API相关类型
- **user.ts** - 用户相关类型
- **common.ts** - 通用类型
- **index.ts** - 类型导出

### constants/ - 常量定义
- **api.ts** - API常量
- **permissions.ts** - 权限常量
- **routes.ts** - 路由常量
- **index.ts** - 常量导出

### styles/ - 全局样式
- **variables.css** - 样式变量
- **global.css** - 全局样式
- **themes/** - 主题样式
  - **default.css** - 默认主题
  - **dark.css** - 暗色主题 