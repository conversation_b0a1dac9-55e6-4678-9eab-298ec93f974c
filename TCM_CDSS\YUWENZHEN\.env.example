# YUWENZHEN - 中医预问诊智能体系统环境配置 (简化版)

# ==================== 应用基础配置 ====================
APP_NAME="YUWENZHEN - 中医预问诊智能体系统"
APP_VERSION="2.0.0"
DEBUG=true
ENVIRONMENT=development
HOST=127.0.0.1
PORT=8000

# ==================== 数据库配置 ====================
# 统一使用SQLite数据库
DATABASE_URL=sqlite:///./data/yuwenzhen.db
DATABASE_ECHO=false

# ==================== 缓存配置 ====================
# 使用本地缓存，不需要Redis
ENABLE_REDIS=false
CACHE_TTL=1800
SESSION_TTL=3600

# ==================== LLM配置 ====================
# LLM服务类型: ollama (主要) 或 openrouter (备选)
LLM_SERVICE=ollama

# Ollama配置 (本地部署，推荐)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:7b

# OpenRouter配置 (备选，需要API密钥)
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=anthropic/claude-3.5-sonnet

# LLM通用配置
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=1024
LLM_TIMEOUT=30

# ==================== 安全配置 ====================
SECRET_KEY=yuwenzhen-dev-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# ==================== CORS配置 ====================
# 开发环境CORS配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_CREDENTIALS=true

# ==================== 文件配置 ====================
MAX_FILE_SIZE=5242880
UPLOAD_DIR=./data/uploads

# ==================== 日志配置 ====================
LOG_LEVEL=DEBUG
LOG_FORMAT=simple

# ==================== 前端配置 ====================
FRONTEND_URL=http://localhost:3000
STATIC_FILES_DIR=./frontend/dist

# ==================== 配置说明 ====================
#
# 简化配置说明:
# 1. 开发环境配置:
#    - 使用SQLite数据库 (无需安装数据库服务)
#    - 使用Ollama本地LLM (需要安装Ollama)
#    - 启用调试模式
#    - 本地缓存 (无需Redis)
#
# 2. 如果使用OpenRouter:
#    - 设置 LLM_SERVICE=openrouter
#    - 配置 OPENROUTER_API_KEY
#
# 3. 快速开始:
#    - 复制此文件为 .env
#    - 如果使用Ollama，确保Ollama服务运行
#    - 如果使用OpenRouter，设置API密钥
#    - 运行 python scripts/start.py dev
