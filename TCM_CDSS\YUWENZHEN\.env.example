# YUWENZHEN - 中医预问诊智能体系统环境配置

# ==================== 应用基础配置 ====================
APP_NAME="YUWENZHEN - 中医预问诊智能体系统"
APP_VERSION="2.0.0"
DEBUG=false
ENVIRONMENT=development
HOST=0.0.0.0
PORT=8000

# ==================== 数据库配置 ====================
# 生产环境推荐使用PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost:5432/yuwenzhen

# 开发环境可以使用SQLite
DATABASE_URL=sqlite:///./data/yuwenzhen.db
DATABASE_ECHO=false

# ==================== Redis缓存配置 ====================
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_PASSWORD=

# ==================== LLM配置 ====================
# LLM服务类型: openrouter 或 ollama
LLM_SERVICE=openrouter

# OpenRouter配置 (推荐用于生产环境)
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=anthropic/claude-3.5-sonnet

# Ollama配置 (适用于本地部署)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:32b

# LLM通用配置
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2048
LLM_TIMEOUT=30

# ==================== 安全配置 ====================
SECRET_KEY=yuwenzhen-secret-key-change-in-production-environment
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
PASSWORD_MIN_LENGTH=8

# ==================== CORS配置 ====================
# 开发环境
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000
# 生产环境示例
# CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

CORS_CREDENTIALS=true
CORS_METHODS=*
CORS_HEADERS=*

# ==================== 缓存配置 ====================
CACHE_TTL=3600
SESSION_TTL=7200

# ==================== 限流配置 ====================
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# ==================== 文件配置 ====================
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./data/uploads

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/app.log

# ==================== 前端配置 ====================
FRONTEND_URL=http://localhost:3000
STATIC_FILES_DIR=./frontend/dist

# ==================== 监控配置 ====================
ENABLE_METRICS=true
METRICS_PORT=9090

# ==================== 邮件配置 (可选) ====================
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=true

# ==================== 第三方服务配置 (可选) ====================
SPEECH_TO_TEXT_API_KEY=
IMAGE_RECOGNITION_API_KEY=

# ==================== 开发配置 ====================
# 开发环境特殊配置
DEV_RELOAD=true
DEV_DEBUG=true

# ==================== 生产配置 ====================
# 生产环境特殊配置
PROD_WORKERS=4
PROD_MAX_CONNECTIONS=1000

# ==================== Docker配置 ====================
# Docker环境变量
DOCKER_ENV=false
CONTAINER_NAME=yuwenzhen

# ==================== 备份配置 ====================
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ==================== 特性开关 ====================
FEATURE_WEBSOCKET=true
FEATURE_ASYNC_PROCESSING=true
FEATURE_CACHING=true
FEATURE_RATE_LIMITING=true

# ==================== 调试配置 ====================
DEBUG_SQL=false
DEBUG_CACHE=false
DEBUG_LLM=false

# ==================== 性能配置 ====================
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30
CONNECTION_POOL_SIZE=20

# ==================== 安全增强配置 ====================
ENABLE_HTTPS=false
SSL_CERT_PATH=
SSL_KEY_PATH=
SECURITY_HEADERS=true

# ==================== 国际化配置 ====================
DEFAULT_LANGUAGE=zh-CN
SUPPORTED_LANGUAGES=zh-CN,en-US

# ==================== 分析配置 ====================
ANALYTICS_ENABLED=false
ANALYTICS_API_KEY=

# ==================== 通知配置 ====================
NOTIFICATION_ENABLED=false
WEBHOOK_URL=

# ==================== 测试配置 ====================
TEST_DATABASE_URL=sqlite:///./test.db
TEST_REDIS_URL=redis://localhost:6379/1

# ==================== 示例配置说明 ====================
# 
# 1. 开发环境快速配置:
#    - 使用SQLite数据库
#    - 使用Ollama本地LLM
#    - 启用调试模式
#
# 2. 生产环境配置:
#    - 使用PostgreSQL数据库
#    - 使用OpenRouter云端LLM
#    - 禁用调试模式
#    - 配置HTTPS和安全头
#
# 3. Docker环境配置:
#    - 设置DOCKER_ENV=true
#    - 调整数据库和Redis连接地址
#    - 配置容器间网络
#
# 4. 安全注意事项:
#    - 生产环境必须更改SECRET_KEY
#    - 限制CORS_ORIGINS到具体域名
#    - 使用强密码和API密钥
#    - 启用HTTPS和安全头
#
# 5. 性能优化:
#    - 根据服务器配置调整PROD_WORKERS
#    - 优化数据库连接池大小
#    - 配置适当的缓存TTL
#    - 设置合理的限流参数
